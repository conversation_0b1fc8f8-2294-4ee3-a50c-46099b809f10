﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>Factor</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lkpSmallUOM.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtAudiancePrice.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="chk_LargeIsStopped.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnDelete.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grd_Mtrx.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btn_AddNewInter_Code.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnDelete.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;tab_PriceLevels.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoItemType.Properties.Items1" xml:space="preserve">
    <value>Inventory Item</value>
  </data>
  <data name="chk_SmallIsStopped.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="rdoSellUOM2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txt_MediumUOMCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repPriceLevels.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_m_MinQty.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lblPrTaxRatio.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtHeight.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_QtyFrom.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_SellPrice.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rep_M_Spin.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="col_m_ReorderLevel.Caption" xml:space="preserve">
    <value>Reorder Level</value>
  </data>
  <data name="&gt;&gt;labelControl16.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="groupBox2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMaxQty.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Items1" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_SLQtyNums.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;labelControl19.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="&gt;&gt;rep_SLQtyNums.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl35.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMinQty.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grp_ItemType.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="rdoItemType.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>218, 24</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="txtMinQty.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;txtLargeUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>96</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="repPriceLevels.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;lblPrTaxVal.Name" xml:space="preserve">
    <value>lblPrTaxVal</value>
  </data>
  <data name="col_m_PurchasePrice.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tabDimension.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_stores.Columns5" xml:space="preserve">
    <value>Name3</value>
  </data>
  <data name="txtDescEn.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>450, 12</value>
  </data>
  <data name="&gt;&gt;gv_Matrix.Name" xml:space="preserve">
    <value>gv_Matrix</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl11.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;labelControl7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtItemCode2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="rdoItemType.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="&gt;&gt;rep_stores.Name" xml:space="preserve">
    <value>rep_stores</value>
  </data>
  <data name="col_m_MaxQty.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>Audiance Price</value>
  </data>
  <data name="&gt;&gt;labelControl29.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoSellUOM0.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="gridView7.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_SalesPerQty.Size" type="System.Drawing.Size, System.Drawing">
    <value>745, 358</value>
  </data>
  <data name="grp_UOM.Text" xml:space="preserve">
    <value>Units of Measure</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="&gt;&gt;txtLargeUOMPrice.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;col_m_Attribute2.Name" xml:space="preserve">
    <value>col_m_Attribute2</value>
  </data>
  <data name="chk_SmallIsStopped.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl12.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_Store.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_SellPrice.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="&gt;&gt;labelControl20.Name" xml:space="preserve">
    <value>labelControl20</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_VariableWeight.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;chk_IsPos.Name" xml:space="preserve">
    <value>chk_IsPos</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtItemCode2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnPrev.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gv_SubTaxes.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM0.Name" xml:space="preserve">
    <value>rdoPrchsUOM0</value>
  </data>
  <data name="&gt;&gt;tab_image.Parent" xml:space="preserve">
    <value>tab_Control1</value>
  </data>
  <data name="txtMinQty.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtReorder.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM0.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpCategory.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="grpExtra.Text" xml:space="preserve">
    <value>Extra Data</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lblSalesDiscountRatio.Name" xml:space="preserve">
    <value>lblSalesDiscountRatio</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtHeight.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_Mtrx.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_Vnd_VendorId.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl26.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpCategory.Properties.Columns25" xml:space="preserve">
    <value />
  </data>
  <data name="cmb_WeightUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>122, 20</value>
  </data>
  <data name="col_QtyTo.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="lkpSmallUOM.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsPos.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl33.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txt_LargeUOMCode.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="colLength.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Items4" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_WarrantyMonths.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;grd_SubTaxes.Name" xml:space="preserve">
    <value>grd_SubTaxes</value>
  </data>
  <data name="btnDelete.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rdoSellUOM0.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtItemCode1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;chk_IsPos.Parent" xml:space="preserve">
    <value>grp_ItemType</value>
  </data>
  <data name="txtMediumUOMFactor.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="&gt;&gt;labelControl34.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colLength.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl7.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="tab_Control1.AppearancePage.Header.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;tabPriceChange.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_Attribute2.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="txtLength.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtItemNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>229, 66</value>
  </data>
  <data name="chk_IsLibra.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_m_MaxQty.Name" xml:space="preserve">
    <value>col_m_MaxQty</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkpLargeUOM.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl14.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cmbChangeSellPrice.Parent" xml:space="preserve">
    <value>tabPriceChange</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="cmb_WeightUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 23</value>
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtItemCode2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tab_InventoryLevels.Text" xml:space="preserve">
    <value>Inventory Levels</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl33.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 13</value>
  </data>
  <data name="&gt;&gt;colHeight.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_m_Attribute3.Name" xml:space="preserve">
    <value>col_m_Attribute3</value>
  </data>
  <data name="col_Rate.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;chk_calcTaxBeforeDisc.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="col_QtyTo.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="&gt;&gt;col_m_PurchasePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdSlPLevel.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl23.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl16.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 13</value>
  </data>
  <data name="&gt;&gt;txtCustomPurchasesTaxRatio.Name" xml:space="preserve">
    <value>txtCustomPurchasesTaxRatio</value>
  </data>
  <data name="&gt;&gt;btnAddPicture.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;lblSalesDiscountRatio.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtCustomSalesTaxRatio.TabIndex" type="System.Int32, mscorlib">
    <value>116</value>
  </data>
  <data name="lblPrTaxVal.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>84, 10</value>
  </data>
  <data name="txtPurchasePrice.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDesc.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 46</value>
  </data>
  <data name="&gt;&gt;txtLargeUOMFactor.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="lkp_SubTaxes.Columns10" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="rdoSellUOM1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;cmbChangePriceMethod.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtLength.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_m_Attribute2.Width" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="grd_SubTaxes.TabIndex" type="System.Int32, mscorlib">
    <value>107</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="grp_international.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="groupBox5.Size" type="System.Drawing.Size, System.Drawing">
    <value>365, 211</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grp_ItemType.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_m_PurchasePrice.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMaxQty.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCategory.Properties.Columns22" xml:space="preserve">
    <value>Name47</value>
  </data>
  <data name="gridColumn4.Width" type="System.Int32, mscorlib">
    <value>121</value>
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;IC_ItemSubTaxesId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_SmallUOMPrice.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtItemNameAr.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtPurchasePrice.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="btnAddPicture.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="txtDescEn.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Properties.Mask.EditMask" xml:space="preserve">
    <value>f0</value>
  </data>
  <data name="&gt;&gt;labelControl22.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gv_Matrix.Appearance.GroupPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn4.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tab_SubTaxes.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtReorder.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>97</value>
  </data>
  <data name="col_m_name.AppearanceCell.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>255, 255, 192</value>
  </data>
  <data name="lkpComp.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;txtMediumUOMPrice.Name" xml:space="preserve">
    <value>txtMediumUOMPrice</value>
  </data>
  <data name="grd_SalesPerQty.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="&gt;&gt;colPrPLName.Name" xml:space="preserve">
    <value>colPrPLName</value>
  </data>
  <data name="tab_InventoryLevels.Size" type="System.Drawing.Size, System.Drawing">
    <value>506, 123</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnPrev.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl16.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtLength.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_Attribute2.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM0.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="tab_Control1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 92</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtReorder.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chkIsExpire.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_stores.Columns4" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="lkpComp.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsDeleted.TabIndex" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="lkpComp.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_LargeUOMPrice.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="tabTax.Size" type="System.Drawing.Size, System.Drawing">
    <value>506, 123</value>
  </data>
  <data name="lkpCategory.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_ItemCode2.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_MaxQty.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items6" xml:space="preserve">
    <value>Avg Price</value>
  </data>
  <data name="labelControl9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_m_MinQty.Caption" xml:space="preserve">
    <value>Min Qty</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="rdoPrchsUOM0.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Rate.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtItemCode1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_m_Attribute3.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_LargeUOMCode.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="lkpComp.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tab_Control1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btn_MatrixPrint.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 23</value>
  </data>
  <data name="tab_Control1.HeaderAutoFill" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtHeight.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_Vendors.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_WarrantyMonths.Parent" xml:space="preserve">
    <value>tabWarranty</value>
  </data>
  <data name="&gt;&gt;labelControl37.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="txt_WarrantyMonths.Location" type="System.Drawing.Point, System.Drawing">
    <value>336, 19</value>
  </data>
  <data name="lblSalesTaxRatio.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;col_m_ItemCode2.Name" xml:space="preserve">
    <value>col_m_ItemCode2</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>475, 10</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl22.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items6" xml:space="preserve">
    <value>Wholesale Unit 1</value>
  </data>
  <data name="col_m_Attribute1.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rdoItemType.Properties.Items7" xml:space="preserve">
    <value>Matrix</value>
  </data>
  <data name="txtSalesDiscRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl24.Name" xml:space="preserve">
    <value>labelControl24</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns" xml:space="preserve">
    <value>UOMId</value>
  </data>
  <data name="labelControl31.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tab_InventoryLevels.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM0.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns" xml:space="preserve">
    <value>UOMId</value>
  </data>
  <data name="&gt;&gt;chk_MediumIsStopped.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="gridView6.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 9</value>
  </data>
  <data name="tabDiscount.Text" xml:space="preserve">
    <value>Discounts</value>
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="labelControl32.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>309, 20</value>
  </data>
  <data name="grp_international.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 306</value>
  </data>
  <data name="chk_IsLibra.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_MediumIsStopped.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;colLength.Name" xml:space="preserve">
    <value>colLength</value>
  </data>
  <data name="&gt;&gt;txt_MediumUOMCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtWidth.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtLargeUOMPrice.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="&gt;&gt;btn_GenMtrx.Parent" xml:space="preserve">
    <value>tab_Matrix</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtWidth.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grdPrPLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>288, 216</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_MinQty.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtPurchasePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tab_InventoryLevels.Name" xml:space="preserve">
    <value>tab_InventoryLevels</value>
  </data>
  <data name="&gt;&gt;chk_SmallIsStopped.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpComp.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="itemPhoto.Size" type="System.Drawing.Size, System.Drawing">
    <value>715, 367</value>
  </data>
  <data name="labelControl5.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;groupBox4.Name" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="cmbChangeSellPrice.EditValue" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grd_SubTaxes.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 14</value>
  </data>
  <data name="lkpCategory.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtLargeUOMFactor.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_PurchasePrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;repPriceLevels.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM0.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl12.Parent" xml:space="preserve">
    <value>tabDimension</value>
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="&gt;&gt;rep_m_attribute.Name" xml:space="preserve">
    <value>rep_m_attribute</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>col_ItemId</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInternationalCode.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceFocused.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="col_m_ReorderLevel.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_m_MinQty.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_Vnd_PurchasePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_PricingWithSmall.Parent" xml:space="preserve">
    <value>grp_ItemType</value>
  </data>
  <data name="txtLength.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="itemPhoto.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="txtPurchaseTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>324, 6</value>
  </data>
  <data name="col_m_MediumUOMPrice.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSalesTaxValue.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_LargeUOMPrice.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtPurchaseTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 20</value>
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>tabPriceChange</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl18.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl34.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Rate.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_IsPos.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btn_AddNewInter_Code.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMaxQty.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 563</value>
  </data>
  <data name="btnAddPicture.ToolTip" xml:space="preserve">
    <value>Upload Pic</value>
  </data>
  <data name="tab_Control1.AppearancePage.Header.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;tab_Other.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtSalesTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>324, 31</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpSmallUOM.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="tabDiscount.Size" type="System.Drawing.Size, System.Drawing">
    <value>506, 123</value>
  </data>
  <data name="&gt;&gt;col_m_MaxQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpLargeUOM.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="lkpLargeUOM.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rdoPrchsUOM2.Location" type="System.Drawing.Point, System.Drawing">
    <value>259, 79</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>228, 9</value>
  </data>
  <data name="lkpMediumUOM.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="grpExtra.Location" type="System.Drawing.Point, System.Drawing">
    <value>255, 302</value>
  </data>
  <data name="txtHeight.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="chk_SmallIsStopped.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lblSalesDiscountRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceFocused.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpCategory.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;btnAddPicture.Name" xml:space="preserve">
    <value>btnAddPicture</value>
  </data>
  <data name="btn_AddNewInter_Code.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 18</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtDescEn.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="rep_stores.Columns7" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtSmallUOMPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>375, 28</value>
  </data>
  <data name="txtItemNameEn.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtMaxQty.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbChangeSellPrice.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpComp.Properties.Columns" xml:space="preserve">
    <value>CompanyNameEn</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl22.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lblSalesTaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>189, 34</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;chkIsExpire.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoItemType.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 17</value>
  </data>
  <data name="lkpComp.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtItemNameAr.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtSalesTaxRatio.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="col_m_MediumUOMPrice.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_LargeUOMPrice.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpSmallUOM.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtAudiancePrice.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_ReorderLevel.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns8" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="btnAddUOM.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="labelControl31.TabIndex" type="System.Int32, mscorlib">
    <value>117</value>
  </data>
  <data name="chk_IsDeleted.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 37</value>
  </data>
  <data name="rdoItemType.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnNext.Text" xml:space="preserve">
    <value>=&gt;</value>
  </data>
  <data name="chk_IsLibra.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl30.Name" xml:space="preserve">
    <value>labelControl30</value>
  </data>
  <data name="labelControl12.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>342, 12</value>
  </data>
  <data name="txtItemNameAr.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtLargeUOMFactor.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSalesTaxValue.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_LargeUOMCode.TabIndex" type="System.Int32, mscorlib">
    <value>124</value>
  </data>
  <data name="rdoSellUOM1.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="rdoSellUOM1.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btnPrev.Name" xml:space="preserve">
    <value>btnPrev</value>
  </data>
  <data name="txtSalesTaxValue.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colPlSmallUOMPrice.Width" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="lblSalesDiscountRatio.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl28.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;grp_UOM.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="repPriceLevels.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="grdPrPLevel.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;tab_image.Name" xml:space="preserve">
    <value>tab_image</value>
  </data>
  <data name="&gt;&gt;tabPriceChange.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lkpCategory.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="itemPhoto.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtInternationalCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM2.Name" xml:space="preserve">
    <value>rdoSellUOM2</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colWidth.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_m_Attribute2.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMaxQty.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtLargeUOMPrice.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnRemovePic.TabIndex" type="System.Int32, mscorlib">
    <value>80</value>
  </data>
  <data name="col_m_ItemId.Caption" xml:space="preserve">
    <value>ItemId</value>
  </data>
  <data name="col_m_PurchasePrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="rep_stores.Columns" xml:space="preserve">
    <value>StoreNameAr</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>tabDiscount</value>
  </data>
  <data name="rdoSellUOM0.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txtLargeUOMPrice.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_IC_Item</value>
  </data>
  <data name="col_m_SmallUOMPrice.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_Vnd_VendorId.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txtSalesTaxValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl33.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 63</value>
  </data>
  <data name="&gt;&gt;gridView7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtAudiancePrice.TabIndex" type="System.Int32, mscorlib">
    <value>108</value>
  </data>
  <data name="&gt;&gt;labelControl8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="rep_stores.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="grp_ItemType.Text" xml:space="preserve">
    <value>Item Type</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="txtPurchasePrice.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_m_Attribute3.Width" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="txtMediumUOMPrice.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl35.TabIndex" type="System.Int32, mscorlib">
    <value>109</value>
  </data>
  <data name="&gt;&gt;col_m_LargeUOMPrice.Name" xml:space="preserve">
    <value>col_m_LargeUOMPrice</value>
  </data>
  <data name="lstInternationalCodes.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chkIsExpire.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="rep_M_Spin.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_QtyFrom.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl1.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>183</value>
  </data>
  <data name="&gt;&gt;col_m_Attribute1.Name" xml:space="preserve">
    <value>col_m_Attribute1</value>
  </data>
  <data name="&gt;&gt;txtCustomPurchasesTaxRatio.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="colPlSmallUOMPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="btnPrev.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 37</value>
  </data>
  <data name="SubTaxId.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;colPLName.Name" xml:space="preserve">
    <value>colPLName</value>
  </data>
  <data name="grd_Vendors.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="&gt;&gt;chk_LargeIsStopped.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rdoPrchsUOM0.TabIndex" type="System.Int32, mscorlib">
    <value>119</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="colPrPLName.Width" type="System.Int32, mscorlib">
    <value>182</value>
  </data>
  <data name="&gt;&gt;labelControl25.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items3" xml:space="preserve">
    <value>Fixed Price</value>
  </data>
  <data name="chk_IsDeleted.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridView7.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="SubTaxId.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grp_ItemType.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 3</value>
  </data>
  <data name="col_Store.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpLargeUOM.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_LargeUOMCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtReorder.Name" xml:space="preserve">
    <value>txtReorder</value>
  </data>
  <data name="&gt;&gt;rdoItemType.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.RadioGroup, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_PricingWithSmall.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="col_m_Attribute2.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit5.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lblSalesTaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="lkpCategory.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblPrTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 13</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtLargeUOMPrice.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="colWidth.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsDeleted.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtItemCode1.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txtPurchaseTaxRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl23.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM0.Name" xml:space="preserve">
    <value>rdoSellUOM0</value>
  </data>
  <data name="txtReorder.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsLibra.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtReorder.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_VariableWeight.Parent" xml:space="preserve">
    <value>grp_ItemType</value>
  </data>
  <data name="col_m_ItemCode2.VisibleIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="colPlMediumUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl16.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMaxQty.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_QtyFrom.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM1.Name" xml:space="preserve">
    <value>rdoSellUOM1</value>
  </data>
  <data name="rep_Vendors.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;chkIsExpire.Name" xml:space="preserve">
    <value>chkIsExpire</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;itemPhoto.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;grd_SalesPerQty.Name" xml:space="preserve">
    <value>grd_SalesPerQty</value>
  </data>
  <data name="&gt;&gt;btnNext.Name" xml:space="preserve">
    <value>btnNext</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;col_Store.Name" xml:space="preserve">
    <value>col_Store</value>
  </data>
  <data name="col_QtyTo.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtLength.Name" xml:space="preserve">
    <value>txtLength</value>
  </data>
  <data name="&gt;&gt;grdQty.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtPurchasePrice.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtDesc.Location" type="System.Drawing.Point, System.Drawing">
    <value>534, 36</value>
  </data>
  <data name="col_m_Attribute1.AppearanceCell.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>255, 255, 192</value>
  </data>
  <data name="&gt;&gt;colPrPriceLevelId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.TabIndex" type="System.Int32, mscorlib">
    <value>114</value>
  </data>
  <data name="txt_MediumUOMCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;grp_international.Name" xml:space="preserve">
    <value>grp_international</value>
  </data>
  <data name="&gt;&gt;txtCustomSalesTaxRatio.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="txtItemNameAr.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtItemNameEn.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtHeight.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM1.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="txtItemCode1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtMinQty.Parent" xml:space="preserve">
    <value>tab_InventoryLevels</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="repPriceLevels.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnAddPicture.Location" type="System.Drawing.Point, System.Drawing">
    <value>723, 304</value>
  </data>
  <data name="lblPrTaxVal.Text" xml:space="preserve">
    <value>Purchase Tax Value</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>808, 31</value>
  </data>
  <data name="lkpComp.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="txtInternationalCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;chk_IsDeleted.Name" xml:space="preserve">
    <value>chk_IsDeleted</value>
  </data>
  <data name="&gt;&gt;lblSalesTaxValue.Name" xml:space="preserve">
    <value>lblSalesTaxValue</value>
  </data>
  <data name="lkpComp.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gv_Matrix.Appearance.GroupPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_m_MinQty.Name" xml:space="preserve">
    <value>col_m_MinQty</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblSalesTaxValue.TabIndex" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="&gt;&gt;txtSmallUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lstInternationalCodes.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="txtItemCode1.Location" type="System.Drawing.Point, System.Drawing">
    <value>581, 39</value>
  </data>
  <data name="chk_IsDeleted.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_SmallIsStopped.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtPurchasePrice.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_Code1.Caption" xml:space="preserve">
    <value>Code1</value>
  </data>
  <data name="labelControl17.TabIndex" type="System.Int32, mscorlib">
    <value>104</value>
  </data>
  <data name="&gt;&gt;txtInternationalCode.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtItemCode2.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblPrTaxRatio.Text" xml:space="preserve">
    <value>Purchase Tax Ratio</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpComp.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="chk_IsDeleted.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="grdSlPLevel.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 19</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSalesTaxValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>Small Unit Purchase Price</value>
  </data>
  <data name="rep_Vendors.Columns8" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value>f</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Name" xml:space="preserve">
    <value>barBtnDelete</value>
  </data>
  <data name="&gt;&gt;labelControl33.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtWidth.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_VariableWeight.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="rep_m_attribute.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnAddCat.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>Warranty Months</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="tab_MainInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>790, 472</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtLength.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barBtnDelete.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="rep_stores.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtReorder.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_stores.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoSellUOM0.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="labelControl28.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>709, 91</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="lkpCategory.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtPurchasePrice.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInternationalCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl10.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rdoSellUOM0.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;itemPhoto.Name" xml:space="preserve">
    <value>itemPhoto</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsLibra.Properties.Caption" xml:space="preserve">
    <value>Libra</value>
  </data>
  <data name="col_m_Attribute2.AppearanceCell.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>255, 255, 192</value>
  </data>
  <data name="labelControl17.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtLength.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lblPrTaxRatio.Name" xml:space="preserve">
    <value>lblPrTaxRatio</value>
  </data>
  <data name="gridView6.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rdoSellUOM1.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 54</value>
  </data>
  <data name="chk_IsPos.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtInternationalCode.Name" xml:space="preserve">
    <value>txtInternationalCode</value>
  </data>
  <data name="lkpMediumUOM.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_LargeIsStopped.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="txtPurchasePrice.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSalesDiscRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtItemCode1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;tab_PriceLevels.Name" xml:space="preserve">
    <value>tab_PriceLevels</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barBtnList.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMediumUOM.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtHeight.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="chk_IsPos.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMediumUOM.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="cmb_WeightUnit.Properties.Items1" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grp_international.Size" type="System.Drawing.Size, System.Drawing">
    <value>211, 122</value>
  </data>
  <data name="chk_MediumIsStopped.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_LargeIsStopped.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="cmbChangePriceMethod.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="chk_IsPos.Properties.Caption" xml:space="preserve">
    <value>Is POS Item</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 13</value>
  </data>
  <data name="colPlLargeUOMPrice.Width" type="System.Int32, mscorlib">
    <value>85</value>
  </data>
  <data name="chk_IsPos.Size" type="System.Drawing.Size, System.Drawing">
    <value>97, 19</value>
  </data>
  <data name="txtItemCode1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rdoSellUOM1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceReadOnly.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpComp.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl35.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 13</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_MaxQty.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl14.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="&gt;&gt;txtPurchaseTaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchasePrice.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;txtItemNameEn.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_QtyTo.Name" xml:space="preserve">
    <value>col_QtyTo</value>
  </data>
  <data name="txtItemCode2.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="groupBox5.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl35.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_name.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;btnAddComp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtLength.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;repPriceLevels.Name" xml:space="preserve">
    <value>repPriceLevels</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSalesTaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 31</value>
  </data>
  <data name="itemPhoto.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl25.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtItemCode2.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 36</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtPurchaseDiscRatio.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="chk_PricingWithSmall.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 152</value>
  </data>
  <data name="&gt;&gt;txtDesc.Name" xml:space="preserve">
    <value>txtDesc</value>
  </data>
  <data name="rdoItemType.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;grdQty.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="textEdit5.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtSalesTaxRatio.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;labelControl19.Name" xml:space="preserve">
    <value>labelControl19</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tabWarranty.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="labelControl32.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtItemCode2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSalesTaxRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btn_AddNewInter_Code.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_SmallUOMPrice.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtPurchasePrice.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="txtReorder.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl23.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="col_m_Attribute3.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblPrTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 9</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_ItemCode2.Width" type="System.Int32, mscorlib">
    <value>84</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM0.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="IC_ItemSubTaxesId.Width" type="System.Int32, mscorlib">
    <value>178</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM2.Name" xml:space="preserve">
    <value>rdoPrchsUOM2</value>
  </data>
  <data name="txtSalesTaxValue.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns1" xml:space="preserve">
    <value>UOMId</value>
  </data>
  <data name="groupBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 229</value>
  </data>
  <data name="col_m_MaxQty.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpSmallUOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>584, 28</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gv_Vendors.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl8.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="chk_IsLibra.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_LargeUOMPrice.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colWidth.Caption" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="&gt;&gt;gridView2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbChangeSellPrice.Name" xml:space="preserve">
    <value>cmbChangeSellPrice</value>
  </data>
  <data name="&gt;&gt;labelControl7.Name" xml:space="preserve">
    <value>labelControl7</value>
  </data>
  <data name="labelControl32.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="&gt;&gt;txtItemNameAr.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rep_m_attribute.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="tabDimension.Size" type="System.Drawing.Size, System.Drawing">
    <value>506, 123</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>363, 211</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.Caption" xml:space="preserve">
    <value>Purchase</value>
  </data>
  <data name="&gt;&gt;grd_SalesPerQty.Parent" xml:space="preserve">
    <value>tab_PricesPerQty</value>
  </data>
  <data name="col_m_MaxQty.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtPurchaseTaxValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;colPrsmallUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lblSalesDiscountRatio.Parent" xml:space="preserve">
    <value>tabDiscount</value>
  </data>
  <data name="chk_IsPos.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grd_SubTaxes.Size" type="System.Drawing.Size, System.Drawing">
    <value>507, 109</value>
  </data>
  <data name="&gt;&gt;tabPriceChange.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grd_Vendors.Size" type="System.Drawing.Size, System.Drawing">
    <value>355, 188</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>62, 13</value>
  </data>
  <data name="&gt;&gt;labelControl17.Parent" xml:space="preserve">
    <value>tabDimension</value>
  </data>
  <data name="&gt;&gt;labelControl24.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_Attribute3.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grp_international.Text" xml:space="preserve">
    <value>International Codes</value>
  </data>
  <data name="lblSalesTaxRatio.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>373, 10</value>
  </data>
  <data name="lkpComp.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceFocused.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_m_MediumUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="col_m_MediumUOMPrice.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 13</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>365, 211</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gv_Matrix.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit5.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl27.Parent" xml:space="preserve">
    <value>tab_InventoryLevels</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;colLength.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_Attribute3.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>438, 241</value>
  </data>
  <data name="&gt;&gt;btnDelete.Name" xml:space="preserve">
    <value>btnDelete</value>
  </data>
  <data name="col_QtyTo.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Name" xml:space="preserve">
    <value>gridColumn15</value>
  </data>
  <data name="lkpComp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtLargeUOMPrice.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="rep_Vendors.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="col_Vnd_ItemId.Caption" xml:space="preserve">
    <value>ItemId</value>
  </data>
  <data name="rep_Vendors.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="rep_Vendors.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtLargeUOMPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>375, 78</value>
  </data>
  <data name="col_SellPrice.Caption" xml:space="preserve">
    <value>Sell Price</value>
  </data>
  <data name="col_m_Attribute1.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl36.TabIndex" type="System.Int32, mscorlib">
    <value>98</value>
  </data>
  <data name="tab_PricesPerQty.Text" xml:space="preserve">
    <value>Sales Prices Per Qty</value>
  </data>
  <data name="gridLocation.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="txtItemCode2.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtPurchasePrice.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtPurchasePrice.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 19</value>
  </data>
  <data name="txtSalesTaxValue.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txtItemCode2.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f</value>
  </data>
  <data name="lkpSmallUOM.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_m_SmallUOMPrice.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_m_MediumUOMPrice.Name" xml:space="preserve">
    <value>col_m_MediumUOMPrice</value>
  </data>
  <data name="lkpSmallUOM.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rdoPrchsUOM1.TabIndex" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="rdoItemType.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 112</value>
  </data>
  <data name="&gt;&gt;col_m_SmallUOMPrice.Name" xml:space="preserve">
    <value>col_m_SmallUOMPrice</value>
  </data>
  <data name="labelControl10.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl25.TabIndex" type="System.Int32, mscorlib">
    <value>104</value>
  </data>
  <data name="cmbChangePriceMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 6</value>
  </data>
  <data name="txtItemCode1.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkpMediumUOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>584, 53</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSalesDiscountRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>97, 13</value>
  </data>
  <data name="col_m_ReorderLevel.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="chk_SmallIsStopped.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="col_QtyFrom.Caption" xml:space="preserve">
    <value>From Qty</value>
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lkpCategory.Properties.Columns23" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;txtItemNameAr.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="txtItemNameAr.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="groupBox1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;rep_SLQtyNums.Name" xml:space="preserve">
    <value>rep_SLQtyNums</value>
  </data>
  <data name="&gt;&gt;txtLargeUOMFactor.Name" xml:space="preserve">
    <value>txtLargeUOMFactor</value>
  </data>
  <data name="&gt;&gt;labelControl36.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="lblPrTaxVal.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;chk_IsDeleted.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl24.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;grdSlPLevel.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="colPrsmallUOMPrice.Caption" xml:space="preserve">
    <value>Purchase Price</value>
  </data>
  <data name="chk_VariableWeight.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lkpComp.Name" xml:space="preserve">
    <value>lkpComp</value>
  </data>
  <data name="&gt;&gt;txtWidth.Parent" xml:space="preserve">
    <value>tabDimension</value>
  </data>
  <data name="txtHeight.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_m_Attribute2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl31.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>183</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 13</value>
  </data>
  <data name="btnAddComp.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="col_m_MediumUOMPrice.Caption" xml:space="preserve">
    <value>Medium UOM Price</value>
  </data>
  <data name="chk_IsLibra.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 31</value>
  </data>
  <data name="btnAddPicture.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 23</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="txtReorder.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 21</value>
  </data>
  <data name="chk_MediumIsStopped.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtItemNameAr.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_ItemCode2.AppearanceCell.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>255, 255, 192</value>
  </data>
  <data name="&gt;&gt;barBtnList.Name" xml:space="preserve">
    <value>barBtnList</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_LargeUOMCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl20.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="txtMinQty.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMinQty.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_WarrantyMonths.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtItemCode2.Size" type="System.Drawing.Size, System.Drawing">
    <value>146, 20</value>
  </data>
  <data name="&gt;&gt;labelControl15.Name" xml:space="preserve">
    <value>labelControl15</value>
  </data>
  <data name="&gt;&gt;grd_Vendors.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="textEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView7.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_SmallUOMPrice.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colPrsmallUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tab_SubTaxes.Size" type="System.Drawing.Size, System.Drawing">
    <value>506, 123</value>
  </data>
  <data name="labelControl16.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="rep_Vendors.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>251, 13</value>
  </data>
  <data name="txtItemNameAr.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl31.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 13</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="grdQty.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="tab_Other.Size" type="System.Drawing.Size, System.Drawing">
    <value>790, 472</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>697, 31</value>
  </data>
  <data name="grpExtra.TabIndex" type="System.Int32, mscorlib">
    <value>107</value>
  </data>
  <data name="chk_VariableWeight.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="cmb_WeightUnit.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items7" type="System.Byte, mscorlib">
    <value>3</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gv_Vendors.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtItemNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>283, 11</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;rep_Vendors.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_ItemCode2.AppearanceHeader.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>255, 255, 192</value>
  </data>
  <data name="lkpLargeUOM.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="rdoPrchsUOM1.Location" type="System.Drawing.Point, System.Drawing">
    <value>259, 54</value>
  </data>
  <data name="labelControl13.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView6.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_M_Spin.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>When cost price change in purchase invoice, use</value>
  </data>
  <data name="rdoSellUOM2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl35.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl29.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="col_m_ItemCode2.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsLibra.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gv_Vendors.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="labelControl32.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="lkpComp.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gv_SalesPerQty.Appearance.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;tabPriceChange.Name" xml:space="preserve">
    <value>tabPriceChange</value>
  </data>
  <data name="&gt;&gt;txtPurchaseDiscRatio.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="lkpCategory.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="tab_Control1.Size" type="System.Drawing.Size, System.Drawing">
    <value>796, 500</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="rdoSellUOM2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMediumUOMPrice.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbChangeSellPrice.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="rep_Vendors.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;colPlSmallUOMPrice.Name" xml:space="preserve">
    <value>colPlSmallUOMPrice</value>
  </data>
  <data name="chk_IsDeleted.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txtReorder.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="col_m_MediumUOMPrice.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl16.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtSalesDiscRatio.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtItemCode1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtPurchasePrice.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="gridLocation.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 17</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="rdoPrchsUOM1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtSalesTaxRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="txtItemCode2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpSmallUOM.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl33.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rep_stores.Columns6" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtDesc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtWidth.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpSmallUOM.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_m_PurchasePrice.Name" xml:space="preserve">
    <value>col_m_PurchasePrice</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM0.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl12.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lblPrTaxVal.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="&gt;&gt;txtDesc.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Name" xml:space="preserve">
    <value>barBtnSave</value>
  </data>
  <data name="btnRemovePic.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lkpLargeUOM.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;tabDimension.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceFocused.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSalesDiscountRatio.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_Attribute1.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rdoSellUOM0.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_SmallIsStopped.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpCategory.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtDesc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="lkpCategory.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtPurchasePrice.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtWidth.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl30.Text" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="tab_Control1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl22.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;SubTaxId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtWidth.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_WarrantyMonths.TabIndex" type="System.Int32, mscorlib">
    <value>99</value>
  </data>
  <data name="txtHeight.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl26.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>wholesale unt1</value>
  </data>
  <data name="&gt;&gt;btn_AddNewInter_Code.Parent" xml:space="preserve">
    <value>grp_international</value>
  </data>
  <data name="&gt;&gt;labelControl37.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl29.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnAddUOM.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="colHeight.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpComp.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridLocation.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="&gt;&gt;rep_stores.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtItemNameEn.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;colPlLargeUOMPrice.Name" xml:space="preserve">
    <value>colPlLargeUOMPrice</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpComp.Properties.AppearanceFocused.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_VariableWeight.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barBtnSave.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="&gt;&gt;colPlMediumUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCategory.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;lkpMediumUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl27.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_name.VisibleIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;col_m_name.Name" xml:space="preserve">
    <value>col_m_name</value>
  </data>
  <data name="itemPhoto.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtMaxQty.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Vnd_ItemId.Width" type="System.Int32, mscorlib">
    <value>92</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl24.TabIndex" type="System.Int32, mscorlib">
    <value>116</value>
  </data>
  <data name="tab_Control1.TabIndex" type="System.Int32, mscorlib">
    <value>119</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lstInternationalCodes.Parent" xml:space="preserve">
    <value>grp_international</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_MediumUOMCode.Name" xml:space="preserve">
    <value>txt_MediumUOMCode</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;grdSlPLevel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpLargeUOM.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_ItemCode2.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colPrPriceLevelId.Caption" xml:space="preserve">
    <value>PrPriceLevelId</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="col_SellPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_m_MaxQty.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repPriceLevels.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tab_PricesPerQty.Name" xml:space="preserve">
    <value>tab_PricesPerQty</value>
  </data>
  <data name="txtMinQty.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl34.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="txtAudiancePrice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rdoItemType.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSalesTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 34</value>
  </data>
  <data name="&gt;&gt;lblPrTaxRatio.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txtInternationalCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;col_m_ItemId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_MinQty.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>Purchase Price Levels</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>Default UOM</value>
  </data>
  <data name="col_SellPrice.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInternationalCode.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="tabExtraData.AppearancePage.Header.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMediumUOMPrice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl37.TabIndex" type="System.Int32, mscorlib">
    <value>128</value>
  </data>
  <data name="lblSalesTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 13</value>
  </data>
  <data name="rdoItemType.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rep_Vendors.Columns1" xml:space="preserve">
    <value>VendorId</value>
  </data>
  <data name="lblPrTaxRatio.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCustomSalesTaxRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtWidth.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtWidth.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit5.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn15.Caption" xml:space="preserve">
    <value>gridColumn15</value>
  </data>
  <data name="&gt;&gt;labelControl36.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Items" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="rdoSellUOM0.TabIndex" type="System.Int32, mscorlib">
    <value>115</value>
  </data>
  <data name="textEdit5.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="groupBox4.Text" xml:space="preserve">
    <value>Sales Price Levels</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;labelControl15.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="col_QtyTo.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsPos.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtPurchaseTaxValue.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtSalesDiscRatio.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="col_SellPrice.Width" type="System.Int32, mscorlib">
    <value>193</value>
  </data>
  <data name="chk_VariableWeight.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="groupBox5.Text" xml:space="preserve">
    <value>Locations</value>
  </data>
  <data name="col_m_PurchasePrice.Caption" xml:space="preserve">
    <value>Purchase Price</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnNext.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtSalesTaxValue.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="grdSlPLevel.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="rep_stores.Columns9" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colPlSmallUOMPrice.Caption" xml:space="preserve">
    <value>Small UOM Price</value>
  </data>
  <data name="tab_image.Size" type="System.Drawing.Size, System.Drawing">
    <value>790, 472</value>
  </data>
  <data name="col_m_PurchasePrice.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtAudiancePrice.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtLength.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btn_MatrixPrint.Location" type="System.Drawing.Point, System.Drawing">
    <value>146, 2</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl13.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items1" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="txtLength.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="gv_Matrix.Appearance.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>114</value>
  </data>
  <data name="txtHeight.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="txtLength.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_m_Attribute1.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnRemovePic.Location" type="System.Drawing.Point, System.Drawing">
    <value>723, 347</value>
  </data>
  <data name="rdoSellUOM1.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="txtItemNameEn.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_MediumUOMCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpLargeUOM.Properties.Columns8" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_MediumIsStopped.Location" type="System.Drawing.Point, System.Drawing">
    <value>217, 53</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="txtLength.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grp_international.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="rep_Vendors.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpCategory.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colPLName.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnPrev.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="col_m_PurchasePrice.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns7" xml:space="preserve">
    <value>UOMId</value>
  </data>
  <data name="col_QtyTo.Caption" xml:space="preserve">
    <value>To Qty</value>
  </data>
  <data name="chk_SmallIsStopped.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 19</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="grd_SalesPerQty.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpSmallUOM.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;grp_UOM.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="&gt;&gt;rep_M_Spin.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx.Parent" xml:space="preserve">
    <value>tab_Matrix</value>
  </data>
  <data name="lkpSmallUOM.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;chk_IsDeleted.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>tab_Other</value>
  </data>
  <data name="txtPurchasePrice.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btn_GenMtrx.Name" xml:space="preserve">
    <value>btn_GenMtrx</value>
  </data>
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtSalesTaxRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl32.TabIndex" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="colPlLargeUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtPurchaseTaxValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="lkpCategory.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl28.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtLargeUOMFactor.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="rep_Vendors.Columns7" xml:space="preserve">
    <value>VenNameAr</value>
  </data>
  <data name="txt_WarrantyMonths.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="&gt;&gt;labelControl30.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="gridView1.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="colLength.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtMinQty.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelControl28.Name" xml:space="preserve">
    <value>labelControl28</value>
  </data>
  <data name="grd_Vendors.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 19</value>
  </data>
  <data name="chk_LargeIsStopped.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnAddUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="rep_M_Spin.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSalesTaxValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoSellUOM0.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 28</value>
  </data>
  <data name="lkpComp.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl16.Name" xml:space="preserve">
    <value>labelControl16</value>
  </data>
  <data name="txtPurchasePrice.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="txtReorder.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="txtSmallUOMPrice.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl15.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rdoSellUOM2.Properties.Caption" xml:space="preserve">
    <value>Sell</value>
  </data>
  <data name="&gt;&gt;txtAudiancePrice.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridView7.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lbl_WeightUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>139, 26</value>
  </data>
  <data name="rdoPrchsUOM0.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 19</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_Attribute1.Width" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_QtyFrom.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInternationalCode.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="chkIsExpire.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SubTaxes.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtLargeUOMFactor.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl18.Name" xml:space="preserve">
    <value>labelControl18</value>
  </data>
  <data name="btnDelete.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="&gt;&gt;labelControl17.Name" xml:space="preserve">
    <value>labelControl17</value>
  </data>
  <data name="txtAudiancePrice.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl13.TabIndex" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="lkpComp.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 88</value>
  </data>
  <data name="&gt;&gt;rdoItemType.Parent" xml:space="preserve">
    <value>grp_ItemType</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtItemCode2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtMaxQty.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="tabExtraData.AppearancePage.Header.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl8.Parent" xml:space="preserve">
    <value>tab_InventoryLevels</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpComp.Properties.Columns1" xml:space="preserve">
    <value>Group F Name</value>
  </data>
  <data name="chk_VariableWeight.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_PricingWithSmall.Properties.Caption" xml:space="preserve">
    <value>Pricing With Small</value>
  </data>
  <data name="labelControl34.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;btnAddCat.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>381, 8</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpCategory.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtWidth.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items3" xml:space="preserve">
    <value>Retail Unit</value>
  </data>
  <data name="txt_MediumUOMCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>146, 20</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;tabDiscount.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="cmbChangePriceMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 20</value>
  </data>
  <data name="chk_IsLibra.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="lblSalesTaxRatio.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtHeight.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMediumUOM.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="SubTaxId.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lkpCategory.Properties.Columns15" xml:space="preserve">
    <value>Category Code</value>
  </data>
  <data name="txtDesc.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txtAudiancePrice.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridView7.Name" xml:space="preserve">
    <value>gridView7</value>
  </data>
  <data name="cmbChangeSellPrice.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 20</value>
  </data>
  <data name="col_m_MaxQty.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rep_stores.Columns8" xml:space="preserve">
    <value />
  </data>
  <data name="btnAddComp.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="lkpComp.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="gridView2.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsPos.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM2.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="gridLocation.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmb_WeightUnit.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceFocused.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chkIsExpire.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtAudiancePrice.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_m_LargeUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_VariableWeight.Name" xml:space="preserve">
    <value>chk_VariableWeight</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtReorder.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMaxQty.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="col_m_Attribute3.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grp_UOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 176</value>
  </data>
  <data name="&gt;&gt;labelControl22.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="chk_SmallIsStopped.TabIndex" type="System.Int32, mscorlib">
    <value>129</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl36.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_WarrantyMonths.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 13</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceReadOnly.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="colHeight.Caption" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="gridView1.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;grdQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMinQty.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Store.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_m_Attribute3.AppearanceCell.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>255, 255, 192</value>
  </data>
  <data name="txtMinQty.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tab_MainInfo.Name" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="&gt;&gt;labelControl31.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="chk_IsDeleted.Properties.Caption" xml:space="preserve">
    <value>Suspended</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCategory.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tab_Control1.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Name" xml:space="preserve">
    <value>gridColumn3</value>
  </data>
  <data name="labelControl25.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpComp.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;lblPrTaxVal.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="col_m_name.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDescEn.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="rep_M_Spin.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="gv_SubTaxes.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtPurchaseTaxRatio.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnAddUOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>556, 28</value>
  </data>
  <data name="txtSalesTaxValue.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;colPriceLevelId.Name" xml:space="preserve">
    <value>colPriceLevelId</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="col_m_MaxQty.Caption" xml:space="preserve">
    <value>Max Qty</value>
  </data>
  <data name="lkpCategory.Properties.Columns26" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;textEdit5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>55, 37</value>
  </data>
  <data name="col_Vnd_PurchasePrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_SalesPerQty.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 18</value>
  </data>
  <data name="btnDelete.ToolTip" xml:space="preserve">
    <value>Delete International Code</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsDeleted.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtSalesTaxRatio.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;grdPrPLevel.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="rdoSellUOM2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_IsDeleted.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colWidth.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_name.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="chk_IsDeleted.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMinQty.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="lkpMediumUOM.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chkIsExpire.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtWidth.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;rdoItemType.Name" xml:space="preserve">
    <value>rdoItemType</value>
  </data>
  <data name="labelControl29.Location" type="System.Drawing.Point, System.Drawing">
    <value>163, 52</value>
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtMaxQty.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;txtItemNameAr.Name" xml:space="preserve">
    <value>txtItemNameAr</value>
  </data>
  <data name="labelControl7.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtPurchaseDiscRatio.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpComp.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;gv_Vendors.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btn_GenMtrx.Text" xml:space="preserve">
    <value>Generate Matrix Item</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_VariableWeight.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 178</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="&gt;&gt;labelControl26.Parent" xml:space="preserve">
    <value>tabWarranty</value>
  </data>
  <data name="&gt;&gt;tab_MainInfo.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMediumUOMPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>375, 53</value>
  </data>
  <data name="col_m_MinQty.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl14.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="&gt;&gt;tabDiscount.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tabDimension.Text" xml:space="preserve">
    <value>Dimensions</value>
  </data>
  <data name="txtMinQty.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="&gt;&gt;txtAudiancePrice.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="txtPurchaseDiscRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSalesDiscountRatio.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMinQty.Location" type="System.Drawing.Point, System.Drawing">
    <value>280, 21</value>
  </data>
  <data name="textEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>461, 28</value>
  </data>
  <data name="&gt;&gt;grpExtra.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpSmallUOM.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl36.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtPurchaseTaxRatio.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_LargeUOMPrice.Width" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="txtItemCode1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSalesDiscRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtItemCode2.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;textEdit5.Name" xml:space="preserve">
    <value>textEdit5</value>
  </data>
  <data name="col_Vnd_PurchasePrice.Width" type="System.Int32, mscorlib">
    <value>105</value>
  </data>
  <data name="txtInternationalCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_SubTaxes.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="colHeight.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtItemCode2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SubTaxes.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;cmbChangeSellPrice.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;cmb_WeightUnit.Name" xml:space="preserve">
    <value>cmb_WeightUnit</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="&gt;&gt;chk_IsLibra.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btn_GenMtrx.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grdPrPLevel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="lkpComp.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="txtItemNameEn.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoItemType.Properties.Items" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpCategory.Properties.Columns27" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="gv_Vendors.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpSmallUOM.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtAudiancePrice.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_SellPrice.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Rate.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmb_WeightUnit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="txtLargeUOMPrice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="col_m_MaxQty.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rep_Vendors.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;tabDimension.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txt_MediumUOMCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 49</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtItemNameAr.Size" type="System.Drawing.Size, System.Drawing">
    <value>498, 20</value>
  </data>
  <data name="&gt;&gt;labelControl29.Name" xml:space="preserve">
    <value>labelControl29</value>
  </data>
  <data name="labelControl36.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl12.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lkpCategory.Location" type="System.Drawing.Point, System.Drawing">
    <value>562, 88</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="rdoPrchsUOM1.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="txtAudiancePrice.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSalesDiscountRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>175, 10</value>
  </data>
  <data name="textEdit5.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_Vnd_PurchasePrice.Name" xml:space="preserve">
    <value>col_Vnd_PurchasePrice</value>
  </data>
  <data name="gv_Matrix.Appearance.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMinQty.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.Location" type="System.Drawing.Point, System.Drawing">
    <value>278, 90</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="labelControl19.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddCat.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceFocused.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl35.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="groupBox4.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;labelControl11.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtSalesDiscRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="labelControl27.TabIndex" type="System.Int32, mscorlib">
    <value>94</value>
  </data>
  <data name="&gt;&gt;txtItemNameEn.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="txtSmallUOMPrice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtLargeUOMPrice.Name" xml:space="preserve">
    <value>txtLargeUOMPrice</value>
  </data>
  <data name="txtMinQty.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtAudiancePrice.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtAudiancePrice.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btn_GenMtrx.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 2</value>
  </data>
  <data name="&gt;&gt;grd_SalesPerQty.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM2.TabIndex" type="System.Int32, mscorlib">
    <value>118</value>
  </data>
  <data name="txtItemNameEn.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grp_UOM.Size" type="System.Drawing.Size, System.Drawing">
    <value>772, 106</value>
  </data>
  <data name="col_m_name.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_Rate.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtItemCode1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtDescEn.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="grd_Mtrx.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="col_m_MinQty.Width" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 13</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="lkpComp.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl19.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="&gt;&gt;grd_SalesPerQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceReadOnly.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_m_Attribute1.VisibleIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="txtReorder.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;tab_image.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="repPriceLevels.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 13</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colHeight.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnDelete.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtItemNameEn.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txtReorder.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="chk_IsPos.Location" type="System.Drawing.Point, System.Drawing">
    <value>127, 152</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtHeight.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 50</value>
  </data>
  <data name="rdoSellUOM0.Properties.Caption" xml:space="preserve">
    <value>Sell</value>
  </data>
  <data name="lkp_SubTaxes.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tab_Control1.AppearancePage.Header.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_SmallIsStopped.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpCategory.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsDeleted.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>459, 126</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;chk_SmallIsStopped.Name" xml:space="preserve">
    <value>chk_SmallIsStopped</value>
  </data>
  <data name="itemPhoto.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lbl_WeightUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 13</value>
  </data>
  <data name="lkpCategory.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCategory.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="col_Rate.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="&gt;&gt;col_m_ReorderLevel.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl26.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="txt_LargeUOMCode.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;groupBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.Parent" xml:space="preserve">
    <value>tab_SubTaxes</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtHeight.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtDescEn.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl36.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_SmallUOMPrice.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelControl19.Text" xml:space="preserve">
    <value>wholesale unt2</value>
  </data>
  <data name="&gt;&gt;txtItemNameEn.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_LargeUOMPrice.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl30.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;gv_SalesPerQty.Name" xml:space="preserve">
    <value>gv_SalesPerQty</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_WarrantyMonths.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="colPLName.Caption" xml:space="preserve">
    <value>Price Level</value>
  </data>
  <data name="lblSalesDiscountRatio.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tab_SubTaxes.Name" xml:space="preserve">
    <value>tab_SubTaxes</value>
  </data>
  <data name="chk_MediumIsStopped.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="col_m_MediumUOMPrice.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cmb_WeightUnit.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtWidth.TabIndex" type="System.Int32, mscorlib">
    <value>103</value>
  </data>
  <data name="&gt;&gt;col_m_ItemId.Name" xml:space="preserve">
    <value>col_m_ItemId</value>
  </data>
  <data name="labelControl29.TabIndex" type="System.Int32, mscorlib">
    <value>123</value>
  </data>
  <data name="&gt;&gt;btnAddUOM.Name" xml:space="preserve">
    <value>btnAddUOM</value>
  </data>
  <data name="labelControl22.TabIndex" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="&gt;&gt;labelControl11.Name" xml:space="preserve">
    <value>labelControl11</value>
  </data>
  <data name="txtInternationalCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpComp.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtItemCode1.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDesc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value>f</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpLargeUOM.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblSalesDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoSellUOM1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl37.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtReorder.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>Code2</value>
  </data>
  <data name="txtInternationalCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl10.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkpComp.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 563</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="SubTaxId.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;grp_ItemType.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lkpComp.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lkpSmallUOM.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lkpCategory.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl5.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="rep_M_Spin.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl34.Location" type="System.Drawing.Point, System.Drawing">
    <value>60, 61</value>
  </data>
  <data name="col_Vnd_VendorId.Width" type="System.Int32, mscorlib">
    <value>252</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="lkpComp.Properties.Columns15" xml:space="preserve">
    <value>CompanyId</value>
  </data>
  <data name="lkpSmallUOM.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btn_AddNewInter_Code.ToolTip" xml:space="preserve">
    <value>Add new International Code to the Item</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtAudiancePrice.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_m_Attribute1.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>298, 10</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns1" xml:space="preserve">
    <value>Name16</value>
  </data>
  <data name="col_Rate.Width" type="System.Int32, mscorlib">
    <value>180</value>
  </data>
  <data name="grdQty.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items4" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="labelControl25.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl23.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="&gt;&gt;tabTax.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="col_m_SmallUOMPrice.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtAudiancePrice.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_MediumUOMPrice.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gv_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_SellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tab_Matrix.Size" type="System.Drawing.Size, System.Drawing">
    <value>790, 472</value>
  </data>
  <data name="&gt;&gt;lkpLargeUOM.Name" xml:space="preserve">
    <value>lkpLargeUOM</value>
  </data>
  <data name="colPlMediumUOMPrice.Caption" xml:space="preserve">
    <value>Medium UOM Price</value>
  </data>
  <data name="grdQty.Size" type="System.Drawing.Size, System.Drawing">
    <value>359, 191</value>
  </data>
  <data name="grdSlPLevel.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="col_m_Attribute3.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtReorder.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_QtyFrom.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDescEn.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 46</value>
  </data>
  <data name="&gt;&gt;labelControl8.Name" xml:space="preserve">
    <value>labelControl8</value>
  </data>
  <data name="&gt;&gt;labelControl32.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl29.Text" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="col_m_MinQty.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;tabExtraData.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="chk_IsLibra.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_ItemPriceId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="colLength.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtReorder.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="tab_SubTaxes.Text" xml:space="preserve">
    <value>Sub Taxes</value>
  </data>
  <data name="&gt;&gt;colPlLargeUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="colLength.Caption" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="labelControl1.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmb_WeightUnit.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;colWidth.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpSmallUOM.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceFocused.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtInternationalCode.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;colWidth.Name" xml:space="preserve">
    <value>colWidth</value>
  </data>
  <data name="lblPrTaxRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtPurchaseTaxValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="txtLargeUOMPrice.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="lstInternationalCodes.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 41</value>
  </data>
  <data name="chk_VariableWeight.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Store.Caption" xml:space="preserve">
    <value>Store</value>
  </data>
  <data name="col_m_LargeUOMPrice.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMaxQty.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_m_MaxQty.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpSmallUOM.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtItemCode1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="tabPriceChange.Size" type="System.Drawing.Size, System.Drawing">
    <value>506, 123</value>
  </data>
  <data name="col_m_SmallUOMPrice.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="lkpLargeUOM.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridView2.Name" xml:space="preserve">
    <value>gridView2</value>
  </data>
  <data name="&gt;&gt;grd_Vendors.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_Attribute3.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>490, 40</value>
  </data>
  <data name="labelControl14.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colPlLargeUOMPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtItemNameAr.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdPrPLevel.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 19</value>
  </data>
  <data name="cmbChangePriceMethod.TabIndex" type="System.Int32, mscorlib">
    <value>94</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>Store</value>
  </data>
  <data name="txtHeight.Properties.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="cmbChangeSellPrice.TabIndex" type="System.Int32, mscorlib">
    <value>95</value>
  </data>
  <data name="lkpMediumUOM.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="col_QtyFrom.Width" type="System.Int32, mscorlib">
    <value>246</value>
  </data>
  <data name="&gt;&gt;gridLocation.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="col_QtyTo.Width" type="System.Int32, mscorlib">
    <value>244</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;IC_ItemSubTaxesId.Name" xml:space="preserve">
    <value>IC_ItemSubTaxesId</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoPrchsUOM2.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 19</value>
  </data>
  <data name="rdoItemType.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_QtyTo.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_QtyTo.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tab_Matrix.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMediumUOMFactor.Location" type="System.Drawing.Point, System.Drawing">
    <value>461, 53</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceFocused.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gv_Matrix.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupBox5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtDescEn.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemId.Caption" xml:space="preserve">
    <value>ItemId</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="tab_PriceLevels.Text" xml:space="preserve">
    <value>Price Levels</value>
  </data>
  <data name="lkpComp.Properties.Columns8" xml:space="preserve">
    <value>Group Name</value>
  </data>
  <data name="&gt;&gt;txtSmallUOMPrice.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>697, 81</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceReadOnly.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtItemNameEn.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chkIsExpire.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl1.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;rep_M_Spin.Name" xml:space="preserve">
    <value>rep_M_Spin</value>
  </data>
  <data name="lkpComp.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMinQty.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtSalesTaxValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="btnDelete.Text" xml:space="preserve">
    <value>X</value>
  </data>
  <data name="txtInternationalCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpComp.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_ReorderLevel.Width" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="col_Vnd_PurchasePrice.Caption" xml:space="preserve">
    <value>Purchase Price</value>
  </data>
  <data name="txtLargeUOMFactor.Location" type="System.Drawing.Point, System.Drawing">
    <value>461, 78</value>
  </data>
  <data name="labelControl7.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnAddCat.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="&gt;&gt;labelControl12.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="col_m_name.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnPrev.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridView2.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lblSalesTaxValue.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="rep_SLQtyNums.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lblPrTaxRatio.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_m_Code1.Name" xml:space="preserve">
    <value>col_m_Code1</value>
  </data>
  <data name="labelControl17.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMaxQty.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtMediumUOMFactor.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="grp_UOM.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;col_QtyFrom.Name" xml:space="preserve">
    <value>col_QtyFrom</value>
  </data>
  <data name="tab_Other.Text" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtSalesTaxRatio.Name" xml:space="preserve">
    <value>txtSalesTaxRatio</value>
  </data>
  <data name="lkpMediumUOM.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>Retail Unit</value>
  </data>
  <data name="rdoSellUOM0.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl15.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtWidth.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx.Name" xml:space="preserve">
    <value>grd_Mtrx</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_ReorderLevel.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_IsPos.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;lblSalesTaxRatio.Name" xml:space="preserve">
    <value>lblSalesTaxRatio</value>
  </data>
  <data name="&gt;&gt;labelControl27.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="txtReorder.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDesc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_LargeUOMCode.Name" xml:space="preserve">
    <value>txt_LargeUOMCode</value>
  </data>
  <data name="&gt;&gt;rdoItemType.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="lbl_WeightUnit.TabIndex" type="System.Int32, mscorlib">
    <value>127</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoSellUOM0.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit5.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsDeleted.Size" type="System.Drawing.Size, System.Drawing">
    <value>90, 19</value>
  </data>
  <data name="txtItemCode1.Size" type="System.Drawing.Size, System.Drawing">
    <value>146, 20</value>
  </data>
  <data name="lblSalesDiscountRatio.Text" xml:space="preserve">
    <value>Sales Discount Ratio</value>
  </data>
  <data name="btnNext.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="gridColumn4.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl35.Name" xml:space="preserve">
    <value>labelControl35</value>
  </data>
  <data name="&gt;&gt;chk_MediumIsStopped.Name" xml:space="preserve">
    <value>chk_MediumIsStopped</value>
  </data>
  <data name="&gt;&gt;colPrPLName.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMaxQty.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="&gt;&gt;txtSalesDiscRatio.Parent" xml:space="preserve">
    <value>tabDiscount</value>
  </data>
  <data name="col_m_Attribute2.AppearanceHeader.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>255, 255, 192</value>
  </data>
  <data name="rep_stores.Columns10" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="chk_IsPos.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;tab_Control1.Name" xml:space="preserve">
    <value>tab_Control1</value>
  </data>
  <data name="chk_VariableWeight.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="rep_Vendors.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtItemCode2.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtSalesTaxValue.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;lbl_WeightUnit.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupBox5.Name" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="txtMinQty.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="SubTaxId.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;tab_image.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnAddPicture.TabIndex" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="txtInternationalCode.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rep_Vendors.Columns" xml:space="preserve">
    <value>VendorId</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_SLQtyNums.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtItemNameEn.Name" xml:space="preserve">
    <value>txtItemNameEn</value>
  </data>
  <data name="&gt;&gt;lkpMediumUOM.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="tabExtraData.HeaderAutoFill" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpComp.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 20</value>
  </data>
  <data name="lkpMediumUOM.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridLocation.Name" xml:space="preserve">
    <value>gridLocation</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl16.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="&gt;&gt;groupBox4.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpMediumUOM.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl16.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblSalesTaxRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtMaxQty.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btn_MatrixPrint.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>442, 24</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tab_MainInfo.Parent" xml:space="preserve">
    <value>tab_Control1</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtLargeUOMFactor.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMaxQty.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grdQty.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;chk_SmallIsStopped.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_LargeIsStopped.Name" xml:space="preserve">
    <value>chk_LargeIsStopped</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="txtMediumUOMFactor.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn2.Width" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="txtLength.Location" type="System.Drawing.Point, System.Drawing">
    <value>165, 9</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="gv_SubTaxes.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsPos.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;grp_ItemType.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="grpExtra.Size" type="System.Drawing.Size, System.Drawing">
    <value>524, 166</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMaxQty.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtInternationalCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 18</value>
  </data>
  <data name="gridView1.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblPrTaxVal.TabIndex" type="System.Int32, mscorlib">
    <value>103</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl11.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="btnDelete.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;SubTaxId.Name" xml:space="preserve">
    <value>SubTaxId</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="lkpComp.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpComp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtItemCode2.Name" xml:space="preserve">
    <value>txtItemCode2</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSalesTaxValue.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl26.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtWidth.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_SellPrice.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>733, 42</value>
  </data>
  <data name="&gt;&gt;tab_PricesPerQty.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;labelControl21.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="&gt;&gt;col_m_Attribute3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtLength.Properties.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="txtLength.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtLength.TabIndex" type="System.Int32, mscorlib">
    <value>105</value>
  </data>
  <data name="chkIsExpire.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="lkpComp.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtWidth.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtItemNameAr.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txtLargeUOMPrice.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtDescEn.Name" xml:space="preserve">
    <value>txtDescEn</value>
  </data>
  <data name="col_m_MaxQty.Width" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="chk_IsPos.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gv_Vendors.Name" xml:space="preserve">
    <value>gv_Vendors</value>
  </data>
  <data name="cmb_WeightUnit.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;rep_m_attribute.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lblSalesTaxValue.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtLargeUOMFactor.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoSellUOM1.Properties.Caption" xml:space="preserve">
    <value>Sell</value>
  </data>
  <data name="&gt;&gt;btnAddCat.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>334, 24</value>
  </data>
  <data name="txtMinQty.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_LargeUOMPrice.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_LargeUOMPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtLength.Parent" xml:space="preserve">
    <value>tabDimension</value>
  </data>
  <data name="col_QtyTo.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_QtyTo.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="txtLength.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl37.Name" xml:space="preserve">
    <value>labelControl37</value>
  </data>
  <data name="&gt;&gt;col_SellPrice.Name" xml:space="preserve">
    <value>col_SellPrice</value>
  </data>
  <data name="lblPrTaxRatio.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMediumUOM.Properties.Columns1" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;labelControl12.Name" xml:space="preserve">
    <value>labelControl12</value>
  </data>
  <data name="chk_MediumIsStopped.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;btnPrev.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="colPrPLName.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInternationalCode.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;btnRemovePic.Name" xml:space="preserve">
    <value>btnRemovePic</value>
  </data>
  <data name="txtMaxQty.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="gridView7.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl22.Name" xml:space="preserve">
    <value>labelControl22</value>
  </data>
  <data name="lstInternationalCodes.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="lkpCategory.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;itemPhoto.Parent" xml:space="preserve">
    <value>tab_image</value>
  </data>
  <data name="labelControl17.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtAudiancePrice.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkpMediumUOM.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;txt_MediumUOMCode.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;labelControl29.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lkpSmallUOM.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDescEn.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblPrTaxVal.Location" type="System.Drawing.Point, System.Drawing">
    <value>189, 9</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>tab_PriceLevels</value>
  </data>
  <data name="btn_AddNewInter_Code.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="txtItemCode2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txtSalesTaxValue.Name" xml:space="preserve">
    <value>txtSalesTaxValue</value>
  </data>
  <data name="col_m_Attribute3.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpCategory.Properties.Columns7" xml:space="preserve">
    <value>CategoryNameAr</value>
  </data>
  <data name="txtPurchasePrice.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="grp_UOM.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>Sales Price</value>
  </data>
  <data name="lblSalesTaxRatio.TabIndex" type="System.Int32, mscorlib">
    <value>112</value>
  </data>
  <data name="groupBox4.TabIndex" type="System.Int32, mscorlib">
    <value>184</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnAddPicture.Parent" xml:space="preserve">
    <value>tab_image</value>
  </data>
  <data name="colWidth.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtAudiancePrice.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colLength.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtItemCode1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="rep_Vendors.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_Vendors.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;chk_IsLibra.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtLength.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCategory.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMinQty.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtAudiancePrice.Name" xml:space="preserve">
    <value>txtAudiancePrice</value>
  </data>
  <data name="col_m_MediumUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="colPlMediumUOMPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="lkpCategory.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMediumUOMPrice.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl19.TabIndex" type="System.Int32, mscorlib">
    <value>91</value>
  </data>
  <data name="labelControl12.TabIndex" type="System.Int32, mscorlib">
    <value>102</value>
  </data>
  <data name="&gt;&gt;col_ItemId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_name.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl27.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtPurchaseTaxValue.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="&gt;&gt;btnAddCat.Name" xml:space="preserve">
    <value>btnAddCat</value>
  </data>
  <data name="lkpLargeUOM.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="col_m_Attribute2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;tabTax.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="colHeight.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lbl_WeightUnit.Name" xml:space="preserve">
    <value>lbl_WeightUnit</value>
  </data>
  <data name="chk_IsLibra.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rdoPrchsUOM1.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 19</value>
  </data>
  <data name="grp_international.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tab_Matrix.Name" xml:space="preserve">
    <value>tab_Matrix</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_m_MediumUOMPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtPurchaseTaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>114, 6</value>
  </data>
  <data name="&gt;&gt;btn_MatrixPrint.Name" xml:space="preserve">
    <value>btn_MatrixPrint</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="chk_calcTaxBeforeDisc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;rep_Vendors.Name" xml:space="preserve">
    <value>rep_Vendors</value>
  </data>
  <data name="&gt;&gt;btnAddComp.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;lkpSmallUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_LargeUOMCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoPrchsUOM2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCustomSalesTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>324, 60</value>
  </data>
  <data name="txtMaxQty.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl27.Name" xml:space="preserve">
    <value>labelControl27</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grpExtra.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="txtMediumUOMPrice.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btn_GenMtrx.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;txtSalesTaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtItemCode1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;tab_SubTaxes.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;grpExtra.Name" xml:space="preserve">
    <value>grpExtra</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="lkpCategory.Properties.Columns" xml:space="preserve">
    <value>CategoryNameEn</value>
  </data>
  <data name="&gt;&gt;tab_Matrix.Parent" xml:space="preserve">
    <value>tab_Control1</value>
  </data>
  <data name="gv_Vendors.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl33.Text" xml:space="preserve">
    <value>Sales Custom Tax Ratio</value>
  </data>
  <data name="chk_LargeIsStopped.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 19</value>
  </data>
  <data name="textEdit5.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtItemNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtItemNameEn.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="rdoSellUOM0.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;btn_MatrixPrint.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="chk_VariableWeight.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 19</value>
  </data>
  <data name="txtMediumUOMPrice.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;grp_ItemType.Name" xml:space="preserve">
    <value>grp_ItemType</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="col_m_MinQty.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txt_LargeUOMCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 78</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl33.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnPrev.Text" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="labelControl12.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="labelControl1.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl13.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblSalesTaxValue.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtPurchaseTaxRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtWidth.Name" xml:space="preserve">
    <value>txtWidth</value>
  </data>
  <data name="cmb_WeightUnit.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_QtyFrom.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpLargeUOM.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Location.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gv_Matrix.Appearance.GroupPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtAudiancePrice.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;chk_calcTaxBeforeDisc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit5.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="lkpComp.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_IsLibra.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_SmallUOMPrice.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gv_Matrix.Appearance.GroupPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMaxQty.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAddComp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl22.Parent" xml:space="preserve">
    <value>tabDimension</value>
  </data>
  <data name="btn_MatrixPrint.Text" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInternationalCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 20</value>
  </data>
  <data name="&gt;&gt;grp_UOM.Name" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="txtReorder.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="txtHeight.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Store.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rdoSellUOM1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;btnNext.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;lstInternationalCodes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageListBoxControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chkIsExpire.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_SellPrice.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtMediumUOMFactor.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSmallUOMPrice.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="tabExtraData.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 15</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>458, 91</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="tabPriceChange.Text" xml:space="preserve">
    <value>Price Change Mode</value>
  </data>
  <data name="gridView7.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpCategory.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="lblSalesTaxValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 13</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnDelete.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl14.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl37.Text" xml:space="preserve">
    <value>Is Stopped</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chkIsExpire.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;grp_ItemType.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="col_m_MediumUOMPrice.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="lblSalesTaxRatio.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpCategory.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtWidth.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkpLargeUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="colHeight.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_SubTaxes.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl34.TabIndex" type="System.Int32, mscorlib">
    <value>118</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>Max Limit</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridLocation.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="grdPrPLevel.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="&gt;&gt;labelControl37.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl7.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns7" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="&gt;&gt;btn_GenMtrx.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpComp.Properties.AppearanceFocused.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rep_SLQtyNums.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="&gt;&gt;btnDelete.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtInternationalCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtMediumUOMPrice.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="&gt;&gt;txtItemCode1.Name" xml:space="preserve">
    <value>txtItemCode1</value>
  </data>
  <data name="chk_MediumIsStopped.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txtLargeUOMPrice.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtPurchaseTaxValue.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="txtItemCode2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnAddUOM.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="txtItemCode1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_SmallUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceFocused.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtItemNameEn.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtPurchaseTaxRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="groupBox5.TabIndex" type="System.Int32, mscorlib">
    <value>184</value>
  </data>
  <data name="txtAudiancePrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>385, 149</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txtMinQty.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_m_name.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_m_Attribute2.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_calcTaxBeforeDisc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl13.Name" xml:space="preserve">
    <value>labelControl13</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpCategory.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="labelControl34.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpCategory.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_LargeUOMPrice.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpComp.Properties.AppearanceFocused.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>tabPriceChange</value>
  </data>
  <data name="tab_Control1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;btnDelete.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_m_Attribute1.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtItemCode2.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="grdPrPLevel.TabIndex" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbChangePriceMethod.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtPurchasePrice.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;tab_Matrix.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;btnNext.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;chk_SmallIsStopped.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>105</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>709, 50</value>
  </data>
  <data name="labelControl10.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repPriceLevels.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_LargeIsStopped.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gv_SalesPerQty.Appearance.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_MediumIsStopped.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="rdoSellUOM2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSalesDiscountRatio.TabIndex" type="System.Int32, mscorlib">
    <value>103</value>
  </data>
  <data name="txtPurchasePrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 123</value>
  </data>
  <data name="labelControl34.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtWidth.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtItemCode1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;labelControl18.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txtCustomSalesTaxRatio.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="labelControl16.TabIndex" type="System.Int32, mscorlib">
    <value>113</value>
  </data>
  <data name="&gt;&gt;chk_LargeIsStopped.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtWidth.Properties.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="&gt;&gt;labelControl17.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkpLargeUOM.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="rep_SLQtyNums.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_SmallUOMPrice.Caption" xml:space="preserve">
    <value>Small UOM Price</value>
  </data>
  <data name="&gt;&gt;labelControl33.Name" xml:space="preserve">
    <value>labelControl33</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="cmb_WeightUnit.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMinQty.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;labelControl25.Name" xml:space="preserve">
    <value>labelControl25</value>
  </data>
  <data name="tab_PriceLevels.Size" type="System.Drawing.Size, System.Drawing">
    <value>790, 472</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpSmallUOM.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SubTaxes.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rep_SLQtyNums.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtMaxQty.Location" type="System.Drawing.Point, System.Drawing">
    <value>164, 21</value>
  </data>
  <data name="col_QtyFrom.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txtPurchaseDiscRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtPurchaseTaxRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="SubTaxId.Caption" xml:space="preserve">
    <value>Sub Taxes</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txtPurchaseTaxValue.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="rdoSellUOM2.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="textEdit5.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl31.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMinQty.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl22.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Rate.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_SmallIsStopped.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtMediumUOMFactor.Name" xml:space="preserve">
    <value>txtMediumUOMFactor</value>
  </data>
  <data name="col_m_ReorderLevel.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemPriceId.Caption" xml:space="preserve">
    <value>ItemPriceId</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;txtMaxQty.Name" xml:space="preserve">
    <value>txtMaxQty</value>
  </data>
  <data name="lkp_SubTaxes.Columns6" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_SubTaxes.Columns7" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SubTaxes.Columns4" xml:space="preserve">
    <value>E_TaxableTypeId</value>
  </data>
  <data name="lkp_SubTaxes.Columns5" xml:space="preserve">
    <value>E_TaxableTypeId</value>
  </data>
  <data name="lkp_SubTaxes.Columns2" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkp_SubTaxes.Columns3" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkp_SubTaxes.Columns1" xml:space="preserve">
    <value>DescriptionAr</value>
  </data>
  <data name="txtDesc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpCategory.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SubTaxes.Columns8" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SubTaxes.Columns9" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_ItemCode2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl31.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtReorder.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmb_WeightUnit.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_MediumUOMCode.TabIndex" type="System.Int32, mscorlib">
    <value>122</value>
  </data>
  <data name="txt_LargeUOMCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>146, 20</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpCategory.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="chk_MediumIsStopped.Properties.Caption" xml:space="preserve">
    <value />
  </data>
  <data name="groupBox3.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="chkIsExpire.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="lkpSmallUOM.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;grp_international.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="&gt;&gt;txtMinQty.Name" xml:space="preserve">
    <value>txtMinQty</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpComp.Properties.Columns14" xml:space="preserve">
    <value>CompanyId</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="chk_VariableWeight.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="itemPhoto.TabIndex" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="rdoSellUOM2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_SellPrice.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_LargeUOMCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items9" xml:space="preserve">
    <value>Wholesale Unit 2</value>
  </data>
  <data name="labelControl7.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 12</value>
  </data>
  <data name="txtItemNameEn.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="chkIsExpire.Properties.Caption" xml:space="preserve">
    <value>Is Expire Item</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceFocused.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnAddPicture.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gv_SalesPerQty.Appearance.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="tabTax.Text" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="grd_Mtrx.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 27</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoPrchsUOM2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_Vendors.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;chk_IsLibra.Parent" xml:space="preserve">
    <value>grp_ItemType</value>
  </data>
  <data name="chk_MediumIsStopped.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="gv_Matrix.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;grpExtra.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="col_Vnd_VendorId.Caption" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="gv_SubTaxes.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_m_LargeUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="itemPhoto.Properties.NullText" xml:space="preserve">
    <value>No Image Data</value>
  </data>
  <data name="&gt;&gt;txtInternationalCode.Parent" xml:space="preserve">
    <value>grp_international</value>
  </data>
  <data name="&gt;&gt;txtAudiancePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl32.Name" xml:space="preserve">
    <value>labelControl32</value>
  </data>
  <data name="lkpLargeUOM.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="&gt;&gt;colPLName.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_Attribute3.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtReorder.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM2.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 79</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txtHeight.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_Attribute3.Caption" xml:space="preserve">
    <value>Mtrx3</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtPurchaseDiscRatio.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;txtSmallUOMPrice.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="barBtnList.Caption" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM2.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="barBtnHelp.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lkpLargeUOM.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="rdoSellUOM2.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit5.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSalesTaxRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtPurchaseTaxRatio.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lblPrTaxVal.Size" type="System.Drawing.Size, System.Drawing">
    <value>94, 13</value>
  </data>
  <data name="&gt;&gt;tabDimension.Name" xml:space="preserve">
    <value>tabDimension</value>
  </data>
  <data name="col_m_MinQty.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_m_ItemCode2.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textEdit5.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtItemNameEn.Size" type="System.Drawing.Size, System.Drawing">
    <value>419, 20</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl24.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="&gt;&gt;lkpComp.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpComp.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtWidth.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;tab_Other.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rdoSellUOM1.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="&gt;&gt;cmb_WeightUnit.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colWidth.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl30.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="&gt;&gt;txtPurchaseTaxRatio.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="rdoItemType.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tabTax.Name" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="tab_Control1.AppearancePage.Header.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gv_SubTaxes.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;grp_international.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;lblPrTaxVal.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lblSalesTaxValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>Main Vendors Prices</value>
  </data>
  <data name="txtAudiancePrice.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl21.TabIndex" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtItemCode1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl26.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl31.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtItemNameAr.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="textEdit5.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSalesTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 20</value>
  </data>
  <data name="txtWidth.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit5.EditValue" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtPurchasePrice.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="col_m_MediumUOMPrice.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtHeight.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl35.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lstInternationalCodes.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 70</value>
  </data>
  <data name="labelControl5.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;grd_Vendors.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_IsDeleted.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="col_QtyFrom.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl24.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="textEdit5.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grp_ItemType.Size" type="System.Drawing.Size, System.Drawing">
    <value>239, 178</value>
  </data>
  <data name="txtMinQty.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_LargeUOMCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="&gt;&gt;labelControl36.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="lkpLargeUOM.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoPrchsUOM2.TabIndex" type="System.Int32, mscorlib">
    <value>121</value>
  </data>
  <data name="labelControl17.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl33.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="colWidth.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtAudiancePrice.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnAddCat.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpSmallUOM.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_ItemCode2.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;textEdit5.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_m_SmallUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gv_SubTaxes.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_LargeIsStopped.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_Attribute3.AppearanceHeader.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>255, 255, 192</value>
  </data>
  <data name="labelControl34.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Name" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lkpMediumUOM.Name" xml:space="preserve">
    <value>lkpMediumUOM</value>
  </data>
  <data name="&gt;&gt;lbl_WeightUnit.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM0.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 19</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;tabTax.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>228, 34</value>
  </data>
  <data name="colPrPLName.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCategory.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 20</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 13</value>
  </data>
  <data name="rep_M_Spin.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tab_Other.Name" xml:space="preserve">
    <value>tab_Other</value>
  </data>
  <data name="txtItemNameAr.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_LargeIsStopped.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="chk_LargeIsStopped.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colWidth.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_SmallIsStopped.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_name.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupBox5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Name" xml:space="preserve">
    <value>barBtnHelp</value>
  </data>
  <data name="gridView7.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpSmallUOM.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tabDiscount.Name" xml:space="preserve">
    <value>tabDiscount</value>
  </data>
  <data name="txtItemNameEn.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_MediumIsStopped.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblSalesTaxValue.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpComp.Properties.AppearanceFocused.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl32.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_VariableWeight.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="&gt;&gt;lbl_WeightUnit.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtMediumUOMPrice.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="chk_PricingWithSmall.Size" type="System.Drawing.Size, System.Drawing">
    <value>115, 19</value>
  </data>
  <data name="&gt;&gt;txtHeight.Name" xml:space="preserve">
    <value>txtHeight</value>
  </data>
  <data name="labelControl19.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 13</value>
  </data>
  <data name="labelControl32.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 63</value>
  </data>
  <data name="col_m_ReorderLevel.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtWidth.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;chk_VariableWeight.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_Rate.Name" xml:space="preserve">
    <value>col_Rate</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView6.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmb_WeightUnit.TabIndex" type="System.Int32, mscorlib">
    <value>126</value>
  </data>
  <data name="txtSmallUOMPrice.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 13</value>
  </data>
  <data name="&gt;&gt;lkpCategory.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtItemNameEn.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tab_PricesPerQty.Parent" xml:space="preserve">
    <value>tab_Control1</value>
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="lkpCategory.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtDesc.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="col_Location.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl31.Name" xml:space="preserve">
    <value>labelControl31</value>
  </data>
  <data name="&gt;&gt;chkIsExpire.Parent" xml:space="preserve">
    <value>grp_ItemType</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="tabWarranty.Size" type="System.Drawing.Size, System.Drawing">
    <value>506, 123</value>
  </data>
  <data name="labelControl18.TabIndex" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="&gt;&gt;txt_MediumUOMCode.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="&gt;&gt;lkpComp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl33.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="tab_MainInfo.Text" xml:space="preserve">
    <value>Main Info</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceReadOnly.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_m_MaxQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM1.Name" xml:space="preserve">
    <value>rdoPrchsUOM1</value>
  </data>
  <data name="txtHeight.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl34.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpComp.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="btn_MatrixPrint.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM2.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="col_m_Attribute2.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridView6.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDescEn.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl30.TabIndex" type="System.Int32, mscorlib">
    <value>125</value>
  </data>
  <data name="gv_SalesPerQty.Appearance.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtCustomPurchasesTaxRatio.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;rdoSellUOM2.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;colPlSmallUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoItemType.EditValue" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtLength.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpCategory.Properties.Columns14" xml:space="preserve">
    <value>CatNumber</value>
  </data>
  <data name="txtMediumUOMFactor.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtWidth.Location" type="System.Drawing.Point, System.Drawing">
    <value>279, 9</value>
  </data>
  <data name="btnAddCat.ToolTip" xml:space="preserve">
    <value>Add New Category</value>
  </data>
  <data name="rep_stores.Columns3" xml:space="preserve">
    <value>Name2</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>808, 31</value>
  </data>
  <data name="txtHeight.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpCategory.Properties.Columns1" xml:space="preserve">
    <value>Category Name</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.TabIndex" type="System.Int32, mscorlib">
    <value>115</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Location.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl26.Location" type="System.Drawing.Point, System.Drawing">
    <value>411, 22</value>
  </data>
  <data name="&gt;&gt;btnNext.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoSellUOM2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnAddCat.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="col_m_ItemCode2.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtLargeUOMPrice.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;labelControl12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtCustomSalesTaxRatio.Name" xml:space="preserve">
    <value>txtCustomSalesTaxRatio</value>
  </data>
  <data name="&gt;&gt;cmbChangeSellPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tabWarranty.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tab_PricesPerQty.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gv_SubTaxes.Name" xml:space="preserve">
    <value>gv_SubTaxes</value>
  </data>
  <data name="chk_SmallIsStopped.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chkIsExpire.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="repPriceLevels.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl29.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM1.TabIndex" type="System.Int32, mscorlib">
    <value>117</value>
  </data>
  <data name="rep_stores.Columns1" xml:space="preserve">
    <value>Name1</value>
  </data>
  <data name="chkIsExpire.Location" type="System.Drawing.Point, System.Drawing">
    <value>127, 130</value>
  </data>
  <data name="labelControl32.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl26.Name" xml:space="preserve">
    <value>labelControl26</value>
  </data>
  <data name="&gt;&gt;tab_Other.Parent" xml:space="preserve">
    <value>tab_Control1</value>
  </data>
  <data name="txtMaxQty.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_m_Attribute1.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmb_WeightUnit.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpLargeUOM.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn1.Name" xml:space="preserve">
    <value>gridColumn1</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_MinQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelControl8.TabIndex" type="System.Int32, mscorlib">
    <value>96</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM0.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="col_Location.Caption" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tabDiscount.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="&gt;&gt;grd_Vendors.Name" xml:space="preserve">
    <value>grd_Vendors</value>
  </data>
  <data name="lblSalesTaxValue.Text" xml:space="preserve">
    <value>Sales Tax Value</value>
  </data>
  <data name="labelControl31.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;rdoSellUOM0.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM1.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="&gt;&gt;labelControl13.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="txtReorder.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Name" xml:space="preserve">
    <value>gridColumn7</value>
  </data>
  <data name="&gt;&gt;lblSalesTaxRatio.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="chk_IsLibra.Location" type="System.Drawing.Point, System.Drawing">
    <value>58, 133</value>
  </data>
  <data name="chk_IsDeleted.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btn_GenMtrx.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 23</value>
  </data>
  <data name="txtItemCode2.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_QtyTo.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbChangePriceMethod.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM1.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="btnAddComp.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Name" xml:space="preserve">
    <value>gridColumn4</value>
  </data>
  <data name="txtItemCode1.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnDelete.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMediumUOMFactor.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="grd_Vendors.TabIndex" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl33.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colPriceLevelId.Caption" xml:space="preserve">
    <value>PriceLevelId</value>
  </data>
  <data name="txtWidth.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtPurchaseDiscRatio.Name" xml:space="preserve">
    <value>txtPurchaseDiscRatio</value>
  </data>
  <data name="txtLength.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpComp.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdQty.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 17</value>
  </data>
  <data name="lblPrTaxVal.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtDescEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>283, 36</value>
  </data>
  <data name="col_m_Attribute2.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtAudiancePrice.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMaxQty.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpMediumUOM.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnPrev.ToolTip" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="&gt;&gt;colPrsmallUOMPrice.Name" xml:space="preserve">
    <value>colPrsmallUOMPrice</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="btnAddUOM.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="txtReorder.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl21.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;btnDelete.Parent" xml:space="preserve">
    <value>grp_international</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Name" xml:space="preserve">
    <value>gridColumn2</value>
  </data>
  <data name="&gt;&gt;txtSalesDiscRatio.Name" xml:space="preserve">
    <value>txtSalesDiscRatio</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="&gt;&gt;txtPurchaseTaxValue.Name" xml:space="preserve">
    <value>txtPurchaseTaxValue</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="col_m_Attribute1.Caption" xml:space="preserve">
    <value>Mtrx1</value>
  </data>
  <data name="&gt;&gt;tab_Control1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;colPrPriceLevelId.Name" xml:space="preserve">
    <value>colPrPriceLevelId</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtSalesDiscRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>100, 7</value>
  </data>
  <data name="txtInternationalCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;chk_IsLibra.Name" xml:space="preserve">
    <value>chk_IsLibra</value>
  </data>
  <data name="txtItemCode2.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpCategory.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="tabWarranty.Text" xml:space="preserve">
    <value>Warranty</value>
  </data>
  <data name="labelControl33.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView6.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView6.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl33.TabIndex" type="System.Int32, mscorlib">
    <value>119</value>
  </data>
  <data name="labelControl18.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;chk_LargeIsStopped.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpComp.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_m_MinQty.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.Caption" xml:space="preserve">
    <value>Purchase</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 20</value>
  </data>
  <data name="txtMaxQty.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpSmallUOM.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl28.Parent" xml:space="preserve">
    <value>tab_InventoryLevels</value>
  </data>
  <data name="rdoSellUOM0.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpComp.Properties.Columns7" xml:space="preserve">
    <value>CompanyNameAr</value>
  </data>
  <data name="&gt;&gt;labelControl14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMediumUOM.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>Order Level</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gv_Matrix.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repPriceLevels.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;labelControl30.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtPurchaseDiscRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>310, 7</value>
  </data>
  <data name="rdoSellUOM1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnDelete.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 41</value>
  </data>
  <data name="col_QtyTo.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtAudiancePrice.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtItemCode2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMinQty.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtPurchaseDiscRatio.Parent" xml:space="preserve">
    <value>tabDiscount</value>
  </data>
  <data name="&gt;&gt;btnAddUOM.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="txtItemNameEn.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="grd_Mtrx.Size" type="System.Drawing.Size, System.Drawing">
    <value>744, 343</value>
  </data>
  <data name="gridView2.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;chk_MediumIsStopped.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="lkpCategory.Properties.Columns24" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="cmbChangePriceMethod.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>300, 241</value>
  </data>
  <data name="rep_M_Spin.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtMaxQty.Parent" xml:space="preserve">
    <value>tab_InventoryLevels</value>
  </data>
  <data name="lkp_SubTaxes.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMediumUOM.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="lkpCategory.Properties.Columns8" xml:space="preserve">
    <value>Category Name</value>
  </data>
  <data name="txtItemNameEn.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSalesDiscRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtInternationalCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblPrTaxVal.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rdoPrchsUOM0.Location" type="System.Drawing.Point, System.Drawing">
    <value>259, 28</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="rdoItemType.Properties.Items8" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="rdoItemType.Properties.Items9" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_m_name.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rdoItemType.Properties.Items2" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rdoItemType.Properties.Items3" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="rdoItemType.Properties.Items4" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="rdoItemType.Properties.Items5" xml:space="preserve">
    <value>Assembly</value>
  </data>
  <data name="rdoItemType.Properties.Items6" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;gv_SalesPerQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDescEn.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="grdSlPLevel.TabIndex" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="txtPurchasePrice.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl32.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;tab_PriceLevels.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="rdoSellUOM1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtReorder.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtHeight.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 20</value>
  </data>
  <data name="lkp_SubTaxes.Columns" xml:space="preserve">
    <value>Description Ar</value>
  </data>
  <data name="&gt;&gt;tabWarranty.Name" xml:space="preserve">
    <value>tabWarranty</value>
  </data>
  <data name="lkp_SubTaxes.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_SmallIsStopped.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="txtPurchasePrice.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsLibra.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 19</value>
  </data>
  <data name="&gt;&gt;gridLocation.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="textEdit5.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtLargeUOMFactor.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chkIsExpire.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkpCategory.Name" xml:space="preserve">
    <value>lkpCategory</value>
  </data>
  <data name="col_m_Attribute1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_m_MediumUOMPrice.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbChangeSellPrice.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;btnRemovePic.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchasePrice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="colPlSmallUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;grdPrPLevel.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtItemCode1.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl35.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;tab_PriceLevels.Parent" xml:space="preserve">
    <value>tab_Control1</value>
  </data>
  <data name="textEdit5.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpComp.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_m_ItemCode2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_LargeIsStopped.TabIndex" type="System.Int32, mscorlib">
    <value>131</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_M_Spin.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl34.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_LargeIsStopped.Location" type="System.Drawing.Point, System.Drawing">
    <value>204, 79</value>
  </data>
  <data name="txtLength.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colHeight.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Items3" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="txtWidth.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCategory.Properties.Columns21" xml:space="preserve">
    <value>CategoryId</value>
  </data>
  <data name="txtSalesDiscRatio.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="col_SellPrice.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="chk_VariableWeight.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="rep_SLQtyNums.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_m_ReorderLevel.Name" xml:space="preserve">
    <value>col_m_ReorderLevel</value>
  </data>
  <data name="&gt;&gt;tab_InventoryLevels.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceReadOnly.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;groupBox5.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.Properties.Caption" xml:space="preserve">
    <value>Calculate Tax Before Discount</value>
  </data>
  <data name="col_m_ItemCode2.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl30.Location" type="System.Drawing.Point, System.Drawing">
    <value>163, 82</value>
  </data>
  <data name="gv_Matrix.Appearance.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_SellPrice.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="tab_image.Text" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="&gt;&gt;txtCustomSalesTaxRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lbl_WeightUnit.Text" xml:space="preserve">
    <value>Weight Unit</value>
  </data>
  <data name="&gt;&gt;cmbChangePriceMethod.Name" xml:space="preserve">
    <value>cmbChangePriceMethod</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceReadOnly.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_QtyFrom.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceFocused.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtItemCode1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;chk_PricingWithSmall.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tab_PricesPerQty.Size" type="System.Drawing.Size, System.Drawing">
    <value>790, 472</value>
  </data>
  <data name="&gt;&gt;btn_AddNewInter_Code.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl11.TabIndex" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpLargeUOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>584, 78</value>
  </data>
  <data name="chk_MediumIsStopped.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="col_m_Attribute2.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;cmbChangePriceMethod.Parent" xml:space="preserve">
    <value>tabPriceChange</value>
  </data>
  <data name="chkIsExpire.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chkIsExpire.Size" type="System.Drawing.Size, System.Drawing">
    <value>97, 19</value>
  </data>
  <data name="lbl_WeightUnit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>F Description</value>
  </data>
  <data name="txtMediumUOMFactor.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbChangePriceMethod.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtPurchasePrice.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="groupBox3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtMinQty.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpCategory.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_QtyTo.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;grdQty.Name" xml:space="preserve">
    <value>grdQty</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>Min Limit</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>tab_Other</value>
  </data>
  <data name="&gt;&gt;chk_MediumIsStopped.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtSalesTaxRatio.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_m_MinQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView6.Name" xml:space="preserve">
    <value>gridView6</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="&gt;&gt;groupBox4.Parent" xml:space="preserve">
    <value>tab_PriceLevels</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl37.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 11</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpComp.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="chk_calcTaxBeforeDisc.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtPurchasePrice.Name" xml:space="preserve">
    <value>txtPurchasePrice</value>
  </data>
  <data name="textEdit5.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_ReorderLevel.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMediumUOM.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtPurchasePrice.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grdSlPLevel.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="rdoPrchsUOM0.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;rdoSellUOM2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tabExtraData.Size" type="System.Drawing.Size, System.Drawing">
    <value>512, 151</value>
  </data>
  <data name="txtLength.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 20</value>
  </data>
  <data name="grd_SubTaxes.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="&gt;&gt;lstInternationalCodes.Name" xml:space="preserve">
    <value>lstInternationalCodes</value>
  </data>
  <data name="colPrsmallUOMPrice.Width" type="System.Int32, mscorlib">
    <value>104</value>
  </data>
  <data name="&gt;&gt;labelControl15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tabExtraData.TabIndex" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="txtWidth.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl19.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="txtItemNameEn.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;labelControl14.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="tabExtraData.AppearancePage.Header.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;btnRemovePic.Parent" xml:space="preserve">
    <value>tab_image</value>
  </data>
  <data name="colPrsmallUOMPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tab_MainInfo.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="col_QtyFrom.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtInternationalCode.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtLargeUOMPrice.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="txtItemCode2.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_Attribute1.AppearanceHeader.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>255, 255, 192</value>
  </data>
  <data name="itemPhoto.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtItemNameEn.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;tabWarranty.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtLength.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gv_SubTaxes.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="&gt;&gt;txtSalesDiscRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="col_m_PurchasePrice.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDesc.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="tab_Matrix.Text" xml:space="preserve">
    <value>Matrix Item</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl10.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl21.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="chk_IsLibra.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_Vendors.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="&gt;&gt;grdSlPLevel.Name" xml:space="preserve">
    <value>grdSlPLevel</value>
  </data>
  <data name="chk_IsPos.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="gridLocation.Size" type="System.Drawing.Size, System.Drawing">
    <value>359, 191</value>
  </data>
  <data name="txtItemCode2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl26.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="colWidth.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtReorder.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtReorder.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="grdSlPLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>426, 216</value>
  </data>
  <data name="labelControl14.TabIndex" type="System.Int32, mscorlib">
    <value>108</value>
  </data>
  <data name="&gt;&gt;lkp_SubTaxes.Name" xml:space="preserve">
    <value>lkp_SubTaxes</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>Purchase Discount Ratio</value>
  </data>
  <data name="txtPurchaseDiscRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 20</value>
  </data>
  <data name="txtPurchaseTaxValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl20.TabIndex" type="System.Int32, mscorlib">
    <value>94</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtWidth.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 34</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtPurchasePrice.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSmallUOMPrice.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns8" xml:space="preserve">
    <value>Name15</value>
  </data>
  <data name="txtHeight.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 9</value>
  </data>
  <data name="labelControl36.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtAudiancePrice.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_SmallIsStopped.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="rep_m_attribute.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txtCustomPurchasesTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>73, 57</value>
  </data>
  <data name="rdoSellUOM1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>733, 69</value>
  </data>
  <data name="rep_M_Spin.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl25.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;chkIsExpire.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="&gt;&gt;chk_calcTaxBeforeDisc.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;labelControl15.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chk_MediumIsStopped.TabIndex" type="System.Int32, mscorlib">
    <value>130</value>
  </data>
  <data name="itemPhoto.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grp_UOM.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;labelControl21.Name" xml:space="preserve">
    <value>labelControl21</value>
  </data>
  <data name="labelControl5.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkpSmallUOM.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="lkpMediumUOM.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;txtLength.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_SLQtyNums.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_name.Width" type="System.Int32, mscorlib">
    <value>108</value>
  </data>
  <data name="txtItemNameAr.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>460, 152</value>
  </data>
  <data name="&gt;&gt;txtHeight.Parent" xml:space="preserve">
    <value>tabDimension</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>Change sales price same percentage as change cost</value>
  </data>
  <data name="col_m_PurchasePrice.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl25.Parent" xml:space="preserve">
    <value>tabDiscount</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;groupBox5.Parent" xml:space="preserve">
    <value>tab_Other</value>
  </data>
  <data name="&gt;&gt;gridView6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchasePrice.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="colPrPLName.Caption" xml:space="preserve">
    <value>Price Level</value>
  </data>
  <data name="col_m_ItemCode2.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmb_WeightUnit.Properties.Items10" type="System.Byte, mscorlib">
    <value>3</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items11" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>228, 12</value>
  </data>
  <data name="col_m_PurchasePrice.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="&gt;&gt;labelControl34.Name" xml:space="preserve">
    <value>labelControl34</value>
  </data>
  <data name="&gt;&gt;col_Vnd_ItemId.Name" xml:space="preserve">
    <value>col_Vnd_ItemId</value>
  </data>
  <data name="&gt;&gt;colPlMediumUOMPrice.Name" xml:space="preserve">
    <value>colPlMediumUOMPrice</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl17.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnAddUOM.ToolTip" xml:space="preserve">
    <value>Add New Unit of Measure</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items" xml:space="preserve">
    <value>Last Price</value>
  </data>
  <data name="lkpSmallUOM.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMediumUOMPrice.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInternationalCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtWidth.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl14.Name" xml:space="preserve">
    <value>labelControl14</value>
  </data>
  <data name="chk_IsPos.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_IsPos.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;btnAddComp.Name" xml:space="preserve">
    <value>btnAddComp</value>
  </data>
  <data name="labelControl26.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="colPLName.Width" type="System.Int32, mscorlib">
    <value>199</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtItemCode1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtMediumUOMFactor.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="&gt;&gt;col_ItemPriceId.Name" xml:space="preserve">
    <value>col_ItemPriceId</value>
  </data>
  <data name="gridView6.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit5.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtItemNameAr.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repPriceLevels.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInternationalCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_ItemId.Name" xml:space="preserve">
    <value>col_ItemId</value>
  </data>
  <data name="&gt;&gt;chk_IsPos.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtLargeUOMPrice.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM1.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txtCustomSalesTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 20</value>
  </data>
  <data name="&gt;&gt;itemPhoto.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PictureEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grp_UOM.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;grp_international.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelControl32.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_IsDeleted.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="col_m_Attribute3.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gv_Matrix.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colHeight.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtMediumUOMPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="colHeight.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_LargeIsStopped.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="lblPrTaxRatio.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns7" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="labelControl20.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lblSalesTaxRatio.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="txtMediumUOMFactor.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="col_m_Attribute1.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_MediumUOMCode.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="btnAddComp.Location" type="System.Drawing.Point, System.Drawing">
    <value>283, 88</value>
  </data>
  <data name="lblPrTaxVal.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Location.Name" xml:space="preserve">
    <value>col_Location</value>
  </data>
  <data name="btnAddPicture.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_m_Attribute2.Caption" xml:space="preserve">
    <value>Mtrx2</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gv_Matrix.Appearance.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;chk_calcTaxBeforeDisc.Name" xml:space="preserve">
    <value>chk_calcTaxBeforeDisc</value>
  </data>
  <data name="txtHeight.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtItemNameAr.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="labelControl31.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 60</value>
  </data>
  <data name="&gt;&gt;labelControl32.Parent" xml:space="preserve">
    <value>tabTax</value>
  </data>
  <data name="&gt;&gt;txtCustomSalesTaxRatio.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_m_name.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_m_PurchasePrice.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>808, 594</value>
  </data>
  <data name="txtHeight.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="labelControl23.TabIndex" type="System.Int32, mscorlib">
    <value>98</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_SLQtyNums.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 12</value>
  </data>
  <data name="labelControl34.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnDelete.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="&gt;&gt;labelControl7.Parent" xml:space="preserve">
    <value>tabDiscount</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtItemNameAr.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;colHeight.Name" xml:space="preserve">
    <value>colHeight</value>
  </data>
  <data name="txtHeight.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btn_MatrixPrint.Parent" xml:space="preserve">
    <value>tab_Matrix</value>
  </data>
  <data name="cmbChangeSellPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 31</value>
  </data>
  <data name="txt_MediumUOMCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gv_Vendors.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtHeight.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtReorder.Parent" xml:space="preserve">
    <value>tab_InventoryLevels</value>
  </data>
  <data name="&gt;&gt;labelControl23.Name" xml:space="preserve">
    <value>labelControl23</value>
  </data>
  <data name="grdSlPLevel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="lkpSmallUOM.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="rdoSellUOM2.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="col_m_name.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnAddUOM.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="chk_SmallIsStopped.Properties.Caption" xml:space="preserve">
    <value />
  </data>
  <data name="txtSmallUOMPrice.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl30.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtMaxQty.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM2.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="chk_SmallIsStopped.Location" type="System.Drawing.Point, System.Drawing">
    <value>209, 24</value>
  </data>
  <data name="txtLength.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txt_LargeUOMCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_ReorderLevel.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpLargeUOM.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;rdoPrchsUOM1.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtHeight.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="SubTaxId.Width" type="System.Int32, mscorlib">
    <value>275</value>
  </data>
  <data name="&gt;&gt;col_SellPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tabExtraData.AppearancePage.Header.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gv_Vendors.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;colPriceLevelId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl11.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btnRemovePic.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnRemovePic.ToolTip" xml:space="preserve">
    <value>Remove Pic</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceReadOnly.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbChangeSellPrice.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="labelControl37.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="lkpSmallUOM.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnAddCat.Location" type="System.Drawing.Point, System.Drawing">
    <value>534, 88</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="itemPhoto.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_LargeIsStopped.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="btnAddComp.ToolTip" xml:space="preserve">
    <value>Add New Company</value>
  </data>
  <data name="&gt;&gt;col_m_Attribute1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gv_SubTaxes.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colLength.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_SLQtyNums.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>70, 13</value>
  </data>
  <data name="col_QtyFrom.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colLength.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rdoItemType.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl25.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="&gt;&gt;col_m_Code1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbChangePriceMethod.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_Vendors.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="chk_LargeIsStopped.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtItemNameAr.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;chk_PricingWithSmall.Name" xml:space="preserve">
    <value>chk_PricingWithSmall</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSalesTaxValue.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMediumUOM.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;labelControl18.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="&gt;&gt;col_Vnd_ItemId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl25.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCustomPurchasesTaxRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_MediumIsStopped.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl20.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtAudiancePrice.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="grdPrPLevel.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 594</value>
  </data>
  <data name="col_m_ReorderLevel.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repPriceLevels.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoSellUOM2.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="SubTaxId.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Parent" xml:space="preserve">
    <value>grpExtra</value>
  </data>
  <data name="&gt;&gt;txtPurchaseTaxRatio.Name" xml:space="preserve">
    <value>txtPurchaseTaxRatio</value>
  </data>
  <data name="&gt;&gt;lkpSmallUOM.Name" xml:space="preserve">
    <value>lkpSmallUOM</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl28.TabIndex" type="System.Int32, mscorlib">
    <value>91</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lblSalesTaxRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl17.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;cmb_WeightUnit.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="&gt;&gt;labelControl20.Parent" xml:space="preserve">
    <value>grp_UOM</value>
  </data>
  <data name="txtItemCode1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btn_AddNewInter_Code.Name" xml:space="preserve">
    <value>btn_AddNewInter_Code</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="&gt;&gt;chk_VariableWeight.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_Vnd_VendorId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblPrTaxRatio.TabIndex" type="System.Int32, mscorlib">
    <value>107</value>
  </data>
  <data name="txtWidth.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 20</value>
  </data>
  <data name="colLength.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpComp.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtItemCode2.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_Vendors.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtHeight.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_stores.Columns2" xml:space="preserve">
    <value>StoreNameEn</value>
  </data>
  <data name="lkpCategory.Properties.AppearanceFocused.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsLibra.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView2.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtLength.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>286, 8</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView7.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Vnd_VendorId.Name" xml:space="preserve">
    <value>col_Vnd_VendorId</value>
  </data>
  <data name="btnRemovePic.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 23</value>
  </data>
  <data name="colPLName.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSalesDiscRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl36.Name" xml:space="preserve">
    <value>labelControl36</value>
  </data>
  <data name="txtLength.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpLargeUOM.Properties.AppearanceDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtLength.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="&gt;&gt;txtMediumUOMFactor.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_PricingWithSmall.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtInternationalCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_Vnd_PurchasePrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnPrev.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>Current Qty</value>
  </data>
  <data name="txtLargeUOMFactor.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="lkpCategory.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="rdoPrchsUOM2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txt_WarrantyMonths.Name" xml:space="preserve">
    <value>txt_WarrantyMonths</value>
  </data>
  <data name="&gt;&gt;txtPurchaseDiscRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtItemCode2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_VariableWeight.Properties.Caption" xml:space="preserve">
    <value>وزن متغير</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_m_LargeUOMPrice.Caption" xml:space="preserve">
    <value>Large UOM Price</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>709, 14</value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="txtMaxQty.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbChangePriceMethod.EditValue" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="col_m_ItemCode2.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_m_ReorderLevel.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtItemCode2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl14.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="labelControl22.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_Mtrx.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl21.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="txt_WarrantyMonths.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="col_m_SmallUOMPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_m_PurchasePrice.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;grdPrPLevel.Name" xml:space="preserve">
    <value>grdPrPLevel</value>
  </data>
  <data name="&gt;&gt;btnAddComp.Parent" xml:space="preserve">
    <value>tab_MainInfo</value>
  </data>
  <data name="chk_IsDeleted.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_WarrantyMonths.Properties.Mask.EditMask" xml:space="preserve">
    <value>n0</value>
  </data>
  <data name="txtMinQty.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl31.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lkpComp.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="groupBox1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_MediumIsStopped.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtPurchaseDiscRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;tab_InventoryLevels.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="colPlLargeUOMPrice.Caption" xml:space="preserve">
    <value>Large UOM Price</value>
  </data>
  <data name="txtItemNameAr.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblPrTaxRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_SalesPerQty.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="txtInternationalCode.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="groupBox2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="grdQty.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;grdPrPLevel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLocation.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="&gt;&gt;txtCustomPurchasesTaxRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtSmallUOMPrice.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Location.Width" type="System.Int32, mscorlib">
    <value>237</value>
  </data>
  <data name="txtDesc.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl26.TabIndex" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="txtMediumUOMPrice.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 20</value>
  </data>
  <data name="&gt;&gt;txtSmallUOMPrice.Name" xml:space="preserve">
    <value>txtSmallUOMPrice</value>
  </data>
  <data name="cmbChangeSellPrice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="barBtnList.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="&gt;&gt;tab_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>Code1</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>697, 56</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtItemCode1.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="col_Rate.Caption" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="col_m_Attribute2.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>808, 0</value>
  </data>
  <data name="labelControl31.Text" xml:space="preserve">
    <value>Purchase Custom Tax Ratio</value>
  </data>
  <data name="chk_LargeIsStopped.Properties.Caption" xml:space="preserve">
    <value />
  </data>
  <data name="IC_ItemSubTaxesId.Caption" xml:space="preserve">
    <value>IC_ItemSubTaxesId</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="txtHeight.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="groupBox4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="lblSalesTaxRatio.Text" xml:space="preserve">
    <value>Sales Tax Ratio</value>
  </data>
  <data name="gv_Vendors.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl35.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.Caption" xml:space="preserve">
    <value>Purchase</value>
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl7.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpMediumUOM.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btn_AddNewInter_Code.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>44</value>
  </metadata>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>142, 17</value>
  </metadata>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>