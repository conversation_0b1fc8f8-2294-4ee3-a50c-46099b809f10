using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Linq;
using System.Data;
using DAL;

namespace Reports
{
    public partial class rpt_HR_Reward : DevExpress.XtraReports.UI.XtraReport
    {
        string PaperDetails, code, RegDate, Amount, Drawer, Account, Notes, TotalWords, Discount, userName;

        public rpt_HR_Reward()
        {
            InitializeComponent();
        }
        public rpt_HR_Reward(string _PaperDetails, string _Code, string _RegDate, string _Amount,
            string _Discount, string _Drawer, string _Account, string _Notes, string _TotalWords, string userName)
        {
            InitializeComponent();
           // getReportHeader();

            this.PaperDetails = _PaperDetails;
            this.code = _Code;
            this.RegDate = _RegDate;
            this.Amount = _Amount;
            this.Drawer = _Drawer;
            this.Account = _Account;
            this.Notes = _Notes;
            this.TotalWords = _TotalWords;
            this.Discount = _Discount;
            this.userName = userName;
            getReportHeader();
        }

        public void LoadData()
        {
            lbl_PaperDetails.Text = PaperDetails;
            lbl_Serial.Text = code;
            lbl_RegDate.Text = RegDate;
            lbl_Amount.Text = Amount;
            lbl_Drawer.Text = Drawer;
            lbl_Account.Text = Account;
            lbl_Notes.Text = Notes;
            lbl_Discount.Text = Discount;
            lblTotalWords.Text = TotalWords;
            lbl_User.Text = userName;
            getReportHeader();
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                txt_tel.Text = comp.CmpTel;
                txt_mobile.Text = comp.CmpMobile;
                lbl_compNameEn.Text = comp.CmpNameEn;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }     
    }
}
