﻿namespace Pharmacy.Forms
{
    partial class frm_CloseInventory
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_CloseInventory));
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject2 = new DevExpress.Utils.SerializableAppearanceObject();
            this.splitContainerControl1 = new DevExpress.XtraEditors.SplitContainerControl();
            this.grdJDetails = new DevExpress.XtraGrid.GridControl();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_DueDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Date = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.colCostCenterType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCostCenter = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_CostCenter = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colCostCenterId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCostCenterName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCostCenterCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemMemoEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.grdColAccountId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colAccount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Account = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_AcNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_AccId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_AcNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCredit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDebit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colSerial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Save = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.repSpin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.reAccCode = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.dtInsertDate = new DevExpress.XtraEditors.DateEdit();
            this.txtJNotes = new DevExpress.XtraEditors.MemoEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.grid_Stores = new DevExpress.XtraGrid.GridControl();
            this.gridView_Inventory = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_StoreNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemStoreBalance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CloseAccBalnce = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Difference = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.bar2 = new DevExpress.XtraBars.Bar();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).BeginInit();
            this.splitContainerControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdJDetails)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Date)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Date.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CostCenter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Account)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.reAccCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInsertDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInsertDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtJNotes.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grid_Stores)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView_Inventory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.SuspendLayout();
            // 
            // splitContainerControl1
            // 
            resources.ApplyResources(this.splitContainerControl1, "splitContainerControl1");
            this.splitContainerControl1.FixedPanel = DevExpress.XtraEditors.SplitFixedPanel.None;
            this.splitContainerControl1.Name = "splitContainerControl1";
            resources.ApplyResources(this.splitContainerControl1.Panel1, "splitContainerControl1.Panel1");
            this.splitContainerControl1.Panel1.Controls.Add(this.grdJDetails);
            this.splitContainerControl1.Panel1.Controls.Add(this.dtInsertDate);
            this.splitContainerControl1.Panel1.Controls.Add(this.txtJNotes);
            this.splitContainerControl1.Panel1.Controls.Add(this.labelControl2);
            this.splitContainerControl1.Panel1.Controls.Add(this.labelControl5);
            resources.ApplyResources(this.splitContainerControl1.Panel2, "splitContainerControl1.Panel2");
            this.splitContainerControl1.Panel2.Controls.Add(this.grid_Stores);
            this.splitContainerControl1.SplitterPosition = 644;
            // 
            // grdJDetails
            // 
            resources.ApplyResources(this.grdJDetails, "grdJDetails");
            this.grdJDetails.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdJDetails.EmbeddedNavigator.AccessibleDescription");
            this.grdJDetails.EmbeddedNavigator.AccessibleName = resources.GetString("grdJDetails.EmbeddedNavigator.AccessibleName");
            this.grdJDetails.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdJDetails.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdJDetails.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdJDetails.EmbeddedNavigator.Anchor")));
            this.grdJDetails.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdJDetails.EmbeddedNavigator.BackgroundImage")));
            this.grdJDetails.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdJDetails.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdJDetails.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdJDetails.EmbeddedNavigator.ImeMode")));
            this.grdJDetails.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdJDetails.EmbeddedNavigator.MaximumSize")));
            this.grdJDetails.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdJDetails.EmbeddedNavigator.TextLocation")));
            this.grdJDetails.EmbeddedNavigator.ToolTip = resources.GetString("grdJDetails.EmbeddedNavigator.ToolTip");
            this.grdJDetails.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdJDetails.EmbeddedNavigator.ToolTipIconType")));
            this.grdJDetails.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdJDetails.EmbeddedNavigator.ToolTipTitle");
            this.grdJDetails.MainView = this.gridView4;
            this.grdJDetails.MenuManager = this.barManager1;
            this.grdJDetails.Name = "grdJDetails";
            this.grdJDetails.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpin,
            this.repositoryItemMemoEdit1,
            this.rep_CostCenter,
            this.rep_Account,
            this.rep_Date,
            this.reAccCode});
            this.grdJDetails.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView4});
            // 
            // gridView4
            // 
            this.gridView4.Appearance.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView4.Appearance.HeaderPanel.Font")));
            this.gridView4.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView4.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView4.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView4.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView4.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView4.Appearance.HeaderPanel.GradientMode")));
            this.gridView4.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView4.Appearance.HeaderPanel.Image")));
            this.gridView4.Appearance.HeaderPanel.Options.UseFont = true;
            this.gridView4.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView4.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView4.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView4.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView4.Appearance.Row.Font = ((System.Drawing.Font)(resources.GetObject("gridView4.Appearance.Row.Font")));
            this.gridView4.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView4.Appearance.Row.FontSizeDelta")));
            this.gridView4.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView4.Appearance.Row.FontStyleDelta")));
            this.gridView4.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView4.Appearance.Row.GradientMode")));
            this.gridView4.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView4.Appearance.Row.Image")));
            this.gridView4.Appearance.Row.Options.UseFont = true;
            this.gridView4.Appearance.Row.Options.UseTextOptions = true;
            this.gridView4.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView4.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView4.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridView4, "gridView4");
            this.gridView4.ColumnPanelRowHeight = 40;
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_DueDate,
            this.colCostCenterType,
            this.colCostCenter,
            this.gridColumn7,
            this.grdColAccountId,
            this.colAccount,
            this.col_AcNumber,
            this.colCredit,
            this.colDebit,
            this.colSerial});
            this.gridView4.GridControl = this.grdJDetails;
            this.gridView4.HorzScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Never;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsNavigation.EnterMoveNextColumn = true;
            this.gridView4.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView4.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView4.OptionsView.RowAutoHeight = true;
            this.gridView4.OptionsView.ShowFooter = true;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            this.gridView4.ScrollStyle = DevExpress.XtraGrid.Views.Grid.ScrollStyleFlags.None;
            // 
            // col_DueDate
            // 
            resources.ApplyResources(this.col_DueDate, "col_DueDate");
            this.col_DueDate.ColumnEdit = this.rep_Date;
            this.col_DueDate.FieldName = "DueDate";
            this.col_DueDate.Name = "col_DueDate";
            this.col_DueDate.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.col_DueDate.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_DueDate.OptionsFilter.AllowAutoFilter = false;
            this.col_DueDate.OptionsFilter.AllowFilter = false;
            // 
            // rep_Date
            // 
            resources.ApplyResources(this.rep_Date, "rep_Date");
            this.rep_Date.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Date.Buttons"))))});
            this.rep_Date.CalendarTimeProperties.AccessibleDescription = resources.GetString("rep_Date.CalendarTimeProperties.AccessibleDescription");
            this.rep_Date.CalendarTimeProperties.AccessibleName = resources.GetString("rep_Date.CalendarTimeProperties.AccessibleName");
            this.rep_Date.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("rep_Date.CalendarTimeProperties.AutoHeight")));
            this.rep_Date.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.rep_Date.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("rep_Date.CalendarTimeProperties.Mask.AutoComplete")));
            this.rep_Date.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("rep_Date.CalendarTimeProperties.Mask.BeepOnError")));
            this.rep_Date.CalendarTimeProperties.Mask.EditMask = resources.GetString("rep_Date.CalendarTimeProperties.Mask.EditMask");
            this.rep_Date.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_Date.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.rep_Date.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_Date.CalendarTimeProperties.Mask.MaskType")));
            this.rep_Date.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("rep_Date.CalendarTimeProperties.Mask.PlaceHolder")));
            this.rep_Date.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_Date.CalendarTimeProperties.Mask.SaveLiteral")));
            this.rep_Date.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_Date.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.rep_Date.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_Date.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.rep_Date.CalendarTimeProperties.NullValuePrompt = resources.GetString("rep_Date.CalendarTimeProperties.NullValuePrompt");
            this.rep_Date.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("rep_Date.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.rep_Date.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("rep_Date.Mask.AutoComplete")));
            this.rep_Date.Mask.BeepOnError = ((bool)(resources.GetObject("rep_Date.Mask.BeepOnError")));
            this.rep_Date.Mask.EditMask = resources.GetString("rep_Date.Mask.EditMask");
            this.rep_Date.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_Date.Mask.IgnoreMaskBlank")));
            this.rep_Date.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_Date.Mask.MaskType")));
            this.rep_Date.Mask.PlaceHolder = ((char)(resources.GetObject("rep_Date.Mask.PlaceHolder")));
            this.rep_Date.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_Date.Mask.SaveLiteral")));
            this.rep_Date.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_Date.Mask.ShowPlaceHolders")));
            this.rep_Date.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_Date.Mask.UseMaskAsDisplayFormat")));
            this.rep_Date.Name = "rep_Date";
            // 
            // colCostCenterType
            // 
            resources.ApplyResources(this.colCostCenterType, "colCostCenterType");
            this.colCostCenterType.FieldName = "CostCenterType";
            this.colCostCenterType.Name = "colCostCenterType";
            this.colCostCenterType.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colCostCenterType.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colCostCenterType.OptionsFilter.AllowAutoFilter = false;
            this.colCostCenterType.OptionsFilter.AllowFilter = false;
            // 
            // colCostCenter
            // 
            resources.ApplyResources(this.colCostCenter, "colCostCenter");
            this.colCostCenter.ColumnEdit = this.rep_CostCenter;
            this.colCostCenter.FieldName = "CostCenter";
            this.colCostCenter.Name = "colCostCenter";
            this.colCostCenter.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colCostCenter.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colCostCenter.OptionsFilter.AllowAutoFilter = false;
            this.colCostCenter.OptionsFilter.AllowFilter = false;
            // 
            // rep_CostCenter
            // 
            resources.ApplyResources(this.rep_CostCenter, "rep_CostCenter");
            this.rep_CostCenter.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.rep_CostCenter.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_CostCenter.Buttons"))))});
            this.rep_CostCenter.Name = "rep_CostCenter";
            this.rep_CostCenter.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            resources.ApplyResources(this.repositoryItemGridLookUpEdit1View, "repositoryItemGridLookUpEdit1View");
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colCostCenterId,
            this.colCostCenterName,
            this.colCostCenterCode});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.BestFitMaxRowCount = 10;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // colCostCenterId
            // 
            resources.ApplyResources(this.colCostCenterId, "colCostCenterId");
            this.colCostCenterId.FieldName = "CostCenterId";
            this.colCostCenterId.Name = "colCostCenterId";
            // 
            // colCostCenterName
            // 
            resources.ApplyResources(this.colCostCenterName, "colCostCenterName");
            this.colCostCenterName.FieldName = "CostCenterName";
            this.colCostCenterName.Name = "colCostCenterName";
            // 
            // colCostCenterCode
            // 
            resources.ApplyResources(this.colCostCenterCode, "colCostCenterCode");
            this.colCostCenterCode.FieldName = "CostCenterCode";
            this.colCostCenterCode.Name = "colCostCenterCode";
            this.colCostCenterCode.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // gridColumn7
            // 
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.ColumnEdit = this.repositoryItemMemoEdit1;
            this.gridColumn7.FieldName = "Notes";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn7.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn7.OptionsFilter.AllowAutoFilter = false;
            this.gridColumn7.OptionsFilter.AllowFilter = false;
            // 
            // repositoryItemMemoEdit1
            // 
            resources.ApplyResources(this.repositoryItemMemoEdit1, "repositoryItemMemoEdit1");
            this.repositoryItemMemoEdit1.Name = "repositoryItemMemoEdit1";
            // 
            // grdColAccountId
            // 
            resources.ApplyResources(this.grdColAccountId, "grdColAccountId");
            this.grdColAccountId.FieldName = "AccountId";
            this.grdColAccountId.Name = "grdColAccountId";
            this.grdColAccountId.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.grdColAccountId.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.grdColAccountId.OptionsFilter.AllowAutoFilter = false;
            this.grdColAccountId.OptionsFilter.AllowFilter = false;
            // 
            // colAccount
            // 
            resources.ApplyResources(this.colAccount, "colAccount");
            this.colAccount.ColumnEdit = this.rep_Account;
            this.colAccount.FieldName = "AccountId";
            this.colAccount.MinWidth = 65;
            this.colAccount.Name = "colAccount";
            this.colAccount.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colAccount.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colAccount.OptionsFilter.AllowAutoFilter = false;
            this.colAccount.OptionsFilter.AllowFilter = false;
            // 
            // rep_Account
            // 
            resources.ApplyResources(this.rep_Account, "rep_Account");
            this.rep_Account.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.rep_Account.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Account.Buttons"))))});
            this.rep_Account.ImmediatePopup = true;
            this.rep_Account.Name = "rep_Account";
            this.rep_Account.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;
            this.rep_Account.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Account.View = this.gridView3;
            // 
            // gridView3
            // 
            this.gridView3.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView3.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView3.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView3.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView3.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView3.Appearance.HeaderPanel.GradientMode")));
            this.gridView3.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView3.Appearance.HeaderPanel.Image")));
            this.gridView3.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView3.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView3.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView3.Appearance.Row.FontSizeDelta")));
            this.gridView3.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView3.Appearance.Row.FontStyleDelta")));
            this.gridView3.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView3.Appearance.Row.GradientMode")));
            this.gridView3.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView3.Appearance.Row.Image")));
            this.gridView3.Appearance.Row.Options.UseTextOptions = true;
            this.gridView3.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView3, "gridView3");
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_AcNameAr,
            this.col_AccId});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // col_AcNameAr
            // 
            resources.ApplyResources(this.col_AcNameAr, "col_AcNameAr");
            this.col_AcNameAr.FieldName = "AccName";
            this.col_AcNameAr.Name = "col_AcNameAr";
            // 
            // col_AccId
            // 
            resources.ApplyResources(this.col_AccId, "col_AccId");
            this.col_AccId.FieldName = "AccountId";
            this.col_AccId.Name = "col_AccId";
            // 
            // col_AcNumber
            // 
            resources.ApplyResources(this.col_AcNumber, "col_AcNumber");
            this.col_AcNumber.FieldName = "AcNumber";
            this.col_AcNumber.Name = "col_AcNumber";
            this.col_AcNumber.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.col_AcNumber.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_AcNumber.OptionsFilter.AllowAutoFilter = false;
            this.col_AcNumber.OptionsFilter.AllowFilter = false;
            // 
            // colCredit
            // 
            this.colCredit.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("colCredit.AppearanceCell.FontSizeDelta")));
            this.colCredit.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colCredit.AppearanceCell.FontStyleDelta")));
            this.colCredit.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colCredit.AppearanceCell.GradientMode")));
            this.colCredit.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("colCredit.AppearanceCell.Image")));
            this.colCredit.AppearanceCell.Options.UseTextOptions = true;
            this.colCredit.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.colCredit, "colCredit");
            this.colCredit.DisplayFormat.FormatString = "n2";
            this.colCredit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colCredit.FieldName = "Credit";
            this.colCredit.GroupFormat.FormatString = "n2";
            this.colCredit.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colCredit.MinWidth = 60;
            this.colCredit.Name = "colCredit";
            this.colCredit.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colCredit.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colCredit.OptionsFilter.AllowAutoFilter = false;
            this.colCredit.OptionsFilter.AllowFilter = false;
            this.colCredit.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colCredit.Summary"))))});
            // 
            // colDebit
            // 
            this.colDebit.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("colDebit.AppearanceCell.FontSizeDelta")));
            this.colDebit.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colDebit.AppearanceCell.FontStyleDelta")));
            this.colDebit.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colDebit.AppearanceCell.GradientMode")));
            this.colDebit.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("colDebit.AppearanceCell.Image")));
            this.colDebit.AppearanceCell.Options.UseTextOptions = true;
            this.colDebit.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.colDebit, "colDebit");
            this.colDebit.DisplayFormat.FormatString = "n2";
            this.colDebit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDebit.FieldName = "Debit";
            this.colDebit.GroupFormat.FormatString = "n2";
            this.colDebit.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDebit.MinWidth = 60;
            this.colDebit.Name = "colDebit";
            this.colDebit.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colDebit.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colDebit.OptionsFilter.AllowAutoFilter = false;
            this.colDebit.OptionsFilter.AllowFilter = false;
            this.colDebit.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colDebit.Summary"))))});
            // 
            // colSerial
            // 
            resources.ApplyResources(this.colSerial, "colSerial");
            this.colSerial.FieldName = "gridColumn31";
            this.colSerial.Name = "colSerial";
            this.colSerial.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.colSerial.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colSerial.OptionsFilter.AllowAutoFilter = false;
            this.colSerial.OptionsFilter.AllowFilter = false;
            this.colSerial.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowMoveBarOnToolbar = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtn_Save,
            this.barBtn_Help,
            this.barButtonClose});
            this.barManager1.MaxItemId = 27;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Save),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtn_Save
            // 
            resources.ApplyResources(this.barBtn_Save, "barBtn_Save");
            this.barBtn_Save.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtn_Save.Id = 0;
            this.barBtn_Save.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtn_Save.Name = "barBtn_Save";
            this.barBtn_Save.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Save.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barButtonClose
            // 
            resources.ApplyResources(this.barButtonClose, "barButtonClose");
            this.barButtonClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barButtonClose.Id = 25;
            this.barButtonClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barButtonClose.Name = "barButtonClose";
            this.barButtonClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barButtonClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // repSpin
            // 
            resources.ApplyResources(this.repSpin, "repSpin");
            this.repSpin.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            resources.ApplyResources(serializableAppearanceObject1, "serializableAppearanceObject1");
            this.repSpin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repSpin.Buttons"))), resources.GetString("repSpin.Buttons1"), ((int)(resources.GetObject("repSpin.Buttons2"))), ((bool)(resources.GetObject("repSpin.Buttons3"))), ((bool)(resources.GetObject("repSpin.Buttons4"))), ((bool)(resources.GetObject("repSpin.Buttons5"))), ((DevExpress.XtraEditors.ImageLocation)(resources.GetObject("repSpin.Buttons6"))), ((System.Drawing.Image)(resources.GetObject("repSpin.Buttons7"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, resources.GetString("repSpin.Buttons8"), ((object)(resources.GetObject("repSpin.Buttons9"))), ((DevExpress.Utils.SuperToolTip)(resources.GetObject("repSpin.Buttons10"))), ((bool)(resources.GetObject("repSpin.Buttons11"))))});
            this.repSpin.DisplayFormat.FormatString = "n3";
            this.repSpin.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repSpin.EditFormat.FormatString = "n3";
            this.repSpin.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repSpin.ExportMode = DevExpress.XtraEditors.Repository.ExportMode.DisplayText;
            this.repSpin.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repSpin.Mask.AutoComplete")));
            this.repSpin.Mask.BeepOnError = ((bool)(resources.GetObject("repSpin.Mask.BeepOnError")));
            this.repSpin.Mask.EditMask = resources.GetString("repSpin.Mask.EditMask");
            this.repSpin.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repSpin.Mask.IgnoreMaskBlank")));
            this.repSpin.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repSpin.Mask.MaskType")));
            this.repSpin.Mask.PlaceHolder = ((char)(resources.GetObject("repSpin.Mask.PlaceHolder")));
            this.repSpin.Mask.SaveLiteral = ((bool)(resources.GetObject("repSpin.Mask.SaveLiteral")));
            this.repSpin.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repSpin.Mask.ShowPlaceHolders")));
            this.repSpin.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repSpin.Mask.UseMaskAsDisplayFormat")));
            this.repSpin.MaxValue = new decimal(new int[] {
            1316134911,
            2328,
            0,
            0});
            this.repSpin.Name = "repSpin";
            this.repSpin.ValidateOnEnterKey = true;
            // 
            // reAccCode
            // 
            resources.ApplyResources(this.reAccCode, "reAccCode");
            resources.ApplyResources(serializableAppearanceObject2, "serializableAppearanceObject2");
            this.reAccCode.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("reAccCode.Buttons"))), resources.GetString("reAccCode.Buttons1"), ((int)(resources.GetObject("reAccCode.Buttons2"))), ((bool)(resources.GetObject("reAccCode.Buttons3"))), ((bool)(resources.GetObject("reAccCode.Buttons4"))), ((bool)(resources.GetObject("reAccCode.Buttons5"))), ((DevExpress.XtraEditors.ImageLocation)(resources.GetObject("reAccCode.Buttons6"))), ((System.Drawing.Image)(resources.GetObject("reAccCode.Buttons7"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject2, resources.GetString("reAccCode.Buttons8"), ((object)(resources.GetObject("reAccCode.Buttons9"))), ((DevExpress.Utils.SuperToolTip)(resources.GetObject("reAccCode.Buttons10"))), ((bool)(resources.GetObject("reAccCode.Buttons11"))))});
            this.reAccCode.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("reAccCode.Mask.AutoComplete")));
            this.reAccCode.Mask.BeepOnError = ((bool)(resources.GetObject("reAccCode.Mask.BeepOnError")));
            this.reAccCode.Mask.EditMask = resources.GetString("reAccCode.Mask.EditMask");
            this.reAccCode.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("reAccCode.Mask.IgnoreMaskBlank")));
            this.reAccCode.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("reAccCode.Mask.MaskType")));
            this.reAccCode.Mask.PlaceHolder = ((char)(resources.GetObject("reAccCode.Mask.PlaceHolder")));
            this.reAccCode.Mask.SaveLiteral = ((bool)(resources.GetObject("reAccCode.Mask.SaveLiteral")));
            this.reAccCode.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("reAccCode.Mask.ShowPlaceHolders")));
            this.reAccCode.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("reAccCode.Mask.UseMaskAsDisplayFormat")));
            this.reAccCode.MaxValue = new decimal(new int[] {
            999999999,
            0,
            0,
            0});
            this.reAccCode.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.reAccCode.Name = "reAccCode";
            // 
            // dtInsertDate
            // 
            resources.ApplyResources(this.dtInsertDate, "dtInsertDate");
            this.dtInsertDate.EnterMoveNextControl = true;
            this.dtInsertDate.MenuManager = this.barManager1;
            this.dtInsertDate.Name = "dtInsertDate";
            this.dtInsertDate.Properties.AccessibleDescription = resources.GetString("dtInsertDate.Properties.AccessibleDescription");
            this.dtInsertDate.Properties.AccessibleName = resources.GetString("dtInsertDate.Properties.AccessibleName");
            this.dtInsertDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.dtInsertDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtInsertDate.Properties.AutoHeight")));
            this.dtInsertDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtInsertDate.Properties.Buttons"))))});
            this.dtInsertDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtInsertDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtInsertDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtInsertDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtInsertDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtInsertDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtInsertDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtInsertDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtInsertDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtInsertDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtInsertDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtInsertDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtInsertDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtInsertDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtInsertDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtInsertDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtInsertDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtInsertDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtInsertDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtInsertDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtInsertDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtInsertDate.Properties.Mask.AutoComplete")));
            this.dtInsertDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtInsertDate.Properties.Mask.BeepOnError")));
            this.dtInsertDate.Properties.Mask.EditMask = resources.GetString("dtInsertDate.Properties.Mask.EditMask");
            this.dtInsertDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInsertDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtInsertDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInsertDate.Properties.Mask.MaskType")));
            this.dtInsertDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtInsertDate.Properties.Mask.PlaceHolder")));
            this.dtInsertDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInsertDate.Properties.Mask.SaveLiteral")));
            this.dtInsertDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInsertDate.Properties.Mask.ShowPlaceHolders")));
            this.dtInsertDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInsertDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtInsertDate.Properties.NullValuePrompt = resources.GetString("dtInsertDate.Properties.NullValuePrompt");
            this.dtInsertDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtInsertDate.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txtJNotes
            // 
            resources.ApplyResources(this.txtJNotes, "txtJNotes");
            this.txtJNotes.MenuManager = this.barManager1;
            this.txtJNotes.Name = "txtJNotes";
            this.txtJNotes.Properties.AccessibleDescription = resources.GetString("txtJNotes.Properties.AccessibleDescription");
            this.txtJNotes.Properties.AccessibleName = resources.GetString("txtJNotes.Properties.AccessibleName");
            this.txtJNotes.Properties.NullValuePrompt = resources.GetString("txtJNotes.Properties.NullValuePrompt");
            this.txtJNotes.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtJNotes.Properties.NullValuePromptShowForEmptyValue")));
            this.txtJNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // grid_Stores
            // 
            resources.ApplyResources(this.grid_Stores, "grid_Stores");
            this.grid_Stores.EmbeddedNavigator.AccessibleDescription = resources.GetString("grid_Stores.EmbeddedNavigator.AccessibleDescription");
            this.grid_Stores.EmbeddedNavigator.AccessibleName = resources.GetString("grid_Stores.EmbeddedNavigator.AccessibleName");
            this.grid_Stores.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grid_Stores.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grid_Stores.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grid_Stores.EmbeddedNavigator.Anchor")));
            this.grid_Stores.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grid_Stores.EmbeddedNavigator.BackgroundImage")));
            this.grid_Stores.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grid_Stores.EmbeddedNavigator.BackgroundImageLayout")));
            this.grid_Stores.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grid_Stores.EmbeddedNavigator.ImeMode")));
            this.grid_Stores.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grid_Stores.EmbeddedNavigator.MaximumSize")));
            this.grid_Stores.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grid_Stores.EmbeddedNavigator.TextLocation")));
            this.grid_Stores.EmbeddedNavigator.ToolTip = resources.GetString("grid_Stores.EmbeddedNavigator.ToolTip");
            this.grid_Stores.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grid_Stores.EmbeddedNavigator.ToolTipIconType")));
            this.grid_Stores.EmbeddedNavigator.ToolTipTitle = resources.GetString("grid_Stores.EmbeddedNavigator.ToolTipTitle");
            this.grid_Stores.MainView = this.gridView_Inventory;
            this.grid_Stores.MenuManager = this.barManager1;
            this.grid_Stores.Name = "grid_Stores";
            this.grid_Stores.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView_Inventory,
            this.gridView2});
            // 
            // gridView_Inventory
            // 
            this.gridView_Inventory.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView_Inventory.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView_Inventory.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView_Inventory.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView_Inventory.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView_Inventory.Appearance.HeaderPanel.GradientMode")));
            this.gridView_Inventory.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView_Inventory.Appearance.HeaderPanel.Image")));
            this.gridView_Inventory.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView_Inventory.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView_Inventory.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView_Inventory.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView_Inventory.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView_Inventory.Appearance.Row.FontSizeDelta")));
            this.gridView_Inventory.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView_Inventory.Appearance.Row.FontStyleDelta")));
            this.gridView_Inventory.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView_Inventory.Appearance.Row.GradientMode")));
            this.gridView_Inventory.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView_Inventory.Appearance.Row.Image")));
            this.gridView_Inventory.Appearance.Row.Options.UseTextOptions = true;
            this.gridView_Inventory.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView_Inventory.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView_Inventory.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridView_Inventory, "gridView_Inventory");
            this.gridView_Inventory.ColumnPanelRowHeight = 40;
            this.gridView_Inventory.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_StoreNameAr,
            this.col_ItemStoreBalance,
            this.col_CloseAccBalnce,
            this.col_Difference});
            this.gridView_Inventory.GridControl = this.grid_Stores;
            this.gridView_Inventory.Name = "gridView_Inventory";
            this.gridView_Inventory.OptionsBehavior.Editable = false;
            this.gridView_Inventory.OptionsBehavior.ReadOnly = true;
            this.gridView_Inventory.OptionsView.ShowFooter = true;
            this.gridView_Inventory.OptionsView.ShowGroupPanel = false;
            // 
            // col_StoreNameAr
            // 
            resources.ApplyResources(this.col_StoreNameAr, "col_StoreNameAr");
            this.col_StoreNameAr.FieldName = "StoreNameAr";
            this.col_StoreNameAr.Name = "col_StoreNameAr";
            // 
            // col_ItemStoreBalance
            // 
            resources.ApplyResources(this.col_ItemStoreBalance, "col_ItemStoreBalance");
            this.col_ItemStoreBalance.DisplayFormat.FormatString = "n2";
            this.col_ItemStoreBalance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ItemStoreBalance.FieldName = "ItemStoreBalance";
            this.col_ItemStoreBalance.Name = "col_ItemStoreBalance";
            this.col_ItemStoreBalance.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ItemStoreBalance.Summary"))), resources.GetString("col_ItemStoreBalance.Summary1"), resources.GetString("col_ItemStoreBalance.Summary2"))});
            // 
            // col_CloseAccBalnce
            // 
            resources.ApplyResources(this.col_CloseAccBalnce, "col_CloseAccBalnce");
            this.col_CloseAccBalnce.DisplayFormat.FormatString = "n2";
            this.col_CloseAccBalnce.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_CloseAccBalnce.FieldName = "CloseAccBalnce";
            this.col_CloseAccBalnce.Name = "col_CloseAccBalnce";
            this.col_CloseAccBalnce.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_CloseAccBalnce.Summary"))), resources.GetString("col_CloseAccBalnce.Summary1"), resources.GetString("col_CloseAccBalnce.Summary2"))});
            // 
            // col_Difference
            // 
            resources.ApplyResources(this.col_Difference, "col_Difference");
            this.col_Difference.DisplayFormat.FormatString = "n2";
            this.col_Difference.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Difference.FieldName = "Difference";
            this.col_Difference.Name = "col_Difference";
            this.col_Difference.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Difference.Summary"))), resources.GetString("col_Difference.Summary1"), resources.GetString("col_Difference.Summary2"))});
            // 
            // gridView2
            // 
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.GridControl = this.grid_Stores;
            this.gridView2.Name = "gridView2";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // frm_CloseInventory
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.splitContainerControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Name = "frm_CloseInventory";
            this.Load += new System.EventHandler(this.frm_SL_PostInvoices_Load);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainerControl1)).EndInit();
            this.splitContainerControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdJDetails)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Date.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Date)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CostCenter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Account)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.reAccCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInsertDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInsertDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtJNotes.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grid_Stores)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView_Inventory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtn_Save;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraBars.BarButtonItem barButtonClose;
        private DevExpress.XtraGrid.GridControl grid_Stores;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView_Inventory;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn col_StoreNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemStoreBalance;
        private DevExpress.XtraGrid.Columns.GridColumn col_CloseAccBalnce;
        private DevExpress.XtraGrid.Columns.GridColumn col_Difference;
        private DevExpress.XtraEditors.SplitContainerControl splitContainerControl1;
        private DevExpress.XtraEditors.DateEdit dtInsertDate;
        private DevExpress.XtraEditors.MemoEdit txtJNotes;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraGrid.GridControl grdJDetails;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn col_DueDate;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit rep_Date;
        private DevExpress.XtraGrid.Columns.GridColumn colCostCenterType;
        private DevExpress.XtraGrid.Columns.GridColumn colCostCenter;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_CostCenter;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn colCostCenterId;
        private DevExpress.XtraGrid.Columns.GridColumn colCostCenterName;
        private DevExpress.XtraGrid.Columns.GridColumn colCostCenterCode;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn grdColAccountId;
        private DevExpress.XtraGrid.Columns.GridColumn colAccount;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Account;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn col_AcNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_AccId;
        private DevExpress.XtraGrid.Columns.GridColumn col_AcNumber;
        private DevExpress.XtraGrid.Columns.GridColumn colCredit;
        private DevExpress.XtraGrid.Columns.GridColumn colDebit;
        private DevExpress.XtraGrid.Columns.GridColumn colSerial;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpin;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit reAccCode;
    }
}