﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Windows.Forms;
using DAL;
using Pharmacy.Forms;
using System.Configuration;
using DevExpress.Utils.Paint;
using System.Threading.Tasks;
using System.Threading;
using System.IO;
using System.Data.SqlClient;
using System.Text.RegularExpressions;
using Pharmacy.Properties;

namespace Pharmacy
{
    static class Program
    {
        public static bool ModelIsERP;
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            //Application.SetCompatibleTextRenderingDefault(true);

            //XPaintTextRender.UseExpandTabs = true;
            //XPaintTextRender.ForceTextRenderPaint();

            //XPaint.ForceGDIPlusPaint();
            //XPaint.ForceAPIPaint();
            //XPaintMixed.ForceTextRenderPaint();


            AppDomain currentDomain = AppDomain.CurrentDomain;
            currentDomain.UnhandledException += new UnhandledExceptionEventHandler(MyHandler);

          
            //try
            {
                ERPDataContext DB = new ERPDataContext(ConfigurationManager.ConnectionStrings[1].ConnectionString.Replace("?", "sa@NeoTech"));
                if (DB.DatabaseExists() == false)
                {
                    DialogResult dr = MessageBox.Show("Create DB?", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                    //return;
                    if (dr == DialogResult.Yes)
                    {
                        DB.CreateDatabase();
                        CreateDB(DB);
                    }
                    //MessageBox.Show("Error in connecting to databse");
                    //return;
                    else return;
                }
                var comp = (from u in DB.ST_CompanyInfos
                            select u).First();

                if (!string.IsNullOrEmpty(comp.ModelGlobalPOS))
                {
                    if (Crypto.DecryptStringAES(comp.ModelGlobalPOS, Crypto.Key) == "SysModelIsERP")
                        ModelIsERP = true;
                    else if (Crypto.DecryptStringAES(comp.ModelGlobalPOS, Crypto.Key) == "SysModelIsPOS")
                        ModelIsERP = false;
                    else
                        ModelIsERP = false;
                }
                else
                    ModelIsERP = false;
            }
            //catch (Exception x)
            //{
            //    MessageBox.Show("Connection to database failed" + "\r\n" + x.Message);
            //    return;
            //}
            //Application.Run(new Forms.ACC.frm_ACC_BudgetList());
            //Application.Run(new Reports.frm_Acc_YearlyBudget("", "", "", 1, new DateTime(2018, 12, 31)));

            //Application.Run(new Form1());

            Application.Run(new frmSplash());
        }
        static void MyHandler(object sender, UnhandledExceptionEventArgs args)
        {
            Exception e = (Exception)args.ExceptionObject;
            Console.WriteLine("MyHandler caught : " + e.Message);
            Console.WriteLine("Runtime terminating: {0}", args.IsTerminating);
            Utilities.save_Log(e.Message, e);
            Utilities.save_Log(args.ExceptionObject.ToString(), e);
        }

        private static void CreateDB(ERPDataContext dB)
        {
            string script = "";
            try
            {
                StreamReader sr = new StreamReader(Application.StartupPath + @"\perpetual.dll");

                script = sr.ReadToEnd(); //file.OpenText().ReadToEnd();
                sr.Close();
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            try
            {
                SqlConnection conn = new SqlConnection(dB.Connection.ConnectionString);

                conn.Open();

                //string comm = "DROP TABLE DYNAMICS";
                //SqlCommand cmd = new SqlCommand(comm, conn);
                //cmd.ExecuteNonQuery();

                string[] commandStrings = Regex.Split(script, @"^\s*GO\s*$",
                               RegexOptions.Multiline | RegexOptions.IgnoreCase);

                foreach (string commandString in commandStrings)
                {
                    if (commandString.Trim() != "")
                    {
                        using (var command = new SqlCommand(commandString, conn))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                }
                conn.Close();
            }
            catch (Exception ex)
            {

                MessageBox.Show(ex.Message);
            }
        }
    }
}