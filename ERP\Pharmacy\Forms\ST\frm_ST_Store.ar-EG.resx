﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>850, 31</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 737</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>850, 0</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 706</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>850, 31</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 706</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>850, 737</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 34</value>
  </data>
  <data name="labelControl85.Location" type="System.Drawing.Point, System.Drawing">
    <value>226, 351</value>
  </data>
  <data name="labelControl85.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 13</value>
  </data>
  <data name="labelControl85.Text" xml:space="preserve">
    <value>عدد خانات التقريب الافتراضية</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="chk_UseLastCostPrice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="chk_UseLastCostPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>495, 511</value>
  </data>
  <data name="chk_UseLastCostPrice.Properties.Caption" xml:space="preserve">
    <value>اعتماد أخر سعر تكلفة في حالة الصرف بدون رصيد</value>
  </data>
  <data name="chk_UseLastCostPrice.Size" type="System.Drawing.Size, System.Drawing">
    <value>266, 19</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="chk_UseLastCostPrice.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="chk_SalesOrderforClient.Location" type="System.Drawing.Point, System.Drawing">
    <value>498, 486</value>
  </data>
  <data name="chk_SalesOrderforClient.Properties.Caption" xml:space="preserve">
    <value>عرض امر البيع للمستخدم المحدد في الفاتورة فقط</value>
  </data>
  <data name="chkOutstanding.Location" type="System.Drawing.Point, System.Drawing">
    <value>531, 461</value>
  </data>
  <data name="chkOutstanding.Properties.Caption" xml:space="preserve">
    <value>قيد الدفع بمبدأ الاستحقاق</value>
  </data>
  <data name="chk_allowMoreThanTax.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 561</value>
  </data>
  <data name="chk_CsTypeValidation.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 534</value>
  </data>
  <data name="chk_CsTypeValidation.Properties.Caption" xml:space="preserve">
    <value>التأكيد على نوع العميل</value>
  </data>
  <data name="chk_CsTypeValidation.Size" type="System.Drawing.Size, System.Drawing">
    <value>127, 19</value>
  </data>
  <data name="ch_Authorize.Location" type="System.Drawing.Point, System.Drawing">
    <value>595, 561</value>
  </data>
  <data name="ch_Authorize.Properties.Caption" xml:space="preserve">
    <value>طلب تفويض قبل التعديل</value>
  </data>
  <data name="dtLastEvaluation.Location" type="System.Drawing.Point, System.Drawing">
    <value>90, 312</value>
  </data>
  <data name="labelControl72.Location" type="System.Drawing.Point, System.Drawing">
    <value>226, 315</value>
  </data>
  <data name="labelControl72.Size" type="System.Drawing.Size, System.Drawing">
    <value>114, 13</value>
  </data>
  <data name="labelControl72.Text" xml:space="preserve">
    <value>أقصى موعد يمكن ترحيله</value>
  </data>
  <data name="imgCostDistributionMethod.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="imgCostDistributionMethod.EditValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="imgCostDistributionMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 212</value>
  </data>
  <data name="imgCostDistributionMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>297, 20</value>
  </data>
  <data name="labelControl55.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl55.Location" type="System.Drawing.Point, System.Drawing">
    <value>572, 215</value>
  </data>
  <data name="labelControl55.Size" type="System.Drawing.Size, System.Drawing">
    <value>189, 13</value>
  </data>
  <data name="labelControl55.Text" xml:space="preserve">
    <value>التوزيع الافتراضي للتكاليف في المشتريات</value>
  </data>
  <data name="ch_usecarsystem.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="ch_usecarsystem.Location" type="System.Drawing.Point, System.Drawing">
    <value>595, 536</value>
  </data>
  <data name="ch_usecarsystem.Properties.Caption" xml:space="preserve">
    <value>استخدام نظام السيارات</value>
  </data>
  <data name="chk_SlInvoice_mustB_Approved.Location" type="System.Drawing.Point, System.Drawing">
    <value>161, 509</value>
  </data>
  <data name="chk_SlInvoice_mustB_Approved.Properties.Caption" xml:space="preserve">
    <value>يجب الموافقه على الفاتورة قبل ترحيلها</value>
  </data>
  <data name="chk_SlInvoice_mustB_Approved.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 19</value>
  </data>
  <data name="chk_ApprovePrQuote.Location" type="System.Drawing.Point, System.Drawing">
    <value>73, 484</value>
  </data>
  <data name="chk_ApprovePrQuote.Properties.Caption" xml:space="preserve">
    <value>يجب الموافقة على عرض سعر الشراء لتحميله في الفواتير</value>
  </data>
  <data name="chk_ApprovePrQuote.Size" type="System.Drawing.Size, System.Drawing">
    <value>286, 19</value>
  </data>
  <data name="chk_ApprovePrOrder.Location" type="System.Drawing.Point, System.Drawing">
    <value>109, 461</value>
  </data>
  <data name="chk_ApprovePrOrder.Properties.Caption" xml:space="preserve">
    <value>يجب الموافقة على أمر الشراء لتحميله في الفواتير</value>
  </data>
  <data name="chk_ApprovePrOrder.Size" type="System.Drawing.Size, System.Drawing">
    <value>250, 19</value>
  </data>
  <data name="chk_ApproveSalesOrder.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_ApproveSalesOrder.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 436</value>
  </data>
  <data name="chk_ApproveSalesOrder.Properties.Caption" xml:space="preserve">
    <value>يجب الموافقة على أمر البيع لتحميله في الفواتير</value>
  </data>
  <data name="chk_ApproveSalesOrder.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="chk_ApproveSalesOrder.Size" type="System.Drawing.Size, System.Drawing">
    <value>354, 19</value>
  </data>
  <data name="chk_SalesEmpMandatory.Location" type="System.Drawing.Point, System.Drawing">
    <value>523, 436</value>
  </data>
  <data name="chk_SalesEmpMandatory.Properties.Caption" xml:space="preserve">
    <value>يجب اختيار مندوب المبيعات في الفواتير</value>
  </data>
  <data name="chk_SellAsRestaurant.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 411</value>
  </data>
  <data name="chk_SellAsRestaurant.Properties.Caption" xml:space="preserve">
    <value>دعم عملية البيع بالمطاعم</value>
  </data>
  <data name="labelControl34.Location" type="System.Drawing.Point, System.Drawing">
    <value>657, 187</value>
  </data>
  <data name="labelControl34.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 13</value>
  </data>
  <data name="labelControl34.Text" xml:space="preserve">
    <value>دورة العمل بالمشتريات</value>
  </data>
  <data name="rdo_PrInvoiceWorkflow.Location" type="System.Drawing.Point, System.Drawing">
    <value>90, 181</value>
  </data>
  <data name="rdo_PrInvoiceWorkflow.Properties.Items1" xml:space="preserve">
    <value>أمر توريد-فاتورة-اذن استلام</value>
  </data>
  <data name="rdo_PrInvoiceWorkflow.Properties.Items3" xml:space="preserve">
    <value>أمر توريد-اذن استلام-فاتورة</value>
  </data>
  <data name="rdo_PrInvoiceWorkflow.Properties.Items5" xml:space="preserve">
    <value>بدون</value>
  </data>
  <data name="rdo_PrInvoiceWorkflow.Size" type="System.Drawing.Size, System.Drawing">
    <value>474, 25</value>
  </data>
  <data name="chk_GroupPOSItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>462, 411</value>
  </data>
  <data name="chk_GroupPOSItems.Properties.Caption" xml:space="preserve">
    <value>تجميع الأصناف بنقطة البيع</value>
  </data>
  <data name="chk_GroupPOSItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>299, 19</value>
  </data>
  <data name="chk_EncodePrInvPerVendor.Location" type="System.Drawing.Point, System.Drawing">
    <value>502, 77</value>
  </data>
  <data name="chk_EncodePrInvPerVendor.Properties.Caption" xml:space="preserve">
    <value>تكويد فاتورة المشتريات حسب المورد</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>667, 157</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>94, 13</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>دورة العمل بالمبيعات</value>
  </data>
  <data name="rdo_InvoiceWorkflow.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rdo_InvoiceWorkflow.Location" type="System.Drawing.Point, System.Drawing">
    <value>90, 152</value>
  </data>
  <data name="rdo_InvoiceWorkflow.Properties.Items1" xml:space="preserve">
    <value>أمر توريد-فاتورة-اذن استلام</value>
  </data>
  <data name="rdo_InvoiceWorkflow.Properties.Items3" xml:space="preserve">
    <value>أمر توريد-اذن استلام- فاتورة</value>
  </data>
  <data name="rdo_InvoiceWorkflow.Properties.Items5" xml:space="preserve">
    <value>بدون</value>
  </data>
  <data name="rdo_InvoiceWorkflow.Size" type="System.Drawing.Size, System.Drawing">
    <value>474, 23</value>
  </data>
  <data name="chk_EncodeItemsPerCategory.Location" type="System.Drawing.Point, System.Drawing">
    <value>407, 127</value>
  </data>
  <data name="chk_EncodeItemsPerCategory.Properties.Caption" xml:space="preserve">
    <value>تكويد الأصناف حسب الفئة</value>
  </data>
  <data name="chk_SalesOrderReserveGood.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 386</value>
  </data>
  <data name="chk_SalesOrderReserveGood.Properties.Caption" xml:space="preserve">
    <value>أمر البيع يقوم بحجز البضاعة بالمخازن</value>
  </data>
  <data name="rd_AutoInvSerialForStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 19</value>
  </data>
  <data name="rd_AutoInvSerialForStore.Properties.Items1" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="rd_AutoInvSerialForStore.Properties.Items3" xml:space="preserve">
    <value>المخزن</value>
  </data>
  <data name="rd_AutoInvSerialForStore.Properties.Items5" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="rd_AutoInvSerialForStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 23</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>592, 24</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 13</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>انشاء تسلسل المخازن على مستوى</value>
  </data>
  <data name="chk_Registered.Location" type="System.Drawing.Point, System.Drawing">
    <value>410, 386</value>
  </data>
  <data name="chk_Registered.Properties.Caption" xml:space="preserve">
    <value>استخدام خيار "مقيد" في سندات القبض والدفع النقدي</value>
  </data>
  <data name="chk_Registered.Size" type="System.Drawing.Size, System.Drawing">
    <value>351, 19</value>
  </data>
  <data name="chk_GenerateNewInvCodeOnSave.Location" type="System.Drawing.Point, System.Drawing">
    <value>446, 102</value>
  </data>
  <data name="chk_GenerateNewInvCodeOnSave.Properties.Caption" xml:space="preserve">
    <value>عند تكرار رقم الفاتورة - انشاء رقم جديد عند الحفظ</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>660, 53</value>
  </data>
  <data name="labelControl19.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 13</value>
  </data>
  <data name="labelControl19.Text" xml:space="preserve">
    <value>عند تكرار ارقام الفواتير</value>
  </data>
  <data name="rd_InvoicesCodeRedundancy.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 48</value>
  </data>
  <data name="rd_InvoicesCodeRedundancy.Properties.Items1" xml:space="preserve">
    <value>منع</value>
  </data>
  <data name="rd_InvoicesCodeRedundancy.Properties.Items3" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="rd_InvoicesCodeRedundancy.Properties.Items5" xml:space="preserve">
    <value>عدم التدخل</value>
  </data>
  <data name="rd_InvoicesCodeRedundancy.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 23</value>
  </data>
  <data name="chkCalcPurchaseTaxPerItem.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 313</value>
  </data>
  <data name="chkCalcPurchaseTaxPerItem.Properties.Caption" xml:space="preserve">
    <value>إحتساب ضريبة المبيعات في المشتريات على مستوى الصنف</value>
  </data>
  <data name="chkCalcPurchaseTaxPerItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>379, 19</value>
  </data>
  <data name="chkPriceIncludePurchaseTax.Location" type="System.Drawing.Point, System.Drawing">
    <value>433, 288</value>
  </data>
  <data name="chkPriceIncludePurchaseTax.Properties.Caption" xml:space="preserve">
    <value>سعر الشراء يشمل ضريبة المبيعات</value>
  </data>
  <data name="dtCloseDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtCloseDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>490, 351</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>625, 354</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 13</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>آخر فترة مالية تم إقفالها بتاريخ</value>
  </data>
  <data name="chkPriceIncludeSalesTax.Location" type="System.Drawing.Point, System.Drawing">
    <value>433, 263</value>
  </data>
  <data name="chkPriceIncludeSalesTax.Properties.Caption" xml:space="preserve">
    <value>سعر البيع يشمل ضريبة المبيعات</value>
  </data>
  <data name="chk_AutoPostSales.Location" type="System.Drawing.Point, System.Drawing">
    <value>454, 238</value>
  </data>
  <data name="chk_AutoPostSales.Properties.Caption" xml:space="preserve">
    <value>ترحيل تلقائي لفواتير البيع</value>
  </data>
  <data name="chk_AutoPostSales.Size" type="System.Drawing.Size, System.Drawing">
    <value>307, 19</value>
  </data>
  <data name="chk_AutoPostSales.ToolTip" xml:space="preserve">
    <value>في حال عدم إختيار هذا الخيار، يجب استخدام شاشة اقفال يومية بيع، ليتم ترحيل الفواتير</value>
  </data>
  <data name="slDefaultDecimalPoint.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="slDefaultDecimalPoint.Location" type="System.Drawing.Point, System.Drawing">
    <value>90, 347</value>
  </data>
  <data name="slDefaultDecimalPoint.Properties.Mask.EditMask" xml:space="preserve">
    <value>N00</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="slDefaultDecimalPoint.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="slDefaultDecimalPoint.Properties.NullValuePrompt" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>768, 650</value>
  </data>
  <data name="groupBox4.Text" xml:space="preserve">
    <value>إعدادات عامة</value>
  </data>
  <data name="tab_gnrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>820, 662</value>
  </data>
  <data name="tab_gnrl.Text" xml:space="preserve">
    <value>إعدادات عامة</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>826, 690</value>
  </data>
  <data name="tabPrinting.Size" type="System.Drawing.Size, System.Drawing">
    <value>820, 662</value>
  </data>
  <data name="tabPrinting.Text" xml:space="preserve">
    <value>الطباعة</value>
  </data>
  <data name="tab_OtherSettings.Size" type="System.Drawing.Size, System.Drawing">
    <value>820, 662</value>
  </data>
  <data name="tab_OtherSettings.Text" xml:space="preserve">
    <value>اعدادات أخرى</value>
  </data>
  <data name="tab_Currency.Size" type="System.Drawing.Size, System.Drawing">
    <value>820, 662</value>
  </data>
  <data name="tab_Currency.Text" xml:space="preserve">
    <value>العملات</value>
  </data>
  <data name="tab_Hr.Size" type="System.Drawing.Size, System.Drawing">
    <value>820, 662</value>
  </data>
  <data name="tab_Hr.Text" xml:space="preserve">
    <value>الموارد البشرية</value>
  </data>
  <data name="tabManufacturing.Size" type="System.Drawing.Size, System.Drawing">
    <value>820, 662</value>
  </data>
  <data name="tabManufacturing.Text" xml:space="preserve">
    <value>التصنيع</value>
  </data>
  <data name="tab_Accounting.Size" type="System.Drawing.Size, System.Drawing">
    <value>820, 662</value>
  </data>
  <data name="tab_Accounting.Text" xml:space="preserve">
    <value>الحسابات</value>
  </data>
  <data name="tab_Inv.Size" type="System.Drawing.Size, System.Drawing">
    <value>820, 662</value>
  </data>
  <data name="tab_Inv.Text" xml:space="preserve">
    <value>المخازن</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>اعدادات</value>
  </data>
  <data name="barBtn_Save.Caption" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="barBtn_Help.Caption" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="chkSellRawMaterial.Location" type="System.Drawing.Point, System.Drawing">
    <value>553, 27</value>
  </data>
  <data name="chkSellRawMaterial.Properties.Caption" xml:space="preserve">
    <value>السماح ببيع الصنف الاولي</value>
  </data>
  <data name="chkSellRawMaterial.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="chkBuyAssembly.Location" type="System.Drawing.Point, System.Drawing">
    <value>561, 52</value>
  </data>
  <data name="chkBuyAssembly.Properties.Caption" xml:space="preserve">
    <value>السماح بشراء المنتج التام</value>
  </data>
  <data name="chkBuyAssembly.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="chkSerialForAssembly.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 88</value>
  </data>
  <data name="chkSerialForAssembly.Properties.Caption" xml:space="preserve">
    <value>انشاء رقم تشغيلة مسلسل لكل منتج تام يتم تصنيعه</value>
  </data>
  <data name="chkSerialForAssembly.Size" type="System.Drawing.Size, System.Drawing">
    <value>357, 19</value>
  </data>
  <data name="chkSerialForAssembly.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="colSampleName.Caption" xml:space="preserve">
    <value>اسم النموذج</value>
  </data>
  <data name="colProcessId.Caption" xml:space="preserve">
    <value>نوع العملية</value>
  </data>
  <data name="colPrintFileName.Caption" xml:space="preserve">
    <value>اسم نموذج الطباعة</value>
  </data>
  <data name="btnInvBooks.Location" type="System.Drawing.Point, System.Drawing">
    <value>323, 237</value>
  </data>
  <data name="btnInvBooks.Text" xml:space="preserve">
    <value>الدفاتر</value>
  </data>
  <data name="cmbReport.Location" type="System.Drawing.Point, System.Drawing">
    <value>502, 32</value>
  </data>
  <data name="cmbReport.Properties.Items" xml:space="preserve">
    <value>فاتورة المبيعات</value>
  </data>
  <data name="cmbReport.Properties.Items3" xml:space="preserve">
    <value>فاتورة المشتريات</value>
  </data>
  <data name="cmbReport.Properties.Items6" xml:space="preserve">
    <value>فاتورة مردود المبيعات</value>
  </data>
  <data name="cmbReport.Properties.Items9" xml:space="preserve">
    <value>فاتورة مردود المشتريات</value>
  </data>
  <data name="cmbReport.Properties.Items12" xml:space="preserve">
    <value>أوراق الدفع</value>
  </data>
  <data name="cmbReport.Properties.Items15" xml:space="preserve">
    <value>أوراق القبض</value>
  </data>
  <data name="cmbReport.Properties.Items18" xml:space="preserve">
    <value>فاتورة نقطة المبيع</value>
  </data>
  <data name="cmbReport.Properties.Items21" xml:space="preserve">
    <value>سند دفع / قبض نقدي</value>
  </data>
  <data name="cmbReport.Properties.Items24" xml:space="preserve">
    <value>بيانات التشغيلة</value>
  </data>
  <data name="cmbReport.Properties.Items27" xml:space="preserve">
    <value>سند إيراد/مصروف</value>
  </data>
  <data name="cmbReport.Properties.Items30" xml:space="preserve">
    <value>سند نقل من مخزن</value>
  </data>
  <data name="cmbReport.Properties.Items33" xml:space="preserve">
    <value>عرض أسعار</value>
  </data>
  <data name="cmbReport.Properties.Items36" xml:space="preserve">
    <value>قيد يوميه</value>
  </data>
  <data name="cmbReport.Properties.Items39" xml:space="preserve">
    <value>كشف الحساب</value>
  </data>
  <data name="cmbReport.Properties.Items42" xml:space="preserve">
    <value>قائمة الأسعار للعملاء</value>
  </data>
  <data name="cmbReport.Properties.Items45" xml:space="preserve">
    <value>أمر شراء</value>
  </data>
  <data name="cmbReport.Properties.Items48" xml:space="preserve">
    <value>التصميم الرئيسي</value>
  </data>
  <data name="cmbReport.Properties.Items51" xml:space="preserve">
    <value>قائمة الأسعار للموردين</value>
  </data>
  <data name="cmbReport.Properties.Items54" xml:space="preserve">
    <value>تقرير مبيعات و عمولات مندوب</value>
  </data>
  <data name="cmbReport.Properties.Items57" xml:space="preserve">
    <value>أمر بيع</value>
  </data>
  <data name="cmbReport.Properties.Items60" xml:space="preserve">
    <value>سند هالك / تالف</value>
  </data>
  <data name="cmbReport.Properties.Items63" xml:space="preserve">
    <value>اذن صرف</value>
  </data>
  <data name="cmbReport.Properties.Items66" xml:space="preserve">
    <value>اذن اضافة</value>
  </data>
  <data name="cmbReport.Properties.Items69" xml:space="preserve">
    <value>المركز المالي</value>
  </data>
  <data name="cmbReport.Properties.Items72" xml:space="preserve">
    <value>قائمة الدخل</value>
  </data>
  <data name="cmbReport.Properties.Items75" xml:space="preserve">
    <value>حساب المصروفات و الايرادات</value>
  </data>
  <data name="cmbReport.Properties.Items78" xml:space="preserve">
    <value>امر عمل</value>
  </data>
  <data name="cmbReport.Properties.Items81" xml:space="preserve">
    <value>أوامر عمل العميل</value>
  </data>
  <data name="cmbReport.Properties.Items84" xml:space="preserve">
    <value>أوامر عمل الجهة</value>
  </data>
  <data name="cmbReport.Properties.Items87" xml:space="preserve">
    <value>استمارة مراقبة الجودة</value>
  </data>
  <data name="cmbReport.Properties.Items90" xml:space="preserve">
    <value>عرض سعر مورد</value>
  </data>
  <data name="cmbReport.Properties.Items93" xml:space="preserve">
    <value>طلب شراء</value>
  </data>
  <data name="cmbReport.Properties.Items96" xml:space="preserve">
    <value>ميزان البسكول</value>
  </data>
  <data name="cmbReport.Properties.Items99" xml:space="preserve">
    <value>قائمة الخامات</value>
  </data>
  <data name="cmbReport.Properties.Items102" xml:space="preserve">
    <value>التصميم الرئيسي بالعرض</value>
  </data>
  <data name="cmbReport.Properties.Items108" xml:space="preserve">
    <value>اشعار مدين</value>
  </data>
  <data name="cmbReport.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 77</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>757, 97</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>مسار نماذج المطبوعات</value>
  </data>
  <data name="btnRedesignReport.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 33</value>
  </data>
  <data name="btnRedesignReport.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 18</value>
  </data>
  <data name="btnRedesignReport.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="btnRedesignReport.Text" xml:space="preserve">
    <value>موافق</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>690, 35</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>اعادة تصميم</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 15</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>770, 192</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>نماذج الطباعة</value>
  </data>
  <data name="labelControl44.Location" type="System.Drawing.Point, System.Drawing">
    <value>670, 66</value>
  </data>
  <data name="labelControl44.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="labelControl44.Text" xml:space="preserve">
    <value>مسار المرفقات</value>
  </data>
  <data name="btnAttachPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>193, 62</value>
  </data>
  <data name="txtAttachPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>231, 63</value>
  </data>
  <data name="txtAttachPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>421, 20</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>691, 46</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>مسار ثاني</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>682, 20</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>المسار الأول</value>
  </data>
  <data name="btnReportPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>193, 38</value>
  </data>
  <data name="txtSecondReportPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>231, 40</value>
  </data>
  <data name="txtSecondReportPath.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtSecondReportPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>421, 20</value>
  </data>
  <data name="txtMyDocReportPathFolder.Location" type="System.Drawing.Point, System.Drawing">
    <value>552, 17</value>
  </data>
  <data name="txtReportMyDocPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>231, 17</value>
  </data>
  <data name="txtReportMyDocPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>315, 20</value>
  </data>
  <data name="groupBox12.Location" type="System.Drawing.Point, System.Drawing">
    <value>238, 588</value>
  </data>
  <data name="groupBox12.Size" type="System.Drawing.Size, System.Drawing">
    <value>536, 63</value>
  </data>
  <data name="groupBox12.Text" xml:space="preserve">
    <value>إعدادات المول</value>
  </data>
  <data name="labelControl82.Location" type="System.Drawing.Point, System.Drawing">
    <value>424, 23</value>
  </data>
  <data name="labelControl82.Size" type="System.Drawing.Size, System.Drawing">
    <value>90, 13</value>
  </data>
  <data name="labelControl82.Text" xml:space="preserve">
    <value>مساحة المول بالمتر</value>
  </data>
  <data name="txtMallTotalSize.Location" type="System.Drawing.Point, System.Drawing">
    <value>332, 20</value>
  </data>
  <data name="groupBox10.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 408</value>
  </data>
  <data name="groupBox10.Size" type="System.Drawing.Size, System.Drawing">
    <value>742, 79</value>
  </data>
  <data name="groupBox10.Text" xml:space="preserve">
    <value>إعدادات الميزان الالكتروني</value>
  </data>
  <data name="rdo_ScaleBarcodeType.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 28</value>
  </data>
  <data name="rdo_ScaleBarcodeType.Properties.Items1" xml:space="preserve">
    <value>رقم تعريفي + كود الصنف + السعر + الوزن</value>
  </data>
  <data name="rdo_ScaleBarcodeType.Properties.Items3" xml:space="preserve">
    <value>رقم تعريفي + كود الصنف + الوزن</value>
  </data>
  <data name="rdo_ScaleBarcodeType.Size" type="System.Drawing.Size, System.Drawing">
    <value>729, 25</value>
  </data>
  <data name="labelControl61.Location" type="System.Drawing.Point, System.Drawing">
    <value>421, 59</value>
  </data>
  <data name="labelControl61.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 13</value>
  </data>
  <data name="labelControl61.Text" xml:space="preserve">
    <value>عدد أرقام السعر</value>
  </data>
  <data name="txtPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>364, 55</value>
  </data>
  <data name="labelControl60.Location" type="System.Drawing.Point, System.Drawing">
    <value>288, 59</value>
  </data>
  <data name="labelControl60.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="labelControl60.Text" xml:space="preserve">
    <value>عدد أرقام الوزن</value>
  </data>
  <data name="labelControl58.Location" type="System.Drawing.Point, System.Drawing">
    <value>544, 58</value>
  </data>
  <data name="labelControl58.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 13</value>
  </data>
  <data name="labelControl58.Text" xml:space="preserve">
    <value>عدد أرقام الصنف</value>
  </data>
  <data name="txtScaleBarcode.Location" type="System.Drawing.Point, System.Drawing">
    <value>241, 55</value>
  </data>
  <data name="txtScaleBarcode.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 20</value>
  </data>
  <data name="txtItemBarcode.Location" type="System.Drawing.Point, System.Drawing">
    <value>502, 56</value>
  </data>
  <data name="txtItemBarcode.Size" type="System.Drawing.Size, System.Drawing">
    <value>36, 20</value>
  </data>
  <data name="labelControl59.Location" type="System.Drawing.Point, System.Drawing">
    <value>667, 60</value>
  </data>
  <data name="labelControl59.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="labelControl59.Text" xml:space="preserve">
    <value>الرقم التعريفي</value>
  </data>
  <data name="txtIdentifier.Location" type="System.Drawing.Point, System.Drawing">
    <value>624, 56</value>
  </data>
  <data name="txtIdentifier.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 20</value>
  </data>
  <data name="groupBox9.Location" type="System.Drawing.Point, System.Drawing">
    <value>238, 499</value>
  </data>
  <data name="groupBox9.Size" type="System.Drawing.Size, System.Drawing">
    <value>536, 83</value>
  </data>
  <data name="groupBox9.Text" xml:space="preserve">
    <value>نوع الجرد</value>
  </data>
  <data name="rd_StockType.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 20</value>
  </data>
  <data name="rd_StockType.Properties.Items1" xml:space="preserve">
    <value>دوري</value>
  </data>
  <data name="rd_StockType.Properties.Items3" xml:space="preserve">
    <value>مستمر</value>
  </data>
  <data name="rd_StockType.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 57</value>
  </data>
  <data name="grp_Barcode.Location" type="System.Drawing.Point, System.Drawing">
    <value>20, 219</value>
  </data>
  <data name="grp_Barcode.Size" type="System.Drawing.Size, System.Drawing">
    <value>754, 274</value>
  </data>
  <data name="grp_Barcode.Text" xml:space="preserve">
    <value>اعدادات الباركود</value>
  </data>
  <data name="chk_UseBarcodeMatchTable.Location" type="System.Drawing.Point, System.Drawing">
    <value>579, 150</value>
  </data>
  <data name="chk_UseBarcodeMatchTable.Properties.Caption" xml:space="preserve">
    <value>استخدام باركود جدول المطابقة</value>
  </data>
  <data name="grp_BarcodeTemplate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="grp_BarcodeTemplate.Location" type="System.Drawing.Point, System.Drawing">
    <value>348, 73</value>
  </data>
  <data name="grp_BarcodeTemplate.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 64</value>
  </data>
  <data name="grp_BarcodeTemplate.Text" xml:space="preserve">
    <value>نموذج الباركود</value>
  </data>
  <data name="labelControl30.Location" type="System.Drawing.Point, System.Drawing">
    <value>219, 19</value>
  </data>
  <data name="labelControl30.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="labelControl30.Text" xml:space="preserve">
    <value>طول كود الدفعة</value>
  </data>
  <data name="txt_BarcodeBatchCodeLength.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 38</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>305, 19</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>طول الكمية</value>
  </data>
  <data name="txt_BarcodeQtyLength.EditValue" type="System.Decimal, mscorlib">
    <value>-1</value>
  </data>
  <data name="txt_BarcodeQtyLength.Location" type="System.Drawing.Point, System.Drawing">
    <value>305, 38</value>
  </data>
  <data name="txt_BarcodeQtyLength.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="labelControl29.Location" type="System.Drawing.Point, System.Drawing">
    <value>127, 19</value>
  </data>
  <data name="labelControl29.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="labelControl29.Text" xml:space="preserve">
    <value>طول كود الصنف</value>
  </data>
  <data name="txt_BarcodeItemCodeLength.EditValue" type="System.Decimal, mscorlib">
    <value>-1</value>
  </data>
  <data name="txt_BarcodeItemCodeLength.Location" type="System.Drawing.Point, System.Drawing">
    <value>116, 38</value>
  </data>
  <data name="txt_BarcodeItemCodeLength.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="labelControl31.Location" type="System.Drawing.Point, System.Drawing">
    <value>79, 19</value>
  </data>
  <data name="labelControl31.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 13</value>
  </data>
  <data name="labelControl31.Text" xml:space="preserve">
    <value>بادئ</value>
  </data>
  <data name="txt_BarcodePrefix.Location" type="System.Drawing.Point, System.Drawing">
    <value>65, 38</value>
  </data>
  <data name="btn_BarcodeMatchTable.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btn_BarcodeMatchTable.Location" type="System.Drawing.Point, System.Drawing">
    <value>410, 147</value>
  </data>
  <data name="btn_BarcodeMatchTable.Text" xml:space="preserve">
    <value>فتح باركود جدول المطابقة</value>
  </data>
  <data name="rd_PrintBarcodePerInventory.Location" type="System.Drawing.Point, System.Drawing">
    <value>579, 20</value>
  </data>
  <data name="rd_PrintBarcodePerInventory.Properties.Items1" xml:space="preserve">
    <value>طباعة باركود مرتبط بالتوريد</value>
  </data>
  <data name="rd_PrintBarcodePerInventory.Properties.Items3" xml:space="preserve">
    <value>طباعة باركود بالنموذج التالي</value>
  </data>
  <data name="rd_PrintBarcodePerInventory.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 48</value>
  </data>
  <data name="grp_DueDatesPeriods.Location" type="System.Drawing.Point, System.Drawing">
    <value>20, 12</value>
  </data>
  <data name="grp_DueDatesPeriods.Size" type="System.Drawing.Size, System.Drawing">
    <value>754, 201</value>
  </data>
  <data name="grp_DueDatesPeriods.Text" xml:space="preserve">
    <value>فترات أعمار الديون</value>
  </data>
  <data name="col_Due_Serial.Caption" xml:space="preserve">
    <value>م</value>
  </data>
  <data name="col_Due_Serial.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Due_Name.Caption" xml:space="preserve">
    <value>اسم الفترة</value>
  </data>
  <data name="col_Due_Name.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_Due_From.Caption" xml:space="preserve">
    <value>من يوم</value>
  </data>
  <data name="col_Due_From.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="col_Due_To.Caption" xml:space="preserve">
    <value>الى يوم</value>
  </data>
  <data name="col_Due_To.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="grdDue.Size" type="System.Drawing.Size, System.Drawing">
    <value>742, 122</value>
  </data>
  <data name="rd_IsFutureDueDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="rd_IsFutureDueDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 19</value>
  </data>
  <data name="rd_IsFutureDueDate.Properties.Items1" xml:space="preserve">
    <value>حساب اعمار الديون لفترات قادمة</value>
  </data>
  <data name="rd_IsFutureDueDate.Properties.Items3" xml:space="preserve">
    <value>حساب اعمار الديون لفترات سابقة</value>
  </data>
  <data name="rd_IsFutureDueDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>742, 48</value>
  </data>
  <data name="grp_Currency.Size" type="System.Drawing.Size, System.Drawing">
    <value>774, 87</value>
  </data>
  <data name="grp_Currency.Text" xml:space="preserve">
    <value>تفقيط العملة</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>617, 71</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>تفقيط العملة</value>
  </data>
  <data name="txtPound1.Location" type="System.Drawing.Point, System.Drawing">
    <value>537, 37</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>546, 19</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>مثال: جنيها</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>36, 19</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 13</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>منازل كسر العملة</value>
  </data>
  <data name="txtCurrencyDigitsCount.Location" type="System.Drawing.Point, System.Drawing">
    <value>58, 37</value>
  </data>
  <data name="txtCurrencyDigitsCount.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>459, 18</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 13</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>مثال: جنيهان</value>
  </data>
  <data name="txtPound3.Location" type="System.Drawing.Point, System.Drawing">
    <value>377, 37</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>143, 19</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 13</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>مثال: قروش</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>377, 19</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 13</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>مثال: جنيهات</value>
  </data>
  <data name="txtPiaster3.Location" type="System.Drawing.Point, System.Drawing">
    <value>137, 37</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 19</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 13</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>مثال: قرشين</value>
  </data>
  <data name="txtPiaster1.Location" type="System.Drawing.Point, System.Drawing">
    <value>297, 37</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>308, 19</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>مثال: قرشا</value>
  </data>
  <data name="txtPiaster2.Location" type="System.Drawing.Point, System.Drawing">
    <value>217, 37</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>641, 28</value>
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>97, 13</value>
  </data>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>اسم العملة الرئيسية</value>
  </data>
  <data name="txtMainCrncName.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 20</value>
  </data>
  <data name="grpCrncChange.Size" type="System.Drawing.Size, System.Drawing">
    <value>774, 170</value>
  </data>
  <data name="grpCrncChange.Text" xml:space="preserve">
    <value>معملات التحويل</value>
  </data>
  <data name="colChngDate.Caption" xml:space="preserve">
    <value>تاريخ تغيير المعامل</value>
  </data>
  <data name="colRate.Caption" xml:space="preserve">
    <value>المعامل</value>
  </data>
  <data name="grdCrncChange.Size" type="System.Drawing.Size, System.Drawing">
    <value>768, 151</value>
  </data>
  <data name="grpCrnc1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grpCrnc1.Size" type="System.Drawing.Size, System.Drawing">
    <value>774, 177</value>
  </data>
  <data name="grpCrnc1.Text" xml:space="preserve">
    <value>العملات الأخرى</value>
  </data>
  <data name="colcrncName.Caption" xml:space="preserve">
    <value>اسم العملة</value>
  </data>
  <data name="colcrncName.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colCurrencyPound1.Caption" xml:space="preserve">
    <value>جنيه</value>
  </data>
  <data name="colCurrencyPound1.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="colCurrencyPound2.Caption" xml:space="preserve">
    <value>جنيهان</value>
  </data>
  <data name="colCurrencyPound2.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colCurrencyPound3.Caption" xml:space="preserve">
    <value>جنيهات</value>
  </data>
  <data name="colCurrencyPound3.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="colCurrencyPiaster1.Caption" xml:space="preserve">
    <value>قرشا</value>
  </data>
  <data name="colCurrencyPiaster1.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="colCurrencyPiaster2.Caption" xml:space="preserve">
    <value>قرشين</value>
  </data>
  <data name="colCurrencyPiaster2.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="colCurrencyPiaster3.Caption" xml:space="preserve">
    <value>قروش</value>
  </data>
  <data name="colCurrencyPiaster3.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="colCurrencyDigitsCount.Caption" xml:space="preserve">
    <value>منازل الكسر</value>
  </data>
  <data name="colCurrencyDigitsCount.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="colLastRate.Caption" xml:space="preserve">
    <value>أخر معامل</value>
  </data>
  <data name="colLastRate.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="grdCrnc.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grdCrnc.Size" type="System.Drawing.Size, System.Drawing">
    <value>768, 158</value>
  </data>
  <data name="chkEgyptianLaw.Location" type="System.Drawing.Point, System.Drawing">
    <value>447, 33</value>
  </data>
  <data name="chkEgyptianLaw.Properties.Caption" xml:space="preserve">
    <value>العمل بقانون العمل المصرى</value>
  </data>
  <data name="chkTotalSalaryType.Location" type="System.Drawing.Point, System.Drawing">
    <value>543, 130</value>
  </data>
  <data name="chkTotalSalaryType.Properties.Caption" xml:space="preserve">
    <value>الأجر الشامل يشمل البدلات</value>
  </data>
  <data name="groupBox11.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 177</value>
  </data>
  <data name="groupBox11.Text" xml:space="preserve">
    <value>التأمينات والضرائب</value>
  </data>
  <data name="lblExemption.Location" type="System.Drawing.Point, System.Drawing">
    <value>187, 51</value>
  </data>
  <data name="lblExemption.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 13</value>
  </data>
  <data name="lblExemption.Text" xml:space="preserve">
    <value>نسبة الإعفاء</value>
  </data>
  <data name="txtExemption.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 48</value>
  </data>
  <data name="labelControl74.Location" type="System.Drawing.Point, System.Drawing">
    <value>187, 25</value>
  </data>
  <data name="labelControl74.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="labelControl74.Text" xml:space="preserve">
    <value>ضريبة صافي الدخل</value>
  </data>
  <data name="labelEmpShare.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 13</value>
  </data>
  <data name="labelEmpShare.Text" xml:space="preserve">
    <value>حصة الموظف</value>
  </data>
  <data name="txtNetSalaryTax.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 22</value>
  </data>
  <data name="txtNetSalaryTax.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="labelMinSalaryInsurance.Location" type="System.Drawing.Point, System.Drawing">
    <value>645, 23</value>
  </data>
  <data name="labelMinSalaryInsurance.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 13</value>
  </data>
  <data name="labelMinSalaryInsurance.Text" xml:space="preserve">
    <value>الحد الأدنى للراتب</value>
  </data>
  <data name="labelMax.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="labelMax.Text" xml:space="preserve">
    <value>الحد الأقصى للراتب</value>
  </data>
  <data name="labelCompanyShare.Location" type="System.Drawing.Point, System.Drawing">
    <value>644, 49</value>
  </data>
  <data name="labelCompanyShare.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 13</value>
  </data>
  <data name="labelCompanyShare.Text" xml:space="preserve">
    <value>حصة الشركة</value>
  </data>
  <data name="labelControl71.Location" type="System.Drawing.Point, System.Drawing">
    <value>694, 158</value>
  </data>
  <data name="labelControl71.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="labelControl71.Text" xml:space="preserve">
    <value>عدد أيام الشهر</value>
  </data>
  <data name="chk_GulfHRAvailable.Location" type="System.Drawing.Point, System.Drawing">
    <value>447, 107</value>
  </data>
  <data name="chk_GulfHRAvailable.Properties.Caption" xml:space="preserve">
    <value>دعم متطلبات الموار البشرية بدول الخليج</value>
  </data>
  <data name="chk_GulfHRAvailable.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="groupBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 262</value>
  </data>
  <data name="groupBox5.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="groupBox5.Text" xml:space="preserve">
    <value>اعدادات ضريبة الدخل</value>
  </data>
  <data name="grd_IncomeTaxLevel.Location" type="System.Drawing.Point, System.Drawing">
    <value>42, 205</value>
  </data>
  <data name="gridBand2.Caption" xml:space="preserve">
    <value>شرائح الضرائب</value>
  </data>
  <data name="col_From.Caption" xml:space="preserve">
    <value>من</value>
  </data>
  <data name="col_To.Caption" xml:space="preserve">
    <value>الى</value>
  </data>
  <data name="col_Ratio.Caption" xml:space="preserve">
    <value>نسبة</value>
  </data>
  <data name="grd_IncomeTaxLevel.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="grd_IncomeTaxLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>692, 150</value>
  </data>
  <data name="grd_IncomeTaxLevel.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridBand1.Caption" xml:space="preserve">
    <value>خصومات ضريبة الدخل</value>
  </data>
  <data name="col_TaxDiscMonthValue.Caption" xml:space="preserve">
    <value>قيمة شهرية</value>
  </data>
  <data name="col_TaxDisAnnualValue.Caption" xml:space="preserve">
    <value>قيمة سنوية</value>
  </data>
  <data name="col_TaxDisName.Caption" xml:space="preserve">
    <value>اسم الخصم</value>
  </data>
  <data name="grd_IncomeTaxDisc.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="grd_IncomeTaxDisc.Size" type="System.Drawing.Size, System.Drawing">
    <value>692, 148</value>
  </data>
  <data name="grd_IncomeTaxDisc.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="chk_IncomeTaxAvailable.Properties.Caption" xml:space="preserve">
    <value>تفعيل حساب ضريبة الدخل</value>
  </data>
  <data name="chk_IncomeTaxAvailable.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_AttendanceIs2Shifts.Location" type="System.Drawing.Point, System.Drawing">
    <value>447, 57</value>
  </data>
  <data name="chk_AttendanceIs2Shifts.Properties.Caption" xml:space="preserve">
    <value>الحضور والانصراف ورديتين في اليوم</value>
  </data>
  <data name="chk_DelayOnAttendTimeOnly.Location" type="System.Drawing.Point, System.Drawing">
    <value>447, 82</value>
  </data>
  <data name="chk_DelayOnAttendTimeOnly.Properties.Caption" xml:space="preserve">
    <value>احتساب تأخير الموظفين على توقيت الحضور فقط</value>
  </data>
  <data name="txt_TotalMonthDays_HR.Location" type="System.Drawing.Point, System.Drawing">
    <value>600, 155</value>
  </data>
  <data name="btn_Transport_Vehicles.Text" xml:space="preserve">
    <value>سيارات النقل</value>
  </data>
  <data name="chk_manfProductsOnly.Location" type="System.Drawing.Point, System.Drawing">
    <value>437, 115</value>
  </data>
  <data name="chk_manfProductsOnly.Properties.Caption" xml:space="preserve">
    <value>تصنيع المنتجات فقط</value>
  </data>
  <data name="grp_Trans_Labor_Revenue.Location" type="System.Drawing.Point, System.Drawing">
    <value>407, 582</value>
  </data>
  <data name="grp_Trans_Labor_Revenue.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grp_Trans_Labor_Revenue.Size" type="System.Drawing.Size, System.Drawing">
    <value>370, 68</value>
  </data>
  <data name="grp_Trans_Labor_Revenue.Text" xml:space="preserve">
    <value>إيرادات نقل عمال</value>
  </data>
  <data name="labelControl67.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 13</value>
  </data>
  <data name="labelControl67.Text" xml:space="preserve">
    <value>ح/ العمل</value>
  </data>
  <data name="lkp_LaborRevenue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_LaborRevenue.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_LaborRevenue.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl68.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 13</value>
  </data>
  <data name="labelControl68.Text" xml:space="preserve">
    <value>ح/ النقل</value>
  </data>
  <data name="lkp_TransferRevenue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_TransferRevenue.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_TransferRevenue.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lkp_TransferRevenue.Size" type="System.Drawing.Size, System.Drawing">
    <value>199, 20</value>
  </data>
  <data name="grpboxOther.Location" type="System.Drawing.Point, System.Drawing">
    <value>30, 598</value>
  </data>
  <data name="grpboxOther.Size" type="System.Drawing.Size, System.Drawing">
    <value>370, 69</value>
  </data>
  <data name="grpboxOther.Text" xml:space="preserve">
    <value>أخرى</value>
  </data>
  <data name="labelControl53.Location" type="System.Drawing.Point, System.Drawing">
    <value>218, 43</value>
  </data>
  <data name="labelControl53.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="labelControl53.Text" xml:space="preserve">
    <value>ح/الاحتفاظ</value>
  </data>
  <data name="lkp_Retention.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 40</value>
  </data>
  <data name="lkp_Retention.Properties.Columns8" xml:space="preserve">
    <value>الحساب</value>
  </data>
  <data name="lkp_Retention.Properties.Columns15" xml:space="preserve">
    <value>كود الحساب</value>
  </data>
  <data name="labelControl54.Location" type="System.Drawing.Point, System.Drawing">
    <value>218, 18</value>
  </data>
  <data name="labelControl54.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 13</value>
  </data>
  <data name="labelControl54.Text" xml:space="preserve">
    <value>ح/الدفعات مقدمة</value>
  </data>
  <data name="lkp_AdvancePayment.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 15</value>
  </data>
  <data name="lkp_AdvancePayment.Properties.Columns8" xml:space="preserve">
    <value>الحساب</value>
  </data>
  <data name="lkp_AdvancePayment.Properties.Columns15" xml:space="preserve">
    <value>كود الحساب</value>
  </data>
  <data name="grp_HR.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 199</value>
  </data>
  <data name="grp_HR.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grp_HR.Size" type="System.Drawing.Size, System.Drawing">
    <value>370, 234</value>
  </data>
  <data name="grp_HR.Text" xml:space="preserve">
    <value>الموارد البشرية</value>
  </data>
  <data name="labelControl84.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="labelControl84.Text" xml:space="preserve">
    <value>ح/ الأجازات</value>
  </data>
  <data name="labelControl83.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="labelControl83.Text" xml:space="preserve">
    <value>ح/ الغيابات</value>
  </data>
  <data name="labelControl75.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="labelControl75.Text" xml:space="preserve">
    <value>ح/ الجزاءات</value>
  </data>
  <data name="lbl_BusinessGainAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 13</value>
  </data>
  <data name="lbl_BusinessGainAcc.Text" xml:space="preserve">
    <value>ح/ ضريبة كسب العمل</value>
  </data>
  <data name="lbl_NetTaxAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>218, 134</value>
  </data>
  <data name="lbl_NetTaxAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="lbl_NetTaxAcc.Text" xml:space="preserve">
    <value>ح/ الضريبة التكافلية</value>
  </data>
  <data name="lbl_InsuranceAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="lbl_InsuranceAcc.Text" xml:space="preserve">
    <value>ح/ التأمينات</value>
  </data>
  <data name="lkp_HrAccuredSalaryAccount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_HrAccuredSalaryAccount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_HrLoanAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_HrLoanAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 42</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 13</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>ح/ مصروفات الرواتب</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 67</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 13</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>ح/ استحقاق الرواتب </value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 17</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 13</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>ح/ ذمم الموظفين</value>
  </data>
  <data name="lkp_HrSalaryAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_HrSalaryAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="groupBox7.Location" type="System.Drawing.Point, System.Drawing">
    <value>31, 536</value>
  </data>
  <data name="groupBox7.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupBox7.Size" type="System.Drawing.Size, System.Drawing">
    <value>370, 60</value>
  </data>
  <data name="groupBox7.Text" xml:space="preserve">
    <value>الايرادات والمصروفات</value>
  </data>
  <data name="lbl_ExpensesAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 13</value>
  </data>
  <data name="lbl_ExpensesAcc.Text" xml:space="preserve">
    <value>ح/ المصروفات</value>
  </data>
  <data name="lbl_RevenueAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="lbl_RevenueAcc.Text" xml:space="preserve">
    <value>ح/ الايرادات</value>
  </data>
  <data name="groupBox6.Location" type="System.Drawing.Point, System.Drawing">
    <value>406, 513</value>
  </data>
  <data name="groupBox6.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupBox6.Text" xml:space="preserve">
    <value>أخرى</value>
  </data>
  <data name="lbl_RealStateRevAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 13</value>
  </data>
  <data name="lbl_RealStateRevAcc.Text" xml:space="preserve">
    <value>ح/ ايراد الاستثمار العقاري</value>
  </data>
  <data name="lkp_RealStateSellRvnuAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 13</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>ح/ الاعتمادات المستندية</value>
  </data>
  <data name="lkp_LcAccount.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_LcAccount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="grpDebitCreditNotes.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 475</value>
  </data>
  <data name="grpDebitCreditNotes.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grpDebitCreditNotes.Size" type="System.Drawing.Size, System.Drawing">
    <value>372, 61</value>
  </data>
  <data name="grpDebitCreditNotes.Text" xml:space="preserve">
    <value>الإشعارات المدينة والدائنة</value>
  </data>
  <data name="lblCreditNotesAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>217, 42</value>
  </data>
  <data name="lblCreditNotesAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 13</value>
  </data>
  <data name="lblCreditNotesAccount.Text" xml:space="preserve">
    <value>ح/ اشعار دائن</value>
  </data>
  <data name="lkp_CreditNotesAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 39</value>
  </data>
  <data name="lkp_CreditNotesAccount.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_CreditNotesAccount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblDebitNotesAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>217, 19</value>
  </data>
  <data name="lblDebitNotesAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 13</value>
  </data>
  <data name="lblDebitNotesAccount.Text" xml:space="preserve">
    <value>ح/ اشعار مدين</value>
  </data>
  <data name="lkp_DebitNotesAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="lkp_DebitNotesAccount.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_DebitNotesAccount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>406, 351</value>
  </data>
  <data name="groupBox3.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>367, 156</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>الضرائب</value>
  </data>
  <data name="labelControl43.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 13</value>
  </data>
  <data name="labelControl43.Text" xml:space="preserve">
    <value>ح/ضريبة الجدول</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 88</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 13</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>ح/ ضريبة الإضافة  - بيع</value>
  </data>
  <data name="lkpSalesAddTaxAccount.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkpSalesAddTaxAccount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 111</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 13</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>ح/ ضريبة الإضافة  - شراء</value>
  </data>
  <data name="lkpPurchaseAddTaxAccount.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkpPurchaseAddTaxAccount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblDeductTaxSl.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 42</value>
  </data>
  <data name="lblDeductTaxSl.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 13</value>
  </data>
  <data name="lblDeductTaxSl.Text" xml:space="preserve">
    <value>ح/ ضريبة الخصم  - بيع</value>
  </data>
  <data name="lkpSalesDeductTaxAccount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblDeductTaxPr.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 65</value>
  </data>
  <data name="lblDeductTaxPr.Size" type="System.Drawing.Size, System.Drawing">
    <value>110, 13</value>
  </data>
  <data name="lblDeductTaxPr.Text" xml:space="preserve">
    <value>ح/ ضريبة الخصم  - شراء</value>
  </data>
  <data name="lkpPurchaseDeductTaxAccount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_TaxAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_TaxAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblTax.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 19</value>
  </data>
  <data name="lblTax.Size" type="System.Drawing.Size, System.Drawing">
    <value>83, 13</value>
  </data>
  <data name="lblTax.Text" xml:space="preserve">
    <value>ح/ ضريبة المبيعات</value>
  </data>
  <data name="grp_Merchandising.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 2</value>
  </data>
  <data name="grp_Merchandising.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grp_Merchandising.Size" type="System.Drawing.Size, System.Drawing">
    <value>370, 191</value>
  </data>
  <data name="grp_Merchandising.Text" xml:space="preserve">
    <value>حسابات قائمة الدخل</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="tabInvType.BorderStyle" type="DevExpress.XtraEditors.Controls.BorderStyles, DevExpress.Utils.v15.1">
    <value>NoBorder</value>
  </data>
  <data name="tabInvType.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="tabContinual.Size" type="System.Drawing.Size, System.Drawing">
    <value>360, 162</value>
  </data>
  <data name="tabInvType.Size" type="System.Drawing.Size, System.Drawing">
    <value>366, 168</value>
  </data>
  <data name="tabPerdiodic.Size" type="System.Drawing.Size, System.Drawing">
    <value>360, 162</value>
  </data>
  <data name="tab_ItemsPost.Size" type="System.Drawing.Size, System.Drawing">
    <value>360, 162</value>
  </data>
  <data name="labelControl77.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 132</value>
  </data>
  <data name="labelControl77.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 13</value>
  </data>
  <data name="labelControl77.Text" xml:space="preserve">
    <value>فرق تكلفة مردودات المشتريات</value>
  </data>
  <data name="lkp_PR_ReturnCostACC.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 129</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 56</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 13</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>ح/ تكلفة البضاعة المباعة</value>
  </data>
  <data name="lkp_CostOfSoldGoods.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 53</value>
  </data>
  <data name="lkp_CostOfSoldGoods.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 31</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 13</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>ح/ مردود المبيعات</value>
  </data>
  <data name="lkp_SalesReturnAcc2.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 29</value>
  </data>
  <data name="lkp_SalesReturnAcc2.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 6</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>ح/ المبيعات</value>
  </data>
  <data name="lkp_SalesAcc2.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 3</value>
  </data>
  <data name="lkp_SalesAcc2.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_intermediatePrInventoryAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 103</value>
  </data>
  <data name="lkp_intermediateInventoryAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 79</value>
  </data>
  <data name="lbl_PR_IntermediateAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 110</value>
  </data>
  <data name="lbl_PR_IntermediateAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>107, 13</value>
  </data>
  <data name="lbl_PR_IntermediateAcc.Text" xml:space="preserve">
    <value>حساب مشتريات وسيط</value>
  </data>
  <data name="lbl_IntermediateAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lbl_IntermediateAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 82</value>
  </data>
  <data name="lbl_IntermediateAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>97, 13</value>
  </data>
  <data name="lbl_IntermediateAcc.Text" xml:space="preserve">
    <value>حساب مبيعات وسيط</value>
  </data>
  <data name="lbl_IntermediateAcc.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl73.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 13</value>
  </data>
  <data name="labelControl73.Text" xml:space="preserve">
    <value>ح/ أرباح وخسائر رأسمالية</value>
  </data>
  <data name="lblPurchaseDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 186</value>
  </data>
  <data name="lblPurchaseDiscount.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 13</value>
  </data>
  <data name="lblPurchaseDiscount.Text" xml:space="preserve">
    <value>ح/ الخصم المكتسب</value>
  </data>
  <data name="lkp_MerchandisingAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 6</value>
  </data>
  <data name="lkp_MerchandisingAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_MerchandisingAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PurchaseDiscountAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 183</value>
  </data>
  <data name="lkp_PurchaseDiscountAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_PurchaseDiscountAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_SalesReturnAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 105</value>
  </data>
  <data name="lkp_SalesReturnAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_SalesReturnAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_SalesReturnAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 20</value>
  </data>
  <data name="lblOpenInventory.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 136</value>
  </data>
  <data name="lblOpenInventory.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 13</value>
  </data>
  <data name="lblOpenInventory.Text" xml:space="preserve">
    <value>ح/ بضاعة أول المدة</value>
  </data>
  <data name="lblSalesReturn.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 109</value>
  </data>
  <data name="lblSalesReturn.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 13</value>
  </data>
  <data name="lblSalesReturn.Text" xml:space="preserve">
    <value>ح/ مردود المبيعات</value>
  </data>
  <data name="lkp_OpenInventoryAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 133</value>
  </data>
  <data name="lkp_OpenInventoryAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_OpenInventoryAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_SalesAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 81</value>
  </data>
  <data name="lkp_SalesAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_SalesAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_SalesDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 208</value>
  </data>
  <data name="lkp_SalesDiscount.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_SalesDiscount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblSales.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 84</value>
  </data>
  <data name="lblSales.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="lblSales.Text" xml:space="preserve">
    <value>ح/ المبيعات</value>
  </data>
  <data name="lblCloseInventory.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 161</value>
  </data>
  <data name="lblCloseInventory.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 13</value>
  </data>
  <data name="lblCloseInventory.Text" xml:space="preserve">
    <value>ح/ بضاعة نهاية المدة</value>
  </data>
  <data name="lkp_PurchasesReturnAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 56</value>
  </data>
  <data name="lkp_PurchasesReturnAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_PurchasesReturnAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblSalesDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 211</value>
  </data>
  <data name="lblSalesDiscount.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 13</value>
  </data>
  <data name="lblSalesDiscount.Text" xml:space="preserve">
    <value>ح/ الخصم المسموح به</value>
  </data>
  <data name="lblPurchasesReturn.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 59</value>
  </data>
  <data name="lblPurchasesReturn.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 13</value>
  </data>
  <data name="lblPurchasesReturn.Text" xml:space="preserve">
    <value>ح/ مردود المشتريات</value>
  </data>
  <data name="lkp_CloseInventoryAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 158</value>
  </data>
  <data name="lkp_CloseInventoryAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_CloseInventoryAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lbl_Purchases.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 34</value>
  </data>
  <data name="lbl_Purchases.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 13</value>
  </data>
  <data name="lbl_Purchases.Text" xml:space="preserve">
    <value>ح/ المشتريات</value>
  </data>
  <data name="lkp_PurchasesAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 31</value>
  </data>
  <data name="lkp_PurchasesAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_PurchasesAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lbl_Merchandising.Location" type="System.Drawing.Point, System.Drawing">
    <value>214, 9</value>
  </data>
  <data name="lbl_Merchandising.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="lbl_Merchandising.Text" xml:space="preserve">
    <value>ح/ المتاجرة</value>
  </data>
  <data name="labelControl32.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 13</value>
  </data>
  <data name="labelControl32.Text" xml:space="preserve">
    <value>ح/ خصم مكتسب</value>
  </data>
  <data name="labelControl33.Size" type="System.Drawing.Size, System.Drawing">
    <value>90, 13</value>
  </data>
  <data name="labelControl33.Text" xml:space="preserve">
    <value>ح/ خصم مسموح به</value>
  </data>
  <data name="grp_Expenses.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 436</value>
  </data>
  <data name="grp_Expenses.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grp_Expenses.Size" type="System.Drawing.Size, System.Drawing">
    <value>371, 38</value>
  </data>
  <data name="grp_Expenses.Text" xml:space="preserve">
    <value>الانتاج</value>
  </data>
  <data name="lblManufacturing.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 14</value>
  </data>
  <data name="lblManufacturing.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 13</value>
  </data>
  <data name="lblManufacturing.Text" xml:space="preserve">
    <value>ح/ مراقبة الانتاج</value>
  </data>
  <data name="lkp_ManufacturingExpAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 11</value>
  </data>
  <data name="lkp_ManufacturingExpAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_ManufacturingExpAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="grp_Liabilities.Location" type="System.Drawing.Point, System.Drawing">
    <value>407, 239</value>
  </data>
  <data name="grp_Liabilities.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grp_Liabilities.Text" xml:space="preserve">
    <value>الخصوم وحقوق الملكية</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 13</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>ح/ مجمع الاهلاك</value>
  </data>
  <data name="lkp_DepreciationAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_DepreciationAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblNotesPayable.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 13</value>
  </data>
  <data name="lblNotesPayable.Text" xml:space="preserve">
    <value>ح/ أوراق الدفع</value>
  </data>
  <data name="lkp_NotesPayableAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_NotesPayableAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblVendors.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="lblVendors.Text" xml:space="preserve">
    <value>ح/ الموردون</value>
  </data>
  <data name="lkp_VendorsAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_VendorsAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblCapital.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 13</value>
  </data>
  <data name="lblCapital.Text" xml:space="preserve">
    <value>ح/ رأس المال</value>
  </data>
  <data name="lkp_CapitalAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_CapitalAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="grp_Assets.Location" type="System.Drawing.Point, System.Drawing">
    <value>406, 2</value>
  </data>
  <data name="grp_Assets.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grp_Assets.Text" xml:space="preserve">
    <value>الاصول</value>
  </data>
  <data name="lkp_CustodyAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 175</value>
  </data>
  <data name="labelControl76.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 178</value>
  </data>
  <data name="labelControl76.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 13</value>
  </data>
  <data name="labelControl76.Text" xml:space="preserve">
    <value>ح/ العهد</value>
  </data>
  <data name="lkp_VisaAccs.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 81</value>
  </data>
  <data name="lkp_VisaAccs.Properties.Columns8" xml:space="preserve">
    <value>الحساب</value>
  </data>
  <data name="lkp_VisaAccs.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="labelControl52.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 84</value>
  </data>
  <data name="labelControl52.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 13</value>
  </data>
  <data name="labelControl52.Text" xml:space="preserve">
    <value>ح/الفيزا</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 152</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 13</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>ح / أوراق تحت التحصيل</value>
  </data>
  <data name="lkp_UnderCollectionAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 149</value>
  </data>
  <data name="lkp_UnderCollectionAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_UnderCollectionAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 15</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>ح/أصول ثابتة</value>
  </data>
  <data name="lkp_FixedAssets.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 12</value>
  </data>
  <data name="lkp_FixedAssets.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_FixedAssets.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_BanksAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 58</value>
  </data>
  <data name="lkp_BanksAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_BanksAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblDrawer.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 38</value>
  </data>
  <data name="lblDrawer.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="lblDrawer.Text" xml:space="preserve">
    <value>ح/ الخزانات</value>
  </data>
  <data name="lkp_DrawersAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 35</value>
  </data>
  <data name="lkp_DrawersAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_DrawersAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblBank.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 61</value>
  </data>
  <data name="lblBank.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 13</value>
  </data>
  <data name="lblBank.Text" xml:space="preserve">
    <value>ح/ البنوك</value>
  </data>
  <data name="lblCustomers.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 106</value>
  </data>
  <data name="lblCustomers.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="lblCustomers.Text" xml:space="preserve">
    <value>ح/ العملاء</value>
  </data>
  <data name="lkp_CustomersAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 103</value>
  </data>
  <data name="lkp_CustomersAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_CustomersAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblNotesReceivable.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 129</value>
  </data>
  <data name="lblNotesReceivable.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 13</value>
  </data>
  <data name="lblNotesReceivable.Text" xml:space="preserve">
    <value>ح/ اوراق القبض</value>
  </data>
  <data name="lkp_NotesReceivableAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 126</value>
  </data>
  <data name="lkp_NotesReceivableAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_NotesReceivableAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lblInventory.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 204</value>
  </data>
  <data name="lblInventory.Size" type="System.Drawing.Size, System.Drawing">
    <value>131, 13</value>
  </data>
  <data name="lblInventory.Text" xml:space="preserve">
    <value>ح/ اقفال مخزون الجرد الدوري</value>
  </data>
  <data name="lkp_InventoryAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 201</value>
  </data>
  <data name="lkp_InventoryAcc.Properties.Columns8" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_InventoryAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="chkPackCount.Location" type="System.Drawing.Point, System.Drawing">
    <value>553, 496</value>
  </data>
  <data name="chkPackCount.Properties.Caption" xml:space="preserve">
    <value>دعم عدد الحزم للأصناف</value>
  </data>
  <data name="chk_max_SL_Invoice.Location" type="System.Drawing.Point, System.Drawing">
    <value>660, 476</value>
  </data>
  <data name="chk_max_SL_Invoice.Properties.Caption" xml:space="preserve">
    <value>الحد الأقصى لفواتير البيع</value>
  </data>
  <data name="chk_max_SL_Invoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>137, 19</value>
  </data>
  <data name="chk_max_SL_Order.Location" type="System.Drawing.Point, System.Drawing">
    <value>665, 456</value>
  </data>
  <data name="chk_max_SL_Order.Properties.Caption" xml:space="preserve">
    <value>الحد الأقصى لأوامر البيع</value>
  </data>
  <data name="chk_max_SL_Order.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 19</value>
  </data>
  <data name="ExpireDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>441, 294</value>
  </data>
  <data name="ExpireDisplay.Properties.Items1" xml:space="preserve">
    <value>شهر/سنة</value>
  </data>
  <data name="ExpireDisplay.Properties.Items3" xml:space="preserve">
    <value>يوم/شهر/سنة</value>
  </data>
  <data name="ExpireDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>182, 23</value>
  </data>
  <data name="grp_Libra.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 545</value>
  </data>
  <data name="grp_Libra.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grp_Libra.Size" type="System.Drawing.Size, System.Drawing">
    <value>417, 102</value>
  </data>
  <data name="grp_Libra.Text" xml:space="preserve">
    <value>اعدادات الليبرا</value>
  </data>
  <data name="txt_KG_SL_Factor.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 68</value>
  </data>
  <data name="txt_KG_SL_Factor.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 20</value>
  </data>
  <data name="txt_KG_PR_Factor.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 42</value>
  </data>
  <data name="txt_KG_PR_Factor.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 20</value>
  </data>
  <data name="labelControl65.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl65.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 45</value>
  </data>
  <data name="labelControl65.Text" xml:space="preserve">
    <value>معامل</value>
  </data>
  <data name="labelControl66.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl66.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 68</value>
  </data>
  <data name="labelControl66.Text" xml:space="preserve">
    <value>معامل</value>
  </data>
  <data name="labelControl62.Location" type="System.Drawing.Point, System.Drawing">
    <value>323, 22</value>
  </data>
  <data name="labelControl62.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="labelControl62.Text" xml:space="preserve">
    <value>ليبرا</value>
  </data>
  <data name="lkp_Libra.Location" type="System.Drawing.Point, System.Drawing">
    <value>183, 19</value>
  </data>
  <data name="lkp_Libra.Properties.Columns" xml:space="preserve">
    <value>UOMId</value>
  </data>
  <data name="lkp_Libra.Properties.Columns7" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="lkp_Libra.Properties.Columns8" xml:space="preserve">
    <value>وحدة القياس</value>
  </data>
  <data name="lkp_Libra.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="labelControl63.Location" type="System.Drawing.Point, System.Drawing">
    <value>323, 45</value>
  </data>
  <data name="labelControl63.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 13</value>
  </data>
  <data name="labelControl63.Text" xml:space="preserve">
    <value>كيلو - الشراء</value>
  </data>
  <data name="labelControl64.Location" type="System.Drawing.Point, System.Drawing">
    <value>323, 68</value>
  </data>
  <data name="labelControl64.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="labelControl64.Text" xml:space="preserve">
    <value>كيلو - البيع</value>
  </data>
  <data name="lkp_KG_PR.Location" type="System.Drawing.Point, System.Drawing">
    <value>183, 42</value>
  </data>
  <data name="lkp_KG_PR.Properties.Columns" xml:space="preserve">
    <value>UOMId</value>
  </data>
  <data name="lkp_KG_PR.Properties.Columns7" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="lkp_KG_PR.Properties.Columns8" xml:space="preserve">
    <value>وحدة القياس</value>
  </data>
  <data name="lkp_KG_PR.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="lkp_KG_SL.Location" type="System.Drawing.Point, System.Drawing">
    <value>183, 65</value>
  </data>
  <data name="lkp_KG_SL.Properties.Columns" xml:space="preserve">
    <value>UOMId</value>
  </data>
  <data name="lkp_KG_SL.Properties.Columns7" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="lkp_KG_SL.Properties.Columns8" xml:space="preserve">
    <value>وحدة القياس</value>
  </data>
  <data name="lkp_KG_SL.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="gbmatrix.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 443</value>
  </data>
  <data name="gbmatrix.Size" type="System.Drawing.Size, System.Drawing">
    <value>420, 102</value>
  </data>
  <data name="gbmatrix.Text" xml:space="preserve">
    <value>اعدادت المصفوفة</value>
  </data>
  <data name="lkp_mtrixModel.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 65</value>
  </data>
  <data name="lkp_mtrixCC.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 42</value>
  </data>
  <data name="labelControl47.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 22</value>
  </data>
  <data name="labelControl47.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="labelControl47.Text" xml:space="preserve">
    <value>مصفوفة الالوان</value>
  </data>
  <data name="lkp_mtrixColor.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 19</value>
  </data>
  <data name="labelControl48.Location" type="System.Drawing.Point, System.Drawing">
    <value>330, 45</value>
  </data>
  <data name="labelControl48.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 13</value>
  </data>
  <data name="labelControl48.Text" xml:space="preserve">
    <value> السعة اللترية</value>
  </data>
  <data name="labelControl49.Location" type="System.Drawing.Point, System.Drawing">
    <value>368, 68</value>
  </data>
  <data name="labelControl49.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="labelControl49.Text" xml:space="preserve">
    <value>الطراز</value>
  </data>
  <data name="chk_StorePurchase.Location" type="System.Drawing.Point, System.Drawing">
    <value>597, 436</value>
  </data>
  <data name="chk_StorePurchase.Properties.Caption" xml:space="preserve">
    <value>تحديد المخزن مقابل كل صنف مشتريات</value>
  </data>
  <data name="chk_StorePurchase.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 19</value>
  </data>
  <data name="chk_StorePurchase.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_StoreSales.Location" type="System.Drawing.Point, System.Drawing">
    <value>607, 416</value>
  </data>
  <data name="chk_StoreSales.Properties.Caption" xml:space="preserve">
    <value>تحديد المخزن مقابل كل صنف مبيعات</value>
  </data>
  <data name="chk_StoreSales.Size" type="System.Drawing.Size, System.Drawing">
    <value>190, 19</value>
  </data>
  <data name="groupBox8.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 85</value>
  </data>
  <data name="groupBox8.Size" type="System.Drawing.Size, System.Drawing">
    <value>420, 352</value>
  </data>
  <data name="groupBox8.Text" xml:space="preserve">
    <value>اسماء الأعمدة</value>
  </data>
  <data name="labelControl81.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 314</value>
  </data>
  <data name="labelControl81.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl81.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="CategoryEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 311</value>
  </data>
  <data name="CategoryAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 311</value>
  </data>
  <data name="labelControl80.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 13</value>
  </data>
  <data name="labelControl80.Text" xml:space="preserve">
    <value>الفئة</value>
  </data>
  <data name="CompanyAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 283</value>
  </data>
  <data name="CompanyEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 285</value>
  </data>
  <data name="labelControl79.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 286</value>
  </data>
  <data name="labelControl79.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl79.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="labelControl78.Text" xml:space="preserve">
    <value>المجموعة</value>
  </data>
  <data name="labelControl69.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 260</value>
  </data>
  <data name="labelControl69.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl69.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txtPiecesCountEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 257</value>
  </data>
  <data name="labelControl70.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="labelControl70.Text" xml:space="preserve">
    <value>عدد القطع</value>
  </data>
  <data name="txtPiecesCountAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 257</value>
  </data>
  <data name="labelControl56.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl56.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 234</value>
  </data>
  <data name="labelControl56.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl56.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txt_SerialNameEn2.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 231</value>
  </data>
  <data name="labelControl57.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl57.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 234</value>
  </data>
  <data name="labelControl57.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="labelControl57.Text" xml:space="preserve">
    <value>رقم السيريال 2</value>
  </data>
  <data name="txt_SerialNameAr2.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 231</value>
  </data>
  <data name="labelControl50.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 208</value>
  </data>
  <data name="labelControl50.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl50.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txt_ExpiredateEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 204</value>
  </data>
  <data name="labelControl51.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 208</value>
  </data>
  <data name="labelControl51.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 13</value>
  </data>
  <data name="labelControl51.Text" xml:space="preserve">
    <value>تاريخ الصلاحية</value>
  </data>
  <data name="txt_ExpiredateAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 204</value>
  </data>
  <data name="labelControl45.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 182</value>
  </data>
  <data name="labelControl45.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl45.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txt_SalesEmployeeEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 178</value>
  </data>
  <data name="labelControl46.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 182</value>
  </data>
  <data name="labelControl46.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="labelControl46.Text" xml:space="preserve">
    <value>مندوب البيع</value>
  </data>
  <data name="txt_SalesEmployeeAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 178</value>
  </data>
  <data name="labelControl39.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 157</value>
  </data>
  <data name="labelControl39.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl39.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txt_DeliveryEmployeeEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 153</value>
  </data>
  <data name="labelControl40.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 157</value>
  </data>
  <data name="labelControl40.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 13</value>
  </data>
  <data name="labelControl40.Text" xml:space="preserve">
    <value>مسئول التسليم</value>
  </data>
  <data name="txt_DeliveryEmployeeAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 153</value>
  </data>
  <data name="labelControl41.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 131</value>
  </data>
  <data name="labelControl41.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl41.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txt_DepartmentEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 127</value>
  </data>
  <data name="labelControl42.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 131</value>
  </data>
  <data name="labelControl42.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="labelControl42.Text" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="txt_DepartmentAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 127</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 101</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txt_StatusEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 97</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 101</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>الحالة</value>
  </data>
  <data name="txt_StatusAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 97</value>
  </data>
  <data name="labelControl37.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 75</value>
  </data>
  <data name="labelControl37.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl37.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txt_PriorityEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 71</value>
  </data>
  <data name="labelControl38.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 75</value>
  </data>
  <data name="labelControl38.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 13</value>
  </data>
  <data name="labelControl38.Text" xml:space="preserve">
    <value>الاهمية</value>
  </data>
  <data name="txt_PriorityAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 71</value>
  </data>
  <data name="lbl_SerialNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 49</value>
  </data>
  <data name="lbl_SerialNameEn.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="lbl_SerialNameEn.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txt_SerialNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 45</value>
  </data>
  <data name="lbl_SerialNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 49</value>
  </data>
  <data name="lbl_SerialNameAr.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 13</value>
  </data>
  <data name="lbl_SerialNameAr.Text" xml:space="preserve">
    <value>رقم السريال</value>
  </data>
  <data name="txt_SerialNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 45</value>
  </data>
  <data name="lbl_BatchNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 23</value>
  </data>
  <data name="lbl_BatchNameEn.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="lbl_BatchNameEn.Text" xml:space="preserve">
    <value>ج</value>
  </data>
  <data name="txt_BatchNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 19</value>
  </data>
  <data name="lbl_BatchNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 23</value>
  </data>
  <data name="lbl_BatchNameAr.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 13</value>
  </data>
  <data name="lbl_BatchNameAr.Text" xml:space="preserve">
    <value>التشغيلة</value>
  </data>
  <data name="txt_BatchNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 19</value>
  </data>
  <data name="chk_Serial.Location" type="System.Drawing.Point, System.Drawing">
    <value>531, 396</value>
  </data>
  <data name="chk_Serial.Properties.Caption" xml:space="preserve">
    <value>دعم رقم السريال للأصناف</value>
  </data>
  <data name="chk_Serial.Size" type="System.Drawing.Size, System.Drawing">
    <value>266, 19</value>
  </data>
  <data name="btn_ImportItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 27</value>
  </data>
  <data name="btn_ImportItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 36</value>
  </data>
  <data name="btn_ImportItems.Text" xml:space="preserve">
    <value>سحب الاصناف من ملف اكسيل</value>
  </data>
  <data name="btn_ReevaluateOutTrns.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 68</value>
  </data>
  <data name="btn_ReevaluateOutTrns.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 35</value>
  </data>
  <data name="btn_ReevaluateOutTrns.Text" xml:space="preserve">
    <value>إعادة تقييم المخزون</value>
  </data>
  <data name="chk_UseQC.Location" type="System.Drawing.Point, System.Drawing">
    <value>564, 376</value>
  </data>
  <data name="chk_UseQC.Properties.Caption" xml:space="preserve">
    <value>دعم  مراقبة الجودة -  م.ج</value>
  </data>
  <data name="chk_UseQC.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 19</value>
  </data>
  <data name="grpUOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>497, 14</value>
  </data>
  <data name="grpUOM.Size" type="System.Drawing.Size, System.Drawing">
    <value>315, 65</value>
  </data>
  <data name="grpUOM.Text" xml:space="preserve">
    <value>وحدات القياس</value>
  </data>
  <data name="chkLargeUom.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 41</value>
  </data>
  <data name="chkLargeUom.Properties.Caption" xml:space="preserve">
    <value>استخدام الوحدة الكبرى للأصناف</value>
  </data>
  <data name="chkLargeUom.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="chkMediumUom.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 16</value>
  </data>
  <data name="chkMediumUom.Properties.Caption" xml:space="preserve">
    <value>استخدام الوحدة المتوسطة للأصناف</value>
  </data>
  <data name="chkMediumUom.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="chkPiecesCount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chkPiecesCount.Location" type="System.Drawing.Point, System.Drawing">
    <value>585, 316</value>
  </data>
  <data name="chkPiecesCount.Properties.Caption" xml:space="preserve">
    <value>دعم عدد القطع للأصناف</value>
  </data>
  <data name="chkPiecesCount.Size" type="System.Drawing.Size, System.Drawing">
    <value>212, 19</value>
  </data>
  <data name="chkPiecesCount.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="chk_AutoSerialBatch.Location" type="System.Drawing.Point, System.Drawing">
    <value>511, 356</value>
  </data>
  <data name="chk_AutoSerialBatch.Properties.Caption" xml:space="preserve">
    <value>إنشاء رقم تشغيلة مسلسل تلقائي للأصناف المشتراه</value>
  </data>
  <data name="chk_AutoSerialBatch.Size" type="System.Drawing.Size, System.Drawing">
    <value>286, 19</value>
  </data>
  <data name="chk_AutoSerialBatch.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="grp_Dim.Location" type="System.Drawing.Point, System.Drawing">
    <value>497, 85</value>
  </data>
  <data name="grp_Dim.Size" type="System.Drawing.Size, System.Drawing">
    <value>315, 189</value>
  </data>
  <data name="grp_Dim.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="grp_Dim.Text" xml:space="preserve">
    <value>أبعاد الصنف</value>
  </data>
  <data name="chkMultiplyDimensions.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 94</value>
  </data>
  <data name="chkMultiplyDimensions.Properties.Items1" xml:space="preserve">
    <value>الابعاد تستخدم فقط لحساب التكعيب او المساحة</value>
  </data>
  <data name="chkMultiplyDimensions.Properties.Items3" xml:space="preserve">
    <value>الابعاد تستخدم لتمييز الاصناف عن بعضها البعض</value>
  </data>
  <data name="chkMultiplyDimensions.Properties.Items5" xml:space="preserve">
    <value>الأبعاد تستخدم لتمييز الأصناف و حساب التكعيب او المساحة</value>
  </data>
  <data name="chkMultiplyDimensions.Size" type="System.Drawing.Size, System.Drawing">
    <value>302, 89</value>
  </data>
  <data name="chkMultiplyDimensions.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="chkMultiplyDimensions.ToolTip" xml:space="preserve">
    <value>Multiply dimensions to calculate area or cube</value>
  </data>
  <data name="chkHeight.Location" type="System.Drawing.Point, System.Drawing">
    <value>210, 69</value>
  </data>
  <data name="chkHeight.Properties.Caption" xml:space="preserve">
    <value>الارتفاع</value>
  </data>
  <data name="chkHeight.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="chkWidth.Location" type="System.Drawing.Point, System.Drawing">
    <value>210, 45</value>
  </data>
  <data name="chkWidth.Properties.Caption" xml:space="preserve">
    <value>العرض</value>
  </data>
  <data name="chkWidth.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="chkLength.Location" type="System.Drawing.Point, System.Drawing">
    <value>210, 21</value>
  </data>
  <data name="chkLength.Properties.Caption" xml:space="preserve">
    <value>الطول</value>
  </data>
  <data name="chkLength.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="chkBatch.Location" type="System.Drawing.Point, System.Drawing">
    <value>548, 336</value>
  </data>
  <data name="chkBatch.Properties.Caption" xml:space="preserve">
    <value>دعم التشغيلة للأصناف</value>
  </data>
  <data name="chkBatch.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="chkExpire.Location" type="System.Drawing.Point, System.Drawing">
    <value>627, 296</value>
  </data>
  <data name="chkExpire.Properties.Caption" xml:space="preserve">
    <value>دعم تواريخ الصلاحية للأصناف</value>
  </data>
  <data name="chkExpire.Size" type="System.Drawing.Size, System.Drawing">
    <value>170, 19</value>
  </data>
  <data name="chkExpire.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
</root>