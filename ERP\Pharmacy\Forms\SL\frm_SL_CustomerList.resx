﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 16</value>
  </metadata>
  <data name="barBtn_Help.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="barBtn_Help.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barbtnImportCustomers.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAA90RVh0VGl0
        bGUAWGxzeDtTZW5kZroUlAAAAqpJREFUOE91kllPU1EUhS8tFHD8If4ZExlkKMODCiIJRqQzQ4EGY9AY
        owkJL0RkCg1DSVMQgYKJgRiJA0ZBoKWitfN4e1vA5T6n9QYebPLlDvustfbet0Khpd5KoLC3DqreWqh6
        iO4aFDC61CjoVCO/owrK9koojeVQGMqIcuTp6aorszIDpDNHZFCPlJQhsRrJVEYWG+YnEIlJCMdSUBgr
        EAiLJC7F72ASedpSCCpLHSTpmCeLqSOozLWIJzPI76zmyZG4BKWpksTXodCXwxciA20JvIEEBM01CCy5
        Y8nKxabXk8jvqkIsmZbb1jnGEYqKUBoqeHKbbQReSm+dHSaDEuogl1xgViPBhJQcS1Aqm9lUwZO19jH4
        WbKujItZ8k8/ddBGHbCFiWJ25jhPrkY0N7PWPopgOMXFbXMjfOZfvPUSHPriWYMrvXou/jdzNjk3M22b
        Ns2TBbYwNjNrmwmzWIXVDRe+7fmwTZyc/KEvcgIpfSzDntn7zc9urK1vY3VjH4IgFAIQGEL30xVqV4Jj
        5Sv6BxaRFNN8lEBQRDxB97SPVvMLDLx0YscdhrH/FTMokg06nywjGElh1xPBpP0j7pkn4PoRpBqw6/Kh
        pukZng858eW7H7sHUWgfLDCDYtmg/dESAhEJb955MO/cgcsTQlf/FK7WPUaLaQgbm/uYmd/CGtW33RHc
        tziYwTnZQP9wEVs7fozbPiCayPBu9g6CeP/JQ7ipmxB/z+pvNw9x12xnBudlA03fAgbH1hEioT+UI3Dq
        PgerD46uo7l9jhlclA1aexzw0Z/DS0vzBpL0nc9eZajOzjXqZ84aqJuHl++Y5tBksqHJaMNtotEwSwdn
        0UCHG3TTuKWdxk3NNG5oplDbMuI8s0T6FREXiEv/4fIp2HMxocgaQPgLGkqm26hPndkAAAAASUVORK5C
        YII=
</value>
  </data>
  <data name="barbtnImportCustomers.LargeGlyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAA90RVh0VGl0
        bGUAWGxzeDtTZW5kZroUlAAAB/RJREFUWEfFVmlTVFcahmbViRKzVWXm8/yN+TJ/YKqSGDPKvrglE0R2
        QdmaqCCCS9C4JSJREUFRo1FAcUSNWhqtxEoERGTrpruhoaEXGnjmec+9l9As8eOcqodzD/f2eZ53O+cN
        AjALfQTrMOkI+ROELgL5v/x+yRHAGbDQhqnq2/bWqpN3se+7u6gSnGjHvhPaXElo8x1UHr+DvcfuoIJz
        xbH/KhTs+aGIe4gQER9cVNUcVFR5PaiQMEYAZ8BCUx4ipG8aPp8f424fnKNuWO1jeNU7jIfP+tDS3oHs
        rxqLuU8YoUQ8f2Ex9ldDng0ELjQBoZXftXMJ+Kdn4PdPc57GpH9Gwce1wO3xwzXhw/CoB4M2Fzp7HCg7
        0oaZGaCp+TlSC86VcK9wQonQ91dDng0ELgwBdKmMSRIJDFLfJAVMTsOrw+3xYWzcB5tjAl09NpQcbFYC
        XG4/Gq//gg05p8zcL4IIyAuDTxC40D4Ku/e4h0u6mYSTJNLINVI1+6bhYQi8ehjsTjc6euzIL/9BCXDz
        vYiov/IUCVuOl3LPSGJWhMEnCIr4Kr4lsjQBEeY4RJTEIrw4RkNRLMIK1iGUCNu+FiFEaN6/EZL3GUzb
        PoUpdw1MOasRTPyjegc6um1IN19QAiRUHiViCmeaHmPtpuoAEYECSuOxqjYfUTW5iDqZi5UnsrHyeBbe
        OpqB5d9sxfLDWxBZnYrIg/9BxP7NCK/aiNC96xFakQxTeRJCypIRnLUaL7rt+GL7Oe4JJWJqmkKmRMgU
        vm98gNVJVSLCyIk5AswUUJNH1/qx7OBmeLyTiNy/CROeSURUbUDE3mTktTXCOebFyJiHhAmwj7hh2hkH
        K2NvMscqAd0DI8gru4SUrFokZZxCUvopJG4V1DAUTxieqxLj5YR4IVCAWO71TiHywCaV3ZH7NjLDJxFO
        8nBa6nR5EVaeoshDdsdjaNiNkNI4WOzjCC6JRnDmx7AOT6Dz9TB+/m0AD5714N7jV7j94CVa73Xg9v1O
        Q8CKBQIk3uL2gnuXFPn2OxeV5WMsMSEP25OE3JsNLDeS7k5QlmfeOAcLrU+/dpYCYhCU+RG944WNnhGv
        DAy58LrfiS4K+v2ljRViR+6uKyJgJSGH1BwBRRTAmBuWj5M4opICxmn1nkSElNNqWp7dUg8bLTeVxijy
        YHM0Bmz0QHE0gjI+Ul4aZUmOcB4WMawMi2Mc/dYxHlQuZO+6JAKiFggIK4xWCed2azGXwyW8Igmjesxz
        WhvgGPEocrHcVBqLQeX6GPTT0qDidboAnorEiAH+fogiBilWvJdpVgLeXkRAjMp2IQ+vXM+4pyjLw8pp
        vR5z0644ZbnJHKMsDypZp1kuKFqrBIyM+eAgsUPmUS/sOqwMi3gzvfiCCFi1QEBoQfTV0B2sd6nzfKnz
        NQjZxlrX69yU8ymCsz9hpn+ssl3iLYRz8c9DRcpiB2EQ23lE25waXPTu1uIGEfDOAgGzD9qQDF1xk5k7
        w2KWmDqJ0Vn3SlgEfzzPtVZgM6CTDzF8blbYloLzQvIuIZfUnwpYKSUjAoR8Nq66hQaETCP3aMRCNofU
        gJWY8Prx5Y56IXmPEAGzp+F8AeKeqByWzLQIUDENJJ5rsWapJBshhDq5lbMQWx1SCR6Ms8K+yK8TkveJ
        Nwp4O3vnZUzzKpaEsou1glli3Uo+W1gZFh5AAyw3BZabnAEDQyw/XtF9LME+q4unqAdN158JyYfEMp1H
        iVhMwKrM0ouaACEkuRZXEhvktFiIG378BcfqHuHoWcFDHCG+OaPh8Gni+wc4VPsTqmvvo/rUfRxgo7Of
        2HOkrU24lhLwTkZJoxLwR0IJdBeLewmxcNvua7wz/Opbo3eQ61s1McZah6zlO9eEFxnmy0IYvpgAic+7
        aYUNvM1mAgilniWuFokt0WsZhfnALdQ2PeVNaMMUrz85TSfYHygw8WSWA87t9an3P//6GhmF9dhe0SyE
        kUsJeC91x/lZASqZdAi5RSWWGz2DThTvv6Wqo46Nx5WWX5WVcok5mTsGZC0eOHHmNsyVl/GybxS55TeE
        cNl8AaobIt7/Mr9eCVCkzGIrIaQDBuxudf0WVN2EgyK7ep24evM3lFXfUFbLcW7ndy6WsUtOwYKTOHTy
        Fi+mYV5QTmTt/FEIly8p4PO8OvjpMiEXCKGW5Rr6ue7qt9OVrWzHvGzHnLjzqI+Nx2Pk7WzAqz4HtwS6
        Xg0hJe0wKo+0oOVut/ruRc8otpaoq/kvilf+qAdNgHQsH2zK0QQoYh1C2s/bT9BHdPbakFvWzEvKg/Yn
        /Th76SlPzknmwxAKyhvxr7i9SM37Fu0PO1iWY+r93Sd9+L3bidRClYRvLSZA5cCG7NNsp5hUkkiEHCQa
        JtW5PkZYbeLK63jeacPR0z9hmKEQMYLObgcePe1ReMmeQP4n7+W7uxS7Oa9JCFcsJkCdhAmpx9tSMmuR
        nEGks8UiEtNrVIuVwBYrIa0GZy/cR3rJNZRXt2KIV65KTvsEr+rAeRZ8L9/t/rqZ7fpFIVw5X4AMaRil
        j5fG4QNCTq6/6vjbPPw9Pu0cetn5DFoZHgvDY5nQIc8LMWibUN+v2Vi7pADxgogQT0g+iJilEJWYUde2
        IecSLWrCelqVkn0ByVmNSCISMzgTiekNiN/agLg0zmnnEUt89nnNbf5+QRXMHSLkTZCbU7pc6fOk05kP
        aT4Wg7yT35kCBPx/gKD/Aaq7NW2UvyRDAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="barBtnPrint.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="barBtnPrint.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnRefresh.Caption" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="barBtnRefresh.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnOpen.Caption" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="barBtnOpen.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1093, 28</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 595</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1093, 0</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 567</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1093, 28</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 567</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>25</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1093, 595</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="chk_IsActive.EditValue" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_IsActive.Location" type="System.Drawing.Point, System.Drawing">
    <value>162, 106</value>
  </data>
  <data name="chk_IsActive.Properties.Caption" xml:space="preserve">
    <value>Active Customer</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="chk_IsActive.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_IsActive.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 19</value>
  </data>
  <data name="chk_IsActive.TabIndex" type="System.Int32, mscorlib">
    <value>219</value>
  </data>
  <data name="&gt;&gt;chk_IsActive.Name" xml:space="preserve">
    <value>chk_IsActive</value>
  </data>
  <data name="&gt;&gt;chk_IsActive.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsActive.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;chk_IsActive.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="chk_IsBlocked.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="chk_IsBlocked.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 131</value>
  </data>
  <data name="chk_IsBlocked.Properties.Caption" xml:space="preserve">
    <value>Block Customer</value>
  </data>
  <data name="chk_IsBlocked.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_IsBlocked.Size" type="System.Drawing.Size, System.Drawing">
    <value>95, 19</value>
  </data>
  <data name="chk_IsBlocked.TabIndex" type="System.Int32, mscorlib">
    <value>218</value>
  </data>
  <data name="&gt;&gt;chk_IsBlocked.Name" xml:space="preserve">
    <value>chk_IsBlocked</value>
  </data>
  <data name="&gt;&gt;chk_IsBlocked.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsBlocked.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;chk_IsBlocked.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lkp_SalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_SalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 55</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkp_SalesEmp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns1" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns8" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns14" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns15" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_SalesEmp.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>256, 20</value>
  </data>
  <data name="lkp_SalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Name" xml:space="preserve">
    <value>lkp_SalesEmp</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl25.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>270, 58</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 13</value>
  </data>
  <data name="labelControl25.TabIndex" type="System.Int32, mscorlib">
    <value>217</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>Sales Empolyee</value>
  </data>
  <data name="&gt;&gt;labelControl25.Name" xml:space="preserve">
    <value>labelControl25</value>
  </data>
  <data name="&gt;&gt;labelControl25.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl25.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;labelControl25.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>270, 84</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>216</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>Price Level</value>
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lkpPriceLevel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpPriceLevel.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 80</value>
  </data>
  <data name="lkpPriceLevel.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns1" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns7" xml:space="preserve">
    <value>IsRatio</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns8" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns14" xml:space="preserve">
    <value>PLName</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns15" xml:space="preserve">
    <value>Price List Name</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns21" xml:space="preserve">
    <value>PriceLevelId</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns22" xml:space="preserve">
    <value>PriceLevelId</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns23" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns24" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns25" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Columns26" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns27" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpPriceLevel.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpPriceLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>256, 20</value>
  </data>
  <data name="lkpPriceLevel.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lkpPriceLevel.Name" xml:space="preserve">
    <value>lkpPriceLevel</value>
  </data>
  <data name="&gt;&gt;lkpPriceLevel.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpPriceLevel.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;lkpPriceLevel.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelControl24.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 34</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="labelControl24.TabIndex" type="System.Int32, mscorlib">
    <value>213</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="&gt;&gt;labelControl24.Name" xml:space="preserve">
    <value>labelControl24</value>
  </data>
  <data name="&gt;&gt;labelControl24.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl24.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;labelControl24.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lkp_Category.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_Category.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 30</value>
  </data>
  <data name="lkp_Category.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Category.Properties.Columns" xml:space="preserve">
    <value>CGNameAr</value>
  </data>
  <data name="lkp_Category.Properties.Columns1" xml:space="preserve">
    <value>فئة العميل</value>
  </data>
  <data name="lkp_Category.Properties.Columns2" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_Category.Properties.Columns3" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_Category.Properties.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Category.Properties.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Category.Properties.Columns6" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Category.Properties.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Category.Properties.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_Category.Properties.Columns9" xml:space="preserve">
    <value>CustomerGroupCode</value>
  </data>
  <data name="lkp_Category.Properties.Columns10" xml:space="preserve">
    <value>CustomerGroupCode</value>
  </data>
  <data name="lkp_Category.Properties.Columns11" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Category.Properties.Columns12" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Category.Properties.Columns13" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Category.Properties.Columns14" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Category.Properties.Columns15" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_Category.Properties.Columns16" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="lkp_Category.Properties.Columns17" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="lkp_Category.Properties.Columns18" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Category.Properties.Columns19" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Category.Properties.Columns20" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Category.Properties.Columns21" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Category.Properties.Columns22" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_Category.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Category.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkp_Category.Size" type="System.Drawing.Size, System.Drawing">
    <value>256, 20</value>
  </data>
  <data name="lkp_Category.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkp_Category.Name" xml:space="preserve">
    <value>lkp_Category</value>
  </data>
  <data name="&gt;&gt;lkp_Category.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Category.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;lkp_Category.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="btnClosePopup.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnClosePopup.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 5</value>
  </data>
  <data name="btnClosePopup.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="btnClosePopup.TabIndex" type="System.Int32, mscorlib">
    <value>211</value>
  </data>
  <data name="btnClosePopup.Text" xml:space="preserve">
    <value>X</value>
  </data>
  <data name="btnClosePopup.ToolTip" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="&gt;&gt;btnClosePopup.Name" xml:space="preserve">
    <value>btnClosePopup</value>
  </data>
  <data name="&gt;&gt;btnClosePopup.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnClosePopup.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;btnClosePopup.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="btn_Continue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btn_Continue.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 143</value>
  </data>
  <data name="btn_Continue.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="btn_Continue.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btn_Continue.Text" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="&gt;&gt;btn_Continue.Name" xml:space="preserve">
    <value>btn_Continue</value>
  </data>
  <data name="&gt;&gt;btn_Continue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btn_Continue.Parent" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;btn_Continue.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="panel_data.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="panel_data.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="panel_data.Size" type="System.Drawing.Size, System.Drawing">
    <value>347, 171</value>
  </data>
  <data name="panel_data.TabIndex" type="System.Int32, mscorlib">
    <value>209</value>
  </data>
  <data name="&gt;&gt;panel_data.Name" xml:space="preserve">
    <value>panel_data</value>
  </data>
  <data name="&gt;&gt;panel_data.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panel_data.Parent" xml:space="preserve">
    <value>popupData</value>
  </data>
  <data name="&gt;&gt;panel_data.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="popupData.Location" type="System.Drawing.Point, System.Drawing">
    <value>353, 213</value>
  </data>
  <data name="popupData.Size" type="System.Drawing.Size, System.Drawing">
    <value>347, 171</value>
  </data>
  <data name="popupData.TabIndex" type="System.Int32, mscorlib">
    <value>213</value>
  </data>
  <data name="popupData.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;popupData.Name" xml:space="preserve">
    <value>popupData</value>
  </data>
  <data name="&gt;&gt;popupData.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.PopupControlContainer, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;popupData.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;popupData.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="grd_Customer.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>615, 17</value>
  </metadata>
  <data name="mi_UpdateCust.Size" type="System.Drawing.Size, System.Drawing">
    <value>174, 22</value>
  </data>
  <data name="mi_UpdateCust.Text" xml:space="preserve">
    <value>Update Customers Data</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>175, 26</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Name" xml:space="preserve">
    <value>contextMenuStrip1</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="grd_Customer.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 43</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="col_PriceLevel.Caption" xml:space="preserve">
    <value>Price List</value>
  </data>
  <data name="col_PriceLevel.Width" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="colDisc.Caption" xml:space="preserve">
    <value>Disc Ratio</value>
  </data>
  <data name="col_MaxCredit.Caption" xml:space="preserve">
    <value>Max Credit</value>
  </data>
  <data name="colCity.Caption" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="colCity.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colCity.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colCity.Width" type="System.Int32, mscorlib">
    <value>92</value>
  </data>
  <data name="colAddress.Caption" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="colAddress.Width" type="System.Int32, mscorlib">
    <value>97</value>
  </data>
  <data name="col_Mobile.Caption" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="col_Mobile.Width" type="System.Int32, mscorlib">
    <value>98</value>
  </data>
  <data name="colTel.Caption" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="colTel.Width" type="System.Int32, mscorlib">
    <value>83</value>
  </data>
  <data name="col_CusNameEn.Caption" xml:space="preserve">
    <value>Customer F Name</value>
  </data>
  <data name="col_CusNameEn.Width" type="System.Int32, mscorlib">
    <value>135</value>
  </data>
  <data name="col_CategoryId.Caption" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="col_CategoryId.Width" type="System.Int32, mscorlib">
    <value>109</value>
  </data>
  <data name="col_Representative.Caption" xml:space="preserve">
    <value>Representative</value>
  </data>
  <data name="col_CusNameAr.Caption" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="col_CusNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CusNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="col_CusNameAr.Width" type="System.Int32, mscorlib">
    <value>178</value>
  </data>
  <data name="col_CusCode.Caption" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="col_CusCode.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CusCode.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="col_CusCode.Width" type="System.Int32, mscorlib">
    <value>91</value>
  </data>
  <data name="col_CustomerId.Caption" xml:space="preserve">
    <value>CustomerId</value>
  </data>
  <data name="colAccountId.Caption" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="col_SalesEmpId.Caption" xml:space="preserve">
    <value>Sales Empolyee</value>
  </data>
  <data name="rep_salesEmp.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_salesEmp.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_salesEmp.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="rep_salesEmp.Columns1" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="rep_salesEmp.Columns2" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="rep_salesEmp.Columns3" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="rep_salesEmp.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="rep_salesEmp.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="rep_salesEmp.Columns6" xml:space="preserve">
    <value />
  </data>
  <data name="rep_salesEmp.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_salesEmp.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="rep_salesEmp.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="col_SalesEmpId.Width" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="col_IdNumber.Caption" xml:space="preserve">
    <value>ID Number</value>
  </data>
  <data name="col_CustomerGroup.Caption" xml:space="preserve">
    <value>Customer Group</value>
  </data>
  <data name="col_Rep_Mobile.Caption" xml:space="preserve">
    <value>Representative Mobile</value>
  </data>
  <data name="col_Rep_ID.Caption" xml:space="preserve">
    <value>Representative ID</value>
  </data>
  <data name="col_TaxCardNumber.Caption" xml:space="preserve">
    <value>Tax Card Number</value>
  </data>
  <data name="col_TaxCardNumber.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_TaxCardNumber.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Street.Caption" xml:space="preserve">
    <value>Street</value>
  </data>
  <data name="col_Street.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Street.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_Governate.Caption" xml:space="preserve">
    <value>Governate</value>
  </data>
  <data name="col_Governate.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Governate.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="col_csType.Caption" xml:space="preserve">
    <value>csType</value>
  </data>
  <data name="col_csType.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_csType.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="col_BuildingNumber.Caption" xml:space="preserve">
    <value>BuildingNumber</value>
  </data>
  <data name="col_BuildingNumber.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_BuildingNumber.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridView1.GroupPanelText" xml:space="preserve">
    <value>Drag any column here to group by that column</value>
  </data>
  <data name="grd_Customer.Size" type="System.Drawing.Size, System.Drawing">
    <value>1081, 547</value>
  </data>
  <data name="grd_Customer.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="&gt;&gt;grd_Customer.Name" xml:space="preserve">
    <value>grd_Customer</value>
  </data>
  <data name="&gt;&gt;grd_Customer.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grd_Customer.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;grd_Customer.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Customers List</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_Help.Name" xml:space="preserve">
    <value>barBtn_Help</value>
  </data>
  <data name="&gt;&gt;barBtn_Help.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtnImportCustomers.Name" xml:space="preserve">
    <value>barbtnImportCustomers</value>
  </data>
  <data name="&gt;&gt;barbtnImportCustomers.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnPrint.Name" xml:space="preserve">
    <value>barBtnPrint</value>
  </data>
  <data name="&gt;&gt;barBtnPrint.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnRefresh.Name" xml:space="preserve">
    <value>barBtnRefresh</value>
  </data>
  <data name="&gt;&gt;barBtnRefresh.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnOpen.Name" xml:space="preserve">
    <value>barBtnOpen</value>
  </data>
  <data name="&gt;&gt;barBtnOpen.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;mi_UpdateCust.Name" xml:space="preserve">
    <value>mi_UpdateCust</value>
  </data>
  <data name="&gt;&gt;mi_UpdateCust.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_PriceLevel.Name" xml:space="preserve">
    <value>col_PriceLevel</value>
  </data>
  <data name="&gt;&gt;col_PriceLevel.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colDisc.Name" xml:space="preserve">
    <value>colDisc</value>
  </data>
  <data name="&gt;&gt;colDisc.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_MaxCredit.Name" xml:space="preserve">
    <value>col_MaxCredit</value>
  </data>
  <data name="&gt;&gt;col_MaxCredit.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colCity.Name" xml:space="preserve">
    <value>colCity</value>
  </data>
  <data name="&gt;&gt;colCity.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colAddress.Name" xml:space="preserve">
    <value>colAddress</value>
  </data>
  <data name="&gt;&gt;colAddress.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Mobile.Name" xml:space="preserve">
    <value>col_Mobile</value>
  </data>
  <data name="&gt;&gt;col_Mobile.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colTel.Name" xml:space="preserve">
    <value>colTel</value>
  </data>
  <data name="&gt;&gt;colTel.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CusNameEn.Name" xml:space="preserve">
    <value>col_CusNameEn</value>
  </data>
  <data name="&gt;&gt;col_CusNameEn.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CategoryId.Name" xml:space="preserve">
    <value>col_CategoryId</value>
  </data>
  <data name="&gt;&gt;col_CategoryId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Representative.Name" xml:space="preserve">
    <value>col_Representative</value>
  </data>
  <data name="&gt;&gt;col_Representative.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CusNameAr.Name" xml:space="preserve">
    <value>col_CusNameAr</value>
  </data>
  <data name="&gt;&gt;col_CusNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CusCode.Name" xml:space="preserve">
    <value>col_CusCode</value>
  </data>
  <data name="&gt;&gt;col_CusCode.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CustomerId.Name" xml:space="preserve">
    <value>col_CustomerId</value>
  </data>
  <data name="&gt;&gt;col_CustomerId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colAccountId.Name" xml:space="preserve">
    <value>colAccountId</value>
  </data>
  <data name="&gt;&gt;colAccountId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SalesEmpId.Name" xml:space="preserve">
    <value>col_SalesEmpId</value>
  </data>
  <data name="&gt;&gt;col_SalesEmpId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_salesEmp.Name" xml:space="preserve">
    <value>rep_salesEmp</value>
  </data>
  <data name="&gt;&gt;rep_salesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_IdNumber.Name" xml:space="preserve">
    <value>col_IdNumber</value>
  </data>
  <data name="&gt;&gt;col_IdNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CustomerGroup.Name" xml:space="preserve">
    <value>col_CustomerGroup</value>
  </data>
  <data name="&gt;&gt;col_CustomerGroup.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Rep_Mobile.Name" xml:space="preserve">
    <value>col_Rep_Mobile</value>
  </data>
  <data name="&gt;&gt;col_Rep_Mobile.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Rep_ID.Name" xml:space="preserve">
    <value>col_Rep_ID</value>
  </data>
  <data name="&gt;&gt;col_Rep_ID.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Region.Name" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="&gt;&gt;Region.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TaxCardNumber.Name" xml:space="preserve">
    <value>col_TaxCardNumber</value>
  </data>
  <data name="&gt;&gt;col_TaxCardNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Country.Name" xml:space="preserve">
    <value>col_Country</value>
  </data>
  <data name="&gt;&gt;col_Country.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Street.Name" xml:space="preserve">
    <value>col_Street</value>
  </data>
  <data name="&gt;&gt;col_Street.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Governate.Name" xml:space="preserve">
    <value>col_Governate</value>
  </data>
  <data name="&gt;&gt;col_Governate.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_csType.Name" xml:space="preserve">
    <value>col_csType</value>
  </data>
  <data name="&gt;&gt;col_csType.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_BuildingNumber.Name" xml:space="preserve">
    <value>col_BuildingNumber</value>
  </data>
  <data name="&gt;&gt;col_BuildingNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_SL_CustomerList</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
</root>