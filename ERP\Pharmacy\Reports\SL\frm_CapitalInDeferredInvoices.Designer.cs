﻿namespace Reports.SL
{
    partial class frm_CapitalInDeferredInvoices
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_CapitalInDeferredInvoices));
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Index = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Customer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalInvoice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PaidInAdvance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Paid = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalCapital = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PercentageOfTheRest = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SL_InvoiceId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grd_data = new DevExpress.XtraGrid.GridControl();
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_PreviewData = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barBtn_Preview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_data)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            this.SuspendLayout();
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.AccessibleDescription = resources.GetString("lblFilter.Properties.AccessibleDescription");
            this.lblFilter.Properties.AccessibleName = resources.GetString("lblFilter.Properties.AccessibleName");
            this.lblFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblFilter.Properties.Appearance.FontSizeDelta")));
            this.lblFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblFilter.Properties.Appearance.FontStyleDelta")));
            this.lblFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblFilter.Properties.Appearance.GradientMode")));
            this.lblFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblFilter.Properties.Appearance.Image")));
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblFilter.Properties.AutoHeight")));
            this.lblFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblFilter.Properties.Mask.AutoComplete")));
            this.lblFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblFilter.Properties.Mask.BeepOnError")));
            this.lblFilter.Properties.Mask.EditMask = resources.GetString("lblFilter.Properties.Mask.EditMask");
            this.lblFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblFilter.Properties.Mask.MaskType")));
            this.lblFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblFilter.Properties.Mask.PlaceHolder")));
            this.lblFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblFilter.Properties.Mask.SaveLiteral")));
            this.lblFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblFilter.Properties.NullValuePrompt = resources.GetString("lblFilter.Properties.NullValuePrompt");
            this.lblFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.AccessibleDescription = resources.GetString("lblDateFilter.Properties.AccessibleDescription");
            this.lblDateFilter.Properties.AccessibleName = resources.GetString("lblDateFilter.Properties.AccessibleName");
            this.lblDateFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblDateFilter.Properties.Appearance.FontSizeDelta")));
            this.lblDateFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblDateFilter.Properties.Appearance.FontStyleDelta")));
            this.lblDateFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblDateFilter.Properties.Appearance.GradientMode")));
            this.lblDateFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblDateFilter.Properties.Appearance.Image")));
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblDateFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblDateFilter.Properties.AutoHeight")));
            this.lblDateFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblDateFilter.Properties.Mask.AutoComplete")));
            this.lblDateFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.BeepOnError")));
            this.lblDateFilter.Properties.Mask.EditMask = resources.GetString("lblDateFilter.Properties.Mask.EditMask");
            this.lblDateFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblDateFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblDateFilter.Properties.Mask.MaskType")));
            this.lblDateFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblDateFilter.Properties.Mask.PlaceHolder")));
            this.lblDateFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.SaveLiteral")));
            this.lblDateFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblDateFilter.Properties.NullValuePrompt = resources.GetString("lblDateFilter.Properties.NullValuePrompt");
            this.lblDateFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblDateFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.AccessibleDescription = resources.GetString("lblReportName.Properties.AccessibleDescription");
            this.lblReportName.Properties.AccessibleName = resources.GetString("lblReportName.Properties.AccessibleName");
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblReportName.Properties.Appearance.FontSizeDelta")));
            this.lblReportName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblReportName.Properties.Appearance.FontStyleDelta")));
            this.lblReportName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblReportName.Properties.Appearance.GradientMode")));
            this.lblReportName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblReportName.Properties.Appearance.Image")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblReportName.Properties.AutoHeight = ((bool)(resources.GetObject("lblReportName.Properties.AutoHeight")));
            this.lblReportName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblReportName.Properties.Mask.AutoComplete")));
            this.lblReportName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblReportName.Properties.Mask.BeepOnError")));
            this.lblReportName.Properties.Mask.EditMask = resources.GetString("lblReportName.Properties.Mask.EditMask");
            this.lblReportName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblReportName.Properties.Mask.IgnoreMaskBlank")));
            this.lblReportName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblReportName.Properties.Mask.MaskType")));
            this.lblReportName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblReportName.Properties.Mask.PlaceHolder")));
            this.lblReportName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblReportName.Properties.Mask.SaveLiteral")));
            this.lblReportName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblReportName.Properties.Mask.ShowPlaceHolders")));
            this.lblReportName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblReportName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblReportName.Properties.NullValuePrompt = resources.GetString("lblReportName.Properties.NullValuePrompt");
            this.lblReportName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblReportName.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.AccessibleDescription = resources.GetString("picLogo.Properties.AccessibleDescription");
            this.picLogo.Properties.AccessibleName = resources.GetString("picLogo.Properties.AccessibleName");
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // gridView1
            // 
            this.gridView1.Appearance.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupFooter.FontSizeDelta")));
            this.gridView1.Appearance.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupFooter.FontStyleDelta")));
            this.gridView1.Appearance.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupFooter.GradientMode")));
            this.gridView1.Appearance.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupFooter.Image")));
            this.gridView1.Appearance.GroupFooter.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.GroupFooter.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.Appearance.HeaderPanel.Font")));
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseFont = true;
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.gridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Image")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.FooterPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.gridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Image")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.ColumnPanelRowHeight = 50;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Index,
            this.col_InvoiceCode,
            this.col_InvoiceDate,
            this.col_Customer,
            this.col_TotalInvoice,
            this.col_PaidInAdvance,
            this.col_Paid,
            this.col_TotalCapital,
            this.col_PercentageOfTheRest,
            this.col_SL_InvoiceId});
            this.gridView1.GridControl = this.grd_data;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            // 
            // col_Index
            // 
            resources.ApplyResources(this.col_Index, "col_Index");
            this.col_Index.FieldName = "Index";
            this.col_Index.Name = "col_Index";
            this.col_Index.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // col_InvoiceCode
            // 
            resources.ApplyResources(this.col_InvoiceCode, "col_InvoiceCode");
            this.col_InvoiceCode.FieldName = "InvoiceCode";
            this.col_InvoiceCode.Name = "col_InvoiceCode";
            // 
            // col_InvoiceDate
            // 
            resources.ApplyResources(this.col_InvoiceDate, "col_InvoiceDate");
            this.col_InvoiceDate.FieldName = "InvoiceDate";
            this.col_InvoiceDate.Name = "col_InvoiceDate";
            // 
            // col_Customer
            // 
            resources.ApplyResources(this.col_Customer, "col_Customer");
            this.col_Customer.FieldName = "Customer";
            this.col_Customer.Name = "col_Customer";
            // 
            // col_TotalInvoice
            // 
            resources.ApplyResources(this.col_TotalInvoice, "col_TotalInvoice");
            this.col_TotalInvoice.DisplayFormat.FormatString = "n2";
            this.col_TotalInvoice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalInvoice.FieldName = "TotalInvoice";
            this.col_TotalInvoice.Name = "col_TotalInvoice";
            // 
            // col_PaidInAdvance
            // 
            resources.ApplyResources(this.col_PaidInAdvance, "col_PaidInAdvance");
            this.col_PaidInAdvance.DisplayFormat.FormatString = "n2";
            this.col_PaidInAdvance.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_PaidInAdvance.FieldName = "PaidInAdvance";
            this.col_PaidInAdvance.Name = "col_PaidInAdvance";
            // 
            // col_Paid
            // 
            resources.ApplyResources(this.col_Paid, "col_Paid");
            this.col_Paid.DisplayFormat.FormatString = "n2";
            this.col_Paid.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Paid.FieldName = "Paid";
            this.col_Paid.Name = "col_Paid";
            // 
            // col_TotalCapital
            // 
            resources.ApplyResources(this.col_TotalCapital, "col_TotalCapital");
            this.col_TotalCapital.DisplayFormat.FormatString = "n2";
            this.col_TotalCapital.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalCapital.FieldName = "TotalCapital";
            this.col_TotalCapital.Name = "col_TotalCapital";
            // 
            // col_PercentageOfTheRest
            // 
            resources.ApplyResources(this.col_PercentageOfTheRest, "col_PercentageOfTheRest");
            this.col_PercentageOfTheRest.DisplayFormat.FormatString = "n2";
            this.col_PercentageOfTheRest.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_PercentageOfTheRest.FieldName = "PercentageOfTheRest";
            this.col_PercentageOfTheRest.Name = "col_PercentageOfTheRest";
            this.col_PercentageOfTheRest.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_PercentageOfTheRest.Summary"))), resources.GetString("col_PercentageOfTheRest.Summary1"), resources.GetString("col_PercentageOfTheRest.Summary2"))});
            // 
            // col_SL_InvoiceId
            // 
            resources.ApplyResources(this.col_SL_InvoiceId, "col_SL_InvoiceId");
            this.col_SL_InvoiceId.FieldName = "SL_InvoiceId";
            this.col_SL_InvoiceId.Name = "col_SL_InvoiceId";
            // 
            // grd_data
            // 
            resources.ApplyResources(this.grd_data, "grd_data");
            this.grd_data.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_data.EmbeddedNavigator.AccessibleDescription");
            this.grd_data.EmbeddedNavigator.AccessibleName = resources.GetString("grd_data.EmbeddedNavigator.AccessibleName");
            this.grd_data.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_data.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_data.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_data.EmbeddedNavigator.Anchor")));
            this.grd_data.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_data.EmbeddedNavigator.BackgroundImage")));
            this.grd_data.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_data.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_data.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_data.EmbeddedNavigator.ImeMode")));
            this.grd_data.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_data.EmbeddedNavigator.MaximumSize")));
            this.grd_data.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_data.EmbeddedNavigator.TextLocation")));
            this.grd_data.EmbeddedNavigator.ToolTip = resources.GetString("grd_data.EmbeddedNavigator.ToolTip");
            this.grd_data.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_data.EmbeddedNavigator.ToolTipIconType")));
            this.grd_data.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_data.EmbeddedNavigator.ToolTipTitle");
            this.grd_data.MainView = this.gridView1;
            this.grd_data.Name = "grd_data";
            this.grd_data.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowMoveBarOnToolbar = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtn_Preview,
            this.barBtn_Help,
            this.barBtnClose,
            this.barBtn_PreviewData,
            this.barButtonItem1,
            this.barButtonItem2,
            this.barButtonItem3,
            this.barButtonItem4,
            this.barButtonItem5});
            this.barManager1.MaxItemId = 38;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("bar1.BarAppearance.Normal.FontSizeDelta")));
            this.bar1.BarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bar1.BarAppearance.Normal.FontStyleDelta")));
            this.bar1.BarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bar1.BarAppearance.Normal.GradientMode")));
            this.bar1.BarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("bar1.BarAppearance.Normal.Image")));
            this.bar1.BarAppearance.Normal.Options.UseTextOptions = true;
            this.bar1.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bar1.BarItemHorzIndent = 9;
            this.bar1.BarItemVertIndent = 0;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(61, 172);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtn_PreviewData, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonItem5, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.DrawSizeGrip = true;
            this.bar1.OptionsBar.MultiLine = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_PreviewData
            // 
            resources.ApplyResources(this.barBtn_PreviewData, "barBtn_PreviewData");
            this.barBtn_PreviewData.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_PreviewData.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtn_PreviewData.Id = 31;
            this.barBtn_PreviewData.Name = "barBtn_PreviewData";
            this.barBtn_PreviewData.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_PreviewData_ItemClick);
            // 
            // barButtonItem5
            // 
            resources.ApplyResources(this.barButtonItem5, "barButtonItem5");
            this.barButtonItem5.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barButtonItem5.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barButtonItem5.Id = 37;
            this.barButtonItem5.Name = "barButtonItem5";
            this.barButtonItem5.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem5_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 28;
            this.barBtnClose.ItemAppearance.Normal.Font = ((System.Drawing.Font)(resources.GetObject("barBtnClose.ItemAppearance.Normal.Font")));
            this.barBtnClose.ItemAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barBtnClose.ItemAppearance.Normal.FontSizeDelta")));
            this.barBtnClose.ItemAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barBtnClose.ItemAppearance.Normal.FontStyleDelta")));
            this.barBtnClose.ItemAppearance.Normal.Options.UseFont = true;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // barBtn_Preview
            // 
            resources.ApplyResources(this.barBtn_Preview, "barBtn_Preview");
            this.barBtn_Preview.ActAsDropDown = true;
            this.barBtn_Preview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Preview.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barBtn_Preview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtn_Preview.Id = 1;
            this.barBtn_Preview.ItemAppearance.Normal.Font = ((System.Drawing.Font)(resources.GetObject("barBtn_Preview.ItemAppearance.Normal.Font")));
            this.barBtn_Preview.ItemAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barBtn_Preview.ItemAppearance.Normal.FontSizeDelta")));
            this.barBtn_Preview.ItemAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barBtn_Preview.ItemAppearance.Normal.FontStyleDelta")));
            this.barBtn_Preview.ItemAppearance.Normal.Options.UseFont = true;
            this.barBtn_Preview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtn_Preview.Name = "barBtn_Preview";
            this.barBtn_Preview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barBtn_Help
            // 
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barButtonItem1
            // 
            resources.ApplyResources(this.barButtonItem1, "barButtonItem1");
            this.barButtonItem1.Id = 33;
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // barButtonItem2
            // 
            resources.ApplyResources(this.barButtonItem2, "barButtonItem2");
            this.barButtonItem2.Id = 34;
            this.barButtonItem2.Name = "barButtonItem2";
            // 
            // barButtonItem3
            // 
            resources.ApplyResources(this.barButtonItem3, "barButtonItem3");
            this.barButtonItem3.Id = 35;
            this.barButtonItem3.Name = "barButtonItem3";
            // 
            // barButtonItem4
            // 
            resources.ApplyResources(this.barButtonItem4, "barButtonItem4");
            this.barButtonItem4.Id = 36;
            this.barButtonItem4.Name = "barButtonItem4";
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // frm_CapitalInDeferredInvoices
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grd_data);
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frm_CapitalInDeferredInvoices";
            this.Load += new System.EventHandler(this.frm_CapitalInDeferredInvoices_Load);
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_data)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn col_Index;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_Customer;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalInvoice;
        private DevExpress.XtraGrid.Columns.GridColumn col_PaidInAdvance;
        private DevExpress.XtraGrid.Columns.GridColumn col_Paid;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalCapital;
        private DevExpress.XtraGrid.GridControl grd_data;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtn_PreviewData;
        private DevExpress.XtraBars.BarButtonItem barBtn_Preview;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem3;
        private DevExpress.XtraBars.BarButtonItem barButtonItem4;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem5;
        private DevExpress.XtraGrid.Columns.GridColumn col_PercentageOfTheRest;
        private DevExpress.XtraGrid.Columns.GridColumn col_SL_InvoiceId;
    }
}