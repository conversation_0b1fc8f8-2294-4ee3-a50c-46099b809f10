﻿namespace Pharmacy.Forms
{
    partial class frm_ST_ChangeUserPass
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_ST_ChangeUserPass));
            this.lblCrntUserName = new DevExpress.XtraEditors.LabelControl();
            this.txtCrntUserName = new DevExpress.XtraEditors.TextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.lblCrntPass = new DevExpress.XtraEditors.LabelControl();
            this.txtCrntPass = new DevExpress.XtraEditors.TextEdit();
            this.grpCrnt = new DevExpress.XtraEditors.GroupControl();
            this.btnSubmit = new DevExpress.XtraEditors.SimpleButton();
            this.grpNew = new DevExpress.XtraEditors.GroupControl();
            this.txtNewPass2 = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.btnSubmit2 = new DevExpress.XtraEditors.SimpleButton();
            this.txtNewUserName = new DevExpress.XtraEditors.TextEdit();
            this.txtNewPass1 = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.txtCrntUserName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCrntPass.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCrnt)).BeginInit();
            this.grpCrnt.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpNew)).BeginInit();
            this.grpNew.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtNewPass2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNewUserName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNewPass1.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // lblCrntUserName
            // 
            resources.ApplyResources(this.lblCrntUserName, "lblCrntUserName");
            this.lblCrntUserName.Name = "lblCrntUserName";
            // 
            // txtCrntUserName
            // 
            resources.ApplyResources(this.txtCrntUserName, "txtCrntUserName");
            this.txtCrntUserName.Name = "txtCrntUserName";
            this.txtCrntUserName.Properties.AccessibleDescription = resources.GetString("txtCrntUserName.Properties.AccessibleDescription");
            this.txtCrntUserName.Properties.AccessibleName = resources.GetString("txtCrntUserName.Properties.AccessibleName");
            this.txtCrntUserName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCrntUserName.Properties.Appearance.FontSizeDelta")));
            this.txtCrntUserName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCrntUserName.Properties.Appearance.FontStyleDelta")));
            this.txtCrntUserName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCrntUserName.Properties.Appearance.GradientMode")));
            this.txtCrntUserName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCrntUserName.Properties.Appearance.Image")));
            this.txtCrntUserName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCrntUserName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCrntUserName.Properties.AppearanceReadOnly.FontSizeDelta = ((int)(resources.GetObject("txtCrntUserName.Properties.AppearanceReadOnly.FontSizeDelta")));
            this.txtCrntUserName.Properties.AppearanceReadOnly.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCrntUserName.Properties.AppearanceReadOnly.FontStyleDelta")));
            this.txtCrntUserName.Properties.AppearanceReadOnly.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtCrntUserName.Properties.AppearanceReadOnly.ForeColor")));
            this.txtCrntUserName.Properties.AppearanceReadOnly.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCrntUserName.Properties.AppearanceReadOnly.GradientMode")));
            this.txtCrntUserName.Properties.AppearanceReadOnly.Image = ((System.Drawing.Image)(resources.GetObject("txtCrntUserName.Properties.AppearanceReadOnly.Image")));
            this.txtCrntUserName.Properties.AppearanceReadOnly.Options.UseForeColor = true;
            this.txtCrntUserName.Properties.AutoHeight = ((bool)(resources.GetObject("txtCrntUserName.Properties.AutoHeight")));
            this.txtCrntUserName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCrntUserName.Properties.Mask.AutoComplete")));
            this.txtCrntUserName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCrntUserName.Properties.Mask.BeepOnError")));
            this.txtCrntUserName.Properties.Mask.EditMask = resources.GetString("txtCrntUserName.Properties.Mask.EditMask");
            this.txtCrntUserName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCrntUserName.Properties.Mask.IgnoreMaskBlank")));
            this.txtCrntUserName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCrntUserName.Properties.Mask.MaskType")));
            this.txtCrntUserName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCrntUserName.Properties.Mask.PlaceHolder")));
            this.txtCrntUserName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCrntUserName.Properties.Mask.SaveLiteral")));
            this.txtCrntUserName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCrntUserName.Properties.Mask.ShowPlaceHolders")));
            this.txtCrntUserName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCrntUserName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCrntUserName.Properties.NullValuePrompt = resources.GetString("txtCrntUserName.Properties.NullValuePrompt");
            this.txtCrntUserName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCrntUserName.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCrntUserName.Properties.ReadOnly = true;
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // lblCrntPass
            // 
            resources.ApplyResources(this.lblCrntPass, "lblCrntPass");
            this.lblCrntPass.Name = "lblCrntPass";
            // 
            // txtCrntPass
            // 
            resources.ApplyResources(this.txtCrntPass, "txtCrntPass");
            this.txtCrntPass.EnterMoveNextControl = true;
            this.txtCrntPass.Name = "txtCrntPass";
            this.txtCrntPass.Properties.AccessibleDescription = resources.GetString("txtCrntPass.Properties.AccessibleDescription");
            this.txtCrntPass.Properties.AccessibleName = resources.GetString("txtCrntPass.Properties.AccessibleName");
            this.txtCrntPass.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCrntPass.Properties.Appearance.FontSizeDelta")));
            this.txtCrntPass.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCrntPass.Properties.Appearance.FontStyleDelta")));
            this.txtCrntPass.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCrntPass.Properties.Appearance.GradientMode")));
            this.txtCrntPass.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCrntPass.Properties.Appearance.Image")));
            this.txtCrntPass.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCrntPass.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCrntPass.Properties.AutoHeight = ((bool)(resources.GetObject("txtCrntPass.Properties.AutoHeight")));
            this.txtCrntPass.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCrntPass.Properties.Mask.AutoComplete")));
            this.txtCrntPass.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCrntPass.Properties.Mask.BeepOnError")));
            this.txtCrntPass.Properties.Mask.EditMask = resources.GetString("txtCrntPass.Properties.Mask.EditMask");
            this.txtCrntPass.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCrntPass.Properties.Mask.IgnoreMaskBlank")));
            this.txtCrntPass.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCrntPass.Properties.Mask.MaskType")));
            this.txtCrntPass.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCrntPass.Properties.Mask.PlaceHolder")));
            this.txtCrntPass.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCrntPass.Properties.Mask.SaveLiteral")));
            this.txtCrntPass.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCrntPass.Properties.Mask.ShowPlaceHolders")));
            this.txtCrntPass.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCrntPass.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCrntPass.Properties.NullValuePrompt = resources.GetString("txtCrntPass.Properties.NullValuePrompt");
            this.txtCrntPass.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCrntPass.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCrntPass.Properties.PasswordChar = '*';
            // 
            // grpCrnt
            // 
            resources.ApplyResources(this.grpCrnt, "grpCrnt");
            this.grpCrnt.AppearanceCaption.FontSizeDelta = ((int)(resources.GetObject("grpCrnt.AppearanceCaption.FontSizeDelta")));
            this.grpCrnt.AppearanceCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("grpCrnt.AppearanceCaption.FontStyleDelta")));
            this.grpCrnt.AppearanceCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("grpCrnt.AppearanceCaption.GradientMode")));
            this.grpCrnt.AppearanceCaption.Image = ((System.Drawing.Image)(resources.GetObject("grpCrnt.AppearanceCaption.Image")));
            this.grpCrnt.AppearanceCaption.Options.UseTextOptions = true;
            this.grpCrnt.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.grpCrnt.Controls.Add(this.btnSubmit);
            this.grpCrnt.Controls.Add(this.txtCrntUserName);
            this.grpCrnt.Controls.Add(this.txtCrntPass);
            this.grpCrnt.Controls.Add(this.lblCrntUserName);
            this.grpCrnt.Controls.Add(this.lblCrntPass);
            this.grpCrnt.Name = "grpCrnt";
            // 
            // btnSubmit
            // 
            resources.ApplyResources(this.btnSubmit, "btnSubmit");
            this.btnSubmit.Name = "btnSubmit";
            this.btnSubmit.Click += new System.EventHandler(this.btnSubmit_Click);
            // 
            // grpNew
            // 
            resources.ApplyResources(this.grpNew, "grpNew");
            this.grpNew.AppearanceCaption.FontSizeDelta = ((int)(resources.GetObject("grpNew.AppearanceCaption.FontSizeDelta")));
            this.grpNew.AppearanceCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("grpNew.AppearanceCaption.FontStyleDelta")));
            this.grpNew.AppearanceCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("grpNew.AppearanceCaption.GradientMode")));
            this.grpNew.AppearanceCaption.Image = ((System.Drawing.Image)(resources.GetObject("grpNew.AppearanceCaption.Image")));
            this.grpNew.AppearanceCaption.Options.UseTextOptions = true;
            this.grpNew.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.grpNew.Controls.Add(this.txtNewPass2);
            this.grpNew.Controls.Add(this.labelControl3);
            this.grpNew.Controls.Add(this.btnSubmit2);
            this.grpNew.Controls.Add(this.txtNewUserName);
            this.grpNew.Controls.Add(this.txtNewPass1);
            this.grpNew.Controls.Add(this.labelControl1);
            this.grpNew.Controls.Add(this.labelControl2);
            this.grpNew.Name = "grpNew";
            // 
            // txtNewPass2
            // 
            resources.ApplyResources(this.txtNewPass2, "txtNewPass2");
            this.txtNewPass2.EnterMoveNextControl = true;
            this.txtNewPass2.Name = "txtNewPass2";
            this.txtNewPass2.Properties.AccessibleDescription = resources.GetString("txtNewPass2.Properties.AccessibleDescription");
            this.txtNewPass2.Properties.AccessibleName = resources.GetString("txtNewPass2.Properties.AccessibleName");
            this.txtNewPass2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtNewPass2.Properties.Appearance.FontSizeDelta")));
            this.txtNewPass2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtNewPass2.Properties.Appearance.FontStyleDelta")));
            this.txtNewPass2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtNewPass2.Properties.Appearance.GradientMode")));
            this.txtNewPass2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtNewPass2.Properties.Appearance.Image")));
            this.txtNewPass2.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNewPass2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNewPass2.Properties.AutoHeight = ((bool)(resources.GetObject("txtNewPass2.Properties.AutoHeight")));
            this.txtNewPass2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtNewPass2.Properties.Mask.AutoComplete")));
            this.txtNewPass2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtNewPass2.Properties.Mask.BeepOnError")));
            this.txtNewPass2.Properties.Mask.EditMask = resources.GetString("txtNewPass2.Properties.Mask.EditMask");
            this.txtNewPass2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtNewPass2.Properties.Mask.IgnoreMaskBlank")));
            this.txtNewPass2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtNewPass2.Properties.Mask.MaskType")));
            this.txtNewPass2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtNewPass2.Properties.Mask.PlaceHolder")));
            this.txtNewPass2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtNewPass2.Properties.Mask.SaveLiteral")));
            this.txtNewPass2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtNewPass2.Properties.Mask.ShowPlaceHolders")));
            this.txtNewPass2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtNewPass2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtNewPass2.Properties.NullValuePrompt = resources.GetString("txtNewPass2.Properties.NullValuePrompt");
            this.txtNewPass2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtNewPass2.Properties.NullValuePromptShowForEmptyValue")));
            this.txtNewPass2.Properties.PasswordChar = '*';
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // btnSubmit2
            // 
            resources.ApplyResources(this.btnSubmit2, "btnSubmit2");
            this.btnSubmit2.Name = "btnSubmit2";
            this.btnSubmit2.Click += new System.EventHandler(this.btnSubmit2_Click);
            // 
            // txtNewUserName
            // 
            resources.ApplyResources(this.txtNewUserName, "txtNewUserName");
            this.txtNewUserName.EnterMoveNextControl = true;
            this.txtNewUserName.Name = "txtNewUserName";
            this.txtNewUserName.Properties.AccessibleDescription = resources.GetString("txtNewUserName.Properties.AccessibleDescription");
            this.txtNewUserName.Properties.AccessibleName = resources.GetString("txtNewUserName.Properties.AccessibleName");
            this.txtNewUserName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtNewUserName.Properties.Appearance.FontSizeDelta")));
            this.txtNewUserName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtNewUserName.Properties.Appearance.FontStyleDelta")));
            this.txtNewUserName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtNewUserName.Properties.Appearance.GradientMode")));
            this.txtNewUserName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtNewUserName.Properties.Appearance.Image")));
            this.txtNewUserName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNewUserName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNewUserName.Properties.AutoHeight = ((bool)(resources.GetObject("txtNewUserName.Properties.AutoHeight")));
            this.txtNewUserName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtNewUserName.Properties.Mask.AutoComplete")));
            this.txtNewUserName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtNewUserName.Properties.Mask.BeepOnError")));
            this.txtNewUserName.Properties.Mask.EditMask = resources.GetString("txtNewUserName.Properties.Mask.EditMask");
            this.txtNewUserName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtNewUserName.Properties.Mask.IgnoreMaskBlank")));
            this.txtNewUserName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtNewUserName.Properties.Mask.MaskType")));
            this.txtNewUserName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtNewUserName.Properties.Mask.PlaceHolder")));
            this.txtNewUserName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtNewUserName.Properties.Mask.SaveLiteral")));
            this.txtNewUserName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtNewUserName.Properties.Mask.ShowPlaceHolders")));
            this.txtNewUserName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtNewUserName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtNewUserName.Properties.NullValuePrompt = resources.GetString("txtNewUserName.Properties.NullValuePrompt");
            this.txtNewUserName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtNewUserName.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txtNewPass1
            // 
            resources.ApplyResources(this.txtNewPass1, "txtNewPass1");
            this.txtNewPass1.EnterMoveNextControl = true;
            this.txtNewPass1.Name = "txtNewPass1";
            this.txtNewPass1.Properties.AccessibleDescription = resources.GetString("txtNewPass1.Properties.AccessibleDescription");
            this.txtNewPass1.Properties.AccessibleName = resources.GetString("txtNewPass1.Properties.AccessibleName");
            this.txtNewPass1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtNewPass1.Properties.Appearance.FontSizeDelta")));
            this.txtNewPass1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtNewPass1.Properties.Appearance.FontStyleDelta")));
            this.txtNewPass1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtNewPass1.Properties.Appearance.GradientMode")));
            this.txtNewPass1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtNewPass1.Properties.Appearance.Image")));
            this.txtNewPass1.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNewPass1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNewPass1.Properties.AutoHeight = ((bool)(resources.GetObject("txtNewPass1.Properties.AutoHeight")));
            this.txtNewPass1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtNewPass1.Properties.Mask.AutoComplete")));
            this.txtNewPass1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtNewPass1.Properties.Mask.BeepOnError")));
            this.txtNewPass1.Properties.Mask.EditMask = resources.GetString("txtNewPass1.Properties.Mask.EditMask");
            this.txtNewPass1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtNewPass1.Properties.Mask.IgnoreMaskBlank")));
            this.txtNewPass1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtNewPass1.Properties.Mask.MaskType")));
            this.txtNewPass1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtNewPass1.Properties.Mask.PlaceHolder")));
            this.txtNewPass1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtNewPass1.Properties.Mask.SaveLiteral")));
            this.txtNewPass1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtNewPass1.Properties.Mask.ShowPlaceHolders")));
            this.txtNewPass1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtNewPass1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtNewPass1.Properties.NullValuePrompt = resources.GetString("txtNewPass1.Properties.NullValuePrompt");
            this.txtNewPass1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtNewPass1.Properties.NullValuePromptShowForEmptyValue")));
            this.txtNewPass1.Properties.PasswordChar = '*';
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // frm_ST_ChangeUserPass
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grpNew);
            this.Controls.Add(this.grpCrnt);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_ST_ChangeUserPass";
            this.Load += new System.EventHandler(this.frm_ST_Print_Load);
            ((System.ComponentModel.ISupportInitialize)(this.txtCrntUserName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCrntPass.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grpCrnt)).EndInit();
            this.grpCrnt.ResumeLayout(false);
            this.grpCrnt.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grpNew)).EndInit();
            this.grpNew.ResumeLayout(false);
            this.grpNew.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtNewPass2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNewUserName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNewPass1.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl lblCrntUserName;
        private DevExpress.XtraEditors.TextEdit txtCrntUserName;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.LabelControl lblCrntPass;
        private DevExpress.XtraEditors.TextEdit txtCrntPass;
        private DevExpress.XtraEditors.GroupControl grpCrnt;
        private DevExpress.XtraEditors.SimpleButton btnSubmit;
        private DevExpress.XtraEditors.GroupControl grpNew;
        private DevExpress.XtraEditors.TextEdit txtNewPass2;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.SimpleButton btnSubmit2;
        private DevExpress.XtraEditors.TextEdit txtNewUserName;
        private DevExpress.XtraEditors.TextEdit txtNewPass1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
    }
}