﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Pharmacy.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="ReceiptPrinterName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="SWPortName" Type="System.String" Scope="User">
      <Value Profile="(Default)">COM1</Value>
    </Setting>
    <Setting Name="SWBaudRate" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">2400</Value>
    </Setting>
    <Setting Name="SWParity" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">2</Value>
    </Setting>
    <Setting Name="SWDataBits" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">7</Value>
    </Setting>
    <Setting Name="SWStopBits" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="SWStartIndex" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">4</Value>
    </Setting>
    <Setting Name="SWLength" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">8</Value>
    </Setting>
    <Setting Name="SWPacketLength" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">22</Value>
    </Setting>
    <Setting Name="SWTimerInterval" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">270</Value>
    </Setting>
    <Setting Name="fb" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Tw" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Lnkd" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="BackEndPoint" Type="System.String" Scope="User">
      <Value Profile="(Default)">http://localhost/</Value>
    </Setting>
    <Setting Name="signuatuer_dll" Type="System.String" Scope="User">
      <Value Profile="(Default)">C:\Users\<USER>\source\repos\centeromeer\LinkIT-E-Invoice\ERP\Pharmacy\bin\McDRSigniture.dll</Value>
    </Setting>
    <Setting Name="RecievedDocumentsMinutesInterval" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">10</Value>
    </Setting>
    <Setting Name="RecievedDocumentsLastSyncDate" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="BackEndPort" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">57978</Value>
    </Setting>
    <Setting Name="ERPConnectionString" Type="(Connection string)" Scope="Application">
      <DesignTimeValue Profile="(Default)">&lt;?xml version="1.0" encoding="utf-16"?&gt;
&lt;SerializableConnectionString xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;
  &lt;ConnectionString&gt;Data Source=.;Initial Catalog=ERPsafa;Integrated Security=True;Pooling=False;TrustServerCertificate=True&lt;/ConnectionString&gt;
  &lt;ProviderName&gt;System.Data.SqlClient&lt;/ProviderName&gt;
&lt;/SerializableConnectionString&gt;</DesignTimeValue>
      <Value Profile="(Default)">Data Source=.;Initial Catalog=ERPsafa;Integrated Security=True;Pooling=False;TrustServerCertificate=True</Value>
    </Setting>
  </Settings>
</SettingsFile>