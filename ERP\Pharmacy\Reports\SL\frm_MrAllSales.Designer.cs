﻿namespace Reports
{
    partial class frm_MrAllSales
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_MrAllSales));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdCategory = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_TotalSellPrice = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colAchieve = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_Weight = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colRatio = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_TargetValue = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_TargetQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_TotalSoldQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_DirectSoldValue = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_DirectSoldQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand_InDirect = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_SellPrice = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_DiscR = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SellPriceBeforeDiscR = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ItemId = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.rep_item = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colParentAcNum = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CustomerGroupId = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.repGrp = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colParentGrpId = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colAcNumber = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            this.cmbLevel = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_item)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repGrp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbLevel.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPreview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnPreview
            // 
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPreview_ItemClick);
            // 
            // barBtnRefresh
            // 
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdCategory
            // 
            resources.ApplyResources(this.grdCategory, "grdCategory");
            this.grdCategory.MainView = this.bandedGridView1;
            this.grdCategory.Name = "grdCategory";
            this.grdCategory.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_item,
            this.repGrp});
            this.grdCategory.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1});
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Appearance.BandPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.Appearance.BandPanel.Font")));
            this.bandedGridView1.Appearance.BandPanel.Options.UseFont = true;
            this.bandedGridView1.Appearance.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.BandPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.bandedGridView1.Appearance.BandPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.GroupFooter.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.GroupRow.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.GroupRow.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.bandedGridView1.Appearance.GroupRow.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.Font")));
            this.bandedGridView1.Appearance.HeaderPanel.Options.UseFont = true;
            this.bandedGridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.Row.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.bandedGridView1.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.AppearancePrint.FooterPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.Font")));
            this.bandedGridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.BackColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.Font")));
            this.bandedGridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.AppearancePrint.GroupRow.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.BackColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.Font")));
            this.bandedGridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.AppearancePrint.HeaderPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.Font")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.BackColor")));
            this.bandedGridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.ForeColor")));
            this.bandedGridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.BorderColor")));
            this.bandedGridView1.AppearancePrint.Row.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.Row.Font")));
            this.bandedGridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.ForeColor")));
            this.bandedGridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand3,
            this.gridBand4,
            this.gridBand_InDirect,
            this.gridBand1});
            this.bandedGridView1.ColumnPanelRowHeight = 50;
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.colParentAcNum,
            this.colAcNumber,
            this.colParentGrpId,
            this.col_CustomerGroupId,
            this.col_ItemId,
            this.col_DirectSoldQty,
            this.col_DirectSoldValue,
            this.col_TargetQty,
            this.col_TargetValue,
            this.colRatio,
            this.col_Weight,
            this.colAchieve,
            this.col_TotalSoldQty,
            this.col_SellPriceBeforeDiscR,
            this.col_DiscR,
            this.col_SellPrice,
            this.col_TotalSellPrice});
            this.bandedGridView1.CustomizationFormBounds = new System.Drawing.Rectangle(947, 225, 222, 429);
            this.bandedGridView1.GridControl = this.grdCategory;
            this.bandedGridView1.GroupCount = 2;
            resources.ApplyResources(this.bandedGridView1, "bandedGridView1");
            this.bandedGridView1.GroupRowHeight = 30;
            this.bandedGridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary"))), resources.GetString("bandedGridView1.GroupSummary1"), this.col_DirectSoldQty, resources.GetString("bandedGridView1.GroupSummary2")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary3"))), resources.GetString("bandedGridView1.GroupSummary4"), this.col_DirectSoldValue, resources.GetString("bandedGridView1.GroupSummary5")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary6"))), resources.GetString("bandedGridView1.GroupSummary7"), this.col_TargetQty, resources.GetString("bandedGridView1.GroupSummary8")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary9"))), resources.GetString("bandedGridView1.GroupSummary10"), this.colRatio, resources.GetString("bandedGridView1.GroupSummary11")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary12"))), resources.GetString("bandedGridView1.GroupSummary13"), this.col_Weight, resources.GetString("bandedGridView1.GroupSummary14")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary15"))), resources.GetString("bandedGridView1.GroupSummary16"), this.colAchieve, resources.GetString("bandedGridView1.GroupSummary17")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary18"))), resources.GetString("bandedGridView1.GroupSummary19"), this.col_TotalSoldQty, resources.GetString("bandedGridView1.GroupSummary20")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary21"))), resources.GetString("bandedGridView1.GroupSummary22"), this.col_TargetValue, resources.GetString("bandedGridView1.GroupSummary23")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary24"))), resources.GetString("bandedGridView1.GroupSummary25"), this.col_TotalSellPrice, resources.GetString("bandedGridView1.GroupSummary26"))});
            this.bandedGridView1.HorzScrollStep = 2;
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.bandedGridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.bandedGridView1.OptionsBehavior.AutoExpandAllGroups = true;
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsNavigation.EnterMoveNextColumn = true;
            this.bandedGridView1.OptionsPrint.ExpandAllGroups = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.bandedGridView1.OptionsView.AllowCellMerge = true;
            this.bandedGridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.bandedGridView1.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.bandedGridView1.OptionsView.RowAutoHeight = true;
            this.bandedGridView1.OptionsView.ShowAutoFilterRow = true;
            this.bandedGridView1.OptionsView.ShowFooter = true;
            this.bandedGridView1.OptionsView.ShowIndicator = false;
            this.bandedGridView1.SortInfo.AddRange(new DevExpress.XtraGrid.Columns.GridColumnSortInfo[] {
            new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.colParentGrpId, DevExpress.Data.ColumnSortOrder.Ascending),
            new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.col_CustomerGroupId, DevExpress.Data.ColumnSortOrder.Ascending)});
            // 
            // gridBand3
            // 
            resources.ApplyResources(this.gridBand3, "gridBand3");
            this.gridBand3.Columns.Add(this.col_TotalSellPrice);
            this.gridBand3.Columns.Add(this.colAchieve);
            this.gridBand3.Columns.Add(this.col_Weight);
            this.gridBand3.Columns.Add(this.colRatio);
            this.gridBand3.Columns.Add(this.col_TargetValue);
            this.gridBand3.Columns.Add(this.col_TargetQty);
            this.gridBand3.Columns.Add(this.col_TotalSoldQty);
            this.gridBand3.VisibleIndex = 0;
            // 
            // col_TotalSellPrice
            // 
            resources.ApplyResources(this.col_TotalSellPrice, "col_TotalSellPrice");
            this.col_TotalSellPrice.DisplayFormat.FormatString = "n2";
            this.col_TotalSellPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalSellPrice.FieldName = "TotalSellPrice";
            this.col_TotalSellPrice.Name = "col_TotalSellPrice";
            this.col_TotalSellPrice.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalSellPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSellPrice.Summary"))), resources.GetString("col_TotalSellPrice.Summary1"), resources.GetString("col_TotalSellPrice.Summary2"))});
            // 
            // colAchieve
            // 
            resources.ApplyResources(this.colAchieve, "colAchieve");
            this.colAchieve.DisplayFormat.FormatString = "p2";
            this.colAchieve.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colAchieve.FieldName = "Achieve";
            this.colAchieve.Name = "colAchieve";
            this.colAchieve.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colAchieve.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colAchieve.Summary"))), resources.GetString("colAchieve.Summary1"), resources.GetString("colAchieve.Summary2"))});
            // 
            // col_Weight
            // 
            resources.ApplyResources(this.col_Weight, "col_Weight");
            this.col_Weight.DisplayFormat.FormatString = "n2";
            this.col_Weight.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Weight.FieldName = "Weight";
            this.col_Weight.Name = "col_Weight";
            this.col_Weight.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_Weight.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_Weight.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Weight.Summary"))), resources.GetString("col_Weight.Summary1"), resources.GetString("col_Weight.Summary2"))});
            // 
            // colRatio
            // 
            resources.ApplyResources(this.colRatio, "colRatio");
            this.colRatio.DisplayFormat.FormatString = "p2";
            this.colRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colRatio.FieldName = "Ratio";
            this.colRatio.Name = "colRatio";
            this.colRatio.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colRatio.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colRatio.Summary"))), resources.GetString("colRatio.Summary1"), resources.GetString("colRatio.Summary2"))});
            // 
            // col_TargetValue
            // 
            resources.ApplyResources(this.col_TargetValue, "col_TargetValue");
            this.col_TargetValue.DisplayFormat.FormatString = "n2";
            this.col_TargetValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TargetValue.FieldName = "TargetValue";
            this.col_TargetValue.Name = "col_TargetValue";
            this.col_TargetValue.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_TargetValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TargetValue.Summary"))), resources.GetString("col_TargetValue.Summary1"), resources.GetString("col_TargetValue.Summary2"))});
            // 
            // col_TargetQty
            // 
            resources.ApplyResources(this.col_TargetQty, "col_TargetQty");
            this.col_TargetQty.DisplayFormat.FormatString = "n2";
            this.col_TargetQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TargetQty.FieldName = "TargetQty";
            this.col_TargetQty.Name = "col_TargetQty";
            this.col_TargetQty.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_TargetQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_TargetQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TargetQty.Summary"))), resources.GetString("col_TargetQty.Summary1"), resources.GetString("col_TargetQty.Summary2"))});
            // 
            // col_TotalSoldQty
            // 
            resources.ApplyResources(this.col_TotalSoldQty, "col_TotalSoldQty");
            this.col_TotalSoldQty.DisplayFormat.FormatString = "n2";
            this.col_TotalSoldQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalSoldQty.FieldName = "TotalSoldQty";
            this.col_TotalSoldQty.Name = "col_TotalSoldQty";
            this.col_TotalSoldQty.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalSoldQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSoldQty.Summary"))), resources.GetString("col_TotalSoldQty.Summary1"), resources.GetString("col_TotalSoldQty.Summary2"))});
            // 
            // gridBand4
            // 
            resources.ApplyResources(this.gridBand4, "gridBand4");
            this.gridBand4.Columns.Add(this.col_DirectSoldValue);
            this.gridBand4.Columns.Add(this.col_DirectSoldQty);
            this.gridBand4.VisibleIndex = 1;
            // 
            // col_DirectSoldValue
            // 
            resources.ApplyResources(this.col_DirectSoldValue, "col_DirectSoldValue");
            this.col_DirectSoldValue.DisplayFormat.FormatString = "n2";
            this.col_DirectSoldValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DirectSoldValue.FieldName = "DirectSoldValue";
            this.col_DirectSoldValue.Name = "col_DirectSoldValue";
            this.col_DirectSoldValue.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_DirectSoldValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DirectSoldValue.Summary"))), resources.GetString("col_DirectSoldValue.Summary1"), resources.GetString("col_DirectSoldValue.Summary2"))});
            // 
            // col_DirectSoldQty
            // 
            resources.ApplyResources(this.col_DirectSoldQty, "col_DirectSoldQty");
            this.col_DirectSoldQty.DisplayFormat.FormatString = "n2";
            this.col_DirectSoldQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DirectSoldQty.FieldName = "DirectSoldQty";
            this.col_DirectSoldQty.Name = "col_DirectSoldQty";
            this.col_DirectSoldQty.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_DirectSoldQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_DirectSoldQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DirectSoldQty.Summary"))), resources.GetString("col_DirectSoldQty.Summary1"), resources.GetString("col_DirectSoldQty.Summary2"))});
            // 
            // gridBand_InDirect
            // 
            resources.ApplyResources(this.gridBand_InDirect, "gridBand_InDirect");
            this.gridBand_InDirect.VisibleIndex = 2;
            // 
            // gridBand1
            // 
            resources.ApplyResources(this.gridBand1, "gridBand1");
            this.gridBand1.Columns.Add(this.col_SellPrice);
            this.gridBand1.Columns.Add(this.col_DiscR);
            this.gridBand1.Columns.Add(this.col_SellPriceBeforeDiscR);
            this.gridBand1.Columns.Add(this.col_ItemId);
            this.gridBand1.Columns.Add(this.colParentAcNum);
            this.gridBand1.Columns.Add(this.col_CustomerGroupId);
            this.gridBand1.Columns.Add(this.colParentGrpId);
            this.gridBand1.Columns.Add(this.colAcNumber);
            this.gridBand1.VisibleIndex = 3;
            // 
            // col_SellPrice
            // 
            resources.ApplyResources(this.col_SellPrice, "col_SellPrice");
            this.col_SellPrice.DisplayFormat.FormatString = "n2";
            this.col_SellPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SellPrice.FieldName = "SellPrice";
            this.col_SellPrice.Name = "col_SellPrice";
            this.col_SellPrice.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_DiscR
            // 
            resources.ApplyResources(this.col_DiscR, "col_DiscR");
            this.col_DiscR.DisplayFormat.FormatString = "p2";
            this.col_DiscR.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscR.FieldName = "DiscR";
            this.col_DiscR.Name = "col_DiscR";
            this.col_DiscR.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_SellPriceBeforeDiscR
            // 
            resources.ApplyResources(this.col_SellPriceBeforeDiscR, "col_SellPriceBeforeDiscR");
            this.col_SellPriceBeforeDiscR.FieldName = "SellPriceBeforeDiscR";
            this.col_SellPriceBeforeDiscR.Name = "col_SellPriceBeforeDiscR";
            this.col_SellPriceBeforeDiscR.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_ItemId
            // 
            resources.ApplyResources(this.col_ItemId, "col_ItemId");
            this.col_ItemId.ColumnEdit = this.rep_item;
            this.col_ItemId.FieldName = "ItemId";
            this.col_ItemId.Name = "col_ItemId";
            this.col_ItemId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemId.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            this.col_ItemId.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // rep_item
            // 
            this.rep_item.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_item.Buttons"))))});
            this.rep_item.Name = "rep_item";
            resources.ApplyResources(this.rep_item, "rep_item");
            this.rep_item.View = this.gridView3;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn6});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn5
            // 
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "ItemNameAr";
            this.gridColumn5.Name = "gridColumn5";
            // 
            // gridColumn6
            // 
            this.gridColumn6.FieldName = "ItemId";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // colParentAcNum
            // 
            this.colParentAcNum.FieldName = "ParentAcNum";
            this.colParentAcNum.Name = "colParentAcNum";
            // 
            // col_CustomerGroupId
            // 
            resources.ApplyResources(this.col_CustomerGroupId, "col_CustomerGroupId");
            this.col_CustomerGroupId.ColumnEdit = this.repGrp;
            this.col_CustomerGroupId.FieldName = "AcNumber";
            this.col_CustomerGroupId.Name = "col_CustomerGroupId";
            this.col_CustomerGroupId.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            this.col_CustomerGroupId.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            this.col_CustomerGroupId.SortMode = DevExpress.XtraGrid.ColumnSortMode.Value;
            // 
            // repGrp
            // 
            this.repGrp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repGrp.Buttons"))))});
            this.repGrp.Name = "repGrp";
            resources.ApplyResources(this.repGrp, "repGrp");
            this.repGrp.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.FieldName = "CustomerGroupId";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.FieldName = "CGNameAr";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // colParentGrpId
            // 
            resources.ApplyResources(this.colParentGrpId, "colParentGrpId");
            this.colParentGrpId.ColumnEdit = this.repGrp;
            this.colParentGrpId.FieldName = "ParentAcNum";
            this.colParentGrpId.Name = "colParentGrpId";
            this.colParentGrpId.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            this.colParentGrpId.SortMode = DevExpress.XtraGrid.ColumnSortMode.Value;
            // 
            // colAcNumber
            // 
            this.colAcNumber.FieldName = "AcNumber";
            this.colAcNumber.Name = "colAcNumber";
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.MenuManager = this.barManager1;
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // cmbLevel
            // 
            resources.ApplyResources(this.cmbLevel, "cmbLevel");
            this.cmbLevel.EnterMoveNextControl = true;
            this.cmbLevel.MenuManager = this.barManager1;
            this.cmbLevel.Name = "cmbLevel";
            this.cmbLevel.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbLevel.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbLevel.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbLevel.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbLevel.Properties.Buttons"))))});
            this.cmbLevel.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbLevel.Properties.Items"), ((object)(resources.GetObject("cmbLevel.Properties.Items1"))), ((int)(resources.GetObject("cmbLevel.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbLevel.Properties.Items3"), ((object)(resources.GetObject("cmbLevel.Properties.Items4"))), ((int)(resources.GetObject("cmbLevel.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbLevel.Properties.Items6"), ((object)(resources.GetObject("cmbLevel.Properties.Items7"))), ((int)(resources.GetObject("cmbLevel.Properties.Items8"))))});
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // frm_MrAllSales
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.cmbLevel);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.grdCategory);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_MrAllSales";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_Rep_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_InvoiceList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_item)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repGrp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbLevel.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdCategory;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_item;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repGrp;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colAchieve;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Weight;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colRatio;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_TargetQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_DirectSoldQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ItemId;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colParentGrpId;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colParentAcNum;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CustomerGroupId;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colAcNumber;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_TotalSoldQty;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbLevel;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SellPrice;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_TotalSellPrice;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_TargetValue;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_DirectSoldValue;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_DiscR;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand_InDirect;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SellPriceBeforeDiscR;
    }
}