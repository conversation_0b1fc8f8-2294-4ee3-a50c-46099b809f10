﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_CustomerItemsSales_OutTrns : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters, custGroupAccNumber;

        int itemId1, itemId2, customerId1, customerId2, custGroupId, storeId1, storeId2;
        byte FltrTyp_item, fltrTyp_Date, FltrTyp_Customer, FltrTyp_Store;
        DateTime date1, date2;

        List<int> lstStores = new List<int>();

        byte FltrTyp_Company;
        int companyId;

        public frm_SL_CustomerItemsSales_OutTrns(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2, int custGroupId, string custGroupAccNumber,
            byte _fltrTyp_Store, int _storeId1, int _storeId2, byte FltrTyp_Company, int companyId)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_Store = _fltrTyp_Store;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;

            this.date1 = date1;
            this.date2 = date2;

            this.customerId1 = customerId1;
            this.customerId2 = customerId2;

            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            this.storeId1 = _storeId1;
            this.storeId2 = _storeId2;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;

            getReportHeader();

            LoadData();

            col_Serial.OptionsColumn.ShowInCustomizationForm = col_Serial.Visible = Shared.st_Store.Serial;

            col_Serial.Caption = Shared.IsEnglish ? Shared.st_Store.SerialNameEn : Shared.st_Store.SerialNameAr;
            col_Serial2.Caption = Shared.IsEnglish ? Shared.st_Store.Serial2NameEn : Shared.st_Store.Serial2NameAr;
            ReportsUtils.ColumnChooser(grdCategory);

            col_OutPiecesCount.Visible = col_SoldPiecesCount.Visible = Shared.st_Store.PiecesCount;
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }

        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            if (FltrTyp_Customer == 1)
                col_CusNameAr.Visible = false;

            if (FltrTyp_item == 1)
                col_ItemNameAr.Visible = false;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stores = DB.IC_Stores.ToList();
            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= storeId2 && store.StoreId >= storeId1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (storeId1 > 0 && (store.StoreId == storeId1 || store.ParentId == storeId1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }
            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();

            #region Sales_Invoices
            var salesInvoices = (from p in DB.SL_Invoices
                                     // where lstStores.Count > 0 ? lstStores.Contains(p.StoreId)  : true

                                 join pd in DB.SL_InvoiceDetails on p.SL_InvoiceId equals pd.SL_InvoiceId
                                 //upate
                                 where lstStores.Count > 0 ? lstStores.Contains(p.StoreId) || lstStores.Contains(pd.StoreId.Value) : true

                                 where fltrTyp_Date == 1 ? p.InvoiceDate.Date == date1 : true
                                 where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                 p.InvoiceDate >= date1 && p.InvoiceDate <= date2 : true
                                 where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                 p.InvoiceDate >= date1 : true
                                 where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                 p.InvoiceDate <= date2 : true

                                 where FltrTyp_Customer == 1 ? p.CustomerId == customerId1 : true
                                 where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                                 p.CustomerId >= customerId1 && p.CustomerId <= customerId2 : true
                                 where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                                 p.CustomerId >= customerId1 : true
                                 where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                                 p.CustomerId <= customerId2 : true

                                 where FltrTyp_item == 1 ? pd.ItemId == itemId1 : true
                                 where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                                 pd.ItemId >= itemId1 && pd.ItemId <= itemId2 : true
                                 where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                                 pd.ItemId >= itemId1 : true
                                 where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                                 pd.ItemId <= itemId2 : true

                                 join t in DB.IC_Items on pd.ItemId equals t.ItemId
                                 where FltrTyp_Company == 1 ? t.Company == companyId : true
                                 join g in DB.IC_Categories on t.Category equals g.CategoryId
                                 where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true

                                 join c in DB.SL_Customers
                                 on p.CustomerId equals c.CustomerId
                                 join a in DB.ACC_Accounts
                                    on c.AccountId equals a.AccountId
                                 where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)


                                 join u in DB.IC_UOMs on pd.UOMId equals u.UOMId

                                 let Cust_Category = DB.SL_CustomerGroups
                                 let Cust_group = DB.SL_Group_Customers
                                 select new
                                 {
                                     InvoiceCode = p.InvoiceCode,
                                     InvoiceDate = p.InvoiceDate.ToShortDateString(),
                                     ItemNameAr = t.ItemNameAr,
                                     t.Height,
                                     t.Length,
                                     t.Width,
                                     Qty = decimal.ToDouble(pd.Qty),
                                     TotalSellPrice = decimal.ToDouble(pd.TotalSellPrice),
                                     UOM = u.UOM,
                                     CusNameAr =Shared.IsEnglish?c.CusNameEn: c.CusNameAr,
                                     PiecesCount = decimal.ToDouble(pd.PiecesCount),
                                     Store = p.StoreId == 0 ?
                                     DB.IC_Stores.SingleOrDefault(c => c.StoreId == pd.StoreId).StoreNameAr :
                                     DB.IC_Stores.SingleOrDefault(c => c.StoreId == p.StoreId).StoreNameAr,
                                     pd.Serial,
                                     pd.Serial2,
                                     p.CrncId,
                                     p.CrncRate,
                                     TotalSellPrice_Local = pd.TotalSellPrice * p.CrncRate,
                                     CustCat = Cust_Category.Where(x => x.CustomerGroupId == c.CategoryId).Select(x => Shared.IsEnglish ? x.CGNameEn : x.CGNameAr).FirstOrDefault(),
                                     CustGroup = Cust_group.Where(x => x.GroupId == c.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault(),
                                     c.City
                                 }).ToList().OrderBy(x => x.CusNameAr).ThenBy(x => x.ItemNameAr).ToList();
            #endregion

            #region OutTransactions
            var OutTranactions = (from p in DB.IC_OutTrns
                                  where lstStores.Count > 0 ? lstStores.Contains(p.StoreId) : true
                                  join pd in DB.IC_OutTrnsDetails on p.OutTrnsId equals pd.OutTrnsId
                                  //update
                                  //  where lstStores.Count > 0 ? lstStores.Contains(p.StoreId)|| lstStores.Contains(pd.StoreId.v) : true

                                  where fltrTyp_Date == 1 ? p.OutTrnsDate.Date == date1 : true
                                  where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                  p.OutTrnsDate >= date1 && p.OutTrnsDate <= date2 : true
                                  where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                  p.OutTrnsDate >= date1 : true
                                  where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                  p.OutTrnsDate <= date2 : true

                                  where FltrTyp_item == 1 ? pd.ItemId == itemId1 : true
                                  where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                                  pd.ItemId >= itemId1 && pd.ItemId <= itemId2 : true
                                  where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                                  pd.ItemId >= itemId1 : true
                                  where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                                  pd.ItemId <= itemId2 : true

                                  where FltrTyp_Customer == 1 ? p.CustomerId == customerId1 : true
                                  where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                                  p.CustomerId >= customerId1 && p.CustomerId <= customerId2 : true
                                  where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                                  p.CustomerId >= customerId1 : true
                                  where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                                  p.CustomerId <= customerId2 : true

                                  join t in DB.IC_Items on pd.ItemId equals t.ItemId
                                  where FltrTyp_Company == 1 ? t.Company == companyId : true
                                  join g in DB.IC_Categories on t.Category equals g.CategoryId
                                  where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true

                                  join c in DB.SL_Customers
                                  on p.CustomerId equals c.CustomerId

                                  join a in DB.ACC_Accounts
                                    on c.AccountId equals a.AccountId
                                  where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                                  join u in DB.IC_UOMs on pd.UOMId equals u.UOMId
                                  let total_cost = DB.IC_ItemStores.Where(z => z.ProcessId == (int)DAL.Process.OutTrns && z.SourceId == pd.OutTrnsDetailId)
                                      .Select(x => x.PurchasePrice).Sum()
                                  select new
                                  {
                                      pd.Serial2,
                                      pd.Serial,
                                      OutTrns_Code = p.OutTrnsCode,
                                      OutTrns_Date = p.OutTrnsDate.ToShortDateString(),
                                      ItemNameAr = t.ItemNameAr,
                                      t.Height,
                                      t.Length,
                                      t.Width,
                                      OutTrns_Qty = pd.Qty == null ? decimal.ToDouble(0) : decimal.ToDouble(pd.Qty),
                                      OutTrns_Unit = u.UOM,
                                      CusNameAr = c.CusNameAr,
                                      OutTrns_TotalCostPrice = total_cost == null ? decimal.ToDouble(0) : decimal.ToDouble(total_cost),
                                      PiecesCount = decimal.ToDouble(pd.PiecesCount),
                                      Store = DB.IC_Stores.SingleOrDefault(c => c.StoreId == p.StoreId).StoreNameAr,
                                      c.City

                                  }).ToList().OrderBy(x => x.CusNameAr).ThenBy(x => x.ItemNameAr).ToList();
            #endregion

            #region Init_DataTable
            DataTable dt = new DataTable();
            dt.Columns.Add("CusNameAr");
            dt.Columns.Add("ItemNameAr");
            dt.Columns.Add("InvoiceDate");
            dt.Columns.Add("InvoiceCode");
            dt.Columns.Add("UOM");
            dt.Columns.Add("Qty");
            dt.Columns.Add("TotalSellPrice");
            dt.Columns.Add("OutTrns_Date");
            dt.Columns.Add("OutTrns_Code");
            dt.Columns.Add("OutTrns_Unit");
            dt.Columns.Add("OutTrns_Qty");
            dt.Columns.Add("OutTrns_TotalCostPrice");
            dt.Columns.Add("SoldPiecesCount");
            dt.Columns.Add("OutPiecesCount");
            dt.Columns.Add("Store");
            dt.Columns.Add("CrncId");
            dt.Columns.Add("CrncRate");
            dt.Columns.Add("TotalSellPrice_Local");
            dt.Columns.Add("CustCat");
            dt.Columns.Add("CustGroup");
            dt.Columns.Add("Serial");
            dt.Columns.Add("Serial2");
            dt.Columns.Add("Height");
            dt.Columns.Add("Length");
            dt.Columns.Add("Width");
            dt.Columns.Add("City");

            #endregion

            rep_Currency.DataSource = Shared.lstCurrency;
            rep_Currency.ValueMember = "CrncId";
            rep_Currency.DisplayMember = "crncName";

            var cust_items = (from d in salesInvoices
                              select new { d.CusNameAr, d.ItemNameAr }).Union
                       (from d in OutTranactions
                        select new { d.CusNameAr, d.ItemNameAr }).Distinct().ToList();

            foreach (var c in cust_items)
            {
                var sales = salesInvoices.Where(x => x.ItemNameAr == c.ItemNameAr && x.CusNameAr == c.CusNameAr).ToList();
                var outtrns = OutTranactions.Where(x => x.ItemNameAr == c.ItemNameAr && x.CusNameAr == c.CusNameAr).ToList();
                int rows_count = sales.Count() >= outtrns.Count() ? sales.Count() : outtrns.Count();

                for (int i = 0; i < rows_count; i++)
                {
                    if (i < sales.Count() && i < outtrns.Count())
                    {
                        var d = sales[i];
                        var o = outtrns[i];
                        dt.Rows.Add(c.CusNameAr, c.ItemNameAr, d.InvoiceDate, d.InvoiceCode, d.UOM, d.Qty, d.TotalSellPrice,
                                o.OutTrns_Date, o.OutTrns_Code, o.OutTrns_Unit, o.OutTrns_Qty, o.OutTrns_TotalCostPrice,
                                d.PiecesCount, o.PiecesCount, o.Store, d.CrncId, Math.Round(d.CrncRate, 3), Math.Round(d.TotalSellPrice_Local, 3),d.CustCat,d.CustGroup, d.Serial != null? d.Serial : o.Serial, d.Serial2 != null ? d.Serial2 : o.Serial2,
                                   d.Height,
                                   d.Length,
                                   d.Width,d.City);
                    }
                    else if (i < sales.Count())
                    {
                        var d = sales[i];
                        dt.Rows.Add(c.CusNameAr, c.ItemNameAr, d.InvoiceDate, d.InvoiceCode, d.UOM, d.Qty, d.TotalSellPrice,
                            DBNull.Value, DBNull.Value, DBNull.Value, DBNull.Value, DBNull.Value, d.PiecesCount, DBNull.Value, d.Store
                            , d.CrncId, Math.Round(d.CrncRate, 3), Math.Round(d.TotalSellPrice_Local, 3), d.CustCat, d.CustGroup, d.Serial, d.Serial2,
                                   d.Height,
                                   d.Length,
                                   d.Width, d.City);
                    }
                    else if (i < outtrns.Count())
                    {
                        var o = outtrns[i];
                        dt.Rows.Add(c.CusNameAr, c.ItemNameAr, DBNull.Value, DBNull.Value, DBNull.Value, DBNull.Value, DBNull.Value,
                                o.OutTrns_Date, o.OutTrns_Code, o.OutTrns_Unit, o.OutTrns_Qty, o.OutTrns_TotalCostPrice, DBNull.Value,
                                o.PiecesCount, o.Store, DBNull.Value, DBNull.Value, DBNull.Value, DBNull.Value, DBNull.Value, o.Serial, o.Serial2,
                                   o.Height,
                                   o.Length,
                                   o.Width, o.City);
                    }
                }
            }

            double salesTotal = 0, outTrnsTotal = 0;

            if (salesInvoices != null)
                salesTotal = salesInvoices.Sum(x => x.TotalSellPrice);
            if (OutTranactions != null)
                outTrnsTotal = OutTranactions.Sum(x => x.OutTrns_TotalCostPrice);
            if (salesTotal > outTrnsTotal)
            {
                lblTotal.Text = Shared.IsEnglish ? ResRptEn.Profit : ResRptAr.Profit;
                lblTotalAmount.Text = (salesTotal - outTrnsTotal).ToString("n2");
            }
            else if (outTrnsTotal > salesTotal)
            {
                lblTotal.Text = Shared.IsEnglish ? ResRptEn.Loss : ResRptAr.Loss;
                lblTotalAmount.Text = (outTrnsTotal - salesTotal).ToString("n2");
            }
            else
            {
                lblTotal.Text = string.Empty;
                lblTotalAmount.Text = "0";
            }

            grdCategory.DataSource = dt;

            bandedGridView1.ClearSorting();
            col_CusNameAr.SortOrder = DevExpress.Data.ColumnSortOrder.Ascending;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column == col_SellPrice
                && bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_TotalSellPrice) != DBNull.Value
                && bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_Qty) != DBNull.Value)
            {
                double total_Sell = Convert.ToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_TotalSellPrice));
                double total_qty = Convert.ToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_Qty));
                e.Value = Math.Round(total_Sell / total_qty, 2);
            }
            if (e.Column == col_OutTrns_CostPrice
                && bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_OutTrns_TotalCostPrice) != DBNull.Value
                && bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_OutTrns_Qty) != DBNull.Value)
            {
                double total_cost = Convert.ToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_OutTrns_TotalCostPrice));
                double total_qty = Convert.ToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_OutTrns_Qty));
                e.Value = Math.Round(total_cost / total_qty, 2);
            }
        }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_CustomerItemsSales_OutTrns).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }


        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);
            
            if (Shared.st_Store.UseLengthDimension)
                col_length.Visible = true;
            if (Shared.st_Store.UseHeightDimension)
                col_height.Visible = true;
            if (Shared.st_Store.UseWidthDimension)
                col_width.Visible = true;

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));
            //LoadPrivilege();
            col_OutPiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
            col_SoldPiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

    }
}