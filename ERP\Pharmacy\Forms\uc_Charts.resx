﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="&gt;&gt;chTaxes.Name" xml:space="preserve">
    <value>chTaxes</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnRefresh.Location" type="System.Drawing.Point, System.Drawing">
    <value>732, 436</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="treeChilds.TabIndex" type="System.Int32, mscorlib">
    <value>122</value>
  </data>
  <data name="chTrade.Size" type="System.Drawing.Size, System.Drawing">
    <value>765, 68</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="&gt;&gt;chSales.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="resource.Angle" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="chSales.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="chTrade.TabIndex" type="System.Int32, mscorlib">
    <value>124</value>
  </data>
  <data name="&gt;&gt;treeChilds.Type" xml:space="preserve">
    <value>System.Windows.Forms.TreeView, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnRefresh.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;chTaxes.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>uc_Charts</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="treeChilds.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="chSales.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 3</value>
  </data>
  <data name="btnRefresh.ToolTip" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="btnRefresh.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 25</value>
  </data>
  <data name="chTaxes.Location" type="System.Drawing.Point, System.Drawing">
    <value>-1, 304</value>
  </data>
  <data name="chTaxes.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="chSales.Size" type="System.Drawing.Size, System.Drawing">
    <value>765, 236</value>
  </data>
  <data name="series2.LegendText" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="series1.LegendText" xml:space="preserve">
    <value>Sales Return</value>
  </data>
  <data name="btnRefresh.TabIndex" type="System.Int32, mscorlib">
    <value>123</value>
  </data>
  <data name="series4.Name" xml:space="preserve">
    <value>Series 2</value>
  </data>
  <data name="&gt;&gt;chTrade.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chTrade.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="series4.LegendText" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="treeChilds.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;chTrade.Type" xml:space="preserve">
    <value>DevExpress.XtraCharts.ChartControl, DevExpress.XtraCharts.v15.1.UI, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chSales.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="series3.LegendText" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="&gt;&gt;chTaxes.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;chTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraCharts.ChartControl, DevExpress.XtraCharts.v15.1.UI, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnRefresh.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="&gt;&gt;chTrade.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;treeChilds.Name" xml:space="preserve">
    <value>treeChilds</value>
  </data>
  <data name="&gt;&gt;btnRefresh.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;chSales.Name" xml:space="preserve">
    <value>chSales</value>
  </data>
  <data name="series3.Name" xml:space="preserve">
    <value>Series 1</value>
  </data>
  <data name="treeChilds.Location" type="System.Drawing.Point, System.Drawing">
    <value>471, 343</value>
  </data>
  <data name="chTaxes.Size" type="System.Drawing.Size, System.Drawing">
    <value>766, 165</value>
  </data>
  <data name="resource.Angle1" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="&gt;&gt;chTrade.Name" xml:space="preserve">
    <value>chTrade</value>
  </data>
  <data name="chTaxes.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="&gt;&gt;treeChilds.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="treeChilds.Size" type="System.Drawing.Size, System.Drawing">
    <value>261, 118</value>
  </data>
  <data name="&gt;&gt;chSales.Type" xml:space="preserve">
    <value>DevExpress.XtraCharts.ChartControl, DevExpress.XtraCharts.v15.1.UI, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;treeChilds.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="chSales.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnRefresh.Name" xml:space="preserve">
    <value>btnRefresh</value>
  </data>
  <data name="treeChilds.RightToLeftLayout" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chTrade.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 238</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>765, 469</value>
  </data>
  <data name="&gt;&gt;btnRefresh.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
</root>