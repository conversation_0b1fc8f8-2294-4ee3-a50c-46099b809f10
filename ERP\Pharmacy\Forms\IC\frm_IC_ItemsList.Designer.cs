﻿namespace Pharmacy.Forms
{
    partial class frm_IC_ItemsList
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_ItemsList));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnOpen = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grd_Items = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colSalesDiscRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPurchaseDiscRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colSalesTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colSalesTaxRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPurchaseTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPurchaseTaxRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.bb = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.colItemCode1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemCode2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemNameEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Category = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Company = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colExpire = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repChkExpire = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.colPur = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colSell = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_IsDeleted = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PicPath = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_WarrantyMonths = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_MediumUOMCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_LargeUOMCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_mtrxParentItem = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_IsPos = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_is_libra = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PricingWithSmall = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_VariableWeight = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Height = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Width = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Length = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_IsOffer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repSpin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.rep_Units = new DevExpress.XtraEditors.Repository.RepositoryItemCheckedComboBoxEdit();
            this.navBarControl1 = new DevExpress.XtraNavBar.NavBarControl();
            this.NBG_Tasks = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBG_Reports = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBI_Itemsales = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_ItemSaleReturn = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_ItemBuy = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_ItemBuyReturn = new DevExpress.XtraNavBar.NavBarItem();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.lblItemsCount = new DevExpress.XtraEditors.LabelControl();
            this.chk_ShowMatrixItems = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Items)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bb)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repChkExpire)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Units)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_ShowMatrixItems.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowMoveBarOnToolbar = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnNew,
            this.barBtnOpen,
            this.barBtn_Help,
            this.barBtnClose,
            this.barBtnRefresh,
            this.barBtnPrint});
            this.barManager1.MaxItemId = 28;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(225, 276);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnOpen),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.DrawSizeGrip = true;
            this.bar1.OptionsBar.MultiLine = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtn_Help.Glyph")));
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnPrint.Glyph")));
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnRefresh
            // 
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnRefresh.Glyph")));
            this.barBtnRefresh.Id = 26;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Refresh_ItemClick);
            // 
            // barBtnNew
            // 
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnNew.Glyph")));
            this.barBtnNew.Id = 0;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_New_ItemClick);
            // 
            // barBtnOpen
            // 
            this.barBtnOpen.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnOpen, "barBtnOpen");
            this.barBtnOpen.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnOpen.Glyph")));
            this.barBtnOpen.Id = 1;
            this.barBtnOpen.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnOpen.Name = "barBtnOpen";
            this.barBtnOpen.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnOpen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Open_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnClose.Glyph")));
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Tabs.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Tabs.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grd_Items
            // 
            resources.ApplyResources(this.grd_Items, "grd_Items");
            this.grd_Items.Cursor = System.Windows.Forms.Cursors.Default;
            this.grd_Items.MainView = this.gridView1;
            this.grd_Items.MenuManager = this.barManager1;
            this.grd_Items.Name = "grd_Items";
            this.grd_Items.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpin,
            this.bb,
            this.repChkExpire,
            this.rep_Units});
            this.grd_Items.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.grd_Items.Click += new System.EventHandler(this.grd_Items_Click);
            this.grd_Items.DoubleClick += new System.EventHandler(this.grd_Items_DoubleClick);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top;
            this.gridView1.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.gridView1.ColumnPanelRowHeight = 45;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colSalesDiscRatio,
            this.colPurchaseDiscRatio,
            this.colSalesTaxValue,
            this.colSalesTaxRatio,
            this.colPurchaseTaxValue,
            this.colPurchaseTaxRatio,
            this.colItemId,
            this.colItemCode1,
            this.colItemCode2,
            this.colItemNameAr,
            this.colItemNameEn,
            this.colDescription,
            this.Category,
            this.Company,
            this.colItemType,
            this.colExpire,
            this.colPur,
            this.colSell,
            this.col_IsDeleted,
            this.col_PicPath,
            this.col_WarrantyMonths,
            this.col_MediumUOMCode,
            this.col_LargeUOMCode,
            this.col_mtrxParentItem,
            this.col_IsPos,
            this.col_is_libra,
            this.col_PricingWithSmall,
            this.col_VariableWeight,
            this.col_Height,
            this.col_Width,
            this.col_Length,
            this.col_IsOffer});
            this.gridView1.CustomizationFormBounds = new System.Drawing.Rectangle(751, 359, 208, 275);
            this.gridView1.GridControl = this.grd_Items;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.RowAutoHeight = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowIndicator = false;
            this.gridView1.ColumnFilterChanged += new System.EventHandler(this.gridView1_ColumnFilterChanged);
            // 
            // colSalesDiscRatio
            // 
            resources.ApplyResources(this.colSalesDiscRatio, "colSalesDiscRatio");
            this.colSalesDiscRatio.DisplayFormat.FormatString = "n2";
            this.colSalesDiscRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colSalesDiscRatio.FieldName = "SalesDiscRatio";
            this.colSalesDiscRatio.Name = "colSalesDiscRatio";
            this.colSalesDiscRatio.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colPurchaseDiscRatio
            // 
            resources.ApplyResources(this.colPurchaseDiscRatio, "colPurchaseDiscRatio");
            this.colPurchaseDiscRatio.DisplayFormat.FormatString = "n2";
            this.colPurchaseDiscRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPurchaseDiscRatio.FieldName = "PurchaseDiscRatio";
            this.colPurchaseDiscRatio.Name = "colPurchaseDiscRatio";
            this.colPurchaseDiscRatio.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colSalesTaxValue
            // 
            resources.ApplyResources(this.colSalesTaxValue, "colSalesTaxValue");
            this.colSalesTaxValue.DisplayFormat.FormatString = "n2";
            this.colSalesTaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colSalesTaxValue.FieldName = "SalesTaxValue";
            this.colSalesTaxValue.Name = "colSalesTaxValue";
            this.colSalesTaxValue.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colSalesTaxRatio
            // 
            resources.ApplyResources(this.colSalesTaxRatio, "colSalesTaxRatio");
            this.colSalesTaxRatio.DisplayFormat.FormatString = "n2";
            this.colSalesTaxRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colSalesTaxRatio.FieldName = "SalesTaxRatio";
            this.colSalesTaxRatio.Name = "colSalesTaxRatio";
            this.colSalesTaxRatio.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colPurchaseTaxValue
            // 
            resources.ApplyResources(this.colPurchaseTaxValue, "colPurchaseTaxValue");
            this.colPurchaseTaxValue.DisplayFormat.FormatString = "n2";
            this.colPurchaseTaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPurchaseTaxValue.FieldName = "PurchaseTaxValue";
            this.colPurchaseTaxValue.Name = "colPurchaseTaxValue";
            this.colPurchaseTaxValue.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colPurchaseTaxRatio
            // 
            resources.ApplyResources(this.colPurchaseTaxRatio, "colPurchaseTaxRatio");
            this.colPurchaseTaxRatio.DisplayFormat.FormatString = "n2";
            this.colPurchaseTaxRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPurchaseTaxRatio.FieldName = "PurchaseTaxRatio";
            this.colPurchaseTaxRatio.Name = "colPurchaseTaxRatio";
            this.colPurchaseTaxRatio.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colItemId
            // 
            this.colItemId.AppearanceCell.Options.UseTextOptions = true;
            this.colItemId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemId.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemId.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colItemId.AppearanceHeader.Options.UseTextOptions = true;
            this.colItemId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemId.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemId.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colItemId.ColumnEdit = this.bb;
            this.colItemId.FieldName = "ItemId";
            this.colItemId.Name = "colItemId";
            this.colItemId.OptionsColumn.ReadOnly = true;
            this.colItemId.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // bb
            // 
            this.bb.Name = "bb";
            // 
            // colItemCode1
            // 
            this.colItemCode1.AppearanceCell.Options.UseTextOptions = true;
            this.colItemCode1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemCode1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemCode1.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colItemCode1.AppearanceHeader.Options.UseTextOptions = true;
            this.colItemCode1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemCode1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemCode1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colItemCode1, "colItemCode1");
            this.colItemCode1.ColumnEdit = this.bb;
            this.colItemCode1.FieldName = "ItemCode1";
            this.colItemCode1.Name = "colItemCode1";
            this.colItemCode1.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colItemCode2
            // 
            this.colItemCode2.AppearanceCell.Options.UseTextOptions = true;
            this.colItemCode2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemCode2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemCode2.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colItemCode2.AppearanceHeader.Options.UseTextOptions = true;
            this.colItemCode2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemCode2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemCode2.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colItemCode2, "colItemCode2");
            this.colItemCode2.ColumnEdit = this.bb;
            this.colItemCode2.FieldName = "ItemCode2";
            this.colItemCode2.Name = "colItemCode2";
            this.colItemCode2.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colItemNameAr
            // 
            this.colItemNameAr.AppearanceCell.Options.UseTextOptions = true;
            this.colItemNameAr.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemNameAr.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemNameAr.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colItemNameAr.AppearanceHeader.Options.UseTextOptions = true;
            this.colItemNameAr.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemNameAr.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemNameAr.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colItemNameAr, "colItemNameAr");
            this.colItemNameAr.ColumnEdit = this.bb;
            this.colItemNameAr.FieldName = "ItemNameAr";
            this.colItemNameAr.Name = "colItemNameAr";
            this.colItemNameAr.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colItemNameEn
            // 
            this.colItemNameEn.AppearanceCell.Options.UseTextOptions = true;
            this.colItemNameEn.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemNameEn.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemNameEn.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colItemNameEn.AppearanceHeader.Options.UseTextOptions = true;
            this.colItemNameEn.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemNameEn.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colItemNameEn.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colItemNameEn, "colItemNameEn");
            this.colItemNameEn.ColumnEdit = this.bb;
            this.colItemNameEn.FieldName = "ItemNameEn";
            this.colItemNameEn.Name = "colItemNameEn";
            // 
            // colDescription
            // 
            this.colDescription.AppearanceCell.Options.UseTextOptions = true;
            this.colDescription.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colDescription.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colDescription.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colDescription.AppearanceHeader.Options.UseTextOptions = true;
            this.colDescription.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colDescription.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colDescription.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colDescription, "colDescription");
            this.colDescription.ColumnEdit = this.bb;
            this.colDescription.FieldName = "Description";
            this.colDescription.Name = "colDescription";
            // 
            // Category
            // 
            this.Category.AppearanceCell.Options.UseTextOptions = true;
            this.Category.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.Category.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.Category.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.Category.AppearanceHeader.Options.UseTextOptions = true;
            this.Category.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.Category.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.Category.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.Category, "Category");
            this.Category.FieldName = "Category";
            this.Category.Name = "Category";
            // 
            // Company
            // 
            this.Company.AppearanceCell.Options.UseTextOptions = true;
            this.Company.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.Company.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.Company.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.Company.AppearanceHeader.Options.UseTextOptions = true;
            this.Company.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.Company.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.Company.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.Company, "Company");
            this.Company.FieldName = "Company";
            this.Company.Name = "Company";
            // 
            // colItemType
            // 
            this.colItemType.AppearanceCell.Options.UseTextOptions = true;
            this.colItemType.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colItemType.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            resources.ApplyResources(this.colItemType, "colItemType");
            this.colItemType.FieldName = "ItemType";
            this.colItemType.Name = "colItemType";
            // 
            // colExpire
            // 
            this.colExpire.AppearanceCell.Options.UseTextOptions = true;
            this.colExpire.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colExpire.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colExpire.AppearanceHeader.Options.UseTextOptions = true;
            this.colExpire.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colExpire.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colExpire, "colExpire");
            this.colExpire.ColumnEdit = this.repChkExpire;
            this.colExpire.FieldName = "Expire";
            this.colExpire.Name = "colExpire";
            // 
            // repChkExpire
            // 
            resources.ApplyResources(this.repChkExpire, "repChkExpire");
            this.repChkExpire.Name = "repChkExpire";
            this.repChkExpire.NullStyle = DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked;
            // 
            // colPur
            // 
            this.colPur.AppearanceCell.Options.UseTextOptions = true;
            this.colPur.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colPur.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPur.AppearanceHeader.Options.UseTextOptions = true;
            this.colPur.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colPur.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPur.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colPur, "colPur");
            this.colPur.DisplayFormat.FormatString = "n2";
            this.colPur.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPur.FieldName = "PurchasePrice";
            this.colPur.Name = "colPur";
            this.colPur.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colSell
            // 
            this.colSell.AppearanceCell.Options.UseTextOptions = true;
            this.colSell.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colSell.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colSell.AppearanceHeader.Options.UseTextOptions = true;
            this.colSell.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colSell.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colSell.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colSell, "colSell");
            this.colSell.DisplayFormat.FormatString = "n2";
            this.colSell.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colSell.FieldName = "SmallUOMPrice";
            this.colSell.Name = "colSell";
            this.colSell.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_IsDeleted
            // 
            this.col_IsDeleted.AppearanceCell.Options.UseTextOptions = true;
            this.col_IsDeleted.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_IsDeleted.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_IsDeleted.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_IsDeleted.AppearanceHeader.Options.UseTextOptions = true;
            this.col_IsDeleted.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_IsDeleted.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_IsDeleted.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_IsDeleted, "col_IsDeleted");
            this.col_IsDeleted.ColumnEdit = this.repChkExpire;
            this.col_IsDeleted.FieldName = "IsDeleted";
            this.col_IsDeleted.Name = "col_IsDeleted";
            // 
            // col_PicPath
            // 
            resources.ApplyResources(this.col_PicPath, "col_PicPath");
            this.col_PicPath.FieldName = "PicPath";
            this.col_PicPath.Name = "col_PicPath";
            this.col_PicPath.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_WarrantyMonths
            // 
            resources.ApplyResources(this.col_WarrantyMonths, "col_WarrantyMonths");
            this.col_WarrantyMonths.FieldName = "WarrantyMonths";
            this.col_WarrantyMonths.Name = "col_WarrantyMonths";
            // 
            // col_MediumUOMCode
            // 
            resources.ApplyResources(this.col_MediumUOMCode, "col_MediumUOMCode");
            this.col_MediumUOMCode.FieldName = "MediumUOMCode";
            this.col_MediumUOMCode.Name = "col_MediumUOMCode";
            this.col_MediumUOMCode.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_LargeUOMCode
            // 
            resources.ApplyResources(this.col_LargeUOMCode, "col_LargeUOMCode");
            this.col_LargeUOMCode.FieldName = "LargeUOMCode";
            this.col_LargeUOMCode.Name = "col_LargeUOMCode";
            this.col_LargeUOMCode.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_mtrxParentItem
            // 
            resources.ApplyResources(this.col_mtrxParentItem, "col_mtrxParentItem");
            this.col_mtrxParentItem.FieldName = "mtrxParentItem";
            this.col_mtrxParentItem.Name = "col_mtrxParentItem";
            this.col_mtrxParentItem.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_IsPos
            // 
            resources.ApplyResources(this.col_IsPos, "col_IsPos");
            this.col_IsPos.FieldName = "IsPos";
            this.col_IsPos.Name = "col_IsPos";
            // 
            // col_is_libra
            // 
            resources.ApplyResources(this.col_is_libra, "col_is_libra");
            this.col_is_libra.FieldName = "is_libra";
            this.col_is_libra.Name = "col_is_libra";
            // 
            // col_PricingWithSmall
            // 
            resources.ApplyResources(this.col_PricingWithSmall, "col_PricingWithSmall");
            this.col_PricingWithSmall.FieldName = "PricingWithSmall";
            this.col_PricingWithSmall.Name = "col_PricingWithSmall";
            // 
            // col_VariableWeight
            // 
            resources.ApplyResources(this.col_VariableWeight, "col_VariableWeight");
            this.col_VariableWeight.FieldName = "VariableWeight";
            this.col_VariableWeight.Name = "col_VariableWeight";
            // 
            // col_Height
            // 
            resources.ApplyResources(this.col_Height, "col_Height");
            this.col_Height.FieldName = "Height";
            this.col_Height.Name = "col_Height";
            // 
            // col_Width
            // 
            resources.ApplyResources(this.col_Width, "col_Width");
            this.col_Width.FieldName = "Width";
            this.col_Width.Name = "col_Width";
            // 
            // col_Length
            // 
            resources.ApplyResources(this.col_Length, "col_Length");
            this.col_Length.FieldName = "Length";
            this.col_Length.Name = "col_Length";
            // 
            // col_IsOffer
            // 
            resources.ApplyResources(this.col_IsOffer, "col_IsOffer");
            this.col_IsOffer.FieldName = "IsOffer";
            this.col_IsOffer.Name = "col_IsOffer";
            // 
            // repSpin
            // 
            resources.ApplyResources(this.repSpin, "repSpin");
            this.repSpin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repSpin.Name = "repSpin";
            // 
            // rep_Units
            // 
            resources.ApplyResources(this.rep_Units, "rep_Units");
            this.rep_Units.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Units.Buttons"))))});
            this.rep_Units.DisplayMember = "Name";
            this.rep_Units.Name = "rep_Units";
            this.rep_Units.ValueMember = "UnitId";
            // 
            // navBarControl1
            // 
            this.navBarControl1.ActiveGroup = this.NBG_Tasks;
            resources.ApplyResources(this.navBarControl1, "navBarControl1");
            this.navBarControl1.Appearance.Background.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Background.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Button.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Button.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonDisabled.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupBackground.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupBackground.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeader.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderActive.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Hint.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Hint.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Item.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemActive.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemDisabled.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.LinkDropTarget.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.LinkDropTarget.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.NavigationPaneHeader.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.NavigationPaneHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.navBarControl1.Groups.AddRange(new DevExpress.XtraNavBar.NavBarGroup[] {
            this.NBG_Tasks,
            this.NBG_Reports});
            this.navBarControl1.Items.AddRange(new DevExpress.XtraNavBar.NavBarItem[] {
            this.NBI_Itemsales,
            this.NBI_ItemSaleReturn,
            this.NBI_ItemBuy,
            this.NBI_ItemBuyReturn});
            this.navBarControl1.Name = "navBarControl1";
            this.navBarControl1.OptionsNavPane.ExpandedWidth = ((int)(resources.GetObject("resource.ExpandedWidth")));
            // 
            // NBG_Tasks
            // 
            resources.ApplyResources(this.NBG_Tasks, "NBG_Tasks");
            this.NBG_Tasks.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Tasks.Name = "NBG_Tasks";
            // 
            // NBG_Reports
            // 
            resources.ApplyResources(this.NBG_Reports, "NBG_Reports");
            this.NBG_Reports.Expanded = true;
            this.NBG_Reports.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Reports.ItemLinks.AddRange(new DevExpress.XtraNavBar.NavBarItemLink[] {
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_Itemsales),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_ItemSaleReturn),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_ItemBuy),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_ItemBuyReturn)});
            this.NBG_Reports.Name = "NBG_Reports";
            // 
            // NBI_Itemsales
            // 
            resources.ApplyResources(this.NBI_Itemsales, "NBI_Itemsales");
            this.NBI_Itemsales.Name = "NBI_Itemsales";
            this.NBI_Itemsales.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // NBI_ItemSaleReturn
            // 
            resources.ApplyResources(this.NBI_ItemSaleReturn, "NBI_ItemSaleReturn");
            this.NBI_ItemSaleReturn.Name = "NBI_ItemSaleReturn";
            this.NBI_ItemSaleReturn.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // NBI_ItemBuy
            // 
            resources.ApplyResources(this.NBI_ItemBuy, "NBI_ItemBuy");
            this.NBI_ItemBuy.Name = "NBI_ItemBuy";
            this.NBI_ItemBuy.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // NBI_ItemBuyReturn
            // 
            resources.ApplyResources(this.NBI_ItemBuyReturn, "NBI_ItemBuyReturn");
            this.NBI_ItemBuyReturn.Name = "NBI_ItemBuyReturn";
            this.NBI_ItemBuyReturn.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // lblItemsCount
            // 
            resources.ApplyResources(this.lblItemsCount, "lblItemsCount");
            this.lblItemsCount.Name = "lblItemsCount";
            // 
            // chk_ShowMatrixItems
            // 
            resources.ApplyResources(this.chk_ShowMatrixItems, "chk_ShowMatrixItems");
            this.chk_ShowMatrixItems.MenuManager = this.barManager1;
            this.chk_ShowMatrixItems.Name = "chk_ShowMatrixItems";
            this.chk_ShowMatrixItems.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_ShowMatrixItems.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_ShowMatrixItems.Properties.Caption = resources.GetString("chk_ShowMatrixItems.Properties.Caption");
            this.chk_ShowMatrixItems.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_ShowMatrixItems.Properties.GlyphAlignment")));
            this.chk_ShowMatrixItems.CheckedChanged += new System.EventHandler(this.chk_ShowMatrixItems_CheckedChanged);
            // 
            // frm_IC_ItemsList
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.chk_ShowMatrixItems);
            this.Controls.Add(this.lblItemsCount);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.navBarControl1);
            this.Controls.Add(this.grd_Items);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frm_IC_ItemsList";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_IC_ItemsList_FormClosing);
            this.Load += new System.EventHandler(this.frm_IC_ItemsList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Items)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bb)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repChkExpire)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Units)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_ShowMatrixItems.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnOpen;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grd_Items;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpin;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraNavBar.NavBarControl navBarControl1;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Tasks;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Reports;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraNavBar.NavBarItem NBI_Itemsales;
        private DevExpress.XtraNavBar.NavBarItem NBI_ItemSaleReturn;
        private DevExpress.XtraGrid.Columns.GridColumn colItemId;
        private DevExpress.XtraGrid.Columns.GridColumn colItemCode1;
        private DevExpress.XtraGrid.Columns.GridColumn colItemCode2;
        private DevExpress.XtraGrid.Columns.GridColumn colItemNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn colItemNameEn;
        private DevExpress.XtraGrid.Columns.GridColumn colDescription;
        private DevExpress.XtraGrid.Columns.GridColumn Category;
        private DevExpress.XtraGrid.Columns.GridColumn Company;
        
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit bb;
        private DevExpress.XtraNavBar.NavBarItem NBI_ItemBuy;
        private DevExpress.XtraNavBar.NavBarItem NBI_ItemBuyReturn;
        private DevExpress.XtraGrid.Columns.GridColumn colItemType;
        private DevExpress.XtraEditors.LabelControl lblItemsCount;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.Columns.GridColumn colPur;
        private DevExpress.XtraGrid.Columns.GridColumn colSell;
        private DevExpress.XtraGrid.Columns.GridColumn colExpire;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repChkExpire;
        private DevExpress.XtraGrid.Columns.GridColumn col_IsDeleted;
        private DevExpress.XtraGrid.Columns.GridColumn colSalesTaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn colSalesTaxRatio;
        private DevExpress.XtraGrid.Columns.GridColumn colPurchaseTaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn colPurchaseTaxRatio;
        private DevExpress.XtraGrid.Columns.GridColumn colSalesDiscRatio;
        private DevExpress.XtraGrid.Columns.GridColumn colPurchaseDiscRatio;
        private DevExpress.XtraGrid.Columns.GridColumn col_PicPath;
        private DevExpress.XtraGrid.Columns.GridColumn col_WarrantyMonths;
        private DevExpress.XtraGrid.Columns.GridColumn col_MediumUOMCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_LargeUOMCode;
        private DevExpress.XtraEditors.CheckEdit chk_ShowMatrixItems;
        private DevExpress.XtraGrid.Columns.GridColumn col_mtrxParentItem;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckedComboBoxEdit rep_Units;
        private DevExpress.XtraGrid.Columns.GridColumn col_IsPos;
        private DevExpress.XtraGrid.Columns.GridColumn col_is_libra;
        private DevExpress.XtraGrid.Columns.GridColumn col_PricingWithSmall;
        private DevExpress.XtraGrid.Columns.GridColumn col_VariableWeight;
        private DevExpress.XtraGrid.Columns.GridColumn col_IsOffer;
        private DevExpress.XtraGrid.Columns.GridColumn col_Height;
        private DevExpress.XtraGrid.Columns.GridColumn col_Width;
        private DevExpress.XtraGrid.Columns.GridColumn col_Length;
    }
}