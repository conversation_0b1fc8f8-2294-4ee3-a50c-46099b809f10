﻿namespace Pharmacy.Reports
{
    partial class frm_ReportViewer1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_ReportViewer1));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Open = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.navBarControl1 = new DevExpress.XtraNavBar.NavBarControl();
            this.NBG_Dept = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBI_IC = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_SL = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_PR = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_Acc = new DevExpress.XtraNavBar.NavBarItem();
            this.lstBxReports = new DevExpress.XtraEditors.ImageListBoxControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Filter = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_FilterType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_FilterType = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.col_From = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_To = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Date = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.rep_Batch = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.rep_Item1 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_Item2 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit2View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_Customer1 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit3View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_Customer2 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit4View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_Vendor1 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit5View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_Vendor2 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit6View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_Store1 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit7View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_Store2 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit8View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_ItemCateogry1 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit9View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_ItemCateogry2 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit10View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_ItemCompany1 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_ItemCompany2 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_Account = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_CustomAccList = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_CostCenter = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_process = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_User = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.rep_Emp = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.groupControl3 = new DevExpress.XtraEditors.GroupControl();
            this.txtDescription = new DevExpress.XtraEditors.MemoEdit();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem3 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem4 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.simpleLabelItem1 = new DevExpress.XtraLayout.SimpleLabelItem();
            this.simpleLabelItem2 = new DevExpress.XtraLayout.SimpleLabelItem();
            this.simpleLabelItem3 = new DevExpress.XtraLayout.SimpleLabelItem();
            this.emptySpaceItem6 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.linqServerModeSource1 = new DevExpress.Data.Linq.LinqServerModeSource();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstBxReports)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_FilterType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Date)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Date.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Batch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Item1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Item2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit2View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Customer1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit3View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Customer2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit4View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit5View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit6View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Store1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit7View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Store2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit8View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCateogry1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit9View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCateogry2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit10View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCompany1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCompany2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Account)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CustomAccList)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CostCenter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_process)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_User)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Emp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).BeginInit();
            this.groupControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleLabelItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleLabelItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleLabelItem3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.linqServerModeSource1)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowMoveBarOnToolbar = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtn_Open,
            this.barBtn_Help,
            this.barBtnClose});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.Appearance.Options.UseTextOptions = true;
            this.bar1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bar1.BarItemHorzIndent = 9;
            this.bar1.BarItemVertIndent = 0;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(225, 276);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Open),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.DrawSizeGrip = true;
            this.bar1.OptionsBar.MultiLine = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barBtn_Open
            // 
            this.barBtn_Open.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Open, "barBtn_Open");
            this.barBtn_Open.Glyph = global::Pharmacy.Properties.Resources.open;
            this.barBtn_Open.Id = 1;
            this.barBtn_Open.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtn_Open.Name = "barBtn_Open";
            this.barBtn_Open.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Open.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Open_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 28;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.Bar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Bar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenu.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenu.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Tabs.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Tabs.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // navBarControl1
            // 
            this.navBarControl1.ActiveGroup = this.NBG_Dept;
            this.navBarControl1.AllowSelectedLink = true;
            resources.ApplyResources(this.navBarControl1, "navBarControl1");
            this.navBarControl1.Appearance.Background.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Background.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Button.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Button.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonDisabled.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupBackground.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupBackground.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeader.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderActive.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Hint.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Hint.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Item.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemActive.BackColor = System.Drawing.Color.Silver;
            this.navBarControl1.Appearance.ItemActive.Options.UseBackColor = true;
            this.navBarControl1.Appearance.ItemActive.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemDisabled.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.LinkDropTarget.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.LinkDropTarget.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.NavigationPaneHeader.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.NavigationPaneHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.navBarControl1.Groups.AddRange(new DevExpress.XtraNavBar.NavBarGroup[] {
            this.NBG_Dept});
            this.navBarControl1.Items.AddRange(new DevExpress.XtraNavBar.NavBarItem[] {
            this.NBI_IC,
            this.NBI_SL,
            this.NBI_PR,
            this.NBI_Acc});
            this.navBarControl1.Name = "navBarControl1";
            this.navBarControl1.OptionsNavPane.ExpandedWidth = ((int)(resources.GetObject("resource.ExpandedWidth")));
            this.navBarControl1.OptionsNavPane.ShowOverflowButton = false;
            this.navBarControl1.OptionsNavPane.ShowOverflowPanel = false;
            this.navBarControl1.PaintStyleKind = DevExpress.XtraNavBar.NavBarViewKind.NavigationPane;
            this.navBarControl1.TabStop = true;
            // 
            // NBG_Dept
            // 
            resources.ApplyResources(this.NBG_Dept, "NBG_Dept");
            this.NBG_Dept.Expanded = true;
            this.NBG_Dept.GroupClientHeight = 327;
            this.NBG_Dept.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Dept.ItemLinks.AddRange(new DevExpress.XtraNavBar.NavBarItemLink[] {
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_IC),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_SL),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_PR),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_Acc)});
            this.NBG_Dept.Name = "NBG_Dept";
            // 
            // NBI_IC
            // 
            resources.ApplyResources(this.NBI_IC, "NBI_IC");
            this.NBI_IC.Name = "NBI_IC";
            this.NBI_IC.LinkPressed += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_IC_LinkClicked);
            // 
            // NBI_SL
            // 
            resources.ApplyResources(this.NBI_SL, "NBI_SL");
            this.NBI_SL.Name = "NBI_SL";
            this.NBI_SL.LinkPressed += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_SL_LinkClicked);
            // 
            // NBI_PR
            // 
            resources.ApplyResources(this.NBI_PR, "NBI_PR");
            this.NBI_PR.Name = "NBI_PR";
            this.NBI_PR.LinkPressed += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_PR_LinkClicked);
            // 
            // NBI_Acc
            // 
            resources.ApplyResources(this.NBI_Acc, "NBI_Acc");
            this.NBI_Acc.Name = "NBI_Acc";
            this.NBI_Acc.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_Acc_LinkClicked);
            // 
            // lstBxReports
            // 
            this.lstBxReports.Appearance.Options.UseTextOptions = true;
            this.lstBxReports.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.lstBxReports, "lstBxReports");
            this.lstBxReports.HotTrackSelectMode = DevExpress.XtraEditors.HotTrackSelectMode.SelectItemOnClick;
            this.lstBxReports.Name = "lstBxReports";
            this.lstBxReports.SelectedValueChanged += new System.EventHandler(this.lstBxReports_SelectedValueChanged);
            // 
            // groupControl1
            // 
            resources.ApplyResources(this.groupControl1, "groupControl1");
            this.groupControl1.AppearanceCaption.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.groupControl1.AppearanceCaption.Options.UseFont = true;
            this.groupControl1.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl1.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.groupControl1.CaptionLocation = DevExpress.Utils.Locations.Top;
            this.groupControl1.Controls.Add(this.lstBxReports);
            this.groupControl1.Name = "groupControl1";
            // 
            // groupControl2
            // 
            resources.ApplyResources(this.groupControl2, "groupControl2");
            this.groupControl2.AppearanceCaption.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.groupControl2.AppearanceCaption.Options.UseFont = true;
            this.groupControl2.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl2.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.groupControl2.CaptionLocation = DevExpress.Utils.Locations.Top;
            this.groupControl2.Controls.Add(this.gridControl1);
            this.groupControl2.Name = "groupControl2";
            // 
            // gridControl1
            // 
            resources.ApplyResources(this.gridControl1, "gridControl1");
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.MenuManager = this.barManager1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_FilterType,
            this.rep_Date,
            this.rep_Batch,
            this.rep_Item1,
            this.rep_Item2,
            this.rep_Customer1,
            this.rep_Customer2,
            this.rep_Vendor1,
            this.rep_Vendor2,
            this.rep_Store1,
            this.rep_Store2,
            this.rep_ItemCateogry1,
            this.rep_ItemCateogry2,
            this.rep_ItemCompany1,
            this.rep_ItemCompany2,
            this.rep_Account,
            this.rep_CustomAccList,
            this.rep_CostCenter,
            this.rep_process,
            this.rep_User,
            this.rep_Emp});
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Filter,
            this.col_FilterType,
            this.col_From,
            this.col_To});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.CustomRowFilter += new DevExpress.XtraGrid.Views.Base.RowFilterEventHandler(this.gridView1_CustomRowFilter);
            this.gridView1.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView1_CustomColumnDisplayText);
            this.gridView1.CustomRowCellEdit += new DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventHandler(this.gridView1_CustomRowCellEdit);
            this.gridView1.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.gridView1_ShowingEditor);
            // 
            // col_Filter
            // 
            resources.ApplyResources(this.col_Filter, "col_Filter");
            this.col_Filter.FieldName = "Filter";
            this.col_Filter.Name = "col_Filter";
            this.col_Filter.OptionsColumn.AllowEdit = false;
            this.col_Filter.OptionsColumn.AllowFocus = false;
            // 
            // col_FilterType
            // 
            resources.ApplyResources(this.col_FilterType, "col_FilterType");
            this.col_FilterType.ColumnEdit = this.rep_FilterType;
            this.col_FilterType.FieldName = "FilterType";
            this.col_FilterType.Name = "col_FilterType";
            // 
            // rep_FilterType
            // 
            resources.ApplyResources(this.rep_FilterType, "rep_FilterType");
            this.rep_FilterType.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_FilterType.Buttons"))))});
            this.rep_FilterType.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_FilterType.Items"), ((object)(resources.GetObject("rep_FilterType.Items1"))), ((int)(resources.GetObject("rep_FilterType.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_FilterType.Items3"), ((object)(resources.GetObject("rep_FilterType.Items4"))), ((int)(resources.GetObject("rep_FilterType.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_FilterType.Items6"), ((object)(resources.GetObject("rep_FilterType.Items7"))), ((int)(resources.GetObject("rep_FilterType.Items8"))))});
            this.rep_FilterType.Name = "rep_FilterType";
            // 
            // col_From
            // 
            resources.ApplyResources(this.col_From, "col_From");
            this.col_From.FieldName = "From";
            this.col_From.Name = "col_From";
            // 
            // col_To
            // 
            resources.ApplyResources(this.col_To, "col_To");
            this.col_To.FieldName = "To";
            this.col_To.Name = "col_To";
            // 
            // rep_Date
            // 
            resources.ApplyResources(this.rep_Date, "rep_Date");
            this.rep_Date.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Date.Buttons"))))});
            this.rep_Date.Name = "rep_Date";
            this.rep_Date.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            // 
            // rep_Batch
            // 
            resources.ApplyResources(this.rep_Batch, "rep_Batch");
            this.rep_Batch.Name = "rep_Batch";
            // 
            // rep_Item1
            // 
            resources.ApplyResources(this.rep_Item1, "rep_Item1");
            this.rep_Item1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Item1.Buttons"))))});
            this.rep_Item1.Name = "rep_Item1";
            this.rep_Item1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Item1.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_Item2
            // 
            resources.ApplyResources(this.rep_Item2, "rep_Item2");
            this.rep_Item2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Item2.Buttons"))))});
            this.rep_Item2.Name = "rep_Item2";
            this.rep_Item2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Item2.View = this.repositoryItemGridLookUpEdit2View;
            // 
            // repositoryItemGridLookUpEdit2View
            // 
            this.repositoryItemGridLookUpEdit2View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn6,
            this.gridColumn7,
            this.gridColumn8});
            this.repositoryItemGridLookUpEdit2View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit2View.Name = "repositoryItemGridLookUpEdit2View";
            this.repositoryItemGridLookUpEdit2View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit2View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_Customer1
            // 
            resources.ApplyResources(this.rep_Customer1, "rep_Customer1");
            this.rep_Customer1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Customer1.Buttons"))))});
            this.rep_Customer1.Name = "rep_Customer1";
            this.rep_Customer1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Customer1.View = this.repositoryItemGridLookUpEdit3View;
            // 
            // repositoryItemGridLookUpEdit3View
            // 
            this.repositoryItemGridLookUpEdit3View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12});
            this.repositoryItemGridLookUpEdit3View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit3View.Name = "repositoryItemGridLookUpEdit3View";
            this.repositoryItemGridLookUpEdit3View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit3View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_Customer2
            // 
            resources.ApplyResources(this.rep_Customer2, "rep_Customer2");
            this.rep_Customer2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Customer2.Buttons"))))});
            this.rep_Customer2.Name = "rep_Customer2";
            this.rep_Customer2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Customer2.View = this.repositoryItemGridLookUpEdit4View;
            // 
            // repositoryItemGridLookUpEdit4View
            // 
            this.repositoryItemGridLookUpEdit4View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn16});
            this.repositoryItemGridLookUpEdit4View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit4View.Name = "repositoryItemGridLookUpEdit4View";
            this.repositoryItemGridLookUpEdit4View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit4View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_Vendor1
            // 
            resources.ApplyResources(this.rep_Vendor1, "rep_Vendor1");
            this.rep_Vendor1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Vendor1.Buttons"))))});
            this.rep_Vendor1.Name = "rep_Vendor1";
            this.rep_Vendor1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Vendor1.View = this.repositoryItemGridLookUpEdit5View;
            // 
            // repositoryItemGridLookUpEdit5View
            // 
            this.repositoryItemGridLookUpEdit5View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit5View.Name = "repositoryItemGridLookUpEdit5View";
            this.repositoryItemGridLookUpEdit5View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit5View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_Vendor2
            // 
            resources.ApplyResources(this.rep_Vendor2, "rep_Vendor2");
            this.rep_Vendor2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Vendor2.Buttons"))))});
            this.rep_Vendor2.Name = "rep_Vendor2";
            this.rep_Vendor2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Vendor2.View = this.repositoryItemGridLookUpEdit6View;
            // 
            // repositoryItemGridLookUpEdit6View
            // 
            this.repositoryItemGridLookUpEdit6View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit6View.Name = "repositoryItemGridLookUpEdit6View";
            this.repositoryItemGridLookUpEdit6View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit6View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_Store1
            // 
            resources.ApplyResources(this.rep_Store1, "rep_Store1");
            this.rep_Store1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Store1.Buttons"))))});
            this.rep_Store1.Name = "rep_Store1";
            this.rep_Store1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Store1.View = this.repositoryItemGridLookUpEdit7View;
            // 
            // repositoryItemGridLookUpEdit7View
            // 
            this.repositoryItemGridLookUpEdit7View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit7View.Name = "repositoryItemGridLookUpEdit7View";
            this.repositoryItemGridLookUpEdit7View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit7View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_Store2
            // 
            resources.ApplyResources(this.rep_Store2, "rep_Store2");
            this.rep_Store2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Store2.Buttons"))))});
            this.rep_Store2.Name = "rep_Store2";
            this.rep_Store2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Store2.View = this.repositoryItemGridLookUpEdit8View;
            // 
            // repositoryItemGridLookUpEdit8View
            // 
            this.repositoryItemGridLookUpEdit8View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit8View.Name = "repositoryItemGridLookUpEdit8View";
            this.repositoryItemGridLookUpEdit8View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit8View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_ItemCateogry1
            // 
            resources.ApplyResources(this.rep_ItemCateogry1, "rep_ItemCateogry1");
            this.rep_ItemCateogry1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_ItemCateogry1.Buttons"))))});
            this.rep_ItemCateogry1.Name = "rep_ItemCateogry1";
            this.rep_ItemCateogry1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_ItemCateogry1.View = this.repositoryItemGridLookUpEdit9View;
            // 
            // repositoryItemGridLookUpEdit9View
            // 
            this.repositoryItemGridLookUpEdit9View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit9View.Name = "repositoryItemGridLookUpEdit9View";
            this.repositoryItemGridLookUpEdit9View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit9View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_ItemCateogry2
            // 
            resources.ApplyResources(this.rep_ItemCateogry2, "rep_ItemCateogry2");
            this.rep_ItemCateogry2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_ItemCateogry2.Buttons"))))});
            this.rep_ItemCateogry2.Name = "rep_ItemCateogry2";
            this.rep_ItemCateogry2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_ItemCateogry2.View = this.repositoryItemGridLookUpEdit10View;
            // 
            // repositoryItemGridLookUpEdit10View
            // 
            this.repositoryItemGridLookUpEdit10View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit10View.Name = "repositoryItemGridLookUpEdit10View";
            this.repositoryItemGridLookUpEdit10View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit10View.OptionsView.ShowGroupPanel = false;
            // 
            // rep_ItemCompany1
            // 
            resources.ApplyResources(this.rep_ItemCompany1, "rep_ItemCompany1");
            this.rep_ItemCompany1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_ItemCompany1.Buttons"))))});
            this.rep_ItemCompany1.Name = "rep_ItemCompany1";
            this.rep_ItemCompany1.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_ItemCompany1.View = this.gridView2;
            // 
            // gridView2
            // 
            this.gridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // rep_ItemCompany2
            // 
            resources.ApplyResources(this.rep_ItemCompany2, "rep_ItemCompany2");
            this.rep_ItemCompany2.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_ItemCompany2.Buttons"))))});
            this.rep_ItemCompany2.Name = "rep_ItemCompany2";
            this.rep_ItemCompany2.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_ItemCompany2.View = this.gridView3;
            // 
            // gridView3
            // 
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // rep_Account
            // 
            resources.ApplyResources(this.rep_Account, "rep_Account");
            this.rep_Account.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Account.Buttons"))))});
            this.rep_Account.Name = "rep_Account";
            this.rep_Account.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Account.View = this.gridView4;
            // 
            // gridView4
            // 
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            // 
            // rep_CustomAccList
            // 
            resources.ApplyResources(this.rep_CustomAccList, "rep_CustomAccList");
            this.rep_CustomAccList.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_CustomAccList.Buttons"))))});
            this.rep_CustomAccList.Name = "rep_CustomAccList";
            this.rep_CustomAccList.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_CustomAccList.View = this.gridView5;
            // 
            // gridView5
            // 
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            // 
            // rep_CostCenter
            // 
            resources.ApplyResources(this.rep_CostCenter, "rep_CostCenter");
            this.rep_CostCenter.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_CostCenter.Buttons"))))});
            this.rep_CostCenter.Name = "rep_CostCenter";
            this.rep_CostCenter.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_CostCenter.View = this.gridView6;
            // 
            // gridView6
            // 
            this.gridView6.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            // 
            // rep_process
            // 
            resources.ApplyResources(this.rep_process, "rep_process");
            this.rep_process.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_process.Buttons"))))});
            this.rep_process.Name = "rep_process";
            this.rep_process.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_process.View = this.gridView7;
            // 
            // gridView7
            // 
            this.gridView7.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            // 
            // rep_User
            // 
            resources.ApplyResources(this.rep_User, "rep_User");
            this.rep_User.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_User.Buttons"))))});
            this.rep_User.Name = "rep_User";
            this.rep_User.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_User.View = this.gridView8;
            // 
            // gridView8
            // 
            this.gridView8.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView8.Name = "gridView8";
            this.gridView8.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView8.OptionsView.ShowGroupPanel = false;
            // 
            // rep_Emp
            // 
            resources.ApplyResources(this.rep_Emp, "rep_Emp");
            this.rep_Emp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Emp.Buttons"))))});
            this.rep_Emp.Name = "rep_Emp";
            this.rep_Emp.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Emp.View = this.gridView9;
            // 
            // gridView9
            // 
            this.gridView9.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView9.Name = "gridView9";
            this.gridView9.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView9.OptionsView.ShowGroupPanel = false;
            // 
            // groupControl3
            // 
            this.groupControl3.AppearanceCaption.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.groupControl3.AppearanceCaption.Options.UseFont = true;
            this.groupControl3.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl3.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.groupControl3.CaptionLocation = DevExpress.Utils.Locations.Top;
            this.groupControl3.Controls.Add(this.txtDescription);
            resources.ApplyResources(this.groupControl3, "groupControl3");
            this.groupControl3.Name = "groupControl3";
            // 
            // txtDescription
            // 
            resources.ApplyResources(this.txtDescription, "txtDescription");
            this.txtDescription.MenuManager = this.barManager1;
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.TabStop = false;
            // 
            // emptySpaceItem1
            // 
            resources.ApplyResources(this.emptySpaceItem1, "emptySpaceItem1");
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 102);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(508, 18);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem3
            // 
            resources.ApplyResources(this.emptySpaceItem3, "emptySpaceItem3");
            this.emptySpaceItem3.Location = new System.Drawing.Point(0, 110);
            this.emptySpaceItem3.Name = "emptySpaceItem3";
            this.emptySpaceItem3.Size = new System.Drawing.Size(508, 10);
            this.emptySpaceItem3.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem4
            // 
            resources.ApplyResources(this.emptySpaceItem4, "emptySpaceItem4");
            this.emptySpaceItem4.Location = new System.Drawing.Point(442, 92);
            this.emptySpaceItem4.Name = "emptySpaceItem4";
            this.emptySpaceItem4.Size = new System.Drawing.Size(66, 28);
            this.emptySpaceItem4.TextSize = new System.Drawing.Size(0, 0);
            // 
            // simpleLabelItem1
            // 
            resources.ApplyResources(this.simpleLabelItem1, "simpleLabelItem1");
            this.simpleLabelItem1.Location = new System.Drawing.Point(397, 92);
            this.simpleLabelItem1.Name = "simpleLabelItem1";
            this.simpleLabelItem1.Size = new System.Drawing.Size(111, 28);
            this.simpleLabelItem1.TextSize = new System.Drawing.Size(31, 13);
            // 
            // simpleLabelItem2
            // 
            resources.ApplyResources(this.simpleLabelItem2, "simpleLabelItem2");
            this.simpleLabelItem2.Location = new System.Drawing.Point(397, 92);
            this.simpleLabelItem2.Name = "simpleLabelItem2";
            this.simpleLabelItem2.Size = new System.Drawing.Size(111, 28);
            this.simpleLabelItem2.TextSize = new System.Drawing.Size(31, 13);
            // 
            // simpleLabelItem3
            // 
            resources.ApplyResources(this.simpleLabelItem3, "simpleLabelItem3");
            this.simpleLabelItem3.Location = new System.Drawing.Point(254, 92);
            this.simpleLabelItem3.Name = "simpleLabelItem3";
            this.simpleLabelItem3.Size = new System.Drawing.Size(254, 28);
            this.simpleLabelItem3.TextSize = new System.Drawing.Size(31, 13);
            // 
            // emptySpaceItem6
            // 
            resources.ApplyResources(this.emptySpaceItem6, "emptySpaceItem6");
            this.emptySpaceItem6.Location = new System.Drawing.Point(361, 115);
            this.emptySpaceItem6.Name = "emptySpaceItem6";
            this.emptySpaceItem6.Size = new System.Drawing.Size(10, 23);
            this.emptySpaceItem6.TextSize = new System.Drawing.Size(0, 0);
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "ItemCode1";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.FieldName = "ItemCode2";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "ItemNameAr";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "ItemNameEn";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // gridColumn5
            // 
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.Name = "gridColumn5";
            // 
            // gridColumn6
            // 
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.Name = "gridColumn6";
            // 
            // gridColumn7
            // 
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.Name = "gridColumn7";
            // 
            // gridColumn8
            // 
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.Name = "gridColumn8";
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "CusCode";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn10
            // 
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.FieldName = "CusNameAr";
            this.gridColumn10.Name = "gridColumn10";
            // 
            // gridColumn11
            // 
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.Name = "gridColumn11";
            // 
            // gridColumn12
            // 
            resources.ApplyResources(this.gridColumn12, "gridColumn12");
            this.gridColumn12.FieldName = "GroupId";
            this.gridColumn12.Name = "gridColumn12";
            // 
            // gridColumn13
            // 
            resources.ApplyResources(this.gridColumn13, "gridColumn13");
            this.gridColumn13.FieldName = "CusCode";
            this.gridColumn13.Name = "gridColumn13";
            // 
            // gridColumn14
            // 
            resources.ApplyResources(this.gridColumn14, "gridColumn14");
            this.gridColumn14.FieldName = "CusNameAr";
            this.gridColumn14.Name = "gridColumn14";
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.Name = "gridColumn15";
            // 
            // gridColumn16
            // 
            resources.ApplyResources(this.gridColumn16, "gridColumn16");
            this.gridColumn16.FieldName = "GroupId";
            this.gridColumn16.Name = "gridColumn16";
            // 
            // frm_ReportViewer1
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupControl3);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.navBarControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.Name = "frm_ReportViewer1";
            this.Load += new System.EventHandler(this.frm_ReportViewer_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstBxReports)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_FilterType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Date.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Date)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Batch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Item1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Item2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit2View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Customer1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit3View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Customer2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit4View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit5View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit6View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Store1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit7View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Store2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit8View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCateogry1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit9View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCateogry2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit10View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCompany1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCompany2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Account)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CustomAccList)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CostCenter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_process)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_User)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Emp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl3)).EndInit();
            this.groupControl3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleLabelItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleLabelItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleLabelItem3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.linqServerModeSource1)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtn_Open;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraNavBar.NavBarControl navBarControl1;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Dept;
        private DevExpress.XtraNavBar.NavBarItem NBI_IC;
        private DevExpress.XtraNavBar.NavBarItem NBI_SL;
        private DevExpress.XtraNavBar.NavBarItem NBI_PR;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.ImageListBoxControl lstBxReports;
        private DevExpress.XtraEditors.GroupControl groupControl3;
        private DevExpress.XtraEditors.MemoEdit txtDescription;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem4;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem3;
        private DevExpress.XtraLayout.SimpleLabelItem simpleLabelItem1;
        private DevExpress.XtraLayout.SimpleLabelItem simpleLabelItem2;
        private DevExpress.XtraLayout.SimpleLabelItem simpleLabelItem3;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem6;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.Data.Linq.LinqServerModeSource linqServerModeSource1;
        private DevExpress.XtraNavBar.NavBarItem NBI_Acc;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn col_Filter;
        private DevExpress.XtraGrid.Columns.GridColumn col_FilterType;
        private DevExpress.XtraGrid.Columns.GridColumn col_From;
        private DevExpress.XtraGrid.Columns.GridColumn col_To;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox rep_FilterType;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit rep_Date;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit rep_Batch;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Item1;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Item2;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit2View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Customer1;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit3View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Customer2;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit4View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Vendor1;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit5View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Vendor2;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit6View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Store1;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit7View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Store2;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit8View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_ItemCateogry1;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit9View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_ItemCateogry2;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit10View;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_ItemCompany1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_ItemCompany2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Account;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_CustomAccList;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_CostCenter;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_process;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_User;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Emp;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
    }
}