﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_SL_Add : DevExpress.XtraReports.UI.XtraReport
    {
        string customer, serial, number, date, store, paymethod, drawer, notes,
            total, tax, discountR, discountV, expenses, net, paied, remains, userName, DeductTaxV, AddTaxV
            , DriverName, VehicleNumber, Destination, scalWeightSerial, addtax, custtax, Handing, BalanceBefore, BalanceAfter, TotalETax;

        DataTable dt_inv_details;
        DataTable dt_weights;
        DataTable dt_subTaxes;
        int currId;
        ERPDataContext DB = new ERPDataContext();

        public rpt_SL_Add()
        {
            InitializeComponent();
        }
        public rpt_SL_Add(string _customer, string _serial, string _number, string _date, string _store, string _paymethod,
            string _drawer, string _notes, string _total, string _tax, string _discountR, string _discountV, string _expenses, string _net, string _paied, string _remains,
            DataTable dt, string userName, string _DeductTaxV, string _AddTaxV, int _currId, string DriverName, string VehicleNumber,
            string Destination, string scalWeightSerial, string _addtax, string _custtax,string _Handing,string _BalanceBefore,string _BalanceAfter, DataTable _dt_weights, DataTable dt_PrintTableSubTaxDetails, string TotalETax)
        {
            InitializeComponent();            
            customer=_customer;
            serial=_serial;
            number=_number; 
            date=_date;
            store=_store;
            paymethod=_paymethod;
            drawer=_drawer;
            notes=_notes;
            total=_total; 
            tax=_tax;
            discountR = _discountR;
            discountV = _discountV;
            expenses=_expenses;
            net=_net;
            paied = _paied;
            remains=_remains;
            this.userName = userName;
            addtax = _addtax;
            custtax = _custtax;
            DeductTaxV = _DeductTaxV;
            AddTaxV = _AddTaxV;

            this.DriverName = DriverName;
            this.VehicleNumber = VehicleNumber;
            this.Destination = Destination;
            this.scalWeightSerial = scalWeightSerial;
            Handing = _Handing;

            this.BalanceBefore = _BalanceBefore;
            this.BalanceAfter = _BalanceAfter;

            currId = _currId;

            dt_inv_details = dt;
            dt_weights = _dt_weights;
          //  this.DataSource = dt_inv_details;
            dt_subTaxes = dt_PrintTableSubTaxDetails;
            this.TotalETax = TotalETax;
            //getReportHeader();
            //LoadData();            
        }        

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;                
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }            
        }

        public void LoadData()
        {
            lbl_date.Text = date;
            lbl_DiscountR.Text = discountR;
            lbl_DiscountV.Text = discountV;
            lbl_Drawer.Text = drawer;
            lbl_ExpensesV.Text = expenses;
            lbl_Net.Text = net;
            lbl_notes.Text = notes;
            lbl_Number.Text = number;
            lbl_Paied.Text = paied;
            lbl_Paymethod.Text = paymethod;
            lbl_Remains.Text = remains;
            lbl_Serial.Text = serial;
            lbl_store.Text = store;
            lbl_TaxV.Text = tax;
            lbl_Total.Text = total;
            lbl_Customer.Text = customer;
            xrLabcus.Text = custtax;
            xrLabaddtax.Text = addtax;
            lblTotalWords.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(net, currId, Shared.lstCurrency) :
                                   HelperAcc.ConvertMoneyToArabicText(net, currId, Shared.lstCurrency);
            lbl_User.Text = userName;
            lbl_DeductTaxV.Text = DeductTaxV;
            lbl_AddTaxV.Text = AddTaxV;

            lbl_DriverName.Text = DriverName;
            lbl_VehicleNumber.Text = VehicleNumber;
            lbl_Destination.Text = Destination;
            lbl_ScaleWeightSerial.Text = scalWeightSerial;
            txt_Handing.Text = Handing;

            lbl_BalanceBefore.Text = BalanceBefore;
            lbl_BalanceAfter.Text = BalanceAfter;
            ERPDataContext db = new ERPDataContext();
            var _customer = db.SL_Customers.Where(x => x.CusNameAr == customer).FirstOrDefault();
            //if (_customer != null)
            //{
            //    lbl_Cust_Address.Text = _customer.Address;
            //    lbl_Cust_Tel.Text = _customer.Tel;
            //    lbl_Cust_Mobile.Text = _customer.Mobile;
            //    lbl_Neighborhood.Text = _customer.Neighborhood;
            //    lbl_Street.Text = _customer.Street;
            //}

            var companyName = "";
            var CompanyTaxNumber = "";
            var company = db.ST_CompanyInfos.Where(a => a.Company_Id == Shared.st_comp.Company_Id).FirstOrDefault();
            if (company != null)
            {
                companyName = Shared.IsEnglish ? company.CmpNameEn : company.CmpNameAr;
                CompanyTaxNumber = company.TaxCard;
                //lbl_CompanyTaxNumber.Text = company.TaxCard;
                //lbl_CommercialBook.Text = company.CommercialBook;
            }
            qrCode.Text =
                "Seller Name     : " + companyName + System.Environment.NewLine +
                "Tax Reg. Number : " + CompanyTaxNumber + System.Environment.NewLine +
                "Customer        : " + _customer.CusNameAr + System.Environment.NewLine +
                "Customer Tax No.: " + _customer.TaxCardNumber + System.Environment.NewLine +
                "Invoice Date    : " + Convert.ToDateTime(date).ToShortDateString() + System.Environment.NewLine +
                "Total Amount    : " + total + System.Environment.NewLine +
                "VAT             : " + TotalETax + System.Environment.NewLine +
                "Net             : " + net;
            //this.DataSource = dt_inv_details;
            lbl_TotalETax.Text = TotalETax;
            decimal totalQty = 0;
            decimal totalP = 0;
            decimal total_Packs = 0;
            //totalQty = dt_inv_details.Compute("Sum(Qty)", string.Empty);
            foreach (DataRow row in dt_inv_details.Rows)
            {
                totalQty += Convert.ToDecimal(row["Qty"]);
                totalP += Convert.ToDecimal(row["PiecesCount"]);

                if (row["Pack"] != null && row["Pack"] != DBNull.Value)
                    total_Packs += Convert.ToDecimal(row["Pack"]);
            }

            lbl_TotalQty.Text = totalQty.ToString();
            lbl_totalPieces.Text = totalP.ToString();
            lbl_TotalPacks.Text = total_Packs.ToString();
            var sl_return = DB.SL_Returns.FirstOrDefault(s => s.ReturnCode == number);
            string updated = sl_return!=null?(sl_return.LastUpdateDate != null ? "معدل" : ""):"";
            lbl_Updated.Text = updated;
            DetailReport.DataSource = dt_inv_details;
            cell_code.DataBindings.Add("Text", DetailReport.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", DetailReport.DataSource, "ItemCode2");
            cell_Disc.DataBindings.Add("Text", DetailReport.DataSource, "DiscountValue");
            cell_Expire.DataBindings.Add("Text", DetailReport.DataSource, "Expire");
            cell_Batch.DataBindings.Add("Text", DetailReport.DataSource, "Batch");
            cell_Price.DataBindings.Add("Text", DetailReport.DataSource, "SellPrice");
            cell_Qty.DataBindings.Add("Text", DetailReport.DataSource, "Qty");
            cell_Total.DataBindings.Add("Text", DetailReport.DataSource, "TotalSellPrice");
            cell_ItemName.DataBindings.Add("Text", DetailReport.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", DetailReport.DataSource, "UOM");
            cell_Height.DataBindings.Add("Text", DetailReport.DataSource, "Height");
            cell_Width.DataBindings.Add("Text", DetailReport.DataSource, "Width");
            cell_Length.DataBindings.Add("Text", DetailReport.DataSource, "Length");
            cell_TotalQty.DataBindings.Add("Text", DetailReport.DataSource, "TotalQty");
            cell_DiscountRatio.DataBindings.Add("Text", DetailReport.DataSource, "DiscountRatio");
            cell_DiscountRatio2.DataBindings.Add("Text", DetailReport.DataSource, "DiscountRatio2");
            cell_DiscountRatio3.DataBindings.Add("Text", DetailReport.DataSource, "DiscountRatio3");
            cell_SalesTaxRatio.DataBindings.Add("Text", DetailReport.DataSource, "SalesTaxRatio");
            cell_SalesTax.DataBindings.Add("Text", DetailReport.DataSource, "SalesTax");
            cell_Serial.DataBindings.Add("Text", DetailReport.DataSource, "Serial");
            cell_ManufactureDate.DataBindings.Add("Text", DetailReport.DataSource, "ManufactureDate");
            cell_ItemDescription.DataBindings.Add("Text", DetailReport.DataSource, "ItemDescription");
            cell_Factor.DataBindings.Add("Text", DetailReport.DataSource, "Factor");
            Cell_MUOM.DataBindings.Add("Text", DetailReport.DataSource, "MUOM");
            Cell_MUOM_Factor.DataBindings.Add("Text", DetailReport.DataSource, "MUOM_Factor");
            cell_Pack.DataBindings.Add("Text", DetailReport.DataSource, "Pack");
            cell_PiecesCount.DataBindings.Add("Text", DetailReport.DataSource, "PiecesCount");

            cell_addTaxValue.DataBindings.Add("Text", DetailReport.DataSource, "addTaxValue");
            cell_tableTaxValue.DataBindings.Add("Text", DetailReport.DataSource, "tableTaxValue");
            cell_bonusDiscount.DataBindings.Add("Text", DetailReport.DataSource, "bonusDiscount");
            //==============subTaxes=====================//
            SubTaxDetails.DataSource = dt_subTaxes;
            cell_subtaxId.DataBindings.Add("Text", SubTaxDetails.DataSource, "SubTaxId");
            cell_Value.DataBindings.Add("Text", SubTaxDetails.DataSource, "Value");
            cell_Rate.DataBindings.Add("Text", SubTaxDetails.DataSource, "Rate");

            if (dt_weights.Rows.Count > 0)
                xrSubreport2.ReportSource = new rpt_multiple_weights(dt_weights);

            getReportHeader();
        }

    }
}
