﻿namespace Reports
{
    partial class uc_Currency_Reports
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(uc_Currency_Reports));
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            this.lkp_Crnc = new DevExpress.XtraEditors.LookUpEdit();
            this.txtRate = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Crnc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRate.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // lkp_Crnc
            // 
            resources.ApplyResources(this.lkp_Crnc, "lkp_Crnc");
            this.lkp_Crnc.EnterMoveNextControl = true;
            this.lkp_Crnc.Name = "lkp_Crnc";
            this.lkp_Crnc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Crnc.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("lkp_Crnc.Properties.AppearanceDisabled.BackColor")));
            this.lkp_Crnc.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("lkp_Crnc.Properties.AppearanceDisabled.ForeColor")));
            this.lkp_Crnc.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.lkp_Crnc.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.lkp_Crnc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Crnc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Crnc.Properties.Buttons"))))});
            this.lkp_Crnc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Crnc.Properties.Columns"), resources.GetString("lkp_Crnc.Properties.Columns1"), ((int)(resources.GetObject("lkp_Crnc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Crnc.Properties.Columns3"))), resources.GetString("lkp_Crnc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Crnc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Crnc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Crnc.Properties.Columns7"), resources.GetString("lkp_Crnc.Properties.Columns8"), ((int)(resources.GetObject("lkp_Crnc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Crnc.Properties.Columns10"))), resources.GetString("lkp_Crnc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_Crnc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Crnc.Properties.Columns13"))))});
            this.lkp_Crnc.Properties.NullText = resources.GetString("lkp_Crnc.Properties.NullText");
            this.lkp_Crnc.EditValueChanged += new System.EventHandler(this.lkp_Crnc_EditValueChanged);
            this.lkp_Crnc.Modified += new System.EventHandler(this.data_Modified);
            // 
            // txtRate
            // 
            resources.ApplyResources(this.txtRate, "txtRate");
            this.txtRate.Name = "txtRate";
            this.txtRate.Properties.Appearance.Options.UseTextOptions = true;
            this.txtRate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.txtRate.Properties.AppearanceReadOnly.BackColor = ((System.Drawing.Color)(resources.GetObject("txtRate.Properties.AppearanceReadOnly.BackColor")));
            this.txtRate.Properties.AppearanceReadOnly.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtRate.Properties.AppearanceReadOnly.ForeColor")));
            this.txtRate.Properties.AppearanceReadOnly.Options.UseBackColor = true;
            this.txtRate.Properties.AppearanceReadOnly.Options.UseForeColor = true;
            this.txtRate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txtRate.Properties.Buttons"))), resources.GetString("txtRate.Properties.Buttons1"), ((int)(resources.GetObject("txtRate.Properties.Buttons2"))), ((bool)(resources.GetObject("txtRate.Properties.Buttons3"))), ((bool)(resources.GetObject("txtRate.Properties.Buttons4"))), ((bool)(resources.GetObject("txtRate.Properties.Buttons5"))), ((DevExpress.XtraEditors.ImageLocation)(resources.GetObject("txtRate.Properties.Buttons6"))), ((System.Drawing.Image)(resources.GetObject("txtRate.Properties.Buttons7"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, resources.GetString("txtRate.Properties.Buttons8"), ((object)(resources.GetObject("txtRate.Properties.Buttons9"))), ((DevExpress.Utils.SuperToolTip)(resources.GetObject("txtRate.Properties.Buttons10"))), ((bool)(resources.GetObject("txtRate.Properties.Buttons11"))))});
            this.txtRate.Properties.HideSelection = false;
            this.txtRate.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtRate.Properties.Mask.EditMask = resources.GetString("txtRate.Properties.Mask.EditMask");
            this.txtRate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtRate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtRate.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.txtRate.Properties.ReadOnly = true;
            this.txtRate.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtRate_Spin);
            this.txtRate.Modified += new System.EventHandler(this.data_Modified);
            // 
            // uc_Currency_Reports
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.txtRate);
            this.Controls.Add(this.lkp_Crnc);
            this.Name = "uc_Currency_Reports";
            this.Load += new System.EventHandler(this.uc_LinkAccount_Load);
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Crnc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRate.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        public DevExpress.XtraEditors.LookUpEdit lkp_Crnc;
        public DevExpress.XtraEditors.SpinEdit txtRate;
    }
}
