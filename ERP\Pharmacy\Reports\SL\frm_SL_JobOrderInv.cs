﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_JobOrderInv: DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, store_id1, store_id2, companyId;
        string categoryNum;
        int Priority, Status, Dept, SalesEmp;
        byte FltrTyp_item, FltrTyp_Store, FltrTyp_Company, FltrTyp_Category;
        byte? itemType;
        List<int> lstStores = new List<int>();

        public frm_SL_JobOrderInv(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Store, int store_id1, int store_id2,
            byte fltrTyp_company, int companyId,
            byte fltrTyp_category, string  categoryNum,
            byte fltrTyp_item, int itemId1, int itemId2,
            int Priority, int Status, int Dept, int SalesEmp, byte? itemType)
        {                       
            //UserCanOpen = LoadPrivilege();
            //if (UserCanOpen == false)
            //    return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Store = fltrTyp_Store;
            this.FltrTyp_Company = fltrTyp_company;
            this.FltrTyp_Category = fltrTyp_category;
            this.FltrTyp_item = fltrTyp_item;


            this.itemId1 = itemId1;
            this.itemId2 = itemId2;
            this.store_id1 = store_id1;
            this.store_id2 = store_id2;
            this.companyId = companyId;
            this.categoryNum = categoryNum;

            this.Priority = Priority;
            this.Status = Status;
            this.Dept = Dept;
            this.SalesEmp = SalesEmp;            
            this.itemType = itemType;

            getReportHeader();

            ERPDataContext DB = new ERPDataContext();

            rep_Status.DataSource = DB.JO_Status.ToList();
            rep_Status.ValueMember = "StatusId";
            rep_Status.DisplayMember = "Status";

            rep_Priority.DataSource = DB.JO_Priorities.ToList();
            rep_Priority.ValueMember = "PriorityId";
            rep_Priority.DisplayMember = "Priority";

            rep_Dept.DataSource = DB.JO_Depts.ToList();
            rep_Dept.ValueMember = "DeptId";
            rep_Dept.DisplayMember = "Department";

            rep_cat.DataSource = DB.IC_Categories.                
                Select(x => new { x.CategoryId, x.CategoryNameAr}).ToList();
            rep_cat.ValueMember = "CategoryId";
            rep_cat.DisplayMember = "CategoryNameAr";

            rep_comp.DataSource = DB.IC_Companies.
               Select(x => new { x.CompanyId, x.CompanyNameAr}).ToList();
            rep_comp.ValueMember = "CompanyId";
            rep_comp.DisplayMember = "CompanyNameAr";

            rep_Customer.DataSource = DB.SL_Customers.
               Select(x => new { x.CustomerId, x.CusNameAr}).ToList();
            rep_Customer.ValueMember = "CustomerId";
            rep_Customer.DisplayMember = "CusNameAr";

            rep_EmpId.DataSource = DB.HR_Employees.
               Select(x => new { x.EmpId, x.EmpName}).ToList();
            rep_EmpId.ValueMember = "EmpId";
            rep_EmpId.DisplayMember = "EmpName";            

            LoadData();
            ReportsUtils.ColumnChooser(grdCategory);     
        }

        
        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }
        
        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var stores = DB.IC_Stores.ToList();

            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= store_id2 && store.StoreId >= store_id1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (store_id1 > 0 && (store.StoreId == store_id1 || store.ParentId == store_id1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }

            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var data = (from s in DB.SL_Invoices
                        where lstStores.Count > 0 ? lstStores.Contains(s.StoreId) : true
                        join j in DB.JO_JobOrders
                        on s.JobOrderId equals j.JobOrderId
                        join sd in DB.SL_InvoiceDetails
                        on s.SL_InvoiceId equals sd.SL_InvoiceId
                        join i in DB.IC_Items
                        on sd.ItemId equals i.ItemId
                        join ic in DB.IC_Categories on i.Category equals ic.CategoryId
                        where defaultCategories.Count() > 0 ? defaultCategories.Contains(ic.CategoryId) : true
                        from cm in DB.HR_EmpItemsCommisions.Where(a => a.ItemId == i.ItemId && a.EmpId == s.SalesEmpId).DefaultIfEmpty()

                        where FltrTyp_item == 1 ? sd.ItemId == itemId1 : true
                        where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                        sd.ItemId >= itemId1 && sd.ItemId <= itemId2 : true
                        where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                        sd.ItemId >= itemId1 : true
                        where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                        sd.ItemId <= itemId2 : true                        

                        where Priority == 0 ? true : j.Priority == Priority
                        where Status == 0 ? true : j.Status == Status
                        where Dept == 0 ? true : j.Dept == Dept
                        where SalesEmp == 0 ? true : s.SalesEmpId == SalesEmp
                        where itemType == null ? true : i.ItemType == itemType
                        join g in DB.IC_Categories on i.Category equals g.CategoryId
                        where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true
                        where FltrTyp_Company == 1 ? i.Company == companyId : true
                        
                        select new
                        {                           
                           j.JOCode,
                           j.Job,
                           j.DeliveryDate,
                           j.Priority,
                           j.Status,
                           j.Dept,
                           s.InvoiceCode,
                           s.InvoiceDate,
                           s.CustomerId,
                           s.SalesEmpId,
                           Total = s.Net+s.DiscountValue,
                           s.DiscountRatio,
                           s.DiscountValue,
                           i.ItemCode1,
                           i.ItemNameAr,
                           i.ItemType,
                           i.Category,
                           i.Company,
                           i.PurchasePrice,
                           sd.Qty,
                           sd.SellPrice,
                           sd.TotalSellPrice,
                           QtyMoreThan = cm == null ? 0 : cm.QtyMoreThan,
                           CmmAmount = cm == null ? 0 : cm.Amount,
                           CmmProfitRatio = cm == null ? 0 : cm.ProfitRatio
                        }).ToList();

            var data2 = (from d in data
                         select new
                         {
                             d.JOCode,
                             d.Job,
                             d.DeliveryDate,
                             d.Priority,
                             d.Status,
                             d.Dept,
                             d.InvoiceCode,
                             d.InvoiceDate,
                             d.CustomerId,
                             d.SalesEmpId,
                             d.Total,
                             d.DiscountRatio,
                             d.DiscountValue,
                             d.ItemCode1,
                             d.ItemNameAr,
                             d.ItemType,
                             d.Category,
                             d.Company,
                             d.PurchasePrice,
                             TotalPurchasePrice = d.PurchasePrice * d.Qty,
                             d.Qty,
                             d.SellPrice,
                             d.TotalSellPrice,
                             d.QtyMoreThan,
                             d.CmmAmount,
                             CmmProfitRatio = d.CmmProfitRatio/100,
                             CalcCommission = d.CmmAmount>0? ((d.Qty - d.QtyMoreThan) * d.CmmAmount):
                             d.CmmProfitRatio >0?
                             ((d.Qty - d.QtyMoreThan) * ((d.SellPrice - d.PurchasePrice)*d.CmmProfitRatio/100)) : 0
                         }).OrderBy(x => x.JOCode).ToList();
            grdCategory.DataSource = data2;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column == colIndex)
                e.Value = e.RowHandle() + 1;

        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SL_JobOrderInv).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }        
    }
}