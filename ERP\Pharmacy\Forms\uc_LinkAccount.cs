﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;

namespace Pharmacy.Forms
{
    public partial class uc_LinkAccount : UserControl
    {
        List<acc> lstAccounts = new List<acc>();
        List<int> lstCustGroupsAccouts = new List<int>();//user can't select customer group acount to bind to, decause it will be a parent account.

        public bool dataModified;

        public uc_LinkAccount()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            try
            {
                lstAccounts = HelperAcc.LoadAccountsTree(0, true);
                lstCustGroupsAccouts = new DAL.ERPDataContext().SL_CustomerGroups.Select(g => g.AccountId).ToList();
            }
            catch
            { }

            lkp_LinkedAcc.Properties.DataSource = lstAccounts;
            lkp_LinkedAcc.Properties.ValueMember = "AccId";
            lkp_LinkedAcc.Properties.DisplayMember = "AccName";
            //ErpUtils.Allow_Incremental_Search(lkp_LinkedAcc);

            rdo_SeparateAcc.Checked = true;//Default state
        }              

        private void uc_LinkAccount_Load(object sender, EventArgs e)
        {

        }

        private void rdo_SeparateAcc_CheckedChanged(object sender, EventArgs e)
        {
            if (((CheckEdit)sender).Name == "rdo_SeparateAcc")
            {
                lkp_LinkedAcc.Enabled = false;
                lkp_LinkedAcc.EditValue = null;
            }
            else if (((CheckEdit)sender).Name == "rdo_LinkedAcc")
                lkp_LinkedAcc.Enabled = true;
        }

        private void rdo_SeparateAcc_Modified(object sender, EventArgs e)
        {
            dataModified = true;
        }

        public void DoValidate()
        {
            rdo_LinkedAcc.DoValidate();
            rdo_SeparateAcc.DoValidate();
            lkp_LinkedAcc.DoValidate();
            dataModified = false;
        }

        private void lkp_LinkedAcc_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            //cannot select parent account            
            if (e.NewValue == null)
                return;

            int AccId = Convert.ToInt32(e.NewValue);
            if (lstAccounts.Where(a => a.ParentId == AccId).Count() > 0)
            {
                //lkp_LinkedAcc.EditValue = null;
                //lkp_LinkedAcc.ErrorText = Shared.IsEnglish ? ResEn.linkAccountParent : ResAr.linkAccountParent;
                e.Cancel = true;
            }

            ////user can't select customer group acount to bind to, decause it will be a parent account.
            //if (lstCustGroupsAccouts.Where(a => a == AccId).Count() > 0)
            //{
            //    //lkp_LinkedAcc.EditValue = null;
            //    //lkp_LinkedAcc.ErrorText = Shared.IsEnglish ? ResEn.linkAccountParent : ResAr.linkAccountParent;
            //    e.Cancel = true;
            //}
        }
    }
}
