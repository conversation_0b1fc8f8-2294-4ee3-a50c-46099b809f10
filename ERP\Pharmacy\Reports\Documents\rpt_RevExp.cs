using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Linq;
using System.Data;
using DAL;

namespace Reports
{
    public partial class rpt_RevExp : DevExpress.XtraReports.UI.XtraReport
    {
        string PaperDetails, code, Serial, RegDate, Drawer, userName;
        DataTable dt = new DataTable();
        double total;
        public rpt_RevExp()
        {
            InitializeComponent();
        }
        public rpt_RevExp(string _PaperDetails,string _code, string _Serial, string _RegDate, string _drawer, string _userName, DataTable _dt,double _total)
        {
            InitializeComponent();
            getReportHeader();
            
            this.code = _code;
            this.Serial = _Serial;
            this.RegDate = _RegDate;            
            this.userName = _userName;
            this.Drawer = _drawer;
            this.PaperDetails = _PaperDetails;
            this.total = _total;
            dt = _dt;
        }

        public void LoadData()
        {
            lbl_PaperDetails.Text = PaperDetails;
            lbl_Code.Text = code;
            lbl_Serial.Text = Serial;
            lbl_RegDate.Text = RegDate;            
            lbl_Drawer.Text = Drawer;            
            lbl_User.Text = userName;
            lbl_Total.Text = total.ToString();
            DetailReport.DataSource = dt;
            cell_Account.DataBindings.Add("Text", DetailReport.DataSource, "Account");
            cell_CostCenter.DataBindings.Add("Text", DetailReport.DataSource, "CostCenter");
            cell_Notes.DataBindings.Add("Text", DetailReport.DataSource, "Notes");
            cell_Amount.DataBindings.Add("Text", DetailReport.DataSource, "Amount");
            cell_AmountWords.DataBindings.Add("Text", DetailReport.DataSource, "AmountWords");
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }     
    }
}
