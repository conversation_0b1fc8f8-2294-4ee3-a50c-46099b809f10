﻿using DAL;
using DAL.Res;
using DevExpress.XtraCharts;
using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy.Forms
{
    public partial class frm_Accounts_statistics : Form
    {
        ERPDataContext DB = new ERPDataContext();
        DataTable dt_Graph = new DataTable();
        DataTable dt_Graph2 = new DataTable();
        UserPriv prvlg;
        public frm_Accounts_statistics()
        {

            RTL.EnCulture(Shared.IsEnglish);

            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
        }

        private void frm_Accounts_statistics_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
            LoadPrivilege();
            dtFromDate.EditValue = MyHelper.Get_Server_DateTime();
            dtToDate.EditValue = MyHelper.Get_Server_DateTime();
            dt_Graph.Columns.Clear();
            dt_Graph2.Columns.Clear();
            dt_Graph.Columns.Add("Date", typeof(DateTime));
            dt_Graph.Columns.Add("Balance", typeof(decimal));
            dt_Graph2.Columns.Add("Date", typeof(DateTime));
            dt_Graph2.Columns.Add("Balance", typeof(decimal));
            dt_Graph2.Columns.Add("Account", typeof(string));
            dt_Graph2.Columns.Add("AccountId", typeof(int));
            lkpCustomList.Properties.DataSource = HelperAcc.GetCustomAccountsLists();
            lkpCustomList.Properties.DisplayMember = Shared.IsEnglish ? "CustomAccListNameEn" : "CustomAccListName";
            lkpCustomList.Properties.ValueMember = "CustomAccListId";
            
            chartControl1.DataSource = dt_Graph;
            XYDiagram diagram = (XYDiagram)chartControl1.Diagram;
        }

        private void barBtnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (lkpCustomList.EditValue == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.ValTxtCstmLst : ResAccAr.ValTxtCstmLst,
                        Shared.IsEnglish == true ? ResAccEn.MsgTWarn : ResAccAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                dt_Graph.Rows.Clear();
                dt_Graph2.Rows.Clear();
                DateTime date_from = dtFromDate.DateTime == DateTime.MinValue ? new DateTime(MyHelper.Get_Server_DateTime().Year, 1, 1) : dtFromDate.DateTime.Date;
                DateTime date_to = dtToDate.DateTime == DateTime.MinValue ? MyHelper.Get_Server_DateTime().AddDays(1) : dtToDate.DateTime.Date;
                DateTime Prev_date = Shared.minDate;
                XYDiagram diagram = (XYDiagram)chartControl1.Diagram;
              
                if (cb_PeriodUnit.EditValue.ToString() == "Day")
                {
                    diagram.AxisX.DateTimeScaleOptions.MeasureUnit = DateTimeMeasureUnit.Day;

                    diagram.AxisX.DateTimeScaleOptions.GridAlignment = DateTimeGridAlignment.Day;
                    Prev_date = date_from.AddDays(-1);
                    for (DateTime d = date_from; d <= date_to; d = d.AddDays(Convert.ToInt32(txt_Period.EditValue)))
                    {
                        DataRow dr = dt_Graph.NewRow();
                        dr["Date"] = d.Date/*.Month*/;
                        dr["Balance"] = 0;



                        dt_Graph.Rows.Add(dr);
                    }
                    //diagram.AxisX.DateTimeOptions.Format = DateTimeFormat.ShortDate;
                    diagram.AxisX.DateTimeOptions.Format = DateTimeFormat.Custom;
                    diagram.AxisX.DateTimeOptions.FormatString = "d MMMM";
                    // diagram.AxisX.Label.TextPattern = "d MMMM";
                }
                else if (cb_PeriodUnit.EditValue.ToString() == "Week")
                {
                    diagram.AxisX.DateTimeScaleOptions.MeasureUnit = DateTimeMeasureUnit.Week;
                    diagram.AxisX.DateTimeScaleOptions.GridAlignment = DateTimeGridAlignment.Week;
                    Prev_date = date_from.AddDays(-7);
                    for (DateTime d = date_from; d <= date_to; d = d.AddDays(Convert.ToInt32(txt_Period.EditValue) * 7))
                    {
                        DataRow dr = dt_Graph.NewRow();
                        dr["Date"] = d.Date/*.Month*/;
                        dr["Balance"] = 0;




                        dt_Graph.Rows.Add(dr);
                    }
                    //diagram.AxisX.DateTimeOptions.Format = DateTimeFormat.ShortDate;
                    diagram.AxisX.DateTimeOptions.Format = DateTimeFormat.Custom;
                    diagram.AxisX.DateTimeOptions.FormatString = "d MMMM";
                    //diagram.AxisX.Label.TextPattern = "d MMMM";
                }
                else if (cb_PeriodUnit.EditValue.ToString() == "Month")
                {
                    Prev_date = date_from;
                    for (DateTime d = date_from; d <= date_to; d = d.AddMonths(Convert.ToInt32(txt_Period.EditValue)))
                    {
                        DataRow dr = dt_Graph.NewRow();
                        dr["Date"] = d.Date.AddMonths(1).AddDays(-1)/*.Month*/;
                        dr["Balance"] = 0;




                        dt_Graph.Rows.Add(dr);
                    }
                    diagram.AxisX.DateTimeScaleOptions.MeasureUnit = DateTimeMeasureUnit.Month;
                    diagram.AxisX.DateTimeScaleOptions.GridAlignment = DateTimeGridAlignment.Month;
                    diagram.AxisX.Label.DateTimeOptions.Format = DateTimeFormat.Custom;
                    diagram.AxisX.DateTimeOptions.FormatString = "MMMM";
                    // diagram.AxisX.Label.TextPattern = "MMMM";
                }
                ERPDataContext DB = new ERPDataContext();


                List<int> List = DB.Acc_CustomAccListDetails.
                        Where(a => a.CustomAccListId == Convert.ToInt32(lkpCustomList.EditValue)).
                            Select(a => a.AccountId).ToList();

                var list = (from s in DB.ACC_Accounts
                            where List.Contains(s.AccountId)
                            // from ss in DB.ACC_Accounts where ss.AcNumber.StartsWith(s.AcNumber)
                            select s.AcNumber).ToList();

                var customizedList = (
                    from ss in DB.ACC_Accounts.AsEnumerable()
                    from l in list.AsEnumerable()
                    where ss.AcNumber.StartsWith(l)
                    select ss.AccountId).ToList();


                
                foreach (DataRow dr in dt_Graph.Rows)
                {
                    var Data = (from d in DB.ACC_JournalDetails.Where(d => List.Count() < 1 ? true : List.Contains(d.AccountId))
                                   join j in DB.ACC_Journals.Where(j => j.IsPosted)
                                   on d.JournalId equals j.JournalId
                                   join a in DB.ACC_Accounts
                                   on d.AccountId equals a.AccountId
                                   where j.InsertDate.Date <= Convert.ToDateTime(dr["Date"]).Date
                                   where j.InsertDate.Date > Prev_date.Date
                                   group new {d,a} by d.AccountId into grp
                                select new OData
                                {
                                    //Balance = grp.Sum(d => d.d.Credit) > grp.Sum(d => d.d.Debit) ? (grp.Sum(d => d.d.Credit) - grp.Sum(d => d.d.Debit)) : (grp.Sum(d => d.d.Debit) - grp.Sum(d => d.d.Credit)),
                                    //Balance = (grp.Sum(d => d.d.Credit) - grp.Sum(d => d.d.Debit))>0? (grp.Sum(d => d.d.Credit) - grp.Sum(d => d.d.Debit)) : ( (grp.Sum(d => d.d.Credit) - grp.Sum(d => d.d.Debit))*-1),
                                    Balance = (grp.Sum(d => d.d.Credit) - grp.Sum(d => d.d.Debit)),
                                    AccountId = grp.Key,
                                    Account = Shared.IsEnglish ? grp.Select(a => a.a.AcNameEn).FirstOrDefault() : grp.Select(a => a.a.AcNameAr).FirstOrDefault()
                                }).ToList();

                    List<OData> Data2 = new List<OData>();
                    Data2.AddRange(Data);

                    if (chkSubAccounts.Checked == true)
                    {
                        if (Data2.Count() > 0)
                        {
                            var result2 = List.Where(p => !Data2.Any(p2 => p2.AccountId == p));

                            foreach (var f in Data2)
                                {
                                    var L = (
                                  from ss in DB.ACC_Accounts.AsEnumerable()
                                  where ss.AccountId == f.AccountId
                                  from acc in DB.ACC_Accounts
                                  where acc.AcNumber.StartsWith(ss.AcNumber)
                                  where acc.AccountId != f.AccountId
                                  select acc.AccountId).ToList();

                                    var result = (from d in DB.ACC_JournalDetails.Where(d => L.Contains(d.AccountId))
                                                  join j in DB.ACC_Journals.Where(j => j.IsPosted)
                                                  on d.JournalId equals j.JournalId
                                                  //join a in DB.ACC_Accounts
                                                  //on d.AccountId equals a.AccountId
                                                  where j.InsertDate.Date <= Convert.ToDateTime(dr["Date"]).Date
                                                  where j.InsertDate.Date > Prev_date.Date
                                                  group d by d.AccountId into grp
                                                  select new
                                                  {

                                                      Balance = (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)),
                                                      //Balance = grp.Sum(d => d.Credit) > grp.Sum(d => d.Debit) ? (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) : (grp.Sum(d => d.Debit) - grp.Sum(d => d.Credit)),
                                                      //Balance = (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) > 0 ? (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) : ((grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) * -1),
                                                      AccountId = grp.Key,
                                                      //Account = Shared.IsEnglish ? grp.Select(a => a.a.AcNameEn).FirstOrDefault() : grp.Select(a => a.a.AcNameAr).FirstOrDefault()
                                                  }).Select(x => x.Balance).ToList().DefaultIfEmpty(0).Sum();
                                    f.Balance = (f.Balance + result);

                           }
                            foreach (var acc2 in result2)
                            {
                                var ParentAccount = DB.ACC_Accounts.FirstOrDefault(a => a.AccountId == acc2);
                                var childs2 = (
                                from ss in DB.ACC_Accounts.AsEnumerable()
                                where ss.AccountId == acc2
                                from ac in DB.ACC_Accounts
                                where ac.AcNumber.StartsWith(ss.AcNumber)
                                where ac.AccountId != ss.AccountId
                                select ac.AccountId).ToList();
                                var Balance = (from d in DB.ACC_JournalDetails.Where(d => childs2.Contains(d.AccountId))
                                               join j in DB.ACC_Journals.Where(j => j.IsPosted)
                                               on d.JournalId equals j.JournalId
                                               //join a in DB.ACC_Accounts
                                               //on d.AccountId equals a.AccountId
                                               where j.InsertDate.Date <= Convert.ToDateTime(dr["Date"]).Date
                                               where j.InsertDate.Date > Prev_date.Date
                                               group d by d.AccountId into grp
                                               select new
                                               {
                                                   //Balance = (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) > 0 ? (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) : ((grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) * -1),
                                                   Balance = (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)),
                                                   //Balance = grp.Sum(d => d.Credit)> grp.Sum(d => d.Debit)?(grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) :(grp.Sum(d => d.Debit)- grp.Sum(d => d.Credit)),
                                                   //AccountId = grp.Key,
                                                   //Account = Shared.IsEnglish ? grp.Select(a => a.a.AcNameEn).FirstOrDefault() : grp.Select(a => a.a.AcNameAr).FirstOrDefault()
                                               }).Select(x => x.Balance).ToList().DefaultIfEmpty(0).Sum();
                                OData od = new OData();
                                od.AccountId = acc2;
                                od.Account = Shared.IsEnglish ? ParentAccount.AcNameEn : ParentAccount.AcNameAr;
                                od.Balance = Balance > 0 ? Balance : Balance * -1;
                                Data2.Add(od);

                            }

                        }
                        else
                        {
                            List<OData> Dlist = new List<OData>();
                            foreach (var acc in List)
                            {
                                var ParentAccount = DB.ACC_Accounts.FirstOrDefault(a => a.AccountId == acc);
                                var childs = (
                                from ss in DB.ACC_Accounts.AsEnumerable()
                                where ss.AccountId == acc
                                from ac in DB.ACC_Accounts
                                where ac.AcNumber.StartsWith(ss.AcNumber)
                                where ac.AccountId != ss.AccountId
                                select ac.AccountId).ToList();
                                var Balance = (from d in DB.ACC_JournalDetails.Where(d => childs.Contains(d.AccountId))
                                               join j in DB.ACC_Journals.Where(j => j.IsPosted)
                                               on d.JournalId equals j.JournalId
                                               //join a in DB.ACC_Accounts
                                               //on d.AccountId equals a.AccountId
                                               where j.InsertDate.Date <= Convert.ToDateTime(dr["Date"]).Date
                                               where j.InsertDate.Date > Prev_date.Date
                                               group d by d.AccountId into grp
                                               select new
                                               {
                                                   //Balance = (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) > 0 ? (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) : ((grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) * -1),
                                                   Balance = (grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)),
                                                   //Balance = grp.Sum(d => d.Credit)> grp.Sum(d => d.Debit)?(grp.Sum(d => d.Credit) - grp.Sum(d => d.Debit)) :(grp.Sum(d => d.Debit)- grp.Sum(d => d.Credit)),
                                                   //AccountId = grp.Key,
                                                   //Account = Shared.IsEnglish ? grp.Select(a => a.a.AcNameEn).FirstOrDefault() : grp.Select(a => a.a.AcNameAr).FirstOrDefault()
                                               }).Select(x => x.Balance).ToList().DefaultIfEmpty(0).Sum();
                                OData od = new OData();
                                od.AccountId = acc;
                                od.Account = Shared.IsEnglish ? ParentAccount.AcNameEn : ParentAccount.AcNameAr;
                                od.Balance = Balance > 0 ? Balance : Balance * -1;
                                Dlist.Add(od);

                            }

                            //var L = (
                            //  from ss in DB.ACC_Accounts.Where(d => List.Count() < 1 ? true : List.Contains(d.AccountId)).AsEnumerable()
                            // // where ss.AccountId == f.AccountId
                            //  from acc in DB.ACC_Accounts
                            //  where acc.AcNumber.StartsWith(ss.AcNumber)
                            //  where acc.AccountId != ss.AccountId
                            //  select acc.AccountId).ToList();
                            //var result = (from d in DB.ACC_JournalDetails.Where(d => L.Contains(d.AccountId))
                            //              join j in DB.ACC_Journals
                            //              on d.JournalId equals j.JournalId
                            //              join a in DB.ACC_Accounts
                            //              on d.AccountId equals a.AccountId
                            //              where j.InsertDate.Date <= Convert.ToDateTime(dr["Date"]).Date
                            //              where j.InsertDate.Date > Prev_date.Date
                            //              group new { d,a } by d.AccountId into grp
                            //              select new OData
                            //              {

                            //                  Balance = (grp.Sum(d => d.d.Credit) - grp.Sum(d => d.d.Debit))*-1,
                            //                  AccountId = grp.Key,
                            //                  Account = Shared.IsEnglish ? grp.Select(a => a.a.AcNameEn).FirstOrDefault() : grp.Select(a => a.a.AcNameAr).FirstOrDefault()
                            //              }).ToList()/*.Select(x => x.Balance).ToList().DefaultIfEmpty(0).Sum()*/;
                            Data2.AddRange(Dlist);
                        }
                    }

                   // dr["Balance"] = balance.DefaultIfEmpty(0).Sum();
                   foreach(var d in Data2)
                    {
                        DataRow ddr = dt_Graph2.NewRow();
                        ddr["Balance"] = d.Balance>0? d.Balance: d.Balance*-1;
                        ddr["Date"] = Convert.ToDateTime(dr["Date"]).Date;
                        ddr["Account"]=d.Account;
                        ddr["AccountId"] = d.AccountId;
                        dt_Graph2.Rows.Add(ddr);
                    }



                    Prev_date = Convert.ToDateTime(dr["Date"]).Date;
                   
                }


                chartControl1.DataSource = null;
                chartControl1.Series.Clear();
               
               var counter = 0;
               // datatable.AsEnumerable().Select(x => new { Name = x.Field<int>("Product"), Date = DateTime.Now });
                foreach (var item in dt_Graph2.AsEnumerable().GroupBy(x => new { id = x.Field<int>("AccountId"),name = x.Field<string>("Account") }).Select(a=>a.Select(x=>new {name= x.Field<string>("Account"), id = x.Field<int>("AccountId") }).FirstOrDefault()))
                {
                    Series newSeries = new Series(item.name.ToString(), ViewType.Line);
                    chartControl1.Series.Add(newSeries);
                    //newSeries.ChangeView(ViewType.Bar);
                    chartControl1.Series[counter].ArgumentDataMember = "Date";
                    chartControl1.Series[counter].ValueDataMembers[0] = "Balance"/*+(count+1)*/;
                    chartControl1.Series[counter].DataSource = (dt_Graph2.AsEnumerable().Where(x => x.Field<int>("AccountId") == item.id).Select(x =>  new { name = x.Field<string>("Account"), id = x.Field<int>("AccountId"),Date= x.Field<DateTime>("Date"), Balance = x.Field<decimal>("Balance") }));

                   
                    counter++;
                }



            }
            catch(Exception ex){ }


        }



        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_Accounts_statistics).FirstOrDefault();

                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnOk.Enabled = false;
            }
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        private class OData
        {
            int accountId;
            string account;
            decimal balance;
            public string Account
            {
                get { return account; }
                set { account = value; }
            }
            public int AccountId
            {
                get { return accountId; }
                set { accountId = value; }
            }
            public decimal Balance
            {
                get { return balance; }
                set { balance = value; }
            }

        }
        }
}
