﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="barBtn_Help.Caption" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  <data name="barMnu_Print.Caption" xml:space="preserve">
    <value>طباعة</value>
  </data>
  <data name="barBtn_Print1.Caption" xml:space="preserve">
    <value>طباعة</value>
  </data>
  <data name="barBtn_PrintData.Caption" xml:space="preserve">
    <value>طباعة بيانات</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barMnu_Print.MenuAppearance.HeaderItemAppearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barMnu_Print.MenuAppearance.HeaderItemAppearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barMnu_Print.MenuAppearance.HeaderItemAppearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barMnu_Print.MenuAppearance.HeaderItemAppearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barBtnRefresh.Caption" xml:space="preserve">
    <value>تحديث</value>
  </data>
  <data name="barBtnOpen.Caption" xml:space="preserve">
    <value>فتح</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>جديد</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="btn_Import.Caption" xml:space="preserve">
    <value>استيراد</value>
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1107, 28</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 466</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1107, 0</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 438</value>
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1107, 28</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 438</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1107, 466</value>
  </data>
  <data name="btnClearSearch.Location" type="System.Drawing.Point, System.Drawing">
    <value>537, 54</value>
  </data>
  <data name="btnClearSearch.Text" xml:space="preserve">
    <value>مسح البحث</value>
  </data>
  <data name="labelControl3.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl3.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl3.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl3.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 23</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>فواتير المبيعات</value>
  </data>
  <data name="dt2.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt2.Location" type="System.Drawing.Point, System.Drawing">
    <value>638, 54</value>
  </data>
  <data name="dt2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="dt2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="dt2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="dt2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="dt2.Properties.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="dt2.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt2.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="dt2.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt2.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="dt2.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt2.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="dt2.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="dt2.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt2.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt2.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt2.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="dt2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>744, 57</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 13</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>الي</value>
  </data>
  <data name="dt1.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt1.Location" type="System.Drawing.Point, System.Drawing">
    <value>770, 54</value>
  </data>
  <data name="dt1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="dt1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="dt1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="dt1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="dt1.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt1.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="dt1.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt1.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="dt1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt1.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="dt1.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="dt1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dt1.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="dt1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>876, 57</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>43, 13</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>الفترة من</value>
  </data>
  <data name="NBG_Tasks.Caption" xml:space="preserve">
    <value>المهام</value>
  </data>
  <data name="NBI_Customers.Caption" xml:space="preserve">
    <value>عرض قائمة العملاء</value>
  </data>
  <data name="navBarControl1.Appearance.Background.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.Background.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.Background.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.Background.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.Button.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.Button.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.Button.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.Button.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.ButtonDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.ButtonDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.ButtonDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.ButtonDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.ButtonHotTracked.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.ButtonHotTracked.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.ButtonHotTracked.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.ButtonHotTracked.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.ButtonPressed.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.ButtonPressed.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.ButtonPressed.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.ButtonPressed.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.GroupBackground.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.GroupBackground.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.GroupBackground.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.GroupBackground.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.GroupHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderActive.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderActive.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderActive.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderActive.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderHotTracked.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderHotTracked.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderHotTracked.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderHotTracked.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderPressed.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderPressed.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderPressed.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.GroupHeaderPressed.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.Hint.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.Hint.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.Hint.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.Hint.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.Item.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.Item.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.Item.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.Item.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.ItemActive.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.ItemActive.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.ItemActive.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.ItemActive.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.ItemDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.ItemDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.ItemDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.ItemDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.ItemHotTracked.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.ItemHotTracked.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.ItemHotTracked.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.ItemHotTracked.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.ItemPressed.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.ItemPressed.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.ItemPressed.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.ItemPressed.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.LinkDropTarget.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.LinkDropTarget.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.LinkDropTarget.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.LinkDropTarget.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="navBarControl1.Appearance.NavigationPaneHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Appearance.NavigationPaneHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="navBarControl1.Appearance.NavigationPaneHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="navBarControl1.Appearance.NavigationPaneHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="NBG_Reports.Caption" xml:space="preserve">
    <value>التقارير</value>
  </data>
  <data name="NBI_ItemsNotSold.Caption" xml:space="preserve">
    <value>اصناف لم تباع مطلقا</value>
  </data>
  <data name="NBI_ItemsMaxSold.Caption" xml:space="preserve">
    <value>الاصناف الأكثر مبيعا</value>
  </data>
  <data name="NBI_ItemsMinSold.Caption" xml:space="preserve">
    <value>الاصناف الأقل مبيعا</value>
  </data>
  <data name="navBarControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>942, 47</value>
  </data>
  <data name="resource.CollapsedWidth" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="resource.ExpandedWidth" type="System.Int32, mscorlib">
    <value>153</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grdCategory.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grdCategory.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grdCategory.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="gridView1.Appearance.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.GroupPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.GroupPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.GroupPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.GroupPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.GroupRow.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.GroupRow.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.GroupRow.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.GroupRow.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.Lines.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_SL_InvoiceId.Caption" xml:space="preserve">
    <value>كود الفاتورة</value>
  </data>
  <data name="col_InvoiceCode.Caption" xml:space="preserve">
    <value>رقم الفاتورة</value>
  </data>
  <data name="col_InvoiceCode.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="col_InvoiceCode.Width" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="col_InvoiceBookId.Caption" xml:space="preserve">
    <value>الدفتر</value>
  </data>
  <data name="rep_InvoiceBook.Columns8" xml:space="preserve">
    <value>الدفتر</value>
  </data>
  <data name="col_InvoiceBookId.Width" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="col_StoreId.Caption" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="col_StoreId.Width" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="col_CustomerId.Caption" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="col_CustomerId.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="col_CustomerId.Width" type="System.Int32, mscorlib">
    <value>156</value>
  </data>
  <data name="col_InvoiceDate.Caption" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="col_InvoiceDate.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="col_InvoiceDate.Width" type="System.Int32, mscorlib">
    <value>85</value>
  </data>
  <data name="col_Notes.Caption" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="col_PayMethod.Caption" xml:space="preserve">
    <value>نوع السداد</value>
  </data>
  <data name="rep_paymethod.Items" xml:space="preserve">
    <value>كاش</value>
  </data>
  <data name="rep_paymethod.Items3" xml:space="preserve">
    <value>آجل</value>
  </data>
  <data name="rep_paymethod.Items6" xml:space="preserve">
    <value>آجل/كاش</value>
  </data>
  <data name="col_PayMethod.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="col_PayMethod.Width" type="System.Int32, mscorlib">
    <value>66</value>
  </data>
  <data name="col_DiscountRatio.Caption" xml:space="preserve">
    <value>ن خصم</value>
  </data>
  <data name="col_DiscountValue.Caption" xml:space="preserve">
    <value>ق خصم</value>
  </data>
  <data name="col_DiscountValue.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_DiscountValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="col_DiscountValue.Width" type="System.Int32, mscorlib">
    <value>73</value>
  </data>
  <data name="col_Expenses.Caption" xml:space="preserve">
    <value>تكاليف أخرى</value>
  </data>
  <data name="col_Expenses.Width" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="colPaid.Caption" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="colRemains.Caption" xml:space="preserve">
    <value>متبقي</value>
  </data>
  <data name="col_UserId.Caption" xml:space="preserve">
    <value>المستخدم</value>
  </data>
  <data name="colStore.Caption" xml:space="preserve">
    <value>كود المخزن</value>
  </data>
  <data name="col_SalesEmpId.Caption" xml:space="preserve">
    <value>مسئول المبيعات</value>
  </data>
  <data name="rep_salesEmp.Columns1" xml:space="preserve">
    <value>الموظف</value>
  </data>
  <data name="col_SalesEmpId.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="col_Net.Caption" xml:space="preserve">
    <value>صافي</value>
  </data>
  <data name="col_Net.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="col_Net.Width" type="System.Int32, mscorlib">
    <value>97</value>
  </data>
  <data name="col_TotalCostPrice.Caption" xml:space="preserve">
    <value>تكلفة</value>
  </data>
  <data name="col_Profit.Caption" xml:space="preserve">
    <value>ربح / خسارة</value>
  </data>
  <data name="col_ProfitRatio.Caption" xml:space="preserve">
    <value>نسبة الربح</value>
  </data>
  <data name="col_ProfitRatio.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Average</value>
  </data>
  <data name="col_ProfitCostRatio.Caption" xml:space="preserve">
    <value>نسبة الربح للتكلفة</value>
  </data>
  <data name="col_ProfitCostRatio.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Average</value>
  </data>
  <data name="col_CategoryId.Caption" xml:space="preserve">
    <value>فئة العميل</value>
  </data>
  <data name="col_CategoryId.VisibleIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="col_CategoryId.Width" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="col_Is_OutTrans.Caption" xml:space="preserve">
    <value>تم الصرف من المخزن</value>
  </data>
  <data name="col_Is_OutTrans.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="col_TaxValue.Caption" xml:space="preserve">
    <value>ضريبة مبيعات</value>
  </data>
  <data name="col_TaxValue.Width" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="col_DeductTaxValue.Caption" xml:space="preserve">
    <value>ضريبة خصم</value>
  </data>
  <data name="col_DeductTaxValue.Width" type="System.Int32, mscorlib">
    <value>69</value>
  </data>
  <data name="col_AddTaxValue.Caption" xml:space="preserve">
    <value>ضريبة اضافة</value>
  </data>
  <data name="col_AddTaxValue.Width" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="col_DueDate.Caption" xml:space="preserve">
    <value>تاريخ الاستحقاق</value>
  </data>
  <data name="col_CrncId.Caption" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="repCrncy.Columns8" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="col_CrncRate.Caption" xml:space="preserve">
    <value>معامل التحويل</value>
  </data>
  <data name="colDriverName.Caption" xml:space="preserve">
    <value>اسم السائق</value>
  </data>
  <data name="colVehicleNumber.Caption" xml:space="preserve">
    <value>رقم العربة</value>
  </data>
  <data name="colDestination.Caption" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="col_Process.Caption" xml:space="preserve">
    <value>المصدر</value>
  </data>
  <data name="col_SourceCode.Caption" xml:space="preserve">
    <value>كود المصدر</value>
  </data>
  <data name="col_IsOffer.Caption" xml:space="preserve">
    <value>عرض</value>
  </data>
  <data name="col_Paid_To.Caption" xml:space="preserve">
    <value>سداد إلى</value>
  </data>
  <data name="col_Paid_To.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="col_Paid_To.Width" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="col_CustomerGroup.Caption" xml:space="preserve">
    <value>مجموعة العميل</value>
  </data>
  <data name="col_CustomerGroup.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_CustomerGroup.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="col_Total.Caption" xml:space="preserve">
    <value>الاجمالي</value>
  </data>
  <data name="col_Total.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="col_IdRegion.Caption" xml:space="preserve">
    <value>المنطقة</value>
  </data>
  <data name="col_IdRegion.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_IdRegion.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="col_IdRegion.Width" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="col_City.Caption" xml:space="preserve">
    <value>المدينة</value>
  </data>
  <data name="col_City.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_City.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="col_City.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="col_Totaltaxes.Caption" xml:space="preserve">
    <value>اجمالي الضريبة</value>
  </data>
  <data name="col_Totaltaxes.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_Totaltaxes.Width" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="gridView1.GroupPanelText" xml:space="preserve">
    <value>اسحب احد الاعمدة هنا لتجميع البيانات علي أساسه</value>
  </data>
  <data name="gridView1.GroupSummary1" xml:space="preserve">
    <value>DiscountValue</value>
  </data>
  <data name="gridView1.GroupSummary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="gridView1.GroupSummary3" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="gridView1.GroupSummary4" xml:space="preserve">
    <value>Expenses</value>
  </data>
  <data name="gridView1.GroupSummary5" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="gridView1.GroupSummary7" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="gridView1.GroupSummary8" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="gridView1.GroupSummary10" xml:space="preserve">
    <value>TaxValue</value>
  </data>
  <data name="gridView1.GroupSummary11" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="gridView1.GroupSummary13" xml:space="preserve">
    <value>DeductTaxValue</value>
  </data>
  <data name="gridView1.GroupSummary14" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="gridView1.GroupSummary16" xml:space="preserve">
    <value>AddTaxValue</value>
  </data>
  <data name="gridView1.GroupSummary17" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="gridView1.GroupSummary19" xml:space="preserve">
    <value>TotalCostPrice</value>
  </data>
  <data name="gridView1.GroupSummary20" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="grdCategory.Size" type="System.Drawing.Size, System.Drawing">
    <value>924, 378</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>فواتير المبيعات</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="mi_OpenDealer.Size" type="System.Drawing.Size, System.Drawing">
    <value>159, 22</value>
  </data>
  <data name="mi_OpenDealer.Text" xml:space="preserve">
    <value>فتح بيانات العميل</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 26</value>
  </data>
</root>