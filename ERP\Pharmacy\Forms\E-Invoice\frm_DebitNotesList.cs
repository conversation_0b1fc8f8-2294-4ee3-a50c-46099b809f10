﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Tulpep.NotificationWindow;
using Models_1.ViewModels;
using Models_1.ViewModels.SignuatureVM;
using Models_1.ViewModels.ValidationVM;
using Models_1.ViewModels.ChangeDocumentStatusVM;
using Models_1.ViewModels.InvoiceVM;
//using McDRSigniture;

namespace Pharmacy.Forms
{
    public partial class frm_DebitNotesList : DevExpress.XtraEditors.XtraForm
    {
        int customerId;
        DateTime dateFrom, dateTo;

        bool Is_OpenForSelect = false;
        public static int SelectedInvId = 0;
        public static string SelectedInvCode;
        bool cars = false;
        List<ValidationMessages> validationMessages_Lst = new List<ValidationMessages>();

        public frm_DebitNotesList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }
        public frm_DebitNotesList(bool _cars, bool IsCars)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
            cars = _cars;

        }

        public frm_DebitNotesList(bool _is_OpenForSelect)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            Is_OpenForSelect = _is_OpenForSelect;
        }
        public frm_DebitNotesList(bool _is_OpenForSelect, int customerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            Is_OpenForSelect = _is_OpenForSelect;
            this.customerId = customerId;
        }

        public frm_DebitNotesList(int customerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.customerId = customerId;
        }


        private void frm_E_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            #region Get Date Range
            if (Shared.SL_I_FromDate != null)
                dt1.DateTime = dateFrom = Shared.SL_I_FromDate.Value;
            else
            {
                dateFrom = Shared.minDate;
                dt1.EditValue = null;
            }

            if (Shared.SL_I_ToDate != null)
                dt2.DateTime = dateTo = Shared.SL_I_ToDate.Value;
            else
            {
                dateTo = Shared.maxDate;
                dt2.EditValue = null;
            }
            #endregion


            #region CustomersGroups
            ERPDataContext DB = new ERPDataContext();
            rep_CategoryId.DataSource = DB.SL_CustomerGroups.Select(x => new { x.CustomerGroupId, x.CGNameAr }).ToList();
            rep_CategoryId.DisplayMember = "CGNameAr";
            rep_CategoryId.ValueMember = "CustomerGroupId";
            #endregion


            GetInvoices();

            #region SalesEmp
            //DataTable dt_SalesEmps = new DataTable();
            //MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            //rep_salesEmp.DataSource = dt_SalesEmps;
            //rep_salesEmp.DisplayMember = "EmpName";
            //rep_salesEmp.ValueMember = "EmpId";
            #endregion



            #region invoice bbok            
            //rep_InvoiceBook.DataSource = DB.ST_InvoiceBooks.Where(x => x.ProcessId == (int)Process.SellInvoice)
            //    .Select(x => new { x.InvoiceBookId, x.InvoiceBookName }).ToList();
            //rep_InvoiceBook.DisplayMember = "InvoiceBookName";
            //rep_InvoiceBook.ValueMember = "InvoiceBookId";
            #endregion

            #region Currencies
            repCrncy.DataSource = Shared.lstCurrency;
            repCrncy.ValueMember = "CrncId";
            repCrncy.DisplayMember = "crncName";
            #endregion

            rep_User.DataSource = DB.HR_Users;
            //rep_Accounts.DataSource = DB.ACC_Accounts;
            //rep_Accounts.ValueMember = "AccountId";
            //rep_Accounts.DisplayMember = Shared.IsEnglish ? "AcNameEn" : "AcNameAr";

            if (Shared.InvoicePostToStore)
                col_Is_OutTrans.Visible = false;

            ErpUtils.Load_Grid_Layout(grdInvoices, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grdInvoices);

            if (Shared.user.HidePurchasePrice)
            {
                col_Profit.Visible = col_Profit.OptionsColumn.ShowInCustomizationForm =
                    col_TotalCostPrice.Visible = col_TotalCostPrice.OptionsColumn.ShowInCustomizationForm =
                    col_ProfitRatio.Visible = col_ProfitRatio.OptionsColumn.ShowInCustomizationForm = false;
            }

            ErpUtils.ColumnChooser(grdInvoices);
            LoadPrivilege();
        }

        private void frm_E_InvoiceList_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                dt1.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                grdInvoices.Focus();
            }
        }

        private void frm_E_InvoiceList_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (dt1.EditValue != null)
                Shared.SL_I_FromDate = dateFrom;
            else
                Shared.SL_I_FromDate = null;

            if (dt2.EditValue != null)
                Shared.SL_I_ToDate = dateTo;
            else
                Shared.SL_I_ToDate = null;

            ErpUtils.save_Grid_Layout(grdInvoices, this.Name.Replace("frm_", ""), true);
        }

        private void dt1_EditValueChanged(object sender, EventArgs e)
        {
            if (dateFrom != DateTime.MinValue && dateTo != DateTime.MinValue)
            {
                if (dt1.DateTime != DateTime.MinValue)
                    dateFrom = dt1.DateTime;
                else
                    dateFrom = Shared.minDate;

                if (dt2.DateTime != DateTime.MinValue)
                    dateTo = dt2.DateTime;
                else
                {
                    dateTo = Shared.maxDate;
                }
            }
        }

        private void btnClearSearch_Click(object sender, EventArgs e)
        {
            dateFrom = Shared.minDate;
            dateTo = Shared.maxDate;
            dt1.EditValue = null;
            dt2.EditValue = null;
            barBtnRefresh.PerformClick();
        }
        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Shared.CarsAvailable == false)
            {
                if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
                    Application.OpenForms["frm_SL_Return"].Close();

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
                    Application.OpenForms["frm_SL_Return"].BringToFront();
                else
                    new frm_SL_Return().Show();
            }
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GetInvoices();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_Invoice();
        }


        private void grdCategory_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_Invoice();
        }

        private void NBI_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            if (((NavBarItem)sender).Name == "NBI_Customers")
            {
                frmMain.OpenSL_Customer();
            }

            var view = grdInvoices.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int strId = Convert.ToInt32(view.GetFocusedRowCellValue(colStore));
            string strName = view.GetFocusedRowCellDisplayText(col_StoreId).ToString();
            string strFltr = (Shared.IsEnglish == true ? ResSLEn.txtStore : ResSLAr.txtStore)//"المخزن: " 
                + strName;

        }

        private void Open_Selected_Invoice()
        {
            var view = grdInvoices.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_SL_InvoiceId));

            if (Is_OpenForSelect == true)
            {
                SelectedInvId = inv_id;
                SelectedInvCode = view.GetRowCellValue(focused_row_index, col_InvoiceCode).ToString();

                this.Close();
                return;
            }
            if (cars == false)
            {
                if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
                    Application.OpenForms["frm_SL_Return"].Close();

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
                    Application.OpenForms["frm_SL_Return"].BringToFront();
                else
                    new frm_SL_Return(inv_id).Show();
            }
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    mi_OpenDealer.Enabled = false;
                }

                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barMnu_Print.Enabled = barBtn_Print1.Enabled = barBtn_PrintData.Enabled = false;
            }
        }

        private void GetInvoices()
        {
            int focusedIndex = (grdInvoices.FocusedView as GridView).FocusedRowHandle;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            try
            {

                var returns = (from r in DB.SL_Returns
                               where r.ReturnDate.Date >= dateFrom && r.ReturnDate.Date <= dateTo
                               where (r.EstatusCode == null || r.EstatusCode != 1 || r.syncDate == null)
                               where Shared.E_invoiceAvailable ? r.InvoiceBookId != null : true
                               //join t in DB.SL_Invoices on r.SourceId equals t.SL_InvoiceId
                               join n in DB.SL_ReturnDetails on r.SL_ReturnId equals n.SL_ReturnId
                               from lkp in DB.LKP_Processes.Where(lkp => lkp.ProcessId == r.ProcessId).DefaultIfEmpty()
                               let Total = Convert.ToDouble(DB.SL_ReturnDetails.Where(a => a.SL_ReturnId == r.SL_ReturnId).Sum(z => z.TotalSellPrice))
                               join m in DB.SL_Customers on r.CustomerId equals m.CustomerId
                               select new
                               {
                                   Process = lkp == null ? "اشعار خصم" : (Shared.IsEnglish ? lkp.ProcessEnglishName : lkp.ProcessName),
                                   DiscountRatio = r.DiscountRatio,
                                   DiscountValue = r.DiscountValue,
                                   Expenses = r.Expenses,
                                   Net = r.Net,
                                   Paid = r.Paid,
                                   Remains = r.Remains,
                                   Total,
                                   InvoiceCode = r.ReturnCode,
                                   InvoiceDate = r.ReturnDate,
                                   r.JornalId,
                                   r.Notes,
                                   r.PayMethod,
                                   SL_InvoiceId = r.SL_ReturnId,
                                   //StoreId = s.StoreNameAr,
                                   StoreId = r.StoreId == 0 ? DB.IC_Stores.FirstOrDefault(s => s.StoreId == r.StoreId).StoreNameAr : DB.IC_Stores.FirstOrDefault(s => s.StoreId == r.StoreId).StoreNameAr,
                                   //store = s.StoreId,
                                   store = r.StoreId,
                                   r.UserId,
                                   CustomerId = m.CusNameAr,
                                   CustId = m.CustomerId,
                                   r.SalesEmpId,
                                   //TotalCostPrice = t.TotalCostPrice,
                                   //Profit = t.Net - t.TotalCostPrice,
                                   GroupId = m.CategoryId,
                                   //t.Is_OutTrans,
                                   r.InvoiceBookId,
                                   r.AddTaxValue,
                                   r.DeductTaxValue,
                                   r.TaxValue,
                                   //t.DueDate,
                                   r.CrncId,
                                   r.CrncRate,
                                   r.DriverName,
                                   r.VehicleNumber,
                                   r.Destination,
                                   //t.IsOffer,
                                   //profitRatio = t.TotalCostPrice == 0 ? 1 : (t.Net > 0 ? (t.Net - t.TotalCostPrice) / t.Net : 0),
                                   //ProfitCostRatio = t.TotalCostPrice == 0 ? 1 : (t.TotalCostPrice > 0 ? (t.Net - t.TotalCostPrice) / t.TotalCostPrice : 0),
                                   r.DrawerAccountId,
                                   CategoryId = m.CategoryId,
                                   m.City,
                                   Region,
                                   r.uuid,
                                   r.Estatus
                               }).Distinct().ToList();
                var allData = returns;
                grdInvoices.DataSource = allData;


                (grdInvoices.FocusedView as GridView).FocusedRowHandle = focusedIndex;
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }

        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مبيعات جديدة");
        }

        private void mi_OpenDealer_Click(object sender, EventArgs e)
        {
            var view = grdInvoices.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int DealerId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_CustId));

            if (ErpUtils.IsFormOpen(typeof(frm_SL_Customer)))
                Application.OpenForms["frm_SL_Customer"].Close();

            new frm_SL_Customer(DealerId).Show();
        }

        private void barBtn_Print1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdInvoices.MinimumSize = grdInvoices.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdInvoices, false).ShowPreview();
            grdInvoices.MinimumSize = new Size(0, 0);
        }

        private void btn_Advanced_Click(object sender, EventArgs e)
        {
            btn_Advanced.Enabled = false;
            gridView1.UpdateCurrentRow();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            List<int> lst = new List<int>();

            var DongleCompany = DB.ST_CompanyInfos.Select(x => x.CertificateCompanyType).FirstOrDefault();
            if (DongleCompany == null)
            {
                MessageBox.Show("يجب تحديد شركة التوقيع الإلكتروني");
                return;
            }
            foreach (var i in gridView1.GetSelectedRows())
            {
                var invoiceIdRejected = Convert.ToInt32(gridView1.GetRowCellDisplayText(i, "SL_InvoiceId"));
                lst.Add(invoiceIdRejected);
                //var invoice = DB.SL_Invoices.FirstOrDefault(a => a.SL_InvoiceId == invoiceIdRejected);
                //if (invoice.syncDate == null)
                //{
                //    invoice.syncDate = DateTime.Now;
                //}
                //else
                //    invoice.lastSyncDate = DateTime.Now;

            }
            //DB.SubmitChanges();
            //SyncInvoicesMCDR(lst).ConfigureAwait(false);
            //   SyncInvoices(lst).ConfigureAwait(false);


            if (DongleCompany == 1) //egypt trust
                SyncInvoices(lst).ConfigureAwait(false);
            else if (DongleCompany == 2)//Mcdr
                SyncInvoicesMCDR(lst).ConfigureAwait(false);

            barBtnRefresh.PerformClick();
        }

        async Task SyncInvoices(List<int> lst)
        {
            ERPDataContext db = new ERPDataContext();
            try
            {
                PopupNotifier Syncpopup = new PopupNotifier();
                Syncpopup.TitleText = "مزامنة";
                Syncpopup.ContentText = "جاري مزامنة الاشعارات مع البوابة، يرجى الانتظار";
                Syncpopup.Image = Properties.Resources.N_journal;
                Syncpopup.Popup();// show  

                HttpClient client = new HttpClient();
                UriBuilder uriBuilder = new UriBuilder(Properties.Settings.Default.BackEndPoint);
                uriBuilder.Port = Properties.Settings.Default.BackEndPort;
                client.BaseAddress = uriBuilder.Uri;
                client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                MyHelper.UpdateST_UserLog(db, "1", "b4 SerializeObject", 1, 1);
                db.SubmitChanges();

                List<Note4Post> debitNote4Posts = new List<Note4Post>();
                foreach (var l in lst)
                    debitNote4Posts.Add(new Note4Post { invoiceId = l, documentType = (int)DocumentType.C });

                var serialzedInvoices = JsonConvert.SerializeObject(debitNote4Posts);
                MyHelper.UpdateST_UserLog(db, "1", "after SerializeObject", 1, 1);
                db.SubmitChanges();
                var content = new StringContent(serialzedInvoices, Encoding.UTF8, "application/json");
                //HttpResponseMessage APIResponse = client.PostAsync(builder.Uri, content).Result;
                MyHelper.UpdateST_UserLog(db, "1", "content ready 2 call GetUnSignDoc", 1, 1);
                db.SubmitChanges();
                #region Step 2: Send Invoices to be validated locally in GetUnSignDoc
                HttpResponseMessage response_ = await client.PostAsync(string.Format("api/Einvoice/SubmitDocuments"), content);

                MyHelper.UpdateST_UserLog(db, "1", "response 2 call   ", 1, 1);
                db.SubmitChanges();
                #endregion
                if (response_.IsSuccessStatusCode)
                {
                    MyHelper.UpdateST_UserLog(db, "1", "success response", 1, 1);
                    db.SubmitChanges();

                    var JsonContent = response_.Content.ReadAsStringAsync().Result;

                    var invoices = db.SL_Returns.Where(x => lst.Contains(x.SL_ReturnId)).ToList();

                    if (response_.IsSuccessStatusCode)
                    {
                        Syncpopup.Hide();

                        #region Step 7: Check for Errors sent back from server
                        JsonContent = response_.Content.ReadAsStringAsync().Result;
                        MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                        MyHelper.UpdateST_UserLog(db, "1", $"Validation Messages From SubmitSignDoc : { JsonContent }", 1, 1);
                        db.SubmitChanges();

                        var ServerErrors = JsonConvert.DeserializeObject<ValidationMessages>(JsonContent);
                        if (ServerErrors.Messages.Count > 0)
                        {
                            validationMessages_Lst.Add(ServerErrors);
                        }
                        #endregion
                        MyHelper.UpdateST_UserLog(db, "1", $"Validation Messages Count : { validationMessages_Lst.Count().ToString() }", 1, 1);
                        db.SubmitChanges();
                        if (validationMessages_Lst.Count > 0)
                        {
                            MessageBox.Show("تم إرسال بعض الاشعارات، ويوجد أخطاء في البعض الآخر", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            string ErrorMsg = "";

                            foreach (var er_Lst in validationMessages_Lst)
                            {
                                foreach (var er in er_Lst.Messages)
                                {
                                    var message = "";
                                    foreach (var m in er.Message)
                                    {
                                        message = string.IsNullOrEmpty(message) ? m : message + " - " + m;
                                    }
                                    ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + message + "; \r\n ";
                                }
                            }

                            MessageBox.Show(ErrorMsg);
                        }
                        else
                            MessageBox.Show("تم الإرسال بنجاح");

                        System.Threading.Thread.Sleep(2000);

                        #region PopupNotifier
                        PopupNotifier popup = new PopupNotifier();
                        popup.TitleText = "تحديث";
                        popup.ContentText = "جاري تحديث حالة الاشعارات، يرجى الانتظار";
                        popup.Image = Properties.Resources.N_journal;
                        popup.Popup();// show  
                        #endregion

                        db.Refresh(System.Data.Linq.RefreshMode.OverwriteCurrentValues, db.SL_Invoices);
                        invoices = db.SL_Returns.Where(x => lst.Contains(x.SL_ReturnId)).ToList();

                        Dictionary<int, string> keyValuePairs = (from i in invoices
                                                                     //join l in lst on i.SL_InvoiceId equals l
                                                                 select new KeyValuePair<int, string>(i.SL_ReturnId, i.uuid)
                                                                 ).ToDictionary(x => x.Key, x => x.Value);
                        List<UpdateDocumentStatus> updateDocumentStatuses = new List<UpdateDocumentStatus>();


                        foreach (var k in keyValuePairs)
                            updateDocumentStatuses.Add(new UpdateDocumentStatus { keyValuePairs = k, documentType = (int)DocumentType.C });


                        var id_UUid = JsonConvert.SerializeObject(updateDocumentStatuses);
                        var id_UUid_content = new StringContent(id_UUid, Encoding.UTF8, "application/json");

                        MyHelper.UpdateST_UserLog(db, "1", "object to send to UpdateAcceptedDocuments: " + id_UUid_content, 1, 1);
                        db.SubmitChanges();

                        HttpResponseMessage UpdateInvoicesState = await client.PostAsync(string.Format("api/Einvoice/UpdateAcceptedDocuments"), id_UUid_content);
                        MyHelper.UpdateST_UserLog(db, "1", "UpdateInvoicesState: " + UpdateInvoicesState.Content.ReadAsStringAsync().Result, 1, 1);
                        db.SubmitChanges();

                        popup.Hide();


                    }
                    else
                    {
                        JsonContent = response_.Content.ReadAsStringAsync().Result;
                        db.SubmitChanges();

                        var ServerErrors = JsonConvert.DeserializeObject<ValidationMessages>(JsonContent);
                        validationMessages_Lst.Add(ServerErrors);
                        if (validationMessages_Lst.Count > 0)
                        {
                            MessageBox.Show("فشل محاولة ارسال الاشعارات، يوجد أخطاء في الاشعارات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            string ErrorMsg = "";

                            foreach (var er_Lst in validationMessages_Lst)
                            {
                                foreach (var er in er_Lst.Messages)
                                {
                                    var message = "";
                                    foreach (var m in er.Message)
                                    {
                                        message = string.IsNullOrEmpty(message) ? m : message + " - " + m;
                                    }
                                    ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + message + "; \r\n ";
                                }
                            }

                            MessageBox.Show(ErrorMsg);
                        }
                    }
                }
                else
                {
                    Syncpopup.Hide();
                    var JsonContent = response_.Content.ReadAsStringAsync().Result;
                    MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                    MyHelper.UpdateST_UserLog(db, "1", "response_ Failed, response_.StatusCode: " + response_.StatusCode, 1, 1);
                    MyHelper.UpdateST_UserLog(db, "1", "response_ Failed, response_: " + response_, 1, 1);
                    MyHelper.UpdateST_UserLog(db, "1", "response_ Failed, response_.RequestMessage: " + response_.RequestMessage, 1, 1);
                    db.SubmitChanges();

                    var ServerErrors = JsonConvert.DeserializeObject<ValidationMessages>(JsonContent);
                    validationMessages_Lst.Add(ServerErrors);

                    if (validationMessages_Lst.Count > 0)
                    {
                        MessageBox.Show("يوجد أخطاء في بعض الاشعارات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        string ErrorMsg = "";

                        foreach (var er_Lst in validationMessages_Lst)
                        {
                            foreach (var er in er_Lst.Messages)
                            {
                                var message = "";
                                foreach (var m in er.Message)
                                {
                                    message = string.IsNullOrEmpty(message) ? m : message + " - " + m;
                                }
                                ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + message + "; \r\n ";
                            }
                        }

                        MessageBox.Show(ErrorMsg);
                    }
                }
                validationMessages_Lst.Clear();
                btn_Advanced.Enabled = true;




                //HttpResponseMessage response_ = await client.PostAsync(string.Format("api/Einvoice/GetUnSignDoc"), content);

                //MyHelper.UpdateST_UserLog(db, "1", "response 2 call   ", 1, 1);
                //db.SubmitChanges();

                //if (response_.IsSuccessStatusCode)
                //{
                //    MyHelper.UpdateST_UserLog(db, "1", "success response", 1, 1);
                //    db.SubmitChanges();

                //    var documents = new List<Models_1.ViewModels.Document>();

                //    #region Step 3: Receive Locally Validated Invoices
                //    var JsonContent = response_.Content.ReadAsStringAsync().Result;
                //    MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                //    db.SubmitChanges();

                //    var DocumentsToSign_ = JsonConvert.DeserializeObject<DocumentsToSignVM>(JsonContent);
                //    MyHelper.UpdateST_UserLog(db, "1", $"Validation Messages From GetUnSignDoc : { DocumentsToSign_.Errors }", 1, 1);
                //    db.SubmitChanges();
                //    #endregion

                //    #region Step 4: Add Local Validation Errors To Errors List
                //    if (DocumentsToSign_.Errors.Messages.Count() > 0)
                //    {
                //        validationMessages_Lst.Add(DocumentsToSign_.Errors);
                //    }
                //    #endregion

                //    //var result = JsonConvert.DeserializeObject<List<SignuatuerGenerator.BL.Model>>(JsonContent);
                //    var to_B_signed_Docs_Lst = DocumentsToSign_.Documents;

                //    MyHelper.UpdateST_UserLog(db, "1", "content is Deserialized", 1, 1);
                //    db.SubmitChanges();

                //    var invoices = db.SL_Returns.Where(x => lst.Contains(x.SL_ReturnId)).ToList();


                //    //foreach (var doc in result)
                //    foreach (var doc in to_B_signed_Docs_Lst)
                //    {
                //        //if (string.IsNullOrEmpty(doc.CretSerial))
                //        //{
                //        //    MyHelper.UpdateST_UserLog(db, "1", "doc.CretSerial -" + doc.CretSerial, 1, 1);
                //        //    db.SubmitChanges();
                //        //    doc.CretSerial = DAL.Shared.Dongle_SN;
                //        //}
                //        MyHelper.UpdateST_UserLog(db, "1", "prepare document to be signed", 1, 1);
                //        db.SubmitChanges();
                //        var document = JsonConvert.DeserializeObject<Models_1.ViewModels.Document>(doc.DocumentJson);

                //        MyHelper.UpdateST_UserLog(db, "1", "sign the doc.", 1, 1);
                //        db.SubmitChanges();

                //        SignuatuerGenerator.BL.Model _model = new SignuatuerGenerator.BL.Model()
                //        {
                //            CretSerial = DAL.Shared.Dongle_SN,
                //            DocumentJson = doc.DocumentJson,
                //            DonglePIN = doc.DonglePIN
                //        };

                //        var sign = SignuatuerGenerator.GetSignuatuer.SignDocument(_model);


                //        MyHelper.UpdateST_UserLog(db, "1", "extract the signature", 1, 1);
                //        db.SubmitChanges();
                //        document.signatures = new List<Models_1.ViewModels.Signature>()
                //        {
                //            new Models_1.ViewModels.Signature(){ signatureType="I",value=sign }
                //        };
                //        document.invoiceId = invoices.Where(x => x.ReturnCode == document.internalID).Select(x => x.SL_ReturnId).FirstOrDefault();

                //        MyHelper.UpdateST_UserLog(db, "1", "adding signed doc. to list", 1, 1);
                //        db.SubmitChanges();
                //        documents.Add(document);

                //    }

                //    MyHelper.UpdateST_UserLog(db, "1", "Serialize documents", 1, 1);
                //    db.SubmitChanges();
                //    var signedinvoice = JsonConvert.SerializeObject(documents);

                //    MyHelper.UpdateST_UserLog(db, "1", "prepare to sync signed content", 1, 1);
                //    db.SubmitChanges();
                //    var _signedcontent = new StringContent(signedinvoice, Encoding.UTF8, "application/json");
                //    HttpResponseMessage submitSigned = await client.PostAsync(string.Format("api/Einvoice/SubmitSignDoc"), _signedcontent);

                //    MyHelper.UpdateST_UserLog(db, "1", "SubmitSignDoc" + _signedcontent, 1, 1);
                //    db.SubmitChanges();
                //    if (submitSigned.IsSuccessStatusCode)
                //    {
                //        Syncpopup.Hide();

                //        #region Step 7: Check for Errors sent back from server
                //        JsonContent = submitSigned.Content.ReadAsStringAsync().Result;
                //        MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                //        MyHelper.UpdateST_UserLog(db, "1", $"Validation Messages From SubmitSignDoc : { JsonContent }", 1, 1);
                //        db.SubmitChanges();

                //        var ServerErrors = JsonConvert.DeserializeObject<ValidationMessages>(JsonContent);
                //        if (ServerErrors.Messages.Count > 0)
                //        {
                //            validationMessages_Lst.Add(ServerErrors);
                //        }
                //        #endregion

                //        MyHelper.UpdateST_UserLog(db, "1", $"Validation Messages Count : { validationMessages_Lst.Count().ToString() }", 1, 1);
                //        db.SubmitChanges();
                //        if (validationMessages_Lst.Count > 0)
                //        {
                //            MessageBox.Show("تم إرسال بعض الاشعارات، ويوجد أخطاء في البعض الآخر", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                //            string ErrorMsg = "";

                //            foreach (var er_Lst in validationMessages_Lst)
                //            {
                //                foreach (var er in er_Lst.Messages)
                //                {
                //                    var message = "";
                //                    foreach (var m in er.Message)
                //                    {
                //                        message = string.IsNullOrEmpty(message) ? m : message + " - " + m;
                //                    }
                //                    //ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + er.Message + "; \r\n ";
                //                    ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + message + "; \r\n ";
                //                }
                //            }

                //            MessageBox.Show(ErrorMsg);
                //        }
                //        else
                //            MessageBox.Show("تم الإرسال بنجاح");

                //        System.Threading.Thread.Sleep(2000);

                //        PopupNotifier popup = new PopupNotifier();
                //        popup.TitleText = "تحديث";
                //        popup.ContentText = "جاري تحديث حالة الاشعارات، يرجى الانتظار";
                //        popup.Image = Properties.Resources.N_journal;
                //        popup.Popup();// show  

                //        db.Refresh(System.Data.Linq.RefreshMode.OverwriteCurrentValues, db.SL_Invoices);
                //        invoices = db.SL_Returns.Where(x => lst.Contains(x.SL_ReturnId)).ToList();

                //        Dictionary<int, string> keyValuePairs = (from i in invoices
                //                                                     //join l in lst on i.SL_InvoiceId equals l
                //                                                 select new KeyValuePair<int, string>(i.SL_ReturnId, i.uuid)
                //                                      ).ToDictionary(x => x.Key, x => x.Value);


                //        List<UpdateDocumentStatus> updateDocumentStatuses = new List<UpdateDocumentStatus>();
                //        foreach (var k in keyValuePairs)
                //            updateDocumentStatuses.Add(new UpdateDocumentStatus { keyValuePairs = k, documentType =(int) DocumentType.C });

                //        var id_UUid = JsonConvert.SerializeObject(updateDocumentStatuses);
                //        var id_UUid_content = new StringContent(id_UUid, Encoding.UTF8, "application/json");

                //        HttpResponseMessage UpdateInvoicesState = await client.PostAsync(string.Format("api/Einvoice/UpdateAcceptedDocuments"), id_UUid_content);
                //        MyHelper.UpdateST_UserLog(db, "1", "UpdateInvoicesState: " + UpdateInvoicesState.Content.ReadAsStringAsync().Result, 1, 1);
                //        db.SubmitChanges();

                //        popup.Hide();


                //    }
                //    else
                //    {
                //        JsonContent = submitSigned.Content.ReadAsStringAsync().Result;
                //        // MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                //        db.SubmitChanges();

                //        var ServerErrors = JsonConvert.DeserializeObject<ValidationMessages>(JsonContent);
                //        validationMessages_Lst.Add(ServerErrors);
                //        if (validationMessages_Lst.Count > 0)
                //        {
                //            MessageBox.Show("فشل محاولة ارسال الاشعارات، يوجد أخطاء في الاشعارات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                //            string ErrorMsg = "";

                //            foreach (var er_Lst in validationMessages_Lst)
                //            {
                //                foreach (var er in er_Lst.Messages)
                //                {
                //                    var message = "";
                //                    foreach (var m in er.Message)
                //                    {
                //                        message = string.IsNullOrEmpty(message) ? m : message + " - " + m;
                //                    }
                //                    ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + message + "; \r\n ";
                //                }
                //            }

                //            MessageBox.Show(ErrorMsg);
                //        }
                //    }
                //}
                //else
                //{
                //    Syncpopup.Hide();
                //    var JsonContent = response_.Content.ReadAsStringAsync().Result;
                //    MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                //    db.SubmitChanges();
                //    var ServerErrors = JsonConvert.DeserializeObject<ValidationMessages>(JsonContent);
                //    validationMessages_Lst.Add(ServerErrors);

                //    if (validationMessages_Lst.Count > 0)
                //    {
                //        MessageBox.Show("يوجد أخطاء في بعض الاشعارات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                //        string ErrorMsg = "";

                //        foreach (var er_Lst in validationMessages_Lst)
                //        {
                //            foreach (var er in er_Lst.Messages)
                //            {
                //                var message = "";
                //                foreach (var m in er.Message)
                //                {
                //                    message = string.IsNullOrEmpty(message) ? m : message + " - " + m;
                //                }
                //                ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + message + "; \r\n ";
                //            }
                //        }

                //        MessageBox.Show(ErrorMsg);
                //    }
                //}
                //validationMessages_Lst.Clear();
                //btn_Advanced.Enabled = true;
            }
            catch (Exception exc)
            {
                MyHelper.UpdateST_UserLog(db, " ", "exception" + exc.InnerException?.Message, 1, 1);
                btn_Advanced.Enabled = true;
                MessageBox.Show(exc.Message);
            }
        }

        async Task SyncInvoicesMCDR(List<int> lst)
        {
            ERPDataContext db = new ERPDataContext();
            try
            {
                #region PopupNotifier
                PopupNotifier Syncpopup = new PopupNotifier();
                Syncpopup.TitleText = "مزامنة";
                Syncpopup.ContentText = "جاري مزامنة الاشعارات مع البوابة، يرجى الانتظار";
                Syncpopup.Image = Properties.Resources.N_journal;
                Syncpopup.Popup();// show  
                #endregion

                #region Create Client
                HttpClient client = new HttpClient();
                UriBuilder uriBuilder = new UriBuilder(Properties.Settings.Default.BackEndPoint);
                uriBuilder.Port = Properties.Settings.Default.BackEndPort;
                client.BaseAddress = uriBuilder.Uri;
                client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                MyHelper.UpdateST_UserLog(db, "1", "b4 SerializeObject", 1, 1);
                db.SubmitChanges();
                #endregion

                #region Step 1: Serialize Invoices
                List<Note4Post> Note4Posts = new List<Note4Post>();
                foreach (var l in lst)
                    Note4Posts.Add(new Note4Post { invoiceId = l, documentType = (int)DocumentType.C });

                var serialzedInvoices = JsonConvert.SerializeObject(Note4Posts);
                MyHelper.UpdateST_UserLog(db, "1", "after SerializeObject", 1, 1);
                db.SubmitChanges();
                var content = new StringContent(serialzedInvoices, Encoding.UTF8, "application/json");
                //HttpResponseMessage APIResponse = client.PostAsync(builder.Uri, content).Result;
                MyHelper.UpdateST_UserLog(db, "1", "content ready 2 call GetUnSignDoc", 1, 1);
                db.SubmitChanges();
                #endregion

                #region Step 2: Send Invoices to be validated locally in GetUnSignDoc
                HttpResponseMessage response_ = await client.PostAsync(string.Format("api/Einvoice/GetUnSignDoc"), content);

                MyHelper.UpdateST_UserLog(db, "1", "response 2 call   ", 1, 1);
                db.SubmitChanges();
                #endregion

                if (response_.IsSuccessStatusCode)
                {
                    MyHelper.UpdateST_UserLog(db, "1", "success response", 1, 1);
                    db.SubmitChanges();

                    var documents = new List<Models_1.ViewModels.Document>();

                    #region Step 3: Receive Locally Validated Invoices
                    var JsonContent = response_.Content.ReadAsStringAsync().Result;
                    MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                    db.SubmitChanges();

                    var DocumentsToSign_ = JsonConvert.DeserializeObject<DocumentsToSignVM>(JsonContent);
                    MyHelper.UpdateST_UserLog(db, "1", $"Validation Messages From GetUnSignDoc : { DocumentsToSign_.Errors }", 1, 1);
                    db.SubmitChanges();
                    #endregion

                    #region Step 4: Add Local Validation Errors To Errors List
                    if (DocumentsToSign_.Errors.Messages.Count() > 0)
                    {
                        validationMessages_Lst.Add(DocumentsToSign_.Errors);
                    }
                    #endregion

                    #region Step 5: Prepare Valid invoices to be Signed

                    //SignuatuerGenerator.BL.Model mm = new SignuatuerGenerator.BL.Model() { CretSerial = "", DocumentJson = "", DonglePIN = "" };

                    //var result = JsonConvert.DeserializeObject<List<SignuatuerGenerator.BL.Model>>(JsonContent);

                    var to_B_signed_Docs_Lst = DocumentsToSign_.Documents;

                    MyHelper.UpdateST_UserLog(db, "1", "content is Deserialized", 1, 1);
                    db.SubmitChanges();

                    var invoices = db.SL_Returns.Where(x => lst.Contains(x.SL_ReturnId)).ToList();
                    foreach (var doc in to_B_signed_Docs_Lst)
                    //foreach (var doc in _2BSignedDoc)
                    {
                        //if (string.IsNullOrEmpty(doc.CretSerial))
                        //{
                        //    MyHelper.UpdateST_UserLog(db, "1", "doc.CretSerial -" + doc.CretSerial, 1, 1);
                        //    db.SubmitChanges();
                        //    doc.CretSerial = DAL.Shared.Dongle_SN;
                        //}
                        MyHelper.UpdateST_UserLog(db, "1", "prepare document to be signed", 1, 1);
                        db.SubmitChanges();
                        var document = JsonConvert.DeserializeObject<Models_1.ViewModels.Document>(doc.DocumentJson);




                        //var CompanyInfo = db.ST_CompanyInfos.FirstOrDefault();
                        //var publicKey = CompanyInfo.PublicKey;
                        //var tokenSerial = CompanyInfo.TokenSerial;
                        // E:\\\eps2003csp1164.dll

                        string libraryPath = Properties.Settings.Default.signuatuer_dll;
                        //var Pin = CompanyInfo.Pin;
                        //var CertificateCompanyType = CompanyInfo.CertificateCompanyType;
                        var sign = "";
                        //   MCDRSignature.GetSignatuerEgyptTrust signture = new SignuatuerGenerator.GetSignatuerEgyptTrust();
                        //sign = MCDRSignature.GetSigniture(doc.DonglePIN, "MCDR 2019", libraryPath, doc.DocumentJson);


                        //SignuatuerGenerator.BL.Model _model = new SignuatuerGenerator.BL.Model()
                        //{
                        //    CretSerial = DAL.Shared.Dongle_SN,
                        //    DocumentJson = doc.DocumentJson,
                        //    DonglePIN = doc.DonglePIN
                        //};


                        //var sign = SignuatuerGenerator.GetSignuatuer.SignDocument(_model);


                        MyHelper.UpdateST_UserLog(db, "1", "extract the signature", 1, 1);
                        db.SubmitChanges();
                        document.signatures = new List<Models_1.ViewModels.Signature>()
                        {
                            new Models_1.ViewModels.Signature(){ signatureType="I",value=sign }
                        };
                        document.invoiceId = invoices.Where(x => x.ReturnCode == document.internalID).Select(x => x.SL_ReturnId).FirstOrDefault();

                        MyHelper.UpdateST_UserLog(db, "1", "adding signed doc. to list", 1, 1);
                        db.SubmitChanges();
                        documents.Add(document);

                    }

                    MyHelper.UpdateST_UserLog(db, "1", "Serialize documents", 1, 1);
                    db.SubmitChanges();
                    var signedinvoice = JsonConvert.SerializeObject(documents);

                    MyHelper.UpdateST_UserLog(db, "1", "prepare to sync signed content", 1, 1);
                    db.SubmitChanges();
                    #endregion

                    #region Step 6: Sign Documents
                    var _signedcontent = new StringContent(signedinvoice, Encoding.UTF8, "application/json");
                    HttpResponseMessage submitSigned = await client.PostAsync(string.Format("api/Einvoice/SubmitSignDoc"), _signedcontent);

                    MyHelper.UpdateST_UserLog(db, "1", "SubmitSignDoc" + _signedcontent, 1, 1);
                    db.SubmitChanges();
                    #endregion

                    if (submitSigned.IsSuccessStatusCode)
                    {
                        Syncpopup.Hide();

                        #region Step 7: Check for Errors sent back from server
                        JsonContent = submitSigned.Content.ReadAsStringAsync().Result;
                        MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                        MyHelper.UpdateST_UserLog(db, "1", $"Validation Messages From SubmitSignDoc : { JsonContent }", 1, 1);
                        db.SubmitChanges();

                        var ServerErrors = JsonConvert.DeserializeObject<ValidationMessages>(JsonContent);
                        if (ServerErrors.Messages.Count > 0)
                        {
                            validationMessages_Lst.Add(ServerErrors);
                        }
                        #endregion
                        MyHelper.UpdateST_UserLog(db, "1", $"Validation Messages Count : { validationMessages_Lst.Count().ToString() }", 1, 1);
                        db.SubmitChanges();
                        if (validationMessages_Lst.Count > 0)
                        {
                            MessageBox.Show("تم إرسال بعض الاشعارات، ويوجد أخطاء في البعض الآخر", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            string ErrorMsg = "";

                            foreach (var er_Lst in validationMessages_Lst)
                            {
                                foreach (var er in er_Lst.Messages)
                                {
                                    var message = "";
                                    foreach (var m in er.Message)
                                    {
                                        message = string.IsNullOrEmpty(message) ? m : message + " - " + m;
                                    }
                                    //ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + er.Message + "; \r\n ";
                                    ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + message + "; \r\n ";
                                }
                            }

                            MessageBox.Show(ErrorMsg);
                        }
                        else
                            MessageBox.Show("تم الإرسال بنجاح");

                        System.Threading.Thread.Sleep(2000);

                        #region PopupNotifier
                        PopupNotifier popup = new PopupNotifier();
                        popup.TitleText = "تحديث";
                        popup.ContentText = "جاري تحديث حالة الاشعارات، يرجى الانتظار";
                        popup.Image = Properties.Resources.N_journal;
                        popup.Popup();// show  
                        #endregion

                        db.Refresh(System.Data.Linq.RefreshMode.OverwriteCurrentValues, db.SL_Invoices);
                        invoices = db.SL_Returns.Where(x => lst.Contains(x.SL_ReturnId)).ToList();

                        Dictionary<int, string> keyValuePairs = (from i in invoices
                                                                     //join l in lst on i.SL_InvoiceId equals l
                                                                 select new KeyValuePair<int, string>(i.SL_ReturnId, i.uuid)
                                                                 ).ToDictionary(x => x.Key, x => x.Value);
                        List<UpdateDocumentStatus> updateDocumentStatuses = new List<UpdateDocumentStatus>();

                        foreach (var k in keyValuePairs)
                            updateDocumentStatuses.Add(new UpdateDocumentStatus { keyValuePairs = k, documentType = (int)DocumentType.C });


                        var id_UUid = JsonConvert.SerializeObject(updateDocumentStatuses);
                        var id_UUid_content = new StringContent(id_UUid, Encoding.UTF8, "application/json");

                        MyHelper.UpdateST_UserLog(db, "1", "object to send to UpdateAcceptedDocuments: " + id_UUid_content, 1, 1);
                        db.SubmitChanges();

                        HttpResponseMessage UpdateInvoicesState = await client.PostAsync(string.Format("api/Einvoice/UpdateAcceptedDocuments"), id_UUid_content);
                        MyHelper.UpdateST_UserLog(db, "1", "UpdateInvoicesState: " + UpdateInvoicesState.Content.ReadAsStringAsync().Result, 1, 1);
                        db.SubmitChanges();

                        popup.Hide();


                    }
                    else
                    {
                        JsonContent = submitSigned.Content.ReadAsStringAsync().Result;
                        // MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                        db.SubmitChanges();

                        var ServerErrors = JsonConvert.DeserializeObject<ValidationMessages>(JsonContent);
                        validationMessages_Lst.Add(ServerErrors);
                        if (validationMessages_Lst.Count > 0)
                        {
                            MessageBox.Show("فشل محاولة ارسال الاشعارات، يوجد أخطاء في الاشعارات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            string ErrorMsg = "";

                            foreach (var er_Lst in validationMessages_Lst)
                            {
                                foreach (var er in er_Lst.Messages)
                                {
                                    var message = "";
                                    foreach (var m in er.Message)
                                    {
                                        message = string.IsNullOrEmpty(message) ? m : message + " - " + m;
                                    }
                                    ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + message + "; \r\n ";
                                }
                            }

                            MessageBox.Show(ErrorMsg);
                        }
                    }
                }
                else
                {
                    Syncpopup.Hide();
                    var JsonContent = response_.Content.ReadAsStringAsync().Result;
                    MyHelper.UpdateST_UserLog(db, "1", "prepare json content", 1, 1);
                    db.SubmitChanges();

                    var ServerErrors = JsonConvert.DeserializeObject<ValidationMessages>(JsonContent);
                    validationMessages_Lst.Add(ServerErrors);

                    if (validationMessages_Lst.Count > 0)
                    {
                        MessageBox.Show("يوجد أخطاء في بعض الاشعارات", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        string ErrorMsg = "";

                        foreach (var er_Lst in validationMessages_Lst)
                        {
                            foreach (var er in er_Lst.Messages)
                            {
                                var message = "";
                                foreach (var m in er.Message)
                                {
                                    message = string.IsNullOrEmpty(message) ? m : message + " - " + m;
                                }
                                //ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + er.Message + "; \r\n ";
                                ErrorMsg = ErrorMsg + "Invoice Code= " + er.invoiceCode + ", Message: " + message + "; \r\n ";
                            }
                        }

                        MessageBox.Show(ErrorMsg);
                    }
                    //MessageBox.Show("فشل في تكوين الملف قبل إرساله");
                }

                validationMessages_Lst.Clear();
                btn_Advanced.Enabled = true;


            }
            catch (Exception exc)
            {
                MyHelper.UpdateST_UserLog(db, " ", "exception" + exc.InnerException?.Message, 1, 1);

                MessageBox.Show(exc.Message);
                btn_Advanced.Enabled = true;
                validationMessages_Lst.Clear();
            }
        }
        private void labelControl1_Click(object sender, EventArgs e)
        {

        }

        private void btn_UpdateInvoices_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Update_Invoices_Status().ConfigureAwait(false);
            barBtnRefresh.PerformClick();
        }

        private async Task Update_Invoices_Status()
        {
            ERPDataContext db = new ERPDataContext();
            PopupNotifier popup = new PopupNotifier();
            popup.TitleText = "تحديث";
            popup.ContentText = "جاري تحديث حالة الاشعارات، يرجى الانتظار";
            popup.Image = Properties.Resources.N_journal;
            popup.Popup();// show  

            db.Refresh(System.Data.Linq.RefreshMode.OverwriteCurrentValues, db.SL_Invoices);
            var invoices = db.SL_Returns.Where(x => (x.EstatusCode == null || x.EstatusCode == 0) && x.syncDate != null).ToList();

            Dictionary<int, string> keyValuePairs = (from i in invoices
                                                         //join l in lst on i.SL_InvoiceId equals l
                                                     select new KeyValuePair<int, string>(i.SL_ReturnId, i.uuid)
                                                     ).ToDictionary(x => x.Key, x => x.Value);


            List<UpdateDocumentStatus> updateDocumentStatuses = new List<UpdateDocumentStatus>();
            foreach (var k in keyValuePairs)
                updateDocumentStatuses.Add(new UpdateDocumentStatus { keyValuePairs = k, documentType = (int)DocumentType.C });

            var id_UUid = JsonConvert.SerializeObject(updateDocumentStatuses);
            var id_UUid_content = new StringContent(id_UUid, Encoding.UTF8, "application/json");

            HttpClient client = new HttpClient();
            UriBuilder uriBuilder = new UriBuilder(Properties.Settings.Default.BackEndPoint);
            uriBuilder.Port = Properties.Settings.Default.BackEndPort;
            client.BaseAddress = uriBuilder.Uri;
            client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
            try
            {
                HttpResponseMessage UpdateInvoicesState = await client.PostAsync(string.Format("api/Einvoice/UpdateAcceptedDocuments"), id_UUid_content);
                MyHelper.UpdateST_UserLog(db, "1", "UpdateInvoicesState: " + UpdateInvoicesState.Content.ReadAsStringAsync().Result, 1, 1);
                db.SubmitChanges();

                popup.Hide();
                MessageBox.Show("تم تحديث حالة الاشعارات");
            }
            catch (Exception exc)
            {
                MessageBox.Show(exc.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void barBtn_PrintData_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdInvoices.MinimumSize = grdInvoices.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdInvoices, false, true).ShowPreview();
            grdInvoices.MinimumSize = new Size(0, 0);
        }
    }
}