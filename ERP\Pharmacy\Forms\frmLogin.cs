﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Configuration;
using DAL;
using DAL.Res;
using DevExpress.XtraEditors;
using System.Net;
using System.Xml;
using System.IO;


namespace Pharmacy.Forms
{
    public partial class frmLogin : DevExpress.XtraEditors.XtraForm
    {
        XmlWriterSettings settings;// stores language(Arabic/English)

        //public static string ServerIPAddress;
        //private Proshot.CommandClient.CMDClient client;
        //public Proshot.CommandClient.CMDClient Client
        //{
        //    get { return client; }
        //}

        public frmLogin()
        {
            ReadSettingsFile();

            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
        }

        private void frmLogin_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            if (Shared.IsEnglish)
                cmbLang.SelectedIndex = 1;
            else
                cmbLang.SelectedIndex = 0;

            if (Program.ModelIsERP == true)
            {
                txttitle.Text = (Shared.IsEnglish == true ? ResEn.AppName : ResAr.AppName);
            }
            else
            {
                txttitle.Text = (Shared.IsEnglish == true ? ResEn.AppPOSName : ResAr.AppPOSName);
                //pictureEdit1.Image = Pharmacy.Properties.Resources.Splash_pos1;
            }

            txttitle.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            ConnectionStringSettingsCollection connections = ConfigurationManager.ConnectionStrings;
            int index = 0;
            foreach (ConnectionStringSettings connection in connections)
            {
                string name = connection.Name;
                string provider = connection.ProviderName;
                string connectionString = connection.ConnectionString;

                if (connection.Name.ToLower() == "localsqlserver" || connection.Name.ToLower() == "localmysqlserver")
                    continue;

                if (connection.Name == "Pharmacy.Properties.Settings.ERPConnectionString")
                    cb_ConnectionString.Properties.Items.Add(new DevExpress.XtraEditors.Controls.ImageComboBoxItem
                        ("My Company", connectionString, index));
                else
                    cb_ConnectionString.Properties.Items.Add(new DevExpress.XtraEditors.Controls.ImageComboBoxItem
                        (name.Replace("Pharmacy.Properties.Settings.", ""), connectionString, index));
                index += 1;
            }
            cb_ConnectionString.SelectedIndex = 0;

        }

        private void frmLogin_Shown(object sender, EventArgs e)
        {

        }

        private void btn_Enter_Click(object sender, EventArgs e)
        {
            if (cb_ConnectionString.EditValue == null)
                return;

            DAL.Config.ConnectionString = cb_ConnectionString.EditValue.ToString().Replace("?", "sa@NeoTech");
            if (txtName.Text == string.Empty)
            {
                txtName.ErrorText = "*";
                return;
            }
            if (txtPass.Text == string.Empty)
            {
                txtPass.ErrorText = "*";
                return;
            }
            ValidateUser();
        }

        private void txtPass_KeyDown(object sender, KeyEventArgs e)
        {
            //validate user wher user press Enter key when focusing on password textBox
            if (e.KeyValue.ToString() == "13")
            {
                btn_Enter.PerformClick();
            }
        }

        private void ValidateUser()
        {
            //AppDomain currentDomain = AppDomain.CurrentDomain;
            //currentDomain.UnhandledException += new UnhandledExceptionEventHandler(MyHandler);

            ErpUtils.Check_Application_Validation();
            if (frmActivation.Is_Activated == false)
                return;

            #region Tcp/IP Connection --commented
            //int serverPort = 8000;
            //if (int.TryParse(Properties.Settings.Default.LmPort.ToString(), out serverPort) == false)
            //{
            //    MessageBox.Show("Port is not recoded correctly in App.config file");
            //    return;
            //}

            ////get server IP Address
            //string srvName = string.Empty;
            //try
            //{
            //    srvName = new ERPDataContext().ST_CompanyInfos.First().ServerName;
            //}
            //catch { }

            //if (srvName == null || srvName == string.Empty)
            //{
            //    MessageBox.Show("لم يتم تحديد الخادم حتي الان");
            //    return;
            //}
            //try
            //{
            //    IPAddress ip = null;
            //    //if (Properties.Settings.Default.ServerIP != string.Empty)
            //    //    ip = IPAddress.Parse(Properties.Settings.Default.ServerIP);

            //    if (!string.IsNullOrEmpty(Properties.Settings.Default.ServerIP) &&
            //        IPAddress.TryParse(Properties.Settings.Default.ServerIP, out ip))
            //    {
            //    }
            //    else if (!string.IsNullOrEmpty(Properties.Settings.Default.ServerIP) &&
            //        Utilities.GetResolvedConnecionIPAddress(Properties.Settings.Default.ServerIP, out ip))
            //    {
            //    }
            //    else if (Utilities.GetResolvedConnecionIPAddress(srvName, out ip))
            //    { 
            //    }
            //    else
            //    {
            //        MessageBox.Show("Can't find Server IP Address!");
            //        return;
            //    }


            //    ServerIPAddress = ip.ToString();
            //    Control.CheckForIllegalCrossThreadCalls = false;
            //    this.client = new Proshot.CommandClient.CMDClient(ip, serverPort, "None");
            //    this.client.CommandReceived += new Proshot.CommandClient.CommandReceivedEventHandler(CommandReceived);
            //    this.client.ConnectingSuccessed += new Proshot.CommandClient.ConnectingSuccessedEventHandler(client_ConnectingSuccessed);
            //    this.client.ConnectingFailed += new Proshot.CommandClient.ConnectingFailedEventHandler(client_ConnectingFailed);
            //}
            //catch
            //{
            //    MessageBox.Show("يرجي اعادة تشغيل الخادم");
            //    return;
            //}
            #endregion

            ERPDataContext DB = new ERPDataContext();
            //string n = Crypto.EncryptStringAES(txtPass.Text.Trim(), Crypto.Key);


            var user = (from u in DB.HR_Users
                        where u.UserName == txtName.Text.Trim() &&
                        u.Password == Crypto.EncryptStringAES(txtPass.Text.Trim(), Crypto.Key) &&
                        u.IsDeleted == false
                        select u).SingleOrDefault();
            if (user == null)
            {
                MessageBox.Show(cmbLang.SelectedIndex == 1 ? ResEn.ValidUser : ResAr.ValidUser, "", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            else
            {
                if (user.AccessType == (byte)AccessType.NoAccess)
                {
                    MessageBox.Show(cmbLang.SelectedIndex == 1 ? ResEn.AccessDenied : ResAr.AccessDenied, "", MessageBoxButtons.OK, MessageBoxIcon.Stop);
                    return;
                }

                try
                {
                    Shared.lst_Users = (from u in DB.HR_Users
                                        select new UserIdName
                                        {
                                            UserId = u.UserId,
                                            Name = u.UserName
                                        }).ToList();

                    #region Load User Profile
                    Shared.UserId = user.UserId;
                    Shared.UserName = user.UserName;


                    Shared.IC_InTrns_FromDate = user.IC_InTrns_FromDate;
                    Shared.IC_InTrns_ToDate = user.IC_InTrns_ToDate;

                    Shared.IC_OutTrns_FromDate = user.IC_OutTrns_FromDate;
                    Shared.IC_OutTrns_ToDate = user.IC_OutTrns_ToDate;

                    Shared.IC_Damage_FromDate = user.IC_Damage_FromDate;
                    Shared.IC_Damage_ToDate = user.IC_Damage_ToDate;

                    Shared.IC_Transfer_FromDate = user.IC_Transfer_FromDate;
                    Shared.IC_Transfer_ToDate = user.IC_Transfer_ToDate;


                    Shared.PR_I_FromDate = user.PR_I_FromDate;
                    Shared.PR_I_ToDate = user.PR_I_ToDate;

                    Shared.PR_R_FromDate = user.PR_R_FromDate;
                    Shared.PR_R_ToDate = user.PR_R_ToDate;

                    Shared.SL_I_FromDate = user.SL_I_FromDate;
                    Shared.SL_I_ToDate = user.SL_I_ToDate;

                    Shared.SL_Q_FromDate = user.SL_Q_FromDate;
                    Shared.SL_Q_ToDate = user.SL_Q_ToDate;

                    Shared.SL_R_FromDate = user.SL_R_FromDate;
                    Shared.SL_R_ToDate = user.SL_R_ToDate;

                    Shared.Mr_InDrctSl_FromDate = user.Mr_InDrctSl_FromDate;
                    Shared.Mr_InDrctSl_ToDate = user.Mr_InDrctSl_ToDate;

                    Shared.Cash_FromDate = user.Cash_FromDate;
                    Shared.Cash_ToDate = user.Cash_ToDate;

                    Shared.Jrnl_FromDate = user.Jrnl_FromDate;
                    Shared.Jrnl_ToDate = user.Jrnl_ToDate;

                    Shared.Acc_CashTransfer_FromDate = user.Acc_CashTransfer_FromDate;
                    Shared.Acc_CashTransfer_ToDate = user.Acc_CashTransfer_ToDate;
                    Shared.Acc_Rev_FromDate = user.Acc_Rev_FromDate;
                    Shared.Acc_Rev_ToDate = user.Acc_Rev_ToDate;
                    Shared.Acc_Exp_FromDate = user.Acc_Exp_FromDate;
                    Shared.Acc_Exp_ToDate = user.Acc_Exp_ToDate;

                    Shared.Acc_RecNote_FromDate = user.Acc_RecNote_FromDate;
                    Shared.Acc_RecNote_ToDate = user.Acc_RecNote_ToDate;
                    Shared.Acc_PayNote_FromDate = user.Acc_PayNote_FromDate;
                    Shared.Acc_PayNote_ToDate = user.Acc_PayNote_ToDate;

                    Shared.Acc_PayNote_Still = user.Acc_PayNote_Still;
                    Shared.Acc_PayNote_Paid = user.Acc_PayNote_Paid;
                    Shared.Acc_PayNote_Rejected = user.Acc_PayNote_Rejected;
                    Shared.Acc_PayNote_Overdue = user.Acc_PayNote_Overdue;
                    Shared.Acc_ReceiveNote_Still = user.Acc_ReceiveNote_Still;
                    Shared.Acc_ReceiveNote_Paid = user.Acc_ReceiveNote_Paid;
                    Shared.Acc_ReceiveNote_Rejected = user.Acc_ReceiveNote_Rejected;
                    Shared.Acc_ReceiveNote_Overdue = user.Acc_ReceiveNote_Overdue;

                    Shared.JO_Reg_FromDate = user.JO_Reg_FromDate;
                    Shared.JO_Reg_ToDate = user.JO_Reg_ToDate;
                    Shared.JO_Due_FromDate = user.JO_Due_FromDate;
                    Shared.JO_Due_ToDate = user.JO_Due_ToDate;

                    Shared.Man_FromDate = user.Man_FromDate;
                    Shared.Man_ToDate = user.Man_ToDate;
                    Shared.Man_QC_FromDate = user.Man_QC_FromDate;
                    Shared.Man_QC_ToDate = user.Man_QC_ToDate;

                    Shared.att_clWeekEnd = user.att_clWeekEnd;
                    Shared.att_clFormalVacation = user.att_clFormalVacation;
                    Shared.att_clEmpVacation = user.att_clEmpVacation;
                    Shared.att_clEmpAbsence = user.att_clEmpAbsence;
                    Shared.att_clDelay = user.att_clDelay;

                    Shared.HR_vacation_FromDate = user.HR_vacation_FromDate;
                    Shared.HR_vacation_ToDate = user.HR_vacation_ToDate;
                    Shared.HR_Absence_FromDate = user.HR_Absence_FromDate;
                    Shared.HR_Absence_ToDate = user.HR_Absence_ToDate;
                    Shared.HR_Delay_FromDate = user.HR_Delay_FromDate;
                    Shared.HR_Delay_ToDate = user.HR_Delay_ToDate;
                    Shared.HR_OverTime_FromDate = user.HR_OverTime_FromDate;
                    Shared.HR_OverTime_ToDate = user.HR_OverTime_ToDate;
                    Shared.HR_Reward_FromDate = user.HR_Reward_FromDate;
                    Shared.HR_Reward_ToDate = user.HR_Reward_ToDate;
                    Shared.HR_Penality_FromDate = user.HR_Penality_FromDate;
                    Shared.HR_Penality_ToDate = user.HR_Penality_ToDate;
                    Shared.HR_Pay_FromDate = user.HR_Pay_FromDate;
                    Shared.HR_Pay_ToDate = user.HR_Pay_ToDate;
                    Shared.HR_Loan_FromDate = user.Loan_FromDate;
                    Shared.HR_Loan_ToDate = user.Loan_ToDate;
                    Shared.HR_Eval_FromDate = user.HR_Eval_FromDate;
                    Shared.HR_Eval_ToDate = user.HR_Eval_ToDate;
                    Shared.HR_Prom_FromDate = user.HR_Prom_FromDate;
                    Shared.HR_Prom_ToDate = user.HR_Prom_ToDate;
                    Shared.HR_SponsrChng_FromDate = user.HR_SponsrChng_FromDate;
                    Shared.HR_SponsrChng_ToDate = user.HR_SponsrChng_ToDate;
                    Shared.HR_Train_FromDate = user.HR_Train_FromDate;
                    Shared.HR_Train_ToDate = user.HR_Train_ToDate;
                    Shared.HR_Train_FromDate = user.HR_Train_FromDate;
                    Shared.HR_Train_ToDate = user.HR_Train_ToDate;
                    Shared.HR_ShiftReplace_FromDate = user.ShiftReplace_FromDate;
                    Shared.HR_ShiftReplace_ToDate = user.ShiftReplace_ToDate;

                    Shared.Acc_DebitNote_FromDate = user.Acc_DebitNote_FromDate;
                    Shared.Acc_DebitNote_ToDate = user.Acc_DebitNote_ToDate;
                    Shared.Acc_CreditNote_FromDate = user.Acc_CreditNote_FromDate;
                    Shared.Acc_CreditNote_ToDate = user.Acc_CreditNote_ToDate;

                    Shared.Weight_FromDate = user.Weight_FromDate;
                    Shared.Weight_ToDate = user.Weight_ToDate;

                    Shared.ActiveNavBarGroup_Loaded = user.ActiveNavBarGroup;
                    Shared.StyleName = user.StyleName;
                    Shared.ShowMainChart_ = user.ShowMainChart.HasValue ? user.ShowMainChart.Value : false;
                    Shared.user = user;
                    HelperAcc.AccSecurityLevel = user.AccSecurityLevel;

                    #endregion

                    SaveSetingsFile();


                    this.Hide();
                    frmMain frm = new frmMain();
                    frm.ShowDialog();

                    //this.client.Disconnect();
                    //this.Close();

                    //else
                    //   MessageBox.Show("الخادم غير متاح !", "خطأ");



                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    MessageBox.Show(ex.InnerException?.Message);
                    //MessageBox.Show(ex.Source);
                }
            }
        }

        //static void MyHandler(object sender, UnhandledExceptionEventArgs args)
        //{
        //    try
        //    {
        //        Exception e = (Exception)args.ExceptionObject;
        //        //MessageBox.Show("MyHandler caught : " + e.Message);
        //        //MessageBox.Show("Runtime terminating: " + args.IsTerminating);
        //        Utilities.save_Log(e.Message, e);
        //        Utilities.save_Log(args.ExceptionObject.ToString(), e);
        //        if (args.IsTerminating)
        //            MessageBox.Show(Shared.IsEnglish ? "Sorry, Fatal error occurred, program will terminate unexpectedly" :
        //                "عذرا، حدث خطأ فادح، سيتم إنهاء البرنامج بشكل غير متوقع",
        //                Shared.IsEnglish ? "Alert" : "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //    }
        //    catch { }
        //}

        #region ConnectToLicenseManager

        //private void client_ConnectingFailed(object sender, EventArgs e)
        //{
        //    MessageBox.Show("الخادم غير متاح !", "خطأ");
        //}

        //private void client_ConnectingSuccessed(object sender, EventArgs e)
        //{
        //    this.client.SendCommand(new Proshot.CommandClient.Command(Proshot.CommandClient.CommandType.IsNameExists, this.client.IP, this.client.NetworkName));
        //}

        //void CommandReceived(object sender, Proshot.CommandClient.CommandEventArgs e)
        //{
        //    if (e.Command.CommandType == Proshot.CommandClient.CommandType.IsNameExists)
        //    {
        //        if (e.Command.MetaData.ToLower() == "false")
        //        {
        //            MessageBox.Show("تم تجاوز عدد الاجهزه المتصله المسموح به, أو لم يتم تحديد عدد الاجهزه المتاحة");
        //            this.client.Disconnect();
        //            Application.Exit();
        //        }
        //        else
        //        {

        //            this.Close();
        //        }
        //    }
        //}


        //private void btnEnter_Click(object sender, EventArgs e)
        //{
        //    this.LoginToServer();
        //}

        //private void LoginToServer()
        //{
        //    this.client.NetworkName = this.txtName.Text.Trim();
        //    this.client.ConnectToServer();
        //}

        //private void frmLogin_FormClosing(object sender, FormClosingEventArgs e)
        //{
        //    this.client.CommandReceived -= new Proshot.CommandClient.CommandReceivedEventHandler(CommandReceived);
        //}
        #endregion

        #region Local Setting File

        /// <summary>
        /// read settings file to get profile language
        /// </summary>
        private void ReadSettingsFile()
        {
            XmlReader reader = null;
            try
            {
                if (!File.Exists(Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + "ERP_Profile.xml"))
                    return;

                reader = XmlReader.Create(Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + "ERP_Profile.xml");

                while (reader.Read())
                {
                    if (reader.NodeType == XmlNodeType.Element && reader.Name == "Language")
                    {
                        string unit = reader.ReadElementContentAsString();

                        #region Reception
                        if (unit == "Ar")
                        {
                            Shared.IsEnglish = false;
                        }
                        else if (unit == "En")
                        {
                            Shared.IsEnglish = true;
                        }
                        #endregion
                    }
                }
            }
            catch { }
            finally
            {
                if (reader != null)
                    reader.Close();
            }
        }

        /// <summary>
        /// save settings file after user login
        /// </summary>
        private void SaveSetingsFile()
        {
            settings = new XmlWriterSettings();
            settings.Indent = true;

            XmlWriter writer = null;
            try
            {
                string Folder_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\GlobalERP";
                if (!System.IO.Directory.Exists(Folder_path))
                    System.IO.Directory.CreateDirectory(Folder_path);

                writer = XmlWriter.Create(
                    Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + "ERP_Profile.xml", settings);

                writer.WriteStartDocument();
                writer.WriteComment("This file is generated by Global Grid ERP System");

                writer.WriteStartElement("Settings");
                if (cmbLang.SelectedIndex == 0)
                {
                    Shared.IsEnglish = false;
                    writer.WriteElementString("Language", "Ar");
                }
                else if (cmbLang.SelectedIndex == 1)
                {
                    Shared.IsEnglish = true;
                    writer.WriteElementString("Language", "En");
                }
                writer.WriteEndElement();
                writer.WriteEndDocument();
                writer.Flush();

            }
            catch
            { }
            finally
            {
                writer.Close();
            }
        }

        #endregion



    }
}
