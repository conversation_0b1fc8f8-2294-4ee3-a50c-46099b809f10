﻿namespace Reports
{
    partial class frm_SL_ItemsSales
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_ItemsSales));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barSubItem2 = new DevExpress.XtraBars.BarSubItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.babtnPrintP = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItem1 = new DevExpress.XtraBars.BarSubItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.barbtnPrint_P = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdCategory = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.col_SoldPiecesCount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldSQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.repositoryItemMemoEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.col_kg_Weight_libra = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_LibraQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldMQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldLQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentPiecesCount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentSQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentMQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentLQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_width = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_length = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_height = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ItemId = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colIndex = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_Category = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentLibra = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_currentKG = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh,
            this.barSubItem1,
            this.barbtnPrint_P,
            this.barSubItem2,
            this.babtnPrintP});
            this.barManager1.MaxItemId = 33;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barSubItem2, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barSubItem1, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barSubItem2
            // 
            this.barSubItem2.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barSubItem2, "barSubItem2");
            this.barSubItem2.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barSubItem2.Id = 31;
            this.barSubItem2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnPrint, DevExpress.XtraBars.BarItemPaintStyle.Caption),
            new DevExpress.XtraBars.LinkPersistInfo(this.babtnPrintP)});
            this.barSubItem2.Name = "barSubItem2";
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // babtnPrintP
            // 
            resources.ApplyResources(this.babtnPrintP, "babtnPrintP");
            this.babtnPrintP.Id = 32;
            this.babtnPrintP.Name = "babtnPrintP";
            this.babtnPrintP.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.babtnPrintP_ItemClick);
            // 
            // barSubItem1
            // 
            this.barSubItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barSubItem1, "barSubItem1");
            this.barSubItem1.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barSubItem1.Id = 29;
            this.barSubItem1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnPreview, DevExpress.XtraBars.BarItemPaintStyle.Caption),
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnPrint_P)});
            this.barSubItem1.Name = "barSubItem1";
            // 
            // barBtnPreview
            // 
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Preview_ItemClick);
            // 
            // barbtnPrint_P
            // 
            resources.ApplyResources(this.barbtnPrint_P, "barbtnPrint_P");
            this.barbtnPrint_P.Id = 30;
            this.barbtnPrint_P.Name = "barbtnPrint_P";
            this.barbtnPrint_P.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barbtnPrint_P_ItemClick);
            // 
            // barBtnRefresh
            // 
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdCategory
            // 
            resources.ApplyResources(this.grdCategory, "grdCategory");
            this.grdCategory.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdCategory.MainView = this.bandedGridView1;
            this.grdCategory.Name = "grdCategory";
            this.grdCategory.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemMemoEdit1});
            this.grdCategory.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1});
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Appearance.BandPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.Appearance.BandPanel.Font")));
            this.bandedGridView1.Appearance.BandPanel.Options.UseFont = true;
            this.bandedGridView1.Appearance.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.Font")));
            this.bandedGridView1.Appearance.HeaderPanel.Options.UseFont = true;
            this.bandedGridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.Row.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.AppearancePrint.BandPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.Font")));
            this.bandedGridView1.AppearancePrint.BandPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.FooterPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.Font")));
            this.bandedGridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.Font")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.BackColor")));
            this.bandedGridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.ForeColor")));
            this.bandedGridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.BorderColor")));
            this.bandedGridView1.AppearancePrint.Row.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.Row.Font")));
            this.bandedGridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.ForeColor")));
            this.bandedGridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand2,
            this.gridBand3,
            this.gridBand1});
            this.bandedGridView1.ColumnPanelRowHeight = 50;
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.col_Category,
            this.col_CurrentLibra,
            this.col_currentKG,
            this.col_width,
            this.col_height,
            this.col_length,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.col_SoldLQty,
            this.col_SoldMQty,
            this.col_SoldSQty,
            this.col_CurrentLQty,
            this.col_CurrentMQty,
            this.col_CurrentSQty,
            this.col_ItemId,
            this.colIndex,
            this.col_CurrentQty,
            this.col_SoldQty,
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.col_CurrentPiecesCount,
            this.col_SoldPiecesCount,
            this.col_LibraQty,
            this.col_kg_Weight_libra});
            this.bandedGridView1.CustomizationFormBounds = new System.Drawing.Rectangle(946, 259, 216, 416);
            this.bandedGridView1.GridControl = this.grdCategory;
            resources.ApplyResources(this.bandedGridView1, "bandedGridView1");
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsBehavior.ReadOnly = true;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.bandedGridView1.OptionsView.RowAutoHeight = true;
            this.bandedGridView1.OptionsView.ShowAutoFilterRow = true;
            this.bandedGridView1.OptionsView.ShowFooter = true;
            this.bandedGridView1.OptionsView.ShowGroupPanel = false;
            this.bandedGridView1.OptionsView.ShowIndicator = false;
            this.bandedGridView1.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            // 
            // col_SoldPiecesCount
            // 
            resources.ApplyResources(this.col_SoldPiecesCount, "col_SoldPiecesCount");
            this.col_SoldPiecesCount.FieldName = "SoldPiecesCount";
            this.col_SoldPiecesCount.Name = "col_SoldPiecesCount";
            this.col_SoldPiecesCount.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldPiecesCount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldPiecesCount.Summary"))))});
            // 
            // col_SoldSQty
            // 
            resources.ApplyResources(this.col_SoldSQty, "col_SoldSQty");
            this.col_SoldSQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_SoldSQty.DisplayFormat.FormatString = "n3";
            this.col_SoldSQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldSQty.FieldName = "SoldSQty";
            this.col_SoldSQty.Name = "col_SoldSQty";
            this.col_SoldSQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldSQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldSQty.Summary"))), resources.GetString("col_SoldSQty.Summary1"), resources.GetString("col_SoldSQty.Summary2"))});
            this.col_SoldSQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // repositoryItemMemoEdit1
            // 
            this.repositoryItemMemoEdit1.Name = "repositoryItemMemoEdit1";
            // 
            // col_kg_Weight_libra
            // 
            this.col_kg_Weight_libra.FieldName = "kg_Weight_libra";
            this.col_kg_Weight_libra.Name = "col_kg_Weight_libra";
            this.col_kg_Weight_libra.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_kg_Weight_libra.Summary"))), resources.GetString("col_kg_Weight_libra.Summary1"), resources.GetString("col_kg_Weight_libra.Summary2"))});
            resources.ApplyResources(this.col_kg_Weight_libra, "col_kg_Weight_libra");
            // 
            // col_LibraQty
            // 
            this.col_LibraQty.FieldName = "LibraQty";
            this.col_LibraQty.Name = "col_LibraQty";
            this.col_LibraQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_LibraQty.Summary"))), resources.GetString("col_LibraQty.Summary1"), resources.GetString("col_LibraQty.Summary2"))});
            resources.ApplyResources(this.col_LibraQty, "col_LibraQty");
            // 
            // col_SoldMQty
            // 
            resources.ApplyResources(this.col_SoldMQty, "col_SoldMQty");
            this.col_SoldMQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_SoldMQty.DisplayFormat.FormatString = "n3";
            this.col_SoldMQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldMQty.FieldName = "SoldMQty";
            this.col_SoldMQty.Name = "col_SoldMQty";
            this.col_SoldMQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldMQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldMQty.Summary"))), resources.GetString("col_SoldMQty.Summary1"), resources.GetString("col_SoldMQty.Summary2"))});
            this.col_SoldMQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_SoldLQty
            // 
            resources.ApplyResources(this.col_SoldLQty, "col_SoldLQty");
            this.col_SoldLQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_SoldLQty.DisplayFormat.FormatString = "n3";
            this.col_SoldLQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldLQty.FieldName = "SoldLQty";
            this.col_SoldLQty.Name = "col_SoldLQty";
            this.col_SoldLQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldLQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldLQty.Summary"))), resources.GetString("col_SoldLQty.Summary1"), resources.GetString("col_SoldLQty.Summary2"))});
            this.col_SoldLQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_CurrentPiecesCount
            // 
            resources.ApplyResources(this.col_CurrentPiecesCount, "col_CurrentPiecesCount");
            this.col_CurrentPiecesCount.FieldName = "CurrentPiecesCount";
            this.col_CurrentPiecesCount.Name = "col_CurrentPiecesCount";
            this.col_CurrentPiecesCount.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_CurrentPiecesCount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_CurrentPiecesCount.Summary"))))});
            // 
            // col_CurrentSQty
            // 
            resources.ApplyResources(this.col_CurrentSQty, "col_CurrentSQty");
            this.col_CurrentSQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_CurrentSQty.DisplayFormat.FormatString = "n3";
            this.col_CurrentSQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_CurrentSQty.FieldName = "CurrentSQty";
            this.col_CurrentSQty.Name = "col_CurrentSQty";
            this.col_CurrentSQty.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_CurrentSQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_CurrentSQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_CurrentSQty.Summary"))), resources.GetString("col_CurrentSQty.Summary1"), resources.GetString("col_CurrentSQty.Summary2"))});
            this.col_CurrentSQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_CurrentMQty
            // 
            resources.ApplyResources(this.col_CurrentMQty, "col_CurrentMQty");
            this.col_CurrentMQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_CurrentMQty.DisplayFormat.FormatString = "n3";
            this.col_CurrentMQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_CurrentMQty.FieldName = "CurrentMQty";
            this.col_CurrentMQty.Name = "col_CurrentMQty";
            this.col_CurrentMQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_CurrentMQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_CurrentMQty.Summary"))), resources.GetString("col_CurrentMQty.Summary1"), resources.GetString("col_CurrentMQty.Summary2"))});
            this.col_CurrentMQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_CurrentLQty
            // 
            resources.ApplyResources(this.col_CurrentLQty, "col_CurrentLQty");
            this.col_CurrentLQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_CurrentLQty.DisplayFormat.FormatString = "n3";
            this.col_CurrentLQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_CurrentLQty.FieldName = "CurrentLQty";
            this.col_CurrentLQty.Name = "col_CurrentLQty";
            this.col_CurrentLQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_CurrentLQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_CurrentLQty.Summary"))), resources.GetString("col_CurrentLQty.Summary1"), resources.GetString("col_CurrentLQty.Summary2"))});
            this.col_CurrentLQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // bandedGridColumn4
            // 
            resources.ApplyResources(this.bandedGridColumn4, "bandedGridColumn4");
            this.bandedGridColumn4.FieldName = "LargeUOMFactor";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.OptionsColumn.ShowInCustomizationForm = false;
            this.bandedGridColumn4.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn5
            // 
            resources.ApplyResources(this.bandedGridColumn5, "bandedGridColumn5");
            this.bandedGridColumn5.FieldName = "MediumUOMFactor";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.OptionsColumn.ShowInCustomizationForm = false;
            this.bandedGridColumn5.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_width
            // 
            resources.ApplyResources(this.col_width, "col_width");
            this.col_width.FieldName = "Width";
            this.col_width.Name = "col_width";
            // 
            // col_length
            // 
            resources.ApplyResources(this.col_length, "col_length");
            this.col_length.FieldName = "Length";
            this.col_length.Name = "col_length";
            // 
            // col_height
            // 
            resources.ApplyResources(this.col_height, "col_height");
            this.col_height.FieldName = "Height";
            this.col_height.Name = "col_height";
            // 
            // col_ItemId
            // 
            resources.ApplyResources(this.col_ItemId, "col_ItemId");
            this.col_ItemId.FieldName = "ItemNameAr";
            this.col_ItemId.Name = "col_ItemId";
            this.col_ItemId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemId.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colIndex
            // 
            resources.ApplyResources(this.colIndex, "colIndex");
            this.colIndex.FieldName = "colIndex";
            this.colIndex.Name = "colIndex";
            this.colIndex.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.colIndex.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // col_Category
            // 
            resources.ApplyResources(this.col_Category, "col_Category");
            this.col_Category.FieldName = "Category";
            this.col_Category.Name = "col_Category";
            // 
            // col_CurrentLibra
            // 
            resources.ApplyResources(this.col_CurrentLibra, "col_CurrentLibra");
            this.col_CurrentLibra.FieldName = "CurrentLibra";
            this.col_CurrentLibra.Name = "col_CurrentLibra";
            // 
            // col_currentKG
            // 
            resources.ApplyResources(this.col_currentKG, "col_currentKG");
            this.col_currentKG.FieldName = "currentKG";
            this.col_currentKG.Name = "col_currentKG";
            // 
            // col_CurrentQty
            // 
            this.col_CurrentQty.FieldName = "CurrentQty";
            this.col_CurrentQty.Name = "col_CurrentQty";
            this.col_CurrentQty.OptionsColumn.ShowInCustomizationForm = false;
            this.col_CurrentQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_SoldQty
            // 
            this.col_SoldQty.FieldName = "SoldQty";
            this.col_SoldQty.Name = "col_SoldQty";
            this.col_SoldQty.OptionsColumn.ShowInCustomizationForm = false;
            this.col_SoldQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn1
            // 
            resources.ApplyResources(this.bandedGridColumn1, "bandedGridColumn1");
            this.bandedGridColumn1.FieldName = "SUom";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.OptionsColumn.ShowInCustomizationForm = false;
            this.bandedGridColumn1.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn2
            // 
            resources.ApplyResources(this.bandedGridColumn2, "bandedGridColumn2");
            this.bandedGridColumn2.FieldName = "MUom";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.OptionsColumn.ShowInCustomizationForm = false;
            this.bandedGridColumn2.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn3
            // 
            resources.ApplyResources(this.bandedGridColumn3, "bandedGridColumn3");
            this.bandedGridColumn3.FieldName = "LUom";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.OptionsColumn.ShowInCustomizationForm = false;
            this.bandedGridColumn3.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.MenuManager = this.barManager1;
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // gridBand2
            // 
            resources.ApplyResources(this.gridBand2, "gridBand2");
            this.gridBand2.Columns.Add(this.col_SoldPiecesCount);
            this.gridBand2.Columns.Add(this.col_SoldSQty);
            this.gridBand2.Columns.Add(this.col_kg_Weight_libra);
            this.gridBand2.Columns.Add(this.col_LibraQty);
            this.gridBand2.Columns.Add(this.col_SoldMQty);
            this.gridBand2.Columns.Add(this.col_SoldLQty);
            this.gridBand2.VisibleIndex = 0;
            // 
            // gridBand3
            // 
            resources.ApplyResources(this.gridBand3, "gridBand3");
            this.gridBand3.Columns.Add(this.col_CurrentPiecesCount);
            this.gridBand3.Columns.Add(this.col_CurrentSQty);
            this.gridBand3.Columns.Add(this.col_CurrentMQty);
            this.gridBand3.Columns.Add(this.col_CurrentLQty);
            this.gridBand3.VisibleIndex = 1;
            // 
            // gridBand1
            // 
            this.gridBand1.Columns.Add(this.bandedGridColumn4);
            this.gridBand1.Columns.Add(this.bandedGridColumn5);
            this.gridBand1.Columns.Add(this.col_width);
            this.gridBand1.Columns.Add(this.col_length);
            this.gridBand1.Columns.Add(this.col_height);
            this.gridBand1.Columns.Add(this.col_Category);
            this.gridBand1.Columns.Add(this.col_ItemId);
            this.gridBand1.Columns.Add(this.colIndex);
            resources.ApplyResources(this.gridBand1, "gridBand1");
            this.gridBand1.VisibleIndex = 2;
            // 
            // frm_SL_ItemsSales
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.grdCategory);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_ItemsSales";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_Rep_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_InvoiceList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdCategory;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldSQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldMQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldLQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentPiecesCount;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentSQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentMQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentLQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ItemId;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colIndex;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldPiecesCount;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit1;
        private DevExpress.XtraBars.BarSubItem barSubItem1;
        private DevExpress.XtraBars.BarButtonItem barbtnPrint_P;
        private DevExpress.XtraBars.BarSubItem barSubItem2;
        private DevExpress.XtraBars.BarButtonItem babtnPrintP;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_width;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_length;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_height;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_LibraQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_kg_Weight_libra;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentLibra;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_currentKG;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Category;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
    }
}