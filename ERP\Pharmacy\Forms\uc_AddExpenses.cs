﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class uc_AddExpenses : DevExpress.XtraEditors.XtraUserControl
    {
        public List<acc> lstAcc;
        public uc_AddExpenses(DataTable dt_expenses)
        {
            InitializeComponent();
            RTL.EnCulture(Shared.IsEnglish);

            if (lstAcc == null)
                lstAcc = HelperAcc.LoadAccountsTree(HelperAcc.ExpensesAcc, true);

            rep_ExpenseAccount.DisplayMember = "AccName";
            rep_ExpenseAccount.ValueMember = "AccId";
            rep_ExpenseAccount.DataSource = lstAcc;            
            
            gridControl1.DataSource = dt_expenses;
            ErpUtils.ColumnChooser(gridControl1);
        }

        private void gridView1_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column == col_Amount && e.Value != null && e.Value != DBNull.Value && e.Value.ToString() != string.Empty)
            {
                try
                {
                    e.DisplayText = Convert.ToDouble(e.Value).ToString();
                }
                catch
                { }
            }
        }

        private void gridView1_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            if ((e.Row as DataRowView).Row["ExpenseAccountId"] == DBNull.Value)
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore;
            else
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;            
        }

        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            if (gridView1.GetRowCellValue(e.RowHandle, col_ExpenseAccountId)==DBNull.Value ||
                gridView1.GetRowCellValue(e.RowHandle, col_ExpenseAccountId).ToString() == string.Empty)
            {
                e.Valid = false;
                gridView1.SetColumnError(col_ExpenseAccountId, Shared.IsEnglish ? ResAccEn.ValtxtAcc : ResAccAr.ValtxtAcc);//"يجب اختيار الحساب";
            }
            if (gridView1.GetRowCellValue(e.RowHandle, col_ExpenseAccountId) != DBNull.Value ||
                gridView1.GetRowCellValue(e.RowHandle, col_ExpenseAccountId).ToString() != string.Empty)            
            {
                int accId = Convert.ToInt32(gridView1.GetRowCellValue(e.RowHandle, col_ExpenseAccountId));
                if (HelperAcc.Is_AccountHasChilds(accId) == true)
                {
                    e.Valid = false;
                    gridView1.SetColumnError(col_ExpenseAccountId,
                    Shared.IsEnglish == true ? ResAccEn.valtxtChildAcc : ResAccAr.valtxtChildAcc);//"يجب اختيار حساب فرعي"
                }
            }
            
            if (gridView1.GetRowCellValue(e.RowHandle, col_Amount) == DBNull.Value ||
                gridView1.GetRowCellValue(e.RowHandle, col_Amount).ToString() == string.Empty||
                Convert.ToDecimal(gridView1.GetRowCellValue(e.RowHandle, col_Amount)) <= 0)
                {
                    e.Valid = false;
                    gridView1.SetColumnError(col_Amount, Shared.IsEnglish ? ResAccEn.ValtxtAmount : ResAccAr.ValtxtAmount);//"يجب تسجيل المبلغ";
                }
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(Shared.IsEnglish == true ? ResPrEn.MsgDelRow : ResPrAr.MsgDelRow,
                    Shared.IsEnglish == true ? ResPrEn.MsgTQues : ResPrAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;
                gridView1.DeleteRow(gridView1.FocusedRowHandle);
            }
            if (e.KeyCode == Keys.Enter)
            {
                try
                {
                    if (gridView1.FocusedColumn == col_ExpenseAccountId)
                    {
                        gridView1.FocusedColumn = col_ExpenseNote;
                        if (gridView1.GetFocusedRowCellValue(col_ExpenseAccountId) == null ||
                            string.IsNullOrEmpty(gridView1.GetFocusedRowCellValue(col_ExpenseAccountId).ToString()))
                            gridView1.FocusedColumn = col_ExpenseAccountId;
                        return;
                    }
                    if (gridView1.FocusedColumn == col_ExpenseNote)
                    {
                        if (gridView1.GetFocusedRowCellValue(col_ExpenseNote) == null ||
                            string.IsNullOrEmpty(gridView1.GetFocusedRowCellValue(col_ExpenseNote).ToString()))
                            gridView1.FocusedColumn = col_Amount;
                        return;
                    }
                    if (gridView1.FocusedColumn == col_Amount)
                        gridView1_KeyDown(sender, new KeyEventArgs(Keys.Tab));

                    if (gridView1.FocusedRowHandle < 0)
                    {
                        gridView1.AddNewRow();
                        gridView1.FocusedColumn = col_ExpenseAccountId;
                    }
                    else
                    {
                        gridView1.FocusedRowHandle = gridView1.FocusedRowHandle + 1;
                        gridView1.FocusedColumn = gridView1.FocusedColumn;
                    }

                    e.Handled = true;
                    return;
                }
                catch
                { }
            }
            if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
            {
                if (gridView1.FocusedColumn.VisibleIndex == 0)
                    gridView1.FocusedColumn = gridView1.VisibleColumns[gridView1.VisibleColumns.Count - 1];
                else
                    gridView1.FocusedColumn = gridView1.VisibleColumns[gridView1.FocusedColumn.VisibleIndex - 1];
                e.Handled = true;
                return;
            }
            if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
            {
                if (gridView1.FocusedColumn.VisibleIndex == gridView1.VisibleColumns.Count)
                    gridView1.FocusedColumn = gridView1.VisibleColumns[0];
                else
                    gridView1.FocusedColumn = gridView1.VisibleColumns[gridView1.FocusedColumn.VisibleIndex + 1];
                e.Handled = true;
                return;
            }
            try
            {

                if ((gridView1.GetFocusedRow() as DataRowView).IsNew == true && gridView1.GetFocusedRowCellValue(col_ExpenseAccountId).ToString() == string.Empty)
                {
                    if (e.KeyCode == Keys.Up)
                        gridView1.DeleteRow(gridView1.FocusedRowHandle);
                }
            }
            catch
            { }
        }

        private void uc_AddExpenses_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
        }        
    }
}
