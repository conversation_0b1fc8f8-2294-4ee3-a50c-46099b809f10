using System;
using System.Data;
using System.Data.Linq;
using System.Linq;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;

namespace Reports
{
    public partial class rpt_SL_CustomerTrans_Sub : DevExpress.XtraReports.UI.XtraReport
    {
        public rpt_SL_CustomerTrans_Sub(int processId, int sourceId)
        {
            InitializeComponent();

            if (Shared.st_Store.PiecesCount == false)
            {
                lbl_PiecesCount.Visible = xrTableCell2.Visible = false;
                xrTableCell7.WidthF += 47;
                lbl_Qty.WidthF += 47;
            }

            ERPDataContext DB = new ERPDataContext();

            var emps = DB.HR_Employees.ToList();

            IList data = null;
            if (processId == 101)
            {
                data = (from qotD in DB.SL_QuoteDetails
                        join u in DB.IC_UOMs
                        on qotD.UOMId equals u.UOMId
                        join i in DB.IC_Items
                        on qotD.ItemId equals i.ItemId
                        where qotD.SL_QuoteId == sourceId
                        select new
                        {
                            ItemId = i.ItemNameAr,
                            Qty = decimal.ToDouble(qotD.Qty),
                            UOM = u.UOM,
                            SellPrice = decimal.ToDouble(qotD.SellPrice),
                            DiscountRatio = decimal.ToDouble(qotD.DiscountRatio),
                            DiscountValue = decimal.ToDouble(qotD.DiscountValue),
                            Total = decimal.ToDouble(qotD.TotalSellPrice),
                            PiecesCount = decimal.ToDouble(qotD.PiecesCount),
                        }).ToList();
                this.DataSource = data;
            }
            if (processId == 102)
            {
                data = (from qotD in DB.SL_SalesOrderDetails
                        join u in DB.IC_UOMs
                        on qotD.UOMId equals u.UOMId
                        join i in DB.IC_Items
                        on qotD.ItemId equals i.ItemId
                            where qotD.SL_SalesOrderId == sourceId
                            select new
                            {
                                ItemId = i.ItemNameAr,
                                Qty = decimal.ToDouble(qotD.Qty),
                                UOM = u.UOM,
                                SellPrice = decimal.ToDouble(qotD.SellPrice),
                                DiscountRatio = decimal.ToDouble(qotD.DiscountRatio),
                                DiscountValue = decimal.ToDouble(qotD.DiscountValue),
                                Total = decimal.ToDouble(qotD.TotalSellPrice),
                                PiecesCount = decimal.ToDouble(qotD.PiecesCount),
                            }).ToList();
                this.DataSource = data;
            }
            if (processId == (int)Process.SellInvoice)
            {
                data = (from qotD in DB.SL_InvoiceDetails
                        join u in DB.IC_UOMs
                        on qotD.UOMId equals u.UOMId
                        join i in DB.IC_Items
                        on qotD.ItemId equals i.ItemId
                            where qotD.SL_InvoiceId == sourceId
                            select new
                            {
                                ItemId = i.ItemNameAr,
                                Qty = decimal.ToDouble(qotD.Qty),
                                UOM = u.UOM,
                                SellPrice = decimal.ToDouble(qotD.SellPrice),
                                DiscountRatio = decimal.ToDouble(qotD.DiscountRatio),
                                DiscountValue = decimal.ToDouble(qotD.DiscountValue),
                                Total = decimal.ToDouble(qotD.TotalSellPrice),
                                PiecesCount = decimal.ToDouble(qotD.PiecesCount),
                            }).ToList();
                this.DataSource = data;
            }
            if (processId == (int)Process.OutTrns)
            {
                data = (from qotD in DB.IC_OutTrnsDetails
                        join u in DB.IC_UOMs
                        on qotD.UOMId equals u.UOMId
                        join i in DB.IC_Items
                        on qotD.ItemId equals i.ItemId
                            where qotD.OutTrnsId == sourceId
                            select new
                            {
                                ItemId = i.ItemNameAr,
                                Qty = decimal.ToDouble(qotD.Qty),
                                UOM = u.UOM,
                                SellPrice = decimal.ToDouble(0),
                                DiscountRatio = decimal.ToDouble(0),
                                DiscountValue = decimal.ToDouble(0),
                                Total = decimal.ToDouble(0),
                                PiecesCount = decimal.ToDouble(qotD.PiecesCount),
                            }).ToList();
                this.DataSource = data;
            }
            if (processId == (int)Process.InTrns)
            {
                data = (from qotD in DB.IC_InTrnsDetails
                        join u in DB.IC_UOMs
                        on qotD.UOMId equals u.UOMId
                        join i in DB.IC_Items
                        on qotD.ItemId equals i.ItemId
                        where qotD.InTrnsId == sourceId
                        select new
                        {
                            ItemId = i.ItemNameAr,
                            Qty = decimal.ToDouble(qotD.Qty),
                            UOM = u.UOM,
                            SellPrice = decimal.ToDouble(0),
                            DiscountRatio = decimal.ToDouble(0),
                            DiscountValue = decimal.ToDouble(0),
                            Total = decimal.ToDouble(0),
                            PiecesCount = decimal.ToDouble(qotD.PiecesCount),
                        }).ToList();
                this.DataSource = data;
            }
            if (processId == (int)Process.SellReturn)
            {
                data = (from qotD in DB.SL_ReturnDetails
                        join u in DB.IC_UOMs
                        on qotD.UOMId equals u.UOMId
                        join i in DB.IC_Items
                        on qotD.ItemId equals i.ItemId
                            where qotD.SL_ReturnId == sourceId
                            select new
                            {
                                ItemId = i.ItemNameAr,
                                Qty = qotD.Qty,
                                UOM = u.UOM,
                                SellPrice = decimal.ToDouble(qotD.SellPrice),
                                DiscountRatio = decimal.ToDouble(qotD.DiscountRatio),
                                DiscountValue = decimal.ToDouble(qotD.DiscountValue),
                                Total = decimal.ToDouble(qotD.TotalSellPrice),
                                PiecesCount = decimal.ToDouble(qotD.PiecesCount),
                            }).ToList();
                this.DataSource = data;
            }

            lbl_Item.DataBindings.Add("Text", data, "ItemId");
            lbl_Qty.DataBindings.Add("Text", data, "Qty");
            lbl_Uom.DataBindings.Add("Text", data, "UOM");
            lbl_Price.DataBindings.Add("Text", data, "SellPrice");
            lbl_DiscRatio.DataBindings.Add("Text", data, "DiscountRatio");
            lbl_DiscValue.DataBindings.Add("Text", data, "DiscountValue");
            lbl_Net.DataBindings.Add("Text", data, "Total");
            lbl_PiecesCount.DataBindings.Add("Text", data, "PiecesCount");
        }

    }
}
