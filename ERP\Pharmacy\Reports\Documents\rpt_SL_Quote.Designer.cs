﻿namespace Reports
{
    partial class rpt_SL_Quote
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Total = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Disc = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DeliverDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_SalesEmp = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Customer = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_store = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lbl_AttnMr_Job = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AttnMr = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.lbl_AdvancedPayment = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ExpensesV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountR = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Net = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Total = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPageInfo2 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotalWords = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Shipping = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_EndDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TaxR = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ExpensesR = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_salesEmp_Job = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Cell_Location = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_SalesTax = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemDescription = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Cell_MUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_MUOM_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrCrossBandLine5 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine8 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine9 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine10 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine11 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine12 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandBox4 = new DevExpress.XtraReports.UI.XRCrossBandBox();
            this.xrCrossBandBox5 = new DevExpress.XtraReports.UI.XRCrossBandBox();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_namef = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            this.Detail.HeightF = 29.16667F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable2.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(11.2079F, 0F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.SizeF = new System.Drawing.SizeF(739.7921F, 29.16667F);
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            this.xrTable2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_namef,
            this.cell_Total,
            this.cell_Disc,
            this.cell_DiscountRatio,
            this.cell_Price,
            this.cell_Qty,
            this.cell_UOM,
            this.cell_ItemName,
            this.cell_code});
            this.xrTableRow2.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            this.xrTableRow2.Weight = 0.54901959587545957D;
            // 
            // cell_Total
            // 
            this.cell_Total.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_Total.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Total.Name = "cell_Total";
            this.cell_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Total.StylePriority.UseBorders = false;
            this.cell_Total.StylePriority.UseFont = false;
            this.cell_Total.StylePriority.UsePadding = false;
            this.cell_Total.StylePriority.UseTextAlignment = false;
            this.cell_Total.Text = "الاجمالي";
            this.cell_Total.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Total.Weight = 0.1460525735082312D;
            // 
            // cell_Disc
            // 
            this.cell_Disc.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_Disc.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Disc.Name = "cell_Disc";
            this.cell_Disc.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Disc.StylePriority.UseBorders = false;
            this.cell_Disc.StylePriority.UseFont = false;
            this.cell_Disc.StylePriority.UsePadding = false;
            this.cell_Disc.StylePriority.UseTextAlignment = false;
            this.cell_Disc.Text = "قيمة خصم";
            this.cell_Disc.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Disc.Weight = 0.21827298036155546D;
            // 
            // cell_DiscountRatio
            // 
            this.cell_DiscountRatio.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_DiscountRatio.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_DiscountRatio.Name = "cell_DiscountRatio";
            this.cell_DiscountRatio.StylePriority.UseBorders = false;
            this.cell_DiscountRatio.StylePriority.UseFont = false;
            this.cell_DiscountRatio.StylePriority.UsePadding = false;
            this.cell_DiscountRatio.StylePriority.UseTextAlignment = false;
            this.cell_DiscountRatio.Text = "نسبة خصم";
            this.cell_DiscountRatio.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.cell_DiscountRatio.Weight = 0.17970735324389264D;
            // 
            // cell_Price
            // 
            this.cell_Price.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_Price.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Price.Name = "cell_Price";
            this.cell_Price.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Price.StylePriority.UseBorders = false;
            this.cell_Price.StylePriority.UseFont = false;
            this.cell_Price.StylePriority.UsePadding = false;
            this.cell_Price.StylePriority.UseTextAlignment = false;
            this.cell_Price.Text = "السعر";
            this.cell_Price.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Price.Weight = 0.25540705873346431D;
            // 
            // cell_Qty
            // 
            this.cell_Qty.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_Qty.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Qty.Name = "cell_Qty";
            this.cell_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Qty.StylePriority.UseBorders = false;
            this.cell_Qty.StylePriority.UseFont = false;
            this.cell_Qty.StylePriority.UsePadding = false;
            this.cell_Qty.StylePriority.UseTextAlignment = false;
            this.cell_Qty.Text = "كمية";
            this.cell_Qty.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Qty.Weight = 0.19322531497974532D;
            // 
            // cell_UOM
            // 
            this.cell_UOM.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_UOM.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_UOM.Name = "cell_UOM";
            this.cell_UOM.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_UOM.StylePriority.UseBorders = false;
            this.cell_UOM.StylePriority.UseFont = false;
            this.cell_UOM.StylePriority.UsePadding = false;
            this.cell_UOM.StylePriority.UseTextAlignment = false;
            this.cell_UOM.Text = "وحدة قياس";
            this.cell_UOM.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_UOM.Weight = 0.26335872642336372D;
            // 
            // cell_ItemName
            // 
            this.cell_ItemName.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_ItemName.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_ItemName.Name = "cell_ItemName";
            this.cell_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_ItemName.StylePriority.UseBorders = false;
            this.cell_ItemName.StylePriority.UseFont = false;
            this.cell_ItemName.StylePriority.UsePadding = false;
            this.cell_ItemName.StylePriority.UseTextAlignment = false;
            this.cell_ItemName.Text = "اســـم الصنف";
            this.cell_ItemName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_ItemName.Weight = 0.58707200800261983D;
            // 
            // cell_code
            // 
            this.cell_code.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_code.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_code.Name = "cell_code";
            this.cell_code.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_code.StylePriority.UseBorders = false;
            this.cell_code.StylePriority.UseFont = false;
            this.cell_code.StylePriority.UsePadding = false;
            this.cell_code.StylePriority.UseTextAlignment = false;
            this.cell_code.Text = "كود";
            this.cell_code.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_code.Weight = 0.12857687506459745D;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLine1,
            this.xrLabel10,
            this.xrLabel2,
            this.lbl_DeliverDate,
            this.lbl_SalesEmp,
            this.lbl_Customer,
            this.xrLabel1,
            this.lbl_date,
            this.xrLabel6,
            this.xrLabel8,
            this.lbl_User,
            this.xrLabel7,
            this.lbl_store,
            this.lblReportName,
            this.lbl_Number,
            this.lblCompName,
            this.picLogo,
            this.lbl_AttnMr_Job,
            this.xrLabel14,
            this.lbl_AttnMr,
            this.xrLabel12,
            this.lbl_notes,
            this.xrLabel4});
            this.TopMargin.HeightF = 318F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLine1
            // 
            this.xrLine1.BackColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderWidth = 0F;
            this.xrLine1.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.LocationFloat = new DevExpress.Utils.PointFloat(4.207825F, 174.2083F);
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.SizeF = new System.Drawing.SizeF(746.7922F, 3.541748F);
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // xrLabel10
            // 
            this.xrLabel10.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(179.2078F, 210.2083F);
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(80.83295F, 24.49998F);
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            this.xrLabel10.Text = "مندوب بيع";
            this.xrLabel10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(682.5414F, 185.7083F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(68.45856F, 24.49998F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "العميل";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DeliverDate
            // 
            this.lbl_DeliverDate.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_DeliverDate.LocationFloat = new DevExpress.Utils.PointFloat(4.207826F, 185.7084F);
            this.lbl_DeliverDate.Name = "lbl_DeliverDate";
            this.lbl_DeliverDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DeliverDate.SizeF = new System.Drawing.SizeF(175F, 24.49998F);
            this.lbl_DeliverDate.StylePriority.UseFont = false;
            this.lbl_DeliverDate.StylePriority.UseTextAlignment = false;
            this.lbl_DeliverDate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_SalesEmp
            // 
            this.lbl_SalesEmp.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_SalesEmp.LocationFloat = new DevExpress.Utils.PointFloat(4.207826F, 210.2083F);
            this.lbl_SalesEmp.Name = "lbl_SalesEmp";
            this.lbl_SalesEmp.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_SalesEmp.SizeF = new System.Drawing.SizeF(175F, 24.49997F);
            this.lbl_SalesEmp.StylePriority.UseFont = false;
            this.lbl_SalesEmp.StylePriority.UseTextAlignment = false;
            this.lbl_SalesEmp.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Customer
            // 
            this.lbl_Customer.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Customer.LocationFloat = new DevExpress.Utils.PointFloat(282.5414F, 185.7083F);
            this.lbl_Customer.Name = "lbl_Customer";
            this.lbl_Customer.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Customer.SizeF = new System.Drawing.SizeF(399.9999F, 24.49994F);
            this.lbl_Customer.StylePriority.UseFont = false;
            this.lbl_Customer.StylePriority.UseTextAlignment = false;
            this.lbl_Customer.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(179.5005F, 185.7083F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(80.54031F, 24.49998F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "تاريخ التسليم";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_date
            // 
            this.lbl_date.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_date.LocationFloat = new DevExpress.Utils.PointFloat(4.207826F, 95.70834F);
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.SizeF = new System.Drawing.SizeF(175F, 24.49999F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            this.lbl_date.Text = "1/2/2013";
            this.lbl_date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel6.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(179.5005F, 144.7083F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "المستخدم";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(179.5005F, 120.2083F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "الفرع";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_User.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_User.LocationFloat = new DevExpress.Utils.PointFloat(4.207826F, 144.7083F);
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.SizeF = new System.Drawing.SizeF(175.2927F, 24.49998F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            this.lbl_User.Text = "..";
            this.lbl_User.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(179.5004F, 95.70834F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "التاريخ";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_store
            // 
            this.lbl_store.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_store.LocationFloat = new DevExpress.Utils.PointFloat(4.207826F, 120.2083F);
            this.lbl_store.Name = "lbl_store";
            this.lbl_store.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_store.SizeF = new System.Drawing.SizeF(175F, 24.49998F);
            this.lbl_store.StylePriority.UseFont = false;
            this.lbl_store.StylePriority.UseTextAlignment = false;
            this.lbl_store.Text = "الرئيسي";
            this.lbl_store.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblReportName
            // 
            this.lblReportName.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lblReportName.LocationFloat = new DevExpress.Utils.PointFloat(151.7083F, 60.08333F);
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.SizeF = new System.Drawing.SizeF(108.6251F, 30F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            this.lblReportName.Text = "عرض أسعار";
            this.lblReportName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Number
            // 
            this.lbl_Number.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lbl_Number.LocationFloat = new DevExpress.Utils.PointFloat(4.207826F, 60.08333F);
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.SizeF = new System.Drawing.SizeF(147.2079F, 30F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            this.lbl_Number.Text = "123";
            this.lbl_Number.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblCompName
            // 
            this.lblCompName.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lblCompName.LocationFloat = new DevExpress.Utils.PointFloat(289.2083F, 60.08333F);
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.SizeF = new System.Drawing.SizeF(461.7917F, 30F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            this.lblCompName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // picLogo
            // 
            this.picLogo.LocationFloat = new DevExpress.Utils.PointFloat(617.9167F, 95.70834F);
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeF = new System.Drawing.SizeF(133.0833F, 74.49997F);
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lbl_AttnMr_Job
            // 
            this.lbl_AttnMr_Job.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_AttnMr_Job.LocationFloat = new DevExpress.Utils.PointFloat(282.5414F, 234.7083F);
            this.lbl_AttnMr_Job.Name = "lbl_AttnMr_Job";
            this.lbl_AttnMr_Job.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AttnMr_Job.SizeF = new System.Drawing.SizeF(399.9998F, 24.49997F);
            this.lbl_AttnMr_Job.StylePriority.UseFont = false;
            this.lbl_AttnMr_Job.StylePriority.UseTextAlignment = false;
            this.lbl_AttnMr_Job.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel14
            // 
            this.xrLabel14.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(682.5414F, 234.7082F);
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(68.45862F, 24.49997F);
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            this.xrLabel14.Text = "الوظيفة";
            this.xrLabel14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_AttnMr
            // 
            this.lbl_AttnMr.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_AttnMr.LocationFloat = new DevExpress.Utils.PointFloat(282.5414F, 210.2083F);
            this.lbl_AttnMr.Name = "lbl_AttnMr";
            this.lbl_AttnMr.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AttnMr.SizeF = new System.Drawing.SizeF(399.9999F, 24.49997F);
            this.lbl_AttnMr.StylePriority.UseFont = false;
            this.lbl_AttnMr.StylePriority.UseTextAlignment = false;
            this.lbl_AttnMr.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel12
            // 
            this.xrLabel12.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel12.LocationFloat = new DevExpress.Utils.PointFloat(682.5413F, 210.2083F);
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.SizeF = new System.Drawing.SizeF(68.45868F, 24.49997F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            this.xrLabel12.Text = "عناية السيد";
            this.xrLabel12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_notes
            // 
            this.lbl_notes.CanGrow = false;
            this.lbl_notes.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_notes.LocationFloat = new DevExpress.Utils.PointFloat(282.5414F, 259.2083F);
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.SizeF = new System.Drawing.SizeF(399.9998F, 50.95831F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            this.lbl_notes.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(682.5413F, 259.2082F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(68.45868F, 24.49997F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "ملاحظات";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 45F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_AdvancedPayment,
            this.xrLabel20,
            this.lbl_ExpensesV,
            this.lbl_DiscountV,
            this.lbl_DiscountR,
            this.lbl_Net,
            this.xrLabel11,
            this.xrLabel3,
            this.lbl_TaxV,
            this.xrLabel9,
            this.xrLabel16,
            this.lbl_Total,
            this.xrPageInfo2,
            this.xrLabel5,
            this.lblTotalWords,
            this.lbl_Shipping,
            this.lbl_EndDate,
            this.lbl_TaxR,
            this.lbl_ExpensesR,
            this.lbl_salesEmp_Job,
            this.xrTable4,
            this.xrTable3});
            this.ReportFooter.HeightF = 178F;
            this.ReportFooter.Name = "ReportFooter";
            this.ReportFooter.PrintAtBottom = true;
            // 
            // lbl_AdvancedPayment
            // 
            this.lbl_AdvancedPayment.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_AdvancedPayment.CanGrow = false;
            this.lbl_AdvancedPayment.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_AdvancedPayment.LocationFloat = new DevExpress.Utils.PointFloat(282.5414F, 101.4997F);
            this.lbl_AdvancedPayment.Name = "lbl_AdvancedPayment";
            this.lbl_AdvancedPayment.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_AdvancedPayment.SizeF = new System.Drawing.SizeF(104.2916F, 25.45847F);
            this.lbl_AdvancedPayment.StylePriority.UseBorders = false;
            this.lbl_AdvancedPayment.StylePriority.UseFont = false;
            this.lbl_AdvancedPayment.StylePriority.UsePadding = false;
            this.lbl_AdvancedPayment.StylePriority.UseTextAlignment = false;
            this.lbl_AdvancedPayment.Text = " ";
            this.lbl_AdvancedPayment.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel20
            // 
            this.xrLabel20.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel20.CanGrow = false;
            this.xrLabel20.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrLabel20.LocationFloat = new DevExpress.Utils.PointFloat(114.2916F, 79.4584F);
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel20.SizeF = new System.Drawing.SizeF(77.19334F, 25.54155F);
            this.xrLabel20.StylePriority.UseBorders = false;
            this.xrLabel20.StylePriority.UseFont = false;
            this.xrLabel20.StylePriority.UsePadding = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            this.xrLabel20.Text = "تكاليف أخرى";
            this.xrLabel20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_ExpensesV
            // 
            this.lbl_ExpensesV.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_ExpensesV.CanGrow = false;
            this.lbl_ExpensesV.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_ExpensesV.LocationFloat = new DevExpress.Utils.PointFloat(11.20791F, 79.45843F);
            this.lbl_ExpensesV.Name = "lbl_ExpensesV";
            this.lbl_ExpensesV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_ExpensesV.SizeF = new System.Drawing.SizeF(102.042F, 25.54156F);
            this.lbl_ExpensesV.StylePriority.UseBorders = false;
            this.lbl_ExpensesV.StylePriority.UseFont = false;
            this.lbl_ExpensesV.StylePriority.UsePadding = false;
            this.lbl_ExpensesV.StylePriority.UseTextAlignment = false;
            this.lbl_ExpensesV.Text = " ";
            this.lbl_ExpensesV.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DiscountV
            // 
            this.lbl_DiscountV.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.lbl_DiscountV.CanGrow = false;
            this.lbl_DiscountV.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_DiscountV.LocationFloat = new DevExpress.Utils.PointFloat(11.20791F, 53.99997F);
            this.lbl_DiscountV.Name = "lbl_DiscountV";
            this.lbl_DiscountV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_DiscountV.SizeF = new System.Drawing.SizeF(102.042F, 25.4584F);
            this.lbl_DiscountV.StylePriority.UseBorders = false;
            this.lbl_DiscountV.StylePriority.UseFont = false;
            this.lbl_DiscountV.StylePriority.UsePadding = false;
            this.lbl_DiscountV.StylePriority.UseTextAlignment = false;
            this.lbl_DiscountV.Text = " ";
            this.lbl_DiscountV.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DiscountR
            // 
            this.lbl_DiscountR.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_DiscountR.CanGrow = false;
            this.lbl_DiscountR.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_DiscountR.LocationFloat = new DevExpress.Utils.PointFloat(114.2916F, 53.99997F);
            this.lbl_DiscountR.Name = "lbl_DiscountR";
            this.lbl_DiscountR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DiscountR.SizeF = new System.Drawing.SizeF(37.49999F, 25.45843F);
            this.lbl_DiscountR.StylePriority.UseBorders = false;
            this.lbl_DiscountR.StylePriority.UseFont = false;
            this.lbl_DiscountR.StylePriority.UseTextAlignment = false;
            this.lbl_DiscountR.Text = " ";
            this.lbl_DiscountR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_Net
            // 
            this.lbl_Net.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Net.CanGrow = false;
            this.lbl_Net.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Net.LocationFloat = new DevExpress.Utils.PointFloat(9.999998F, 112.0415F);
            this.lbl_Net.Name = "lbl_Net";
            this.lbl_Net.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Net.SizeF = new System.Drawing.SizeF(104.2916F, 25.45847F);
            this.lbl_Net.StylePriority.UseBorders = false;
            this.lbl_Net.StylePriority.UseFont = false;
            this.lbl_Net.StylePriority.UsePadding = false;
            this.lbl_Net.StylePriority.UseTextAlignment = false;
            this.lbl_Net.Text = " ";
            this.lbl_Net.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel11
            // 
            this.xrLabel11.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel11.CanGrow = false;
            this.xrLabel11.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(114.2916F, 112.5F);
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(77.19334F, 25.4584F);
            this.xrLabel11.StylePriority.UseBorders = false;
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.StylePriority.UsePadding = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            this.xrLabel11.Text = "الصافي ";
            this.xrLabel11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel3.CanGrow = false;
            this.xrLabel3.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(114.2916F, 1.999982F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(77.19334F, 25.45843F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UsePadding = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "الاجمالي";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_TaxV
            // 
            this.lbl_TaxV.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.lbl_TaxV.CanGrow = false;
            this.lbl_TaxV.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_TaxV.LocationFloat = new DevExpress.Utils.PointFloat(11.20791F, 28.00001F);
            this.lbl_TaxV.Name = "lbl_TaxV";
            this.lbl_TaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_TaxV.SizeF = new System.Drawing.SizeF(102.042F, 25.45843F);
            this.lbl_TaxV.StylePriority.UseBorders = false;
            this.lbl_TaxV.StylePriority.UseFont = false;
            this.lbl_TaxV.StylePriority.UsePadding = false;
            this.lbl_TaxV.StylePriority.UseTextAlignment = false;
            this.lbl_TaxV.Text = " ";
            this.lbl_TaxV.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel9.CanGrow = false;
            this.xrLabel9.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(151.7916F, 53.99997F);
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(39.69333F, 25.45843F);
            this.xrLabel9.StylePriority.UseBorders = false;
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            this.xrLabel9.Text = "خصم";
            this.xrLabel9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel16.CanGrow = false;
            this.xrLabel16.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrLabel16.LocationFloat = new DevExpress.Utils.PointFloat(114.2916F, 28.00001F);
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel16.SizeF = new System.Drawing.SizeF(77.19334F, 25.45843F);
            this.xrLabel16.StylePriority.UseBorders = false;
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UsePadding = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            this.xrLabel16.Text = "ض.ع";
            this.xrLabel16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Total
            // 
            this.lbl_Total.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.lbl_Total.CanGrow = false;
            this.lbl_Total.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Total.LocationFloat = new DevExpress.Utils.PointFloat(11.20791F, 1.999982F);
            this.lbl_Total.Name = "lbl_Total";
            this.lbl_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Total.SizeF = new System.Drawing.SizeF(102.042F, 25.45843F);
            this.lbl_Total.StylePriority.UseBorders = false;
            this.lbl_Total.StylePriority.UseFont = false;
            this.lbl_Total.StylePriority.UsePadding = false;
            this.lbl_Total.StylePriority.UseTextAlignment = false;
            this.lbl_Total.Text = " ";
            this.lbl_Total.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrPageInfo2
            // 
            this.xrPageInfo2.Format = "Page {0} of {1} ";
            this.xrPageInfo2.LocationFloat = new DevExpress.Utils.PointFloat(643.625F, 144.9584F);
            this.xrPageInfo2.Name = "xrPageInfo2";
            this.xrPageInfo2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo2.SizeF = new System.Drawing.SizeF(109.375F, 23F);
            this.xrPageInfo2.StylePriority.UseTextAlignment = false;
            this.xrPageInfo2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel5
            // 
            this.xrLabel5.BackColor = System.Drawing.Color.Silver;
            this.xrLabel5.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(522.5F, 144.9584F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(66.29163F, 24.49995F);
            this.xrLabel5.StylePriority.UseBackColor = false;
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "فقط وقدره";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblTotalWords
            // 
            this.lblTotalWords.BackColor = System.Drawing.Color.Silver;
            this.lblTotalWords.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lblTotalWords.LocationFloat = new DevExpress.Utils.PointFloat(9.999998F, 144.9584F);
            this.lblTotalWords.Name = "lblTotalWords";
            this.lblTotalWords.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotalWords.SizeF = new System.Drawing.SizeF(510.6673F, 24.49995F);
            this.lblTotalWords.StylePriority.UseBackColor = false;
            this.lblTotalWords.StylePriority.UseFont = false;
            this.lblTotalWords.StylePriority.UseTextAlignment = false;
            this.lblTotalWords.Text = "..";
            this.lblTotalWords.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Shipping
            // 
            this.lbl_Shipping.LocationFloat = new DevExpress.Utils.PointFloat(425.0835F, 103.9582F);
            this.lbl_Shipping.Name = "lbl_Shipping";
            this.lbl_Shipping.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Shipping.SizeF = new System.Drawing.SizeF(84.91656F, 23F);
            this.lbl_Shipping.Text = "lbl_Shipping";
            this.lbl_Shipping.Visible = false;
            // 
            // lbl_EndDate
            // 
            this.lbl_EndDate.LocationFloat = new DevExpress.Utils.PointFloat(668.0835F, 78.45842F);
            this.lbl_EndDate.Name = "lbl_EndDate";
            this.lbl_EndDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_EndDate.SizeF = new System.Drawing.SizeF(84.91656F, 23F);
            this.lbl_EndDate.Text = "lbl_EndDate";
            this.lbl_EndDate.Visible = false;
            // 
            // lbl_TaxR
            // 
            this.lbl_TaxR.LocationFloat = new DevExpress.Utils.PointFloat(425.0835F, 79.45833F);
            this.lbl_TaxR.Name = "lbl_TaxR";
            this.lbl_TaxR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_TaxR.SizeF = new System.Drawing.SizeF(80.99997F, 23F);
            this.lbl_TaxR.Text = "lbl_TaxR";
            this.lbl_TaxR.Visible = false;
            // 
            // lbl_ExpensesR
            // 
            this.lbl_ExpensesR.LocationFloat = new DevExpress.Utils.PointFloat(506.0835F, 79.45833F);
            this.lbl_ExpensesR.Name = "lbl_ExpensesR";
            this.lbl_ExpensesR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ExpensesR.SizeF = new System.Drawing.SizeF(81F, 23F);
            this.lbl_ExpensesR.Text = "lbl_ExpensesR";
            this.lbl_ExpensesR.Visible = false;
            // 
            // lbl_salesEmp_Job
            // 
            this.lbl_salesEmp_Job.LocationFloat = new DevExpress.Utils.PointFloat(587.0835F, 79.45833F);
            this.lbl_salesEmp_Job.Name = "lbl_salesEmp_Job";
            this.lbl_salesEmp_Job.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_salesEmp_Job.SizeF = new System.Drawing.SizeF(81F, 23F);
            this.lbl_salesEmp_Job.Text = "lbl_salesEmp_Job";
            this.lbl_salesEmp_Job.Visible = false;
            // 
            // xrTable4
            // 
            this.xrTable4.LocationFloat = new DevExpress.Utils.PointFloat(278.0786F, 53.45842F);
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            this.xrTable4.SizeF = new System.Drawing.SizeF(474.9214F, 25F);
            this.xrTable4.StylePriority.UseTextAlignment = false;
            this.xrTable4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTable4.Visible = false;
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.Cell_Location,
            this.cell_SalesTax,
            this.cell_ItemDescription,
            this.cell_code2,
            this.cell_Height,
            this.cell_Width,
            this.cell_Length,
            this.cell_TotalQty,
            this.cell_Factor});
            this.xrTableRow4.Name = "xrTableRow4";
            this.xrTableRow4.Weight = 1D;
            // 
            // Cell_Location
            // 
            this.Cell_Location.Name = "Cell_Location";
            this.Cell_Location.Text = "Cell_Location";
            this.Cell_Location.Weight = 0.6423926267098683D;
            // 
            // cell_SalesTax
            // 
            this.cell_SalesTax.Name = "cell_SalesTax";
            this.cell_SalesTax.Text = "cell_SalesTax";
            this.cell_SalesTax.Weight = 0.6423926267098683D;
            // 
            // cell_ItemDescription
            // 
            this.cell_ItemDescription.Name = "cell_ItemDescription";
            this.cell_ItemDescription.Text = "ItemDescription";
            this.cell_ItemDescription.Weight = 0.70310449487824855D;
            // 
            // cell_code2
            // 
            this.cell_code2.Name = "cell_code2";
            this.cell_code2.Text = "cell_code2";
            this.cell_code2.Weight = 0.64044365289041716D;
            // 
            // cell_Height
            // 
            this.cell_Height.Name = "cell_Height";
            this.cell_Height.Text = "Height";
            this.cell_Height.Weight = 0.60721489573696164D;
            // 
            // cell_Width
            // 
            this.cell_Width.Name = "cell_Width";
            this.cell_Width.Text = "Width";
            this.cell_Width.Weight = 0.55632987280983826D;
            // 
            // cell_Length
            // 
            this.cell_Length.Name = "cell_Length";
            this.cell_Length.Text = "Length";
            this.cell_Length.Weight = 0.645568307770225D;
            // 
            // cell_TotalQty
            // 
            this.cell_TotalQty.Name = "cell_TotalQty";
            this.cell_TotalQty.Text = "TotalQty";
            this.cell_TotalQty.Weight = 0.34841793494505491D;
            // 
            // cell_Factor
            // 
            this.cell_Factor.Name = "cell_Factor";
            this.cell_Factor.Text = "cell_Factor";
            this.cell_Factor.Weight = 0.34841793494505491D;
            // 
            // xrTable3
            // 
            this.xrTable3.LocationFloat = new DevExpress.Utils.PointFloat(423.8333F, 28.4584F);
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.SizeF = new System.Drawing.SizeF(329.1667F, 25F);
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.Cell_MUOM,
            this.Cell_MUOM_Factor});
            this.xrTableRow3.Name = "xrTableRow3";
            this.xrTableRow3.Visible = false;
            this.xrTableRow3.Weight = 1D;
            // 
            // Cell_MUOM
            // 
            this.Cell_MUOM.Name = "Cell_MUOM";
            this.Cell_MUOM.Weight = 1.4270834350585937D;
            // 
            // Cell_MUOM_Factor
            // 
            this.Cell_MUOM_Factor.Name = "Cell_MUOM_Factor";
            this.Cell_MUOM_Factor.Weight = 1.5729165649414063D;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            this.PageHeader.HeightF = 36.125F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(11.20791F, 0F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(739.7921F, 36.125F);
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell2,
            this.xrTableCell1,
            this.xrTableCell9,
            this.xrTableCell4,
            this.xrTableCell7,
            this.xrTableCell6,
            this.xrTableCell5,
            this.xrTableCell3,
            this.xrTableCell8});
            this.xrTableRow1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            this.xrTableRow1.Weight = 1D;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell1.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.StylePriority.UseBackColor = false;
            this.xrTableCell1.StylePriority.UseBorders = false;
            this.xrTableCell1.Text = "الاجمالي";
            this.xrTableCell1.Weight = 0.1472260003373505D;
            // 
            // xrTableCell9
            // 
            this.xrTableCell9.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell9.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.StylePriority.UseBackColor = false;
            this.xrTableCell9.StylePriority.UseBorders = false;
            this.xrTableCell9.Text = "قيمة خصم";
            this.xrTableCell9.Weight = 0.21974292694909528D;
            // 
            // xrTableCell4
            // 
            this.xrTableCell4.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell4.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.StylePriority.UseBackColor = false;
            this.xrTableCell4.StylePriority.UseBorders = false;
            this.xrTableCell4.Text = "نسبة خصم";
            this.xrTableCell4.Weight = 0.18143496158043104D;
            // 
            // xrTableCell7
            // 
            this.xrTableCell7.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell7.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.StylePriority.UseBackColor = false;
            this.xrTableCell7.StylePriority.UseBorders = false;
            this.xrTableCell7.Text = "السعر";
            this.xrTableCell7.Weight = 0.25749641654004785D;
            // 
            // xrTableCell6
            // 
            this.xrTableCell6.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell6.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.StylePriority.UseBorders = false;
            this.xrTableCell6.Text = "كمية";
            this.xrTableCell6.Weight = 0.19474041543885021D;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell5.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseBackColor = false;
            this.xrTableCell5.StylePriority.UseBorders = false;
            this.xrTableCell5.Text = "وحدة قياس";
            this.xrTableCell5.Weight = 0.26258901842328841D;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            this.xrTableCell3.Text = "اســـم الصنف";
            this.xrTableCell3.Weight = 0.59467451142931127D;
            // 
            // xrTableCell8
            // 
            this.xrTableCell8.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell8.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseBackColor = false;
            this.xrTableCell8.StylePriority.UseBorders = false;
            this.xrTableCell8.Text = "كود";
            this.xrTableCell8.Weight = 0.12960977863566553D;
            // 
            // xrCrossBandLine5
            // 
            this.xrCrossBandLine5.EndBand = this.ReportFooter;
            this.xrCrossBandLine5.EndPointFloat = new DevExpress.Utils.PointFloat(341.5F, 0F);
            this.xrCrossBandLine5.LocationFloat = new DevExpress.Utils.PointFloat(341.5F, 0F);
            this.xrCrossBandLine5.Name = "xrCrossBandLine5";
            this.xrCrossBandLine5.StartBand = this.PageHeader;
            this.xrCrossBandLine5.StartPointFloat = new DevExpress.Utils.PointFloat(341.5F, 0F);
            this.xrCrossBandLine5.WidthF = 1.041656F;
            // 
            // xrCrossBandLine8
            // 
            this.xrCrossBandLine8.EndBand = this.ReportFooter;
            this.xrCrossBandLine8.EndPointFloat = new DevExpress.Utils.PointFloat(189.5F, 0F);
            this.xrCrossBandLine8.LocationFloat = new DevExpress.Utils.PointFloat(189.5F, 0F);
            this.xrCrossBandLine8.Name = "xrCrossBandLine8";
            this.xrCrossBandLine8.StartBand = this.PageHeader;
            this.xrCrossBandLine8.StartPointFloat = new DevExpress.Utils.PointFloat(189.5F, 0F);
            this.xrCrossBandLine8.WidthF = 1F;
            // 
            // xrCrossBandLine9
            // 
            this.xrCrossBandLine9.EndBand = this.ReportFooter;
            this.xrCrossBandLine9.EndPointFloat = new DevExpress.Utils.PointFloat(252F, 0F);
            this.xrCrossBandLine9.LocationFloat = new DevExpress.Utils.PointFloat(252F, 0F);
            this.xrCrossBandLine9.Name = "xrCrossBandLine9";
            this.xrCrossBandLine9.StartBand = this.PageHeader;
            this.xrCrossBandLine9.StartPointFloat = new DevExpress.Utils.PointFloat(252F, 0F);
            this.xrCrossBandLine9.WidthF = 1F;
            // 
            // xrCrossBandLine10
            // 
            this.xrCrossBandLine10.EndBand = this.ReportFooter;
            this.xrCrossBandLine10.EndPointFloat = new DevExpress.Utils.PointFloat(500F, 0F);
            this.xrCrossBandLine10.LocationFloat = new DevExpress.Utils.PointFloat(500F, 0F);
            this.xrCrossBandLine10.Name = "xrCrossBandLine10";
            this.xrCrossBandLine10.StartBand = this.PageHeader;
            this.xrCrossBandLine10.StartPointFloat = new DevExpress.Utils.PointFloat(500F, 0F);
            this.xrCrossBandLine10.WidthF = 1F;
            // 
            // xrCrossBandLine11
            // 
            this.xrCrossBandLine11.EndBand = this.ReportFooter;
            this.xrCrossBandLine11.EndPointFloat = new DevExpress.Utils.PointFloat(706.5F, 0F);
            this.xrCrossBandLine11.LocationFloat = new DevExpress.Utils.PointFloat(706.5F, 0F);
            this.xrCrossBandLine11.Name = "xrCrossBandLine11";
            this.xrCrossBandLine11.StartBand = this.PageHeader;
            this.xrCrossBandLine11.StartPointFloat = new DevExpress.Utils.PointFloat(706.5F, 0F);
            this.xrCrossBandLine11.WidthF = 1F;
            // 
            // xrCrossBandLine12
            // 
            this.xrCrossBandLine12.EndBand = this.ReportFooter;
            this.xrCrossBandLine12.EndPointFloat = new DevExpress.Utils.PointFloat(408.5F, 0F);
            this.xrCrossBandLine12.LocationFloat = new DevExpress.Utils.PointFloat(408.5F, 0F);
            this.xrCrossBandLine12.Name = "xrCrossBandLine12";
            this.xrCrossBandLine12.StartBand = this.PageHeader;
            this.xrCrossBandLine12.StartPointFloat = new DevExpress.Utils.PointFloat(408.5F, 0F);
            this.xrCrossBandLine12.WidthF = 1F;
            // 
            // xrCrossBandBox4
            // 
            this.xrCrossBandBox4.BorderWidth = 1F;
            this.xrCrossBandBox4.EndBand = this.ReportFooter;
            this.xrCrossBandBox4.EndPointFloat = new DevExpress.Utils.PointFloat(9.999998F, 0F);
            this.xrCrossBandBox4.LocationFloat = new DevExpress.Utils.PointFloat(9.999998F, 0F);
            this.xrCrossBandBox4.Name = "xrCrossBandBox4";
            this.xrCrossBandBox4.StartBand = this.PageHeader;
            this.xrCrossBandBox4.StartPointFloat = new DevExpress.Utils.PointFloat(9.999998F, 0F);
            this.xrCrossBandBox4.WidthF = 743.0001F;
            // 
            // xrCrossBandBox5
            // 
            this.xrCrossBandBox5.BorderWidth = 1F;
            this.xrCrossBandBox5.EndBand = this.ReportFooter;
            this.xrCrossBandBox5.EndPointFloat = new DevExpress.Utils.PointFloat(9.999998F, 106.875F);
            this.xrCrossBandBox5.LocationFloat = new DevExpress.Utils.PointFloat(9.999998F, 0F);
            this.xrCrossBandBox5.Name = "xrCrossBandBox5";
            this.xrCrossBandBox5.StartBand = this.PageHeader;
            this.xrCrossBandBox5.StartPointFloat = new DevExpress.Utils.PointFloat(9.999998F, 0F);
            this.xrCrossBandBox5.WidthF = 104.2916F;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell2.Multiline = true;
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.StylePriority.UseBackColor = false;
            this.xrTableCell2.StylePriority.UseBorders = false;
            this.xrTableCell2.Text = "name f\r\n";
            this.xrTableCell2.Weight = 0.1472260003373505D;
            // 
            // cell_namef
            // 
            this.cell_namef.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_namef.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_namef.Multiline = true;
            this.cell_namef.Name = "cell_namef";
            this.cell_namef.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_namef.StylePriority.UseBorders = false;
            this.cell_namef.StylePriority.UseFont = false;
            this.cell_namef.StylePriority.UsePadding = false;
            this.cell_namef.StylePriority.UseTextAlignment = false;
            this.cell_namef.Text = "name f\r\n";
            this.cell_namef.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_namef.Weight = 0.1460525735082312D;
            // 
            // rpt_SL_Quote
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter});
            this.CrossBandControls.AddRange(new DevExpress.XtraReports.UI.XRCrossBandControl[] {
            this.xrCrossBandLine8,
            this.xrCrossBandLine9,
            this.xrCrossBandBox4,
            this.xrCrossBandLine12,
            this.xrCrossBandLine10,
            this.xrCrossBandLine11,
            this.xrCrossBandLine5,
            this.xrCrossBandBox5});
            this.Margins = new System.Drawing.Printing.Margins(27, 42, 318, 45);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM_Factor;
        private DevExpress.XtraReports.UI.XRLabel lbl_AttnMr_Job;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel lbl_AttnMr;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell cell_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_Length;
        private DevExpress.XtraReports.UI.XRTableCell cell_TotalQty;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemDescription;
        private DevExpress.XtraReports.UI.XRLabel lbl_salesEmp_Job;
        private DevExpress.XtraReports.UI.XRLabel lbl_TaxR;
        private DevExpress.XtraReports.UI.XRLabel lbl_ExpensesR;
        private DevExpress.XtraReports.UI.XRLabel lbl_EndDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_Shipping;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel lbl_store;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lbl_DeliverDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_SalesEmp;
        private DevExpress.XtraReports.UI.XRLabel lbl_Customer;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo2;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lblTotalWords;
        private DevExpress.XtraReports.UI.XRTableCell cell_Factor;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine5;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Total;
        private DevExpress.XtraReports.UI.XRTableCell cell_Disc;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio;
        private DevExpress.XtraReports.UI.XRTableCell cell_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_code;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine8;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine9;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine10;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine11;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine12;
        private DevExpress.XtraReports.UI.XRCrossBandBox xrCrossBandBox4;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountV;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountR;
        private DevExpress.XtraReports.UI.XRLabel lbl_Net;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel lbl_TaxV;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel lbl_Total;
        private DevExpress.XtraReports.UI.XRCrossBandBox xrCrossBandBox5;
        private DevExpress.XtraReports.UI.XRTableCell cell_code2;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel lbl_ExpensesV;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesTax;
        private DevExpress.XtraReports.UI.XRLabel lbl_AdvancedPayment;
        private DevExpress.XtraReports.UI.XRTableCell Cell_Location;
        private DevExpress.XtraReports.UI.XRTableCell cell_namef;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
    }
}
