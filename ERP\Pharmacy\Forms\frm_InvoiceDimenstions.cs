﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;

namespace Pharmacy.Forms
{
    public partial class frm_InvoiceDimenstions : XtraForm
    {
        Process ProcessId;

        public static decimal
            PRInv_Height = 0, PRInv_Width = 0, PRInv_Length = 0,
            PRRet_Height = 0, PRRet_Width = 0, PRRet_Length = 0,
            SLInv_Height = 0, SLInv_Width = 0, SLInv_Length = 0,
            SLRet_Height = 0, SLRet_Width = 0, SLRet_Length = 0;

        public frm_InvoiceDimenstions(Process _ProcessId)
        {
            ProcessId = _ProcessId;

            if (ProcessId == Process.SellInvoice)
            {
                Form frm = Application.OpenForms["frm_SL_Invoice"];

                this.Location = new Point((frm.Width / 2 - this.Width) + frm.Location.X,
                    (frm.Location.Y) + 95);
            }
            if (ProcessId == Process.SellReturn)
            {
                Form frm = Application.OpenForms["frm_SL_Return"];

                this.Location = new Point((frm.Width / 2 - this.Width) + frm.Location.X,
                    (frm.Location.Y) + 70);
            }
            if (ProcessId == Process.PurchaseInvoice)
            {
                Form frm = Application.OpenForms["frm_PR_Invoice"];

                this.Location = new Point((frm.Width / 2 - this.Width) + frm.Location.X,
                    (frm.Location.Y) + 70);
            }
            if (ProcessId == Process.PurchaseReturn)
            {
                Form frm = Application.OpenForms["frm_PR_Return"];

                this.Location = new Point((frm.Width / 2 - this.Width) + frm.Location.X,
                    (frm.Location.Y) + 70);
            }

            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            if (ProcessId == Process.PurchaseInvoice)
            {
                txt_Height.EditValue = PRInv_Height;
                txt_Width.EditValue = PRInv_Width;
                txt_Length.EditValue = PRInv_Length;
            }

            if (ProcessId == Process.PurchaseReturn)
            {
                txt_Height.EditValue = PRRet_Height;
                txt_Width.EditValue = PRRet_Width;
                txt_Length.EditValue = PRRet_Length;
            }

            if (ProcessId == Process.SellInvoice)
            {
                txt_Height.EditValue = SLInv_Height;
                txt_Width.EditValue = SLInv_Width;
                txt_Length.EditValue = SLInv_Length;
            }

            if (ProcessId == Process.SellReturn)
            {
                txt_Height.EditValue = SLRet_Height;
                txt_Width.EditValue = SLRet_Width;
                txt_Length.EditValue = SLRet_Length;
            }
        }

        private void txt_DiscR1_EditValueChanged(object sender, EventArgs e)
        {
            if (ProcessId == Process.PurchaseInvoice)
                PRInv_Height = Convert.ToDecimal(txt_Height.EditValue);

            if (ProcessId == Process.PurchaseReturn)
                PRRet_Height = Convert.ToDecimal(txt_Height.EditValue);

            if (ProcessId == Process.SellInvoice)
                SLInv_Height = Convert.ToDecimal(txt_Height.EditValue);

            if (ProcessId == Process.SellReturn)
                SLRet_Height = Convert.ToDecimal(txt_Height.EditValue);
        }

        private void txt_DiscR2_EditValueChanged(object sender, EventArgs e)
        {
            if (ProcessId == Process.PurchaseInvoice)
                PRInv_Width = Convert.ToDecimal(txt_Width.EditValue);

            if (ProcessId == Process.PurchaseReturn)
                PRRet_Width = Convert.ToDecimal(txt_Width.EditValue);

            if (ProcessId == Process.SellInvoice)
                SLInv_Width = Convert.ToDecimal(txt_Width.EditValue);

            if (ProcessId == Process.SellReturn)
                SLRet_Width = Convert.ToDecimal(txt_Width.EditValue);
        }

        private void txt_DiscR3_EditValueChanged(object sender, EventArgs e)
        {
            if (ProcessId == Process.PurchaseInvoice)
                PRInv_Length = Convert.ToDecimal(txt_Length.EditValue);

            if (ProcessId == Process.PurchaseReturn)
                PRRet_Length = Convert.ToDecimal(txt_Length.EditValue);

            if (ProcessId == Process.SellInvoice)
                SLInv_Length = Convert.ToDecimal(txt_Length.EditValue);

            if (ProcessId == Process.SellReturn)
                SLRet_Length = Convert.ToDecimal(txt_Length.EditValue);
        }

        private void frm_InvoiceDiscs_FormClosing(object sender, FormClosingEventArgs e)
        {
            PRInv_Height = 0; PRInv_Width = 0; PRInv_Length = 0;
            PRRet_Height = 0; PRRet_Width = 0; PRRet_Length = 0;
            SLInv_Height = 0; SLInv_Width = 0; SLInv_Length = 0;
            SLRet_Height = 0; SLRet_Width = 0; SLRet_Length = 0;
        }

        private void txt_DiscR3_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter || e.KeyCode == Keys.Tab)
            {
                if (ProcessId == Process.SellInvoice)
                {
                    Application.OpenForms["frm_SL_Invoice"].BringToFront();
                }
                if (ProcessId == Process.SellReturn)
                {
                    Application.OpenForms["frm_SL_Return"].BringToFront();
                }
                if (ProcessId == Process.PurchaseInvoice)
                {
                    Application.OpenForms["frm_PR_Invoice"].BringToFront();
                }
                if (ProcessId == Process.PurchaseReturn)
                {
                    Application.OpenForms["frm_PR_Return"].BringToFront();
                }
            }
        }
    }
}
