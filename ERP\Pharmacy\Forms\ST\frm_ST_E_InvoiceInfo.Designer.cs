﻿namespace Pharmacy.Forms
{
    partial class frm_ST_E_InvoiceInfo
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_ST_E_InvoiceInfo));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Save = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.lkpCompanyType = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl35 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.txtClientId = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtCompanyNameEn = new DevExpress.XtraEditors.TextEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtCompanyNameAr = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtTaxCard = new DevExpress.XtraEditors.TextEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.txtCommercialBook = new DevExpress.XtraEditors.TextEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.txtClientSecret1 = new DevExpress.XtraEditors.TextEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.textEdit3 = new DevExpress.XtraEditors.TextEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.txtDonglePIN = new DevExpress.XtraEditors.TextEdit();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.txtPublickey = new DevExpress.XtraEditors.TextEdit();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.txtTokenserial = new DevExpress.XtraEditors.TextEdit();
            this.chk_allowMoreThanTax = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.spinDocumentThreshold = new DevExpress.XtraEditors.SpinEdit();
            this.spinRoundValue = new DevExpress.XtraEditors.SpinEdit();
            this.spinValidationDays = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.txtECode = new DevExpress.XtraEditors.TextEdit();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.txtRegionCity = new DevExpress.XtraEditors.TextEdit();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.txtStreet = new DevExpress.XtraEditors.TextEdit();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.txtBuildingNumber = new DevExpress.XtraEditors.TextEdit();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.txtGovernate = new DevExpress.XtraEditors.TextEdit();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.txtActivityType = new DevExpress.XtraEditors.TextEdit();
            this.txtServerName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl29 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_country = new DevExpress.XtraEditors.LookUpEdit();
            this.btnTestAPI = new DevExpress.XtraEditors.SimpleButton();
            this.txtuuid = new DevExpress.XtraEditors.TextEdit();
            this.btnTestlibrary = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl30 = new DevExpress.XtraEditors.LabelControl();
            this.txtLibraryPath = new DevExpress.XtraEditors.TextEdit();
            this.Btnslots = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl31 = new DevExpress.XtraEditors.LabelControl();
            this.BtnShowClientScrt = new DevExpress.XtraEditors.SimpleButton();
            this.BtnShowClientId = new DevExpress.XtraEditors.SimpleButton();
            this.BtnShowDnglPIN = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCompanyType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtClientId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameEn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameAr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxCard.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCommercialBook.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtClientSecret1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDonglePIN.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPublickey.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTokenserial.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_allowMoreThanTax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinDocumentThreshold.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinRoundValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinValidationDays.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtECode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRegionCity.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStreet.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBuildingNumber.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGovernate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtActivityType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtServerName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_country.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtuuid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLibraryPath.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowMoveBarOnToolbar = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtn_Save,
            this.barBtn_Help,
            this.barButtonClose});
            this.barManager1.MaxItemId = 26;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Save),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtn_Save
            // 
            resources.ApplyResources(this.barBtn_Save, "barBtn_Save");
            this.barBtn_Save.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Save.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtn_Save.Id = 0;
            this.barBtn_Save.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtn_Save.Name = "barBtn_Save";
            this.barBtn_Save.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Save.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barButtonClose
            // 
            resources.ApplyResources(this.barButtonClose, "barButtonClose");
            this.barButtonClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barButtonClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barButtonClose.Id = 25;
            this.barButtonClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barButtonClose.Name = "barButtonClose";
            this.barButtonClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barButtonClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // lkpCompanyType
            // 
            resources.ApplyResources(this.lkpCompanyType, "lkpCompanyType");
            this.lkpCompanyType.EnterMoveNextControl = true;
            this.lkpCompanyType.Name = "lkpCompanyType";
            this.lkpCompanyType.Properties.AccessibleDescription = resources.GetString("lkpCompanyType.Properties.AccessibleDescription");
            this.lkpCompanyType.Properties.AccessibleName = resources.GetString("lkpCompanyType.Properties.AccessibleName");
            this.lkpCompanyType.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpCompanyType.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpCompanyType.Properties.Appearance.FontSizeDelta")));
            this.lkpCompanyType.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCompanyType.Properties.Appearance.FontStyleDelta")));
            this.lkpCompanyType.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCompanyType.Properties.Appearance.GradientMode")));
            this.lkpCompanyType.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpCompanyType.Properties.Appearance.Image")));
            this.lkpCompanyType.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpCompanyType.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCompanyType.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkpCompanyType.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpCompanyType.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpCompanyType.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCompanyType.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpCompanyType.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCompanyType.Properties.AppearanceDropDown.GradientMode")));
            this.lkpCompanyType.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpCompanyType.Properties.AppearanceDropDown.Image")));
            this.lkpCompanyType.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpCompanyType.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpCompanyType.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpCompanyType.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpCompanyType.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCompanyType.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpCompanyType.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCompanyType.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpCompanyType.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpCompanyType.Properties.AppearanceDropDownHeader.Image")));
            this.lkpCompanyType.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpCompanyType.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpCompanyType.Properties.AutoHeight = ((bool)(resources.GetObject("lkpCompanyType.Properties.AutoHeight")));
            this.lkpCompanyType.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpCompanyType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpCompanyType.Properties.Buttons"))))});
            this.lkpCompanyType.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCompanyType.Properties.Columns"), resources.GetString("lkpCompanyType.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCompanyType.Properties.Columns2"), resources.GetString("lkpCompanyType.Properties.Columns3"))});
            this.lkpCompanyType.Properties.DisplayMember = "Name";
            this.lkpCompanyType.Properties.NullText = resources.GetString("lkpCompanyType.Properties.NullText");
            this.lkpCompanyType.Properties.NullValuePrompt = resources.GetString("lkpCompanyType.Properties.NullValuePrompt");
            this.lkpCompanyType.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpCompanyType.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpCompanyType.Properties.PopupSizeable = false;
            this.lkpCompanyType.Properties.ValueMember = "Id";
            this.lkpCompanyType.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl35
            // 
            resources.ApplyResources(this.labelControl35, "labelControl35");
            this.labelControl35.Name = "labelControl35";
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // txtClientId
            // 
            resources.ApplyResources(this.txtClientId, "txtClientId");
            this.txtClientId.EnterMoveNextControl = true;
            this.txtClientId.Name = "txtClientId";
            this.txtClientId.Properties.AccessibleDescription = resources.GetString("txtClientId.Properties.AccessibleDescription");
            this.txtClientId.Properties.AccessibleName = resources.GetString("txtClientId.Properties.AccessibleName");
            this.txtClientId.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtClientId.Properties.Appearance.FontSizeDelta")));
            this.txtClientId.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtClientId.Properties.Appearance.FontStyleDelta")));
            this.txtClientId.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtClientId.Properties.Appearance.GradientMode")));
            this.txtClientId.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtClientId.Properties.Appearance.Image")));
            this.txtClientId.Properties.Appearance.Options.UseTextOptions = true;
            this.txtClientId.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtClientId.Properties.AutoHeight = ((bool)(resources.GetObject("txtClientId.Properties.AutoHeight")));
            this.txtClientId.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtClientId.Properties.Mask.AutoComplete")));
            this.txtClientId.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtClientId.Properties.Mask.BeepOnError")));
            this.txtClientId.Properties.Mask.EditMask = resources.GetString("txtClientId.Properties.Mask.EditMask");
            this.txtClientId.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtClientId.Properties.Mask.IgnoreMaskBlank")));
            this.txtClientId.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtClientId.Properties.Mask.MaskType")));
            this.txtClientId.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtClientId.Properties.Mask.PlaceHolder")));
            this.txtClientId.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtClientId.Properties.Mask.SaveLiteral")));
            this.txtClientId.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtClientId.Properties.Mask.ShowPlaceHolders")));
            this.txtClientId.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtClientId.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtClientId.Properties.MaxLength = 50;
            this.txtClientId.Properties.NullValuePrompt = resources.GetString("txtClientId.Properties.NullValuePrompt");
            this.txtClientId.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtClientId.Properties.NullValuePromptShowForEmptyValue")));
            this.txtClientId.Properties.PasswordChar = '*';
            this.txtClientId.Properties.UseSystemPasswordChar = true;
            this.txtClientId.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // txtCompanyNameEn
            // 
            resources.ApplyResources(this.txtCompanyNameEn, "txtCompanyNameEn");
            this.txtCompanyNameEn.EnterMoveNextControl = true;
            this.txtCompanyNameEn.Name = "txtCompanyNameEn";
            this.txtCompanyNameEn.Properties.AccessibleDescription = resources.GetString("txtCompanyNameEn.Properties.AccessibleDescription");
            this.txtCompanyNameEn.Properties.AccessibleName = resources.GetString("txtCompanyNameEn.Properties.AccessibleName");
            this.txtCompanyNameEn.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.FontSizeDelta")));
            this.txtCompanyNameEn.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.FontStyleDelta")));
            this.txtCompanyNameEn.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.GradientMode")));
            this.txtCompanyNameEn.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.Image")));
            this.txtCompanyNameEn.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCompanyNameEn.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCompanyNameEn.Properties.AutoHeight = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.AutoHeight")));
            this.txtCompanyNameEn.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCompanyNameEn.Properties.Mask.AutoComplete")));
            this.txtCompanyNameEn.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.BeepOnError")));
            this.txtCompanyNameEn.Properties.Mask.EditMask = resources.GetString("txtCompanyNameEn.Properties.Mask.EditMask");
            this.txtCompanyNameEn.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.IgnoreMaskBlank")));
            this.txtCompanyNameEn.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCompanyNameEn.Properties.Mask.MaskType")));
            this.txtCompanyNameEn.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCompanyNameEn.Properties.Mask.PlaceHolder")));
            this.txtCompanyNameEn.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.SaveLiteral")));
            this.txtCompanyNameEn.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.ShowPlaceHolders")));
            this.txtCompanyNameEn.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCompanyNameEn.Properties.MaxLength = 50;
            this.txtCompanyNameEn.Properties.NullValuePrompt = resources.GetString("txtCompanyNameEn.Properties.NullValuePrompt");
            this.txtCompanyNameEn.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCompanyNameEn.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // txtCompanyNameAr
            // 
            resources.ApplyResources(this.txtCompanyNameAr, "txtCompanyNameAr");
            this.txtCompanyNameAr.EnterMoveNextControl = true;
            this.txtCompanyNameAr.Name = "txtCompanyNameAr";
            this.txtCompanyNameAr.Properties.AccessibleDescription = resources.GetString("txtCompanyNameAr.Properties.AccessibleDescription");
            this.txtCompanyNameAr.Properties.AccessibleName = resources.GetString("txtCompanyNameAr.Properties.AccessibleName");
            this.txtCompanyNameAr.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.FontSizeDelta")));
            this.txtCompanyNameAr.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.FontStyleDelta")));
            this.txtCompanyNameAr.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.GradientMode")));
            this.txtCompanyNameAr.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.Image")));
            this.txtCompanyNameAr.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCompanyNameAr.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCompanyNameAr.Properties.AutoHeight = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.AutoHeight")));
            this.txtCompanyNameAr.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCompanyNameAr.Properties.Mask.AutoComplete")));
            this.txtCompanyNameAr.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.BeepOnError")));
            this.txtCompanyNameAr.Properties.Mask.EditMask = resources.GetString("txtCompanyNameAr.Properties.Mask.EditMask");
            this.txtCompanyNameAr.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.IgnoreMaskBlank")));
            this.txtCompanyNameAr.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCompanyNameAr.Properties.Mask.MaskType")));
            this.txtCompanyNameAr.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCompanyNameAr.Properties.Mask.PlaceHolder")));
            this.txtCompanyNameAr.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.SaveLiteral")));
            this.txtCompanyNameAr.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.ShowPlaceHolders")));
            this.txtCompanyNameAr.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCompanyNameAr.Properties.MaxLength = 50;
            this.txtCompanyNameAr.Properties.NullValuePrompt = resources.GetString("txtCompanyNameAr.Properties.NullValuePrompt");
            this.txtCompanyNameAr.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCompanyNameAr.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // txtTaxCard
            // 
            resources.ApplyResources(this.txtTaxCard, "txtTaxCard");
            this.txtTaxCard.EnterMoveNextControl = true;
            this.txtTaxCard.Name = "txtTaxCard";
            this.txtTaxCard.Properties.AccessibleDescription = resources.GetString("txtTaxCard.Properties.AccessibleDescription");
            this.txtTaxCard.Properties.AccessibleName = resources.GetString("txtTaxCard.Properties.AccessibleName");
            this.txtTaxCard.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtTaxCard.Properties.Appearance.FontSizeDelta")));
            this.txtTaxCard.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtTaxCard.Properties.Appearance.FontStyleDelta")));
            this.txtTaxCard.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtTaxCard.Properties.Appearance.GradientMode")));
            this.txtTaxCard.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtTaxCard.Properties.Appearance.Image")));
            this.txtTaxCard.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTaxCard.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTaxCard.Properties.AutoHeight = ((bool)(resources.GetObject("txtTaxCard.Properties.AutoHeight")));
            this.txtTaxCard.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTaxCard.Properties.Mask.AutoComplete")));
            this.txtTaxCard.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.BeepOnError")));
            this.txtTaxCard.Properties.Mask.EditMask = resources.GetString("txtTaxCard.Properties.Mask.EditMask");
            this.txtTaxCard.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.IgnoreMaskBlank")));
            this.txtTaxCard.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTaxCard.Properties.Mask.MaskType")));
            this.txtTaxCard.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTaxCard.Properties.Mask.PlaceHolder")));
            this.txtTaxCard.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.SaveLiteral")));
            this.txtTaxCard.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.ShowPlaceHolders")));
            this.txtTaxCard.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTaxCard.Properties.MaxLength = 50;
            this.txtTaxCard.Properties.NullValuePrompt = resources.GetString("txtTaxCard.Properties.NullValuePrompt");
            this.txtTaxCard.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTaxCard.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTaxCard.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Name = "labelControl13";
            // 
            // txtCommercialBook
            // 
            resources.ApplyResources(this.txtCommercialBook, "txtCommercialBook");
            this.txtCommercialBook.EnterMoveNextControl = true;
            this.txtCommercialBook.Name = "txtCommercialBook";
            this.txtCommercialBook.Properties.AccessibleDescription = resources.GetString("txtCommercialBook.Properties.AccessibleDescription");
            this.txtCommercialBook.Properties.AccessibleName = resources.GetString("txtCommercialBook.Properties.AccessibleName");
            this.txtCommercialBook.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCommercialBook.Properties.Appearance.FontSizeDelta")));
            this.txtCommercialBook.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCommercialBook.Properties.Appearance.FontStyleDelta")));
            this.txtCommercialBook.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCommercialBook.Properties.Appearance.GradientMode")));
            this.txtCommercialBook.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCommercialBook.Properties.Appearance.Image")));
            this.txtCommercialBook.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCommercialBook.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCommercialBook.Properties.AutoHeight = ((bool)(resources.GetObject("txtCommercialBook.Properties.AutoHeight")));
            this.txtCommercialBook.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCommercialBook.Properties.Mask.AutoComplete")));
            this.txtCommercialBook.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.BeepOnError")));
            this.txtCommercialBook.Properties.Mask.EditMask = resources.GetString("txtCommercialBook.Properties.Mask.EditMask");
            this.txtCommercialBook.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.IgnoreMaskBlank")));
            this.txtCommercialBook.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCommercialBook.Properties.Mask.MaskType")));
            this.txtCommercialBook.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCommercialBook.Properties.Mask.PlaceHolder")));
            this.txtCommercialBook.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.SaveLiteral")));
            this.txtCommercialBook.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.ShowPlaceHolders")));
            this.txtCommercialBook.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCommercialBook.Properties.MaxLength = 50;
            this.txtCommercialBook.Properties.NullValuePrompt = resources.GetString("txtCommercialBook.Properties.NullValuePrompt");
            this.txtCommercialBook.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCommercialBook.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCommercialBook.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Name = "labelControl12";
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Name = "labelControl7";
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // txtClientSecret1
            // 
            resources.ApplyResources(this.txtClientSecret1, "txtClientSecret1");
            this.txtClientSecret1.EnterMoveNextControl = true;
            this.txtClientSecret1.Name = "txtClientSecret1";
            this.txtClientSecret1.Properties.AccessibleDescription = resources.GetString("txtClientSecret1.Properties.AccessibleDescription");
            this.txtClientSecret1.Properties.AccessibleName = resources.GetString("txtClientSecret1.Properties.AccessibleName");
            this.txtClientSecret1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtClientSecret1.Properties.Appearance.FontSizeDelta")));
            this.txtClientSecret1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtClientSecret1.Properties.Appearance.FontStyleDelta")));
            this.txtClientSecret1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtClientSecret1.Properties.Appearance.GradientMode")));
            this.txtClientSecret1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtClientSecret1.Properties.Appearance.Image")));
            this.txtClientSecret1.Properties.Appearance.Options.UseTextOptions = true;
            this.txtClientSecret1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtClientSecret1.Properties.AutoHeight = ((bool)(resources.GetObject("txtClientSecret1.Properties.AutoHeight")));
            this.txtClientSecret1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtClientSecret1.Properties.Mask.AutoComplete")));
            this.txtClientSecret1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtClientSecret1.Properties.Mask.BeepOnError")));
            this.txtClientSecret1.Properties.Mask.EditMask = resources.GetString("txtClientSecret1.Properties.Mask.EditMask");
            this.txtClientSecret1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtClientSecret1.Properties.Mask.IgnoreMaskBlank")));
            this.txtClientSecret1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtClientSecret1.Properties.Mask.MaskType")));
            this.txtClientSecret1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtClientSecret1.Properties.Mask.PlaceHolder")));
            this.txtClientSecret1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtClientSecret1.Properties.Mask.SaveLiteral")));
            this.txtClientSecret1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtClientSecret1.Properties.Mask.ShowPlaceHolders")));
            this.txtClientSecret1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtClientSecret1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtClientSecret1.Properties.MaxLength = 50;
            this.txtClientSecret1.Properties.NullValuePrompt = resources.GetString("txtClientSecret1.Properties.NullValuePrompt");
            this.txtClientSecret1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtClientSecret1.Properties.NullValuePromptShowForEmptyValue")));
            this.txtClientSecret1.Properties.PasswordChar = '*';
            this.txtClientSecret1.Properties.UseSystemPasswordChar = true;
            this.txtClientSecret1.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // textEdit3
            // 
            resources.ApplyResources(this.textEdit3, "textEdit3");
            this.textEdit3.EnterMoveNextControl = true;
            this.textEdit3.Name = "textEdit3";
            this.textEdit3.Properties.AccessibleDescription = resources.GetString("textEdit3.Properties.AccessibleDescription");
            this.textEdit3.Properties.AccessibleName = resources.GetString("textEdit3.Properties.AccessibleName");
            this.textEdit3.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("textEdit3.Properties.Appearance.FontSizeDelta")));
            this.textEdit3.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("textEdit3.Properties.Appearance.FontStyleDelta")));
            this.textEdit3.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("textEdit3.Properties.Appearance.GradientMode")));
            this.textEdit3.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("textEdit3.Properties.Appearance.Image")));
            this.textEdit3.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit3.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit3.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit3.Properties.AutoHeight")));
            this.textEdit3.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("textEdit3.Properties.Mask.AutoComplete")));
            this.textEdit3.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("textEdit3.Properties.Mask.BeepOnError")));
            this.textEdit3.Properties.Mask.EditMask = resources.GetString("textEdit3.Properties.Mask.EditMask");
            this.textEdit3.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit3.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit3.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("textEdit3.Properties.Mask.MaskType")));
            this.textEdit3.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("textEdit3.Properties.Mask.PlaceHolder")));
            this.textEdit3.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit3.Properties.Mask.SaveLiteral")));
            this.textEdit3.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit3.Properties.Mask.ShowPlaceHolders")));
            this.textEdit3.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("textEdit3.Properties.Mask.UseMaskAsDisplayFormat")));
            this.textEdit3.Properties.MaxLength = 50;
            this.textEdit3.Properties.NullValuePrompt = resources.GetString("textEdit3.Properties.NullValuePrompt");
            this.textEdit3.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("textEdit3.Properties.NullValuePromptShowForEmptyValue")));
            this.textEdit3.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Name = "labelControl14";
            // 
            // txtDonglePIN
            // 
            resources.ApplyResources(this.txtDonglePIN, "txtDonglePIN");
            this.txtDonglePIN.EnterMoveNextControl = true;
            this.txtDonglePIN.Name = "txtDonglePIN";
            this.txtDonglePIN.Properties.AccessibleDescription = resources.GetString("txtDonglePIN.Properties.AccessibleDescription");
            this.txtDonglePIN.Properties.AccessibleName = resources.GetString("txtDonglePIN.Properties.AccessibleName");
            this.txtDonglePIN.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtDonglePIN.Properties.Appearance.FontSizeDelta")));
            this.txtDonglePIN.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtDonglePIN.Properties.Appearance.FontStyleDelta")));
            this.txtDonglePIN.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtDonglePIN.Properties.Appearance.GradientMode")));
            this.txtDonglePIN.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtDonglePIN.Properties.Appearance.Image")));
            this.txtDonglePIN.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDonglePIN.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDonglePIN.Properties.AutoHeight = ((bool)(resources.GetObject("txtDonglePIN.Properties.AutoHeight")));
            this.txtDonglePIN.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtDonglePIN.Properties.Mask.AutoComplete")));
            this.txtDonglePIN.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtDonglePIN.Properties.Mask.BeepOnError")));
            this.txtDonglePIN.Properties.Mask.EditMask = resources.GetString("txtDonglePIN.Properties.Mask.EditMask");
            this.txtDonglePIN.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDonglePIN.Properties.Mask.IgnoreMaskBlank")));
            this.txtDonglePIN.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDonglePIN.Properties.Mask.MaskType")));
            this.txtDonglePIN.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtDonglePIN.Properties.Mask.PlaceHolder")));
            this.txtDonglePIN.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDonglePIN.Properties.Mask.SaveLiteral")));
            this.txtDonglePIN.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDonglePIN.Properties.Mask.ShowPlaceHolders")));
            this.txtDonglePIN.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtDonglePIN.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtDonglePIN.Properties.MaxLength = 50;
            this.txtDonglePIN.Properties.NullValuePrompt = resources.GetString("txtDonglePIN.Properties.NullValuePrompt");
            this.txtDonglePIN.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtDonglePIN.Properties.NullValuePromptShowForEmptyValue")));
            this.txtDonglePIN.Properties.PasswordChar = '*';
            this.txtDonglePIN.Properties.UseSystemPasswordChar = true;
            this.txtDonglePIN.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // txtPublickey
            // 
            resources.ApplyResources(this.txtPublickey, "txtPublickey");
            this.txtPublickey.EnterMoveNextControl = true;
            this.txtPublickey.Name = "txtPublickey";
            this.txtPublickey.Properties.AccessibleDescription = resources.GetString("txtPublickey.Properties.AccessibleDescription");
            this.txtPublickey.Properties.AccessibleName = resources.GetString("txtPublickey.Properties.AccessibleName");
            this.txtPublickey.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtPublickey.Properties.Appearance.FontSizeDelta")));
            this.txtPublickey.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtPublickey.Properties.Appearance.FontStyleDelta")));
            this.txtPublickey.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtPublickey.Properties.Appearance.GradientMode")));
            this.txtPublickey.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtPublickey.Properties.Appearance.Image")));
            this.txtPublickey.Properties.Appearance.Options.UseTextOptions = true;
            this.txtPublickey.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtPublickey.Properties.AutoHeight = ((bool)(resources.GetObject("txtPublickey.Properties.AutoHeight")));
            this.txtPublickey.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtPublickey.Properties.Mask.AutoComplete")));
            this.txtPublickey.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtPublickey.Properties.Mask.BeepOnError")));
            this.txtPublickey.Properties.Mask.EditMask = resources.GetString("txtPublickey.Properties.Mask.EditMask");
            this.txtPublickey.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtPublickey.Properties.Mask.IgnoreMaskBlank")));
            this.txtPublickey.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtPublickey.Properties.Mask.MaskType")));
            this.txtPublickey.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtPublickey.Properties.Mask.PlaceHolder")));
            this.txtPublickey.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtPublickey.Properties.Mask.SaveLiteral")));
            this.txtPublickey.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtPublickey.Properties.Mask.ShowPlaceHolders")));
            this.txtPublickey.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtPublickey.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtPublickey.Properties.MaxLength = 50;
            this.txtPublickey.Properties.NullValuePrompt = resources.GetString("txtPublickey.Properties.NullValuePrompt");
            this.txtPublickey.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtPublickey.Properties.NullValuePromptShowForEmptyValue")));
            this.txtPublickey.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Name = "labelControl16";
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Name = "labelControl17";
            // 
            // txtTokenserial
            // 
            resources.ApplyResources(this.txtTokenserial, "txtTokenserial");
            this.txtTokenserial.EnterMoveNextControl = true;
            this.txtTokenserial.Name = "txtTokenserial";
            this.txtTokenserial.Properties.AccessibleDescription = resources.GetString("txtTokenserial.Properties.AccessibleDescription");
            this.txtTokenserial.Properties.AccessibleName = resources.GetString("txtTokenserial.Properties.AccessibleName");
            this.txtTokenserial.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtTokenserial.Properties.Appearance.FontSizeDelta")));
            this.txtTokenserial.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtTokenserial.Properties.Appearance.FontStyleDelta")));
            this.txtTokenserial.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtTokenserial.Properties.Appearance.GradientMode")));
            this.txtTokenserial.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtTokenserial.Properties.Appearance.Image")));
            this.txtTokenserial.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTokenserial.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTokenserial.Properties.AutoHeight = ((bool)(resources.GetObject("txtTokenserial.Properties.AutoHeight")));
            this.txtTokenserial.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTokenserial.Properties.Mask.AutoComplete")));
            this.txtTokenserial.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTokenserial.Properties.Mask.BeepOnError")));
            this.txtTokenserial.Properties.Mask.EditMask = resources.GetString("txtTokenserial.Properties.Mask.EditMask");
            this.txtTokenserial.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTokenserial.Properties.Mask.IgnoreMaskBlank")));
            this.txtTokenserial.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTokenserial.Properties.Mask.MaskType")));
            this.txtTokenserial.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTokenserial.Properties.Mask.PlaceHolder")));
            this.txtTokenserial.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTokenserial.Properties.Mask.SaveLiteral")));
            this.txtTokenserial.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTokenserial.Properties.Mask.ShowPlaceHolders")));
            this.txtTokenserial.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTokenserial.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTokenserial.Properties.MaxLength = 50;
            this.txtTokenserial.Properties.NullValuePrompt = resources.GetString("txtTokenserial.Properties.NullValuePrompt");
            this.txtTokenserial.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTokenserial.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTokenserial.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // chk_allowMoreThanTax
            // 
            resources.ApplyResources(this.chk_allowMoreThanTax, "chk_allowMoreThanTax");
            this.chk_allowMoreThanTax.Name = "chk_allowMoreThanTax";
            this.chk_allowMoreThanTax.Properties.AccessibleDescription = resources.GetString("chk_allowMoreThanTax.Properties.AccessibleDescription");
            this.chk_allowMoreThanTax.Properties.AccessibleName = resources.GetString("chk_allowMoreThanTax.Properties.AccessibleName");
            this.chk_allowMoreThanTax.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_allowMoreThanTax.Properties.Appearance.FontSizeDelta")));
            this.chk_allowMoreThanTax.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_allowMoreThanTax.Properties.Appearance.FontStyleDelta")));
            this.chk_allowMoreThanTax.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_allowMoreThanTax.Properties.Appearance.GradientMode")));
            this.chk_allowMoreThanTax.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_allowMoreThanTax.Properties.Appearance.Image")));
            this.chk_allowMoreThanTax.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_allowMoreThanTax.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_allowMoreThanTax.Properties.AutoHeight = ((bool)(resources.GetObject("chk_allowMoreThanTax.Properties.AutoHeight")));
            this.chk_allowMoreThanTax.Properties.AutoWidth = true;
            this.chk_allowMoreThanTax.Properties.Caption = resources.GetString("chk_allowMoreThanTax.Properties.Caption");
            this.chk_allowMoreThanTax.Properties.DisplayValueChecked = resources.GetString("chk_allowMoreThanTax.Properties.DisplayValueChecked");
            this.chk_allowMoreThanTax.Properties.DisplayValueGrayed = resources.GetString("chk_allowMoreThanTax.Properties.DisplayValueGrayed");
            this.chk_allowMoreThanTax.Properties.DisplayValueUnchecked = resources.GetString("chk_allowMoreThanTax.Properties.DisplayValueUnchecked");
            // 
            // labelControl18
            // 
            resources.ApplyResources(this.labelControl18, "labelControl18");
            this.labelControl18.Name = "labelControl18";
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Name = "labelControl19";
            // 
            // spinDocumentThreshold
            // 
            resources.ApplyResources(this.spinDocumentThreshold, "spinDocumentThreshold");
            this.spinDocumentThreshold.Name = "spinDocumentThreshold";
            this.spinDocumentThreshold.Properties.AccessibleDescription = resources.GetString("spinDocumentThreshold.Properties.AccessibleDescription");
            this.spinDocumentThreshold.Properties.AccessibleName = resources.GetString("spinDocumentThreshold.Properties.AccessibleName");
            this.spinDocumentThreshold.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.spinDocumentThreshold.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("spinDocumentThreshold.Properties.Appearance.FontSizeDelta")));
            this.spinDocumentThreshold.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("spinDocumentThreshold.Properties.Appearance.FontStyleDelta")));
            this.spinDocumentThreshold.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("spinDocumentThreshold.Properties.Appearance.GradientMode")));
            this.spinDocumentThreshold.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("spinDocumentThreshold.Properties.Appearance.Image")));
            this.spinDocumentThreshold.Properties.Appearance.Options.UseTextOptions = true;
            this.spinDocumentThreshold.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.spinDocumentThreshold.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("spinDocumentThreshold.Properties.AppearanceDisabled.BackColor")));
            this.spinDocumentThreshold.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("spinDocumentThreshold.Properties.AppearanceDisabled.FontSizeDelta")));
            this.spinDocumentThreshold.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("spinDocumentThreshold.Properties.AppearanceDisabled.FontStyleDelta")));
            this.spinDocumentThreshold.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("spinDocumentThreshold.Properties.AppearanceDisabled.ForeColor")));
            this.spinDocumentThreshold.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("spinDocumentThreshold.Properties.AppearanceDisabled.GradientMode")));
            this.spinDocumentThreshold.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("spinDocumentThreshold.Properties.AppearanceDisabled.Image")));
            this.spinDocumentThreshold.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.spinDocumentThreshold.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.spinDocumentThreshold.Properties.AutoHeight = ((bool)(resources.GetObject("spinDocumentThreshold.Properties.AutoHeight")));
            this.spinDocumentThreshold.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("spinDocumentThreshold.Properties.Buttons"))))});
            this.spinDocumentThreshold.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.spinDocumentThreshold.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("spinDocumentThreshold.Properties.Mask.AutoComplete")));
            this.spinDocumentThreshold.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("spinDocumentThreshold.Properties.Mask.BeepOnError")));
            this.spinDocumentThreshold.Properties.Mask.EditMask = resources.GetString("spinDocumentThreshold.Properties.Mask.EditMask");
            this.spinDocumentThreshold.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("spinDocumentThreshold.Properties.Mask.IgnoreMaskBlank")));
            this.spinDocumentThreshold.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("spinDocumentThreshold.Properties.Mask.MaskType")));
            this.spinDocumentThreshold.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("spinDocumentThreshold.Properties.Mask.PlaceHolder")));
            this.spinDocumentThreshold.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("spinDocumentThreshold.Properties.Mask.SaveLiteral")));
            this.spinDocumentThreshold.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("spinDocumentThreshold.Properties.Mask.ShowPlaceHolders")));
            this.spinDocumentThreshold.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("spinDocumentThreshold.Properties.Mask.UseMaskAsDisplayFormat")));
            this.spinDocumentThreshold.Properties.NullValuePrompt = resources.GetString("spinDocumentThreshold.Properties.NullValuePrompt");
            this.spinDocumentThreshold.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("spinDocumentThreshold.Properties.NullValuePromptShowForEmptyValue")));
            this.spinDocumentThreshold.TabStop = false;
            this.spinDocumentThreshold.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // spinRoundValue
            // 
            resources.ApplyResources(this.spinRoundValue, "spinRoundValue");
            this.spinRoundValue.Name = "spinRoundValue";
            this.spinRoundValue.Properties.AccessibleDescription = resources.GetString("spinRoundValue.Properties.AccessibleDescription");
            this.spinRoundValue.Properties.AccessibleName = resources.GetString("spinRoundValue.Properties.AccessibleName");
            this.spinRoundValue.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.spinRoundValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("spinRoundValue.Properties.Appearance.FontSizeDelta")));
            this.spinRoundValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("spinRoundValue.Properties.Appearance.FontStyleDelta")));
            this.spinRoundValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("spinRoundValue.Properties.Appearance.GradientMode")));
            this.spinRoundValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("spinRoundValue.Properties.Appearance.Image")));
            this.spinRoundValue.Properties.Appearance.Options.UseTextOptions = true;
            this.spinRoundValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.spinRoundValue.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("spinRoundValue.Properties.AppearanceDisabled.BackColor")));
            this.spinRoundValue.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("spinRoundValue.Properties.AppearanceDisabled.FontSizeDelta")));
            this.spinRoundValue.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("spinRoundValue.Properties.AppearanceDisabled.FontStyleDelta")));
            this.spinRoundValue.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("spinRoundValue.Properties.AppearanceDisabled.ForeColor")));
            this.spinRoundValue.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("spinRoundValue.Properties.AppearanceDisabled.GradientMode")));
            this.spinRoundValue.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("spinRoundValue.Properties.AppearanceDisabled.Image")));
            this.spinRoundValue.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.spinRoundValue.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.spinRoundValue.Properties.AutoHeight = ((bool)(resources.GetObject("spinRoundValue.Properties.AutoHeight")));
            this.spinRoundValue.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("spinRoundValue.Properties.Buttons"))))});
            this.spinRoundValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.spinRoundValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("spinRoundValue.Properties.Mask.AutoComplete")));
            this.spinRoundValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("spinRoundValue.Properties.Mask.BeepOnError")));
            this.spinRoundValue.Properties.Mask.EditMask = resources.GetString("spinRoundValue.Properties.Mask.EditMask");
            this.spinRoundValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("spinRoundValue.Properties.Mask.IgnoreMaskBlank")));
            this.spinRoundValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("spinRoundValue.Properties.Mask.MaskType")));
            this.spinRoundValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("spinRoundValue.Properties.Mask.PlaceHolder")));
            this.spinRoundValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("spinRoundValue.Properties.Mask.SaveLiteral")));
            this.spinRoundValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("spinRoundValue.Properties.Mask.ShowPlaceHolders")));
            this.spinRoundValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("spinRoundValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.spinRoundValue.Properties.NullValuePrompt = resources.GetString("spinRoundValue.Properties.NullValuePrompt");
            this.spinRoundValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("spinRoundValue.Properties.NullValuePromptShowForEmptyValue")));
            this.spinRoundValue.TabStop = false;
            this.spinRoundValue.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // spinValidationDays
            // 
            resources.ApplyResources(this.spinValidationDays, "spinValidationDays");
            this.spinValidationDays.Name = "spinValidationDays";
            this.spinValidationDays.Properties.AccessibleDescription = resources.GetString("spinValidationDays.Properties.AccessibleDescription");
            this.spinValidationDays.Properties.AccessibleName = resources.GetString("spinValidationDays.Properties.AccessibleName");
            this.spinValidationDays.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.spinValidationDays.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("spinValidationDays.Properties.Appearance.FontSizeDelta")));
            this.spinValidationDays.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("spinValidationDays.Properties.Appearance.FontStyleDelta")));
            this.spinValidationDays.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("spinValidationDays.Properties.Appearance.GradientMode")));
            this.spinValidationDays.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("spinValidationDays.Properties.Appearance.Image")));
            this.spinValidationDays.Properties.Appearance.Options.UseTextOptions = true;
            this.spinValidationDays.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.spinValidationDays.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("spinValidationDays.Properties.AppearanceDisabled.BackColor")));
            this.spinValidationDays.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("spinValidationDays.Properties.AppearanceDisabled.FontSizeDelta")));
            this.spinValidationDays.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("spinValidationDays.Properties.AppearanceDisabled.FontStyleDelta")));
            this.spinValidationDays.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("spinValidationDays.Properties.AppearanceDisabled.ForeColor")));
            this.spinValidationDays.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("spinValidationDays.Properties.AppearanceDisabled.GradientMode")));
            this.spinValidationDays.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("spinValidationDays.Properties.AppearanceDisabled.Image")));
            this.spinValidationDays.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.spinValidationDays.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.spinValidationDays.Properties.AutoHeight = ((bool)(resources.GetObject("spinValidationDays.Properties.AutoHeight")));
            this.spinValidationDays.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("spinValidationDays.Properties.Buttons"))))});
            this.spinValidationDays.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.spinValidationDays.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("spinValidationDays.Properties.Mask.AutoComplete")));
            this.spinValidationDays.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("spinValidationDays.Properties.Mask.BeepOnError")));
            this.spinValidationDays.Properties.Mask.EditMask = resources.GetString("spinValidationDays.Properties.Mask.EditMask");
            this.spinValidationDays.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("spinValidationDays.Properties.Mask.IgnoreMaskBlank")));
            this.spinValidationDays.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("spinValidationDays.Properties.Mask.MaskType")));
            this.spinValidationDays.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("spinValidationDays.Properties.Mask.PlaceHolder")));
            this.spinValidationDays.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("spinValidationDays.Properties.Mask.SaveLiteral")));
            this.spinValidationDays.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("spinValidationDays.Properties.Mask.ShowPlaceHolders")));
            this.spinValidationDays.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("spinValidationDays.Properties.Mask.UseMaskAsDisplayFormat")));
            this.spinValidationDays.Properties.NullValuePrompt = resources.GetString("spinValidationDays.Properties.NullValuePrompt");
            this.spinValidationDays.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("spinValidationDays.Properties.NullValuePromptShowForEmptyValue")));
            this.spinValidationDays.TabStop = false;
            this.spinValidationDays.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Name = "labelControl20";
            // 
            // labelControl21
            // 
            resources.ApplyResources(this.labelControl21, "labelControl21");
            this.labelControl21.Name = "labelControl21";
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Name = "labelControl22";
            // 
            // txtECode
            // 
            resources.ApplyResources(this.txtECode, "txtECode");
            this.txtECode.EnterMoveNextControl = true;
            this.txtECode.Name = "txtECode";
            this.txtECode.Properties.AccessibleDescription = resources.GetString("txtECode.Properties.AccessibleDescription");
            this.txtECode.Properties.AccessibleName = resources.GetString("txtECode.Properties.AccessibleName");
            this.txtECode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtECode.Properties.Appearance.FontSizeDelta")));
            this.txtECode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtECode.Properties.Appearance.FontStyleDelta")));
            this.txtECode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtECode.Properties.Appearance.GradientMode")));
            this.txtECode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtECode.Properties.Appearance.Image")));
            this.txtECode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtECode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtECode.Properties.AutoHeight = ((bool)(resources.GetObject("txtECode.Properties.AutoHeight")));
            this.txtECode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtECode.Properties.Mask.AutoComplete")));
            this.txtECode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtECode.Properties.Mask.BeepOnError")));
            this.txtECode.Properties.Mask.EditMask = resources.GetString("txtECode.Properties.Mask.EditMask");
            this.txtECode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtECode.Properties.Mask.IgnoreMaskBlank")));
            this.txtECode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtECode.Properties.Mask.MaskType")));
            this.txtECode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtECode.Properties.Mask.PlaceHolder")));
            this.txtECode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtECode.Properties.Mask.SaveLiteral")));
            this.txtECode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtECode.Properties.Mask.ShowPlaceHolders")));
            this.txtECode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtECode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtECode.Properties.MaxLength = 50;
            this.txtECode.Properties.NullValuePrompt = resources.GetString("txtECode.Properties.NullValuePrompt");
            this.txtECode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtECode.Properties.NullValuePromptShowForEmptyValue")));
            this.txtECode.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl24
            // 
            resources.ApplyResources(this.labelControl24, "labelControl24");
            this.labelControl24.Name = "labelControl24";
            // 
            // txtRegionCity
            // 
            resources.ApplyResources(this.txtRegionCity, "txtRegionCity");
            this.txtRegionCity.EnterMoveNextControl = true;
            this.txtRegionCity.Name = "txtRegionCity";
            this.txtRegionCity.Properties.AccessibleDescription = resources.GetString("txtRegionCity.Properties.AccessibleDescription");
            this.txtRegionCity.Properties.AccessibleName = resources.GetString("txtRegionCity.Properties.AccessibleName");
            this.txtRegionCity.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtRegionCity.Properties.Appearance.FontSizeDelta")));
            this.txtRegionCity.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtRegionCity.Properties.Appearance.FontStyleDelta")));
            this.txtRegionCity.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtRegionCity.Properties.Appearance.GradientMode")));
            this.txtRegionCity.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtRegionCity.Properties.Appearance.Image")));
            this.txtRegionCity.Properties.Appearance.Options.UseTextOptions = true;
            this.txtRegionCity.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtRegionCity.Properties.AutoHeight = ((bool)(resources.GetObject("txtRegionCity.Properties.AutoHeight")));
            this.txtRegionCity.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtRegionCity.Properties.Mask.AutoComplete")));
            this.txtRegionCity.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtRegionCity.Properties.Mask.BeepOnError")));
            this.txtRegionCity.Properties.Mask.EditMask = resources.GetString("txtRegionCity.Properties.Mask.EditMask");
            this.txtRegionCity.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtRegionCity.Properties.Mask.IgnoreMaskBlank")));
            this.txtRegionCity.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtRegionCity.Properties.Mask.MaskType")));
            this.txtRegionCity.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtRegionCity.Properties.Mask.PlaceHolder")));
            this.txtRegionCity.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtRegionCity.Properties.Mask.SaveLiteral")));
            this.txtRegionCity.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtRegionCity.Properties.Mask.ShowPlaceHolders")));
            this.txtRegionCity.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtRegionCity.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtRegionCity.Properties.MaxLength = 50;
            this.txtRegionCity.Properties.NullValuePrompt = resources.GetString("txtRegionCity.Properties.NullValuePrompt");
            this.txtRegionCity.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtRegionCity.Properties.NullValuePromptShowForEmptyValue")));
            this.txtRegionCity.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl26
            // 
            resources.ApplyResources(this.labelControl26, "labelControl26");
            this.labelControl26.Name = "labelControl26";
            // 
            // txtStreet
            // 
            resources.ApplyResources(this.txtStreet, "txtStreet");
            this.txtStreet.EnterMoveNextControl = true;
            this.txtStreet.Name = "txtStreet";
            this.txtStreet.Properties.AccessibleDescription = resources.GetString("txtStreet.Properties.AccessibleDescription");
            this.txtStreet.Properties.AccessibleName = resources.GetString("txtStreet.Properties.AccessibleName");
            this.txtStreet.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtStreet.Properties.Appearance.FontSizeDelta")));
            this.txtStreet.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtStreet.Properties.Appearance.FontStyleDelta")));
            this.txtStreet.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtStreet.Properties.Appearance.GradientMode")));
            this.txtStreet.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtStreet.Properties.Appearance.Image")));
            this.txtStreet.Properties.Appearance.Options.UseTextOptions = true;
            this.txtStreet.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtStreet.Properties.AutoHeight = ((bool)(resources.GetObject("txtStreet.Properties.AutoHeight")));
            this.txtStreet.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtStreet.Properties.Mask.AutoComplete")));
            this.txtStreet.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtStreet.Properties.Mask.BeepOnError")));
            this.txtStreet.Properties.Mask.EditMask = resources.GetString("txtStreet.Properties.Mask.EditMask");
            this.txtStreet.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtStreet.Properties.Mask.IgnoreMaskBlank")));
            this.txtStreet.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtStreet.Properties.Mask.MaskType")));
            this.txtStreet.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtStreet.Properties.Mask.PlaceHolder")));
            this.txtStreet.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtStreet.Properties.Mask.SaveLiteral")));
            this.txtStreet.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtStreet.Properties.Mask.ShowPlaceHolders")));
            this.txtStreet.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtStreet.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtStreet.Properties.MaxLength = 50;
            this.txtStreet.Properties.NullValuePrompt = resources.GetString("txtStreet.Properties.NullValuePrompt");
            this.txtStreet.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtStreet.Properties.NullValuePromptShowForEmptyValue")));
            this.txtStreet.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl27
            // 
            resources.ApplyResources(this.labelControl27, "labelControl27");
            this.labelControl27.Name = "labelControl27";
            // 
            // labelControl23
            // 
            resources.ApplyResources(this.labelControl23, "labelControl23");
            this.labelControl23.Name = "labelControl23";
            // 
            // txtBuildingNumber
            // 
            resources.ApplyResources(this.txtBuildingNumber, "txtBuildingNumber");
            this.txtBuildingNumber.EnterMoveNextControl = true;
            this.txtBuildingNumber.Name = "txtBuildingNumber";
            this.txtBuildingNumber.Properties.AccessibleDescription = resources.GetString("txtBuildingNumber.Properties.AccessibleDescription");
            this.txtBuildingNumber.Properties.AccessibleName = resources.GetString("txtBuildingNumber.Properties.AccessibleName");
            this.txtBuildingNumber.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtBuildingNumber.Properties.Appearance.FontSizeDelta")));
            this.txtBuildingNumber.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtBuildingNumber.Properties.Appearance.FontStyleDelta")));
            this.txtBuildingNumber.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtBuildingNumber.Properties.Appearance.GradientMode")));
            this.txtBuildingNumber.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtBuildingNumber.Properties.Appearance.Image")));
            this.txtBuildingNumber.Properties.Appearance.Options.UseTextOptions = true;
            this.txtBuildingNumber.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtBuildingNumber.Properties.AutoHeight = ((bool)(resources.GetObject("txtBuildingNumber.Properties.AutoHeight")));
            this.txtBuildingNumber.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtBuildingNumber.Properties.Mask.AutoComplete")));
            this.txtBuildingNumber.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtBuildingNumber.Properties.Mask.BeepOnError")));
            this.txtBuildingNumber.Properties.Mask.EditMask = resources.GetString("txtBuildingNumber.Properties.Mask.EditMask");
            this.txtBuildingNumber.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtBuildingNumber.Properties.Mask.IgnoreMaskBlank")));
            this.txtBuildingNumber.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtBuildingNumber.Properties.Mask.MaskType")));
            this.txtBuildingNumber.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtBuildingNumber.Properties.Mask.PlaceHolder")));
            this.txtBuildingNumber.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtBuildingNumber.Properties.Mask.SaveLiteral")));
            this.txtBuildingNumber.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtBuildingNumber.Properties.Mask.ShowPlaceHolders")));
            this.txtBuildingNumber.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtBuildingNumber.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtBuildingNumber.Properties.MaxLength = 50;
            this.txtBuildingNumber.Properties.NullValuePrompt = resources.GetString("txtBuildingNumber.Properties.NullValuePrompt");
            this.txtBuildingNumber.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtBuildingNumber.Properties.NullValuePromptShowForEmptyValue")));
            this.txtBuildingNumber.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Name = "labelControl25";
            // 
            // txtGovernate
            // 
            resources.ApplyResources(this.txtGovernate, "txtGovernate");
            this.txtGovernate.EnterMoveNextControl = true;
            this.txtGovernate.Name = "txtGovernate";
            this.txtGovernate.Properties.AccessibleDescription = resources.GetString("txtGovernate.Properties.AccessibleDescription");
            this.txtGovernate.Properties.AccessibleName = resources.GetString("txtGovernate.Properties.AccessibleName");
            this.txtGovernate.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtGovernate.Properties.Appearance.FontSizeDelta")));
            this.txtGovernate.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtGovernate.Properties.Appearance.FontStyleDelta")));
            this.txtGovernate.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtGovernate.Properties.Appearance.GradientMode")));
            this.txtGovernate.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtGovernate.Properties.Appearance.Image")));
            this.txtGovernate.Properties.Appearance.Options.UseTextOptions = true;
            this.txtGovernate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtGovernate.Properties.AutoHeight = ((bool)(resources.GetObject("txtGovernate.Properties.AutoHeight")));
            this.txtGovernate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtGovernate.Properties.Mask.AutoComplete")));
            this.txtGovernate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtGovernate.Properties.Mask.BeepOnError")));
            this.txtGovernate.Properties.Mask.EditMask = resources.GetString("txtGovernate.Properties.Mask.EditMask");
            this.txtGovernate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtGovernate.Properties.Mask.IgnoreMaskBlank")));
            this.txtGovernate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtGovernate.Properties.Mask.MaskType")));
            this.txtGovernate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtGovernate.Properties.Mask.PlaceHolder")));
            this.txtGovernate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtGovernate.Properties.Mask.SaveLiteral")));
            this.txtGovernate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtGovernate.Properties.Mask.ShowPlaceHolders")));
            this.txtGovernate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtGovernate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtGovernate.Properties.MaxLength = 50;
            this.txtGovernate.Properties.NullValuePrompt = resources.GetString("txtGovernate.Properties.NullValuePrompt");
            this.txtGovernate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtGovernate.Properties.NullValuePromptShowForEmptyValue")));
            this.txtGovernate.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl28
            // 
            resources.ApplyResources(this.labelControl28, "labelControl28");
            this.labelControl28.Name = "labelControl28";
            // 
            // txtActivityType
            // 
            resources.ApplyResources(this.txtActivityType, "txtActivityType");
            this.txtActivityType.EnterMoveNextControl = true;
            this.txtActivityType.Name = "txtActivityType";
            this.txtActivityType.Properties.AccessibleDescription = resources.GetString("txtActivityType.Properties.AccessibleDescription");
            this.txtActivityType.Properties.AccessibleName = resources.GetString("txtActivityType.Properties.AccessibleName");
            this.txtActivityType.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtActivityType.Properties.Appearance.FontSizeDelta")));
            this.txtActivityType.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtActivityType.Properties.Appearance.FontStyleDelta")));
            this.txtActivityType.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtActivityType.Properties.Appearance.GradientMode")));
            this.txtActivityType.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtActivityType.Properties.Appearance.Image")));
            this.txtActivityType.Properties.Appearance.Options.UseTextOptions = true;
            this.txtActivityType.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtActivityType.Properties.AutoHeight = ((bool)(resources.GetObject("txtActivityType.Properties.AutoHeight")));
            this.txtActivityType.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtActivityType.Properties.Mask.AutoComplete")));
            this.txtActivityType.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.BeepOnError")));
            this.txtActivityType.Properties.Mask.EditMask = resources.GetString("txtActivityType.Properties.Mask.EditMask");
            this.txtActivityType.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.IgnoreMaskBlank")));
            this.txtActivityType.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtActivityType.Properties.Mask.MaskType")));
            this.txtActivityType.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtActivityType.Properties.Mask.PlaceHolder")));
            this.txtActivityType.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.SaveLiteral")));
            this.txtActivityType.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.ShowPlaceHolders")));
            this.txtActivityType.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtActivityType.Properties.MaxLength = 50;
            this.txtActivityType.Properties.NullValuePrompt = resources.GetString("txtActivityType.Properties.NullValuePrompt");
            this.txtActivityType.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtActivityType.Properties.NullValuePromptShowForEmptyValue")));
            this.txtActivityType.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // txtServerName
            // 
            resources.ApplyResources(this.txtServerName, "txtServerName");
            this.txtServerName.EnterMoveNextControl = true;
            this.txtServerName.Name = "txtServerName";
            this.txtServerName.Properties.AccessibleDescription = resources.GetString("txtServerName.Properties.AccessibleDescription");
            this.txtServerName.Properties.AccessibleName = resources.GetString("txtServerName.Properties.AccessibleName");
            this.txtServerName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtServerName.Properties.Appearance.FontSizeDelta")));
            this.txtServerName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtServerName.Properties.Appearance.FontStyleDelta")));
            this.txtServerName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtServerName.Properties.Appearance.GradientMode")));
            this.txtServerName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtServerName.Properties.Appearance.Image")));
            this.txtServerName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtServerName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtServerName.Properties.AutoHeight = ((bool)(resources.GetObject("txtServerName.Properties.AutoHeight")));
            this.txtServerName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtServerName.Properties.Mask.AutoComplete")));
            this.txtServerName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtServerName.Properties.Mask.BeepOnError")));
            this.txtServerName.Properties.Mask.EditMask = resources.GetString("txtServerName.Properties.Mask.EditMask");
            this.txtServerName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtServerName.Properties.Mask.IgnoreMaskBlank")));
            this.txtServerName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtServerName.Properties.Mask.MaskType")));
            this.txtServerName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtServerName.Properties.Mask.PlaceHolder")));
            this.txtServerName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtServerName.Properties.Mask.SaveLiteral")));
            this.txtServerName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtServerName.Properties.Mask.ShowPlaceHolders")));
            this.txtServerName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtServerName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtServerName.Properties.MaxLength = 50;
            this.txtServerName.Properties.NullValuePrompt = resources.GetString("txtServerName.Properties.NullValuePrompt");
            this.txtServerName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtServerName.Properties.NullValuePromptShowForEmptyValue")));
            this.txtServerName.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl29
            // 
            resources.ApplyResources(this.labelControl29, "labelControl29");
            this.labelControl29.Name = "labelControl29";
            // 
            // lkp_country
            // 
            resources.ApplyResources(this.lkp_country, "lkp_country");
            this.lkp_country.EnterMoveNextControl = true;
            this.lkp_country.Name = "lkp_country";
            this.lkp_country.Properties.AccessibleDescription = resources.GetString("lkp_country.Properties.AccessibleDescription");
            this.lkp_country.Properties.AccessibleName = resources.GetString("lkp_country.Properties.AccessibleName");
            this.lkp_country.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_country.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_country.Properties.Appearance.FontSizeDelta")));
            this.lkp_country.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_country.Properties.Appearance.FontStyleDelta")));
            this.lkp_country.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_country.Properties.Appearance.GradientMode")));
            this.lkp_country.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_country.Properties.Appearance.Image")));
            this.lkp_country.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_country.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_country.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_country.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkp_country.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkp_country.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_country.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkp_country.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_country.Properties.AppearanceDropDown.GradientMode")));
            this.lkp_country.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkp_country.Properties.AppearanceDropDown.Image")));
            this.lkp_country.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkp_country.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_country.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkp_country.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkp_country.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_country.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkp_country.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_country.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkp_country.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkp_country.Properties.AppearanceDropDownHeader.Image")));
            this.lkp_country.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkp_country.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_country.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_country.Properties.AutoHeight")));
            this.lkp_country.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_country.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_country.Properties.Buttons"))))});
            this.lkp_country.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_country.Properties.Columns"), resources.GetString("lkp_country.Properties.Columns1"), ((int)(resources.GetObject("lkp_country.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_country.Properties.Columns3"))), resources.GetString("lkp_country.Properties.Columns4"), ((bool)(resources.GetObject("lkp_country.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_country.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_country.Properties.Columns7"), resources.GetString("lkp_country.Properties.Columns8"))});
            this.lkp_country.Properties.DisplayMember = "CGNameAr";
            this.lkp_country.Properties.NullText = resources.GetString("lkp_country.Properties.NullText");
            this.lkp_country.Properties.NullValuePrompt = resources.GetString("lkp_country.Properties.NullValuePrompt");
            this.lkp_country.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_country.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_country.Properties.PopupSizeable = false;
            this.lkp_country.Properties.ValueMember = "CustomerGroupId";
            this.lkp_country.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // btnTestAPI
            // 
            resources.ApplyResources(this.btnTestAPI, "btnTestAPI");
            this.btnTestAPI.Name = "btnTestAPI";
            this.btnTestAPI.Click += new System.EventHandler(this.btnTestAPI_Click);
            // 
            // txtuuid
            // 
            resources.ApplyResources(this.txtuuid, "txtuuid");
            this.txtuuid.EnterMoveNextControl = true;
            this.txtuuid.Name = "txtuuid";
            this.txtuuid.Properties.AccessibleDescription = resources.GetString("txtuuid.Properties.AccessibleDescription");
            this.txtuuid.Properties.AccessibleName = resources.GetString("txtuuid.Properties.AccessibleName");
            this.txtuuid.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtuuid.Properties.Appearance.FontSizeDelta")));
            this.txtuuid.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtuuid.Properties.Appearance.FontStyleDelta")));
            this.txtuuid.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtuuid.Properties.Appearance.GradientMode")));
            this.txtuuid.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtuuid.Properties.Appearance.Image")));
            this.txtuuid.Properties.Appearance.Options.UseTextOptions = true;
            this.txtuuid.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtuuid.Properties.AutoHeight = ((bool)(resources.GetObject("txtuuid.Properties.AutoHeight")));
            this.txtuuid.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtuuid.Properties.Mask.AutoComplete")));
            this.txtuuid.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtuuid.Properties.Mask.BeepOnError")));
            this.txtuuid.Properties.Mask.EditMask = resources.GetString("txtuuid.Properties.Mask.EditMask");
            this.txtuuid.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtuuid.Properties.Mask.IgnoreMaskBlank")));
            this.txtuuid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtuuid.Properties.Mask.MaskType")));
            this.txtuuid.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtuuid.Properties.Mask.PlaceHolder")));
            this.txtuuid.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtuuid.Properties.Mask.SaveLiteral")));
            this.txtuuid.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtuuid.Properties.Mask.ShowPlaceHolders")));
            this.txtuuid.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtuuid.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtuuid.Properties.MaxLength = 50;
            this.txtuuid.Properties.NullValuePrompt = resources.GetString("txtuuid.Properties.NullValuePrompt");
            this.txtuuid.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtuuid.Properties.NullValuePromptShowForEmptyValue")));
            this.txtuuid.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // btnTestlibrary
            // 
            resources.ApplyResources(this.btnTestlibrary, "btnTestlibrary");
            this.btnTestlibrary.Name = "btnTestlibrary";
            this.btnTestlibrary.Click += new System.EventHandler(this.btnTestlibrary_Click);
            // 
            // labelControl30
            // 
            resources.ApplyResources(this.labelControl30, "labelControl30");
            this.labelControl30.Name = "labelControl30";
            // 
            // txtLibraryPath
            // 
            resources.ApplyResources(this.txtLibraryPath, "txtLibraryPath");
            this.txtLibraryPath.EnterMoveNextControl = true;
            this.txtLibraryPath.Name = "txtLibraryPath";
            this.txtLibraryPath.Properties.AccessibleDescription = resources.GetString("txtLibraryPath.Properties.AccessibleDescription");
            this.txtLibraryPath.Properties.AccessibleName = resources.GetString("txtLibraryPath.Properties.AccessibleName");
            this.txtLibraryPath.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtLibraryPath.Properties.Appearance.FontSizeDelta")));
            this.txtLibraryPath.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtLibraryPath.Properties.Appearance.FontStyleDelta")));
            this.txtLibraryPath.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtLibraryPath.Properties.Appearance.GradientMode")));
            this.txtLibraryPath.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtLibraryPath.Properties.Appearance.Image")));
            this.txtLibraryPath.Properties.Appearance.Options.UseTextOptions = true;
            this.txtLibraryPath.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtLibraryPath.Properties.AutoHeight = ((bool)(resources.GetObject("txtLibraryPath.Properties.AutoHeight")));
            this.txtLibraryPath.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtLibraryPath.Properties.Mask.AutoComplete")));
            this.txtLibraryPath.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtLibraryPath.Properties.Mask.BeepOnError")));
            this.txtLibraryPath.Properties.Mask.EditMask = resources.GetString("txtLibraryPath.Properties.Mask.EditMask");
            this.txtLibraryPath.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtLibraryPath.Properties.Mask.IgnoreMaskBlank")));
            this.txtLibraryPath.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtLibraryPath.Properties.Mask.MaskType")));
            this.txtLibraryPath.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtLibraryPath.Properties.Mask.PlaceHolder")));
            this.txtLibraryPath.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtLibraryPath.Properties.Mask.SaveLiteral")));
            this.txtLibraryPath.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtLibraryPath.Properties.Mask.ShowPlaceHolders")));
            this.txtLibraryPath.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtLibraryPath.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtLibraryPath.Properties.NullValuePrompt = resources.GetString("txtLibraryPath.Properties.NullValuePrompt");
            this.txtLibraryPath.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtLibraryPath.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // Btnslots
            // 
            resources.ApplyResources(this.Btnslots, "Btnslots");
            this.Btnslots.Name = "Btnslots";
            this.Btnslots.Click += new System.EventHandler(this.Btnslots_Click);
            // 
            // labelControl31
            // 
            resources.ApplyResources(this.labelControl31, "labelControl31");
            this.labelControl31.Name = "labelControl31";
            // 
            // BtnShowClientScrt
            // 
            resources.ApplyResources(this.BtnShowClientScrt, "BtnShowClientScrt");
            this.BtnShowClientScrt.Name = "BtnShowClientScrt";
            this.BtnShowClientScrt.Click += new System.EventHandler(this.BtnShowClientScrt_Click);
            // 
            // BtnShowClientId
            // 
            resources.ApplyResources(this.BtnShowClientId, "BtnShowClientId");
            this.BtnShowClientId.Name = "BtnShowClientId";
            this.BtnShowClientId.Click += new System.EventHandler(this.BtnShowClientId_Click);
            // 
            // BtnShowDnglPIN
            // 
            resources.ApplyResources(this.BtnShowDnglPIN, "BtnShowDnglPIN");
            this.BtnShowDnglPIN.Name = "BtnShowDnglPIN";
            this.BtnShowDnglPIN.Click += new System.EventHandler(this.BtnShowDnglPIN_Click);
            // 
            // frm_ST_E_InvoiceInfo
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.BtnShowDnglPIN);
            this.Controls.Add(this.BtnShowClientId);
            this.Controls.Add(this.BtnShowClientScrt);
            this.Controls.Add(this.txtLibraryPath);
            this.Controls.Add(this.labelControl31);
            this.Controls.Add(this.labelControl30);
            this.Controls.Add(this.btnTestlibrary);
            this.Controls.Add(this.Btnslots);
            this.Controls.Add(this.btnTestAPI);
            this.Controls.Add(this.lkp_country);
            this.Controls.Add(this.txtServerName);
            this.Controls.Add(this.labelControl29);
            this.Controls.Add(this.labelControl23);
            this.Controls.Add(this.txtBuildingNumber);
            this.Controls.Add(this.labelControl25);
            this.Controls.Add(this.txtGovernate);
            this.Controls.Add(this.labelControl28);
            this.Controls.Add(this.txtECode);
            this.Controls.Add(this.labelControl24);
            this.Controls.Add(this.txtRegionCity);
            this.Controls.Add(this.labelControl26);
            this.Controls.Add(this.txtuuid);
            this.Controls.Add(this.txtStreet);
            this.Controls.Add(this.labelControl27);
            this.Controls.Add(this.labelControl21);
            this.Controls.Add(this.labelControl22);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl20);
            this.Controls.Add(this.spinValidationDays);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.spinRoundValue);
            this.Controls.Add(this.spinDocumentThreshold);
            this.Controls.Add(this.labelControl18);
            this.Controls.Add(this.labelControl19);
            this.Controls.Add(this.chk_allowMoreThanTax);
            this.Controls.Add(this.txtPublickey);
            this.Controls.Add(this.labelControl16);
            this.Controls.Add(this.txtTokenserial);
            this.Controls.Add(this.labelControl17);
            this.Controls.Add(this.txtDonglePIN);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.textEdit3);
            this.Controls.Add(this.labelControl14);
            this.Controls.Add(this.txtClientSecret1);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.txtActivityType);
            this.Controls.Add(this.txtCommercialBook);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.txtTaxCard);
            this.Controls.Add(this.labelControl13);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.txtClientId);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.txtCompanyNameEn);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.txtCompanyNameAr);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.labelControl35);
            this.Controls.Add(this.lkpCompanyType);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_ST_E_InvoiceInfo";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_ST_Print_FormClosing);
            this.Load += new System.EventHandler(this.frm_ST_Print_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCompanyType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtClientId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameEn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameAr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxCard.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCommercialBook.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtClientSecret1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDonglePIN.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPublickey.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTokenserial.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_allowMoreThanTax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinDocumentThreshold.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinRoundValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinValidationDays.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtECode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRegionCity.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStreet.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBuildingNumber.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGovernate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtActivityType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtServerName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_country.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtuuid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLibraryPath.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtn_Save;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraBars.BarButtonItem barButtonClose;
        private DevExpress.XtraEditors.LookUpEdit lkpCompanyType;
        private DevExpress.XtraEditors.LabelControl labelControl35;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.TextEdit txtClientId;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtCompanyNameEn;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.TextEdit txtCompanyNameAr;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtTaxCard;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.TextEdit txtCommercialBook;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.TextEdit txtDonglePIN;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.TextEdit textEdit3;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.TextEdit txtClientSecret1;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.TextEdit txtPublickey;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.TextEdit txtTokenserial;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.CheckEdit chk_allowMoreThanTax;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.SpinEdit spinValidationDays;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit spinRoundValue;
        private DevExpress.XtraEditors.SpinEdit spinDocumentThreshold;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.TextEdit txtECode;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.TextEdit txtRegionCity;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.TextEdit txtStreet;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.TextEdit txtBuildingNumber;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.TextEdit txtGovernate;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.TextEdit txtActivityType;
        private DevExpress.XtraEditors.TextEdit txtServerName;
        private DevExpress.XtraEditors.LabelControl labelControl29;
        private DevExpress.XtraEditors.LookUpEdit lkp_country;
        private DevExpress.XtraEditors.SimpleButton btnTestAPI;
        private DevExpress.XtraEditors.TextEdit txtuuid;
        private DevExpress.XtraEditors.LabelControl labelControl30;
        private DevExpress.XtraEditors.SimpleButton btnTestlibrary;
        private DevExpress.XtraEditors.TextEdit txtLibraryPath;
        private DevExpress.XtraEditors.LabelControl labelControl31;
        private DevExpress.XtraEditors.SimpleButton Btnslots;
        private DevExpress.XtraEditors.SimpleButton BtnShowClientId;
        private DevExpress.XtraEditors.SimpleButton BtnShowClientScrt;
        private DevExpress.XtraEditors.SimpleButton BtnShowDnglPIN;
    }
}