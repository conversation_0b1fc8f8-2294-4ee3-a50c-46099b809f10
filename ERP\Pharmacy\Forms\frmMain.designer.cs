﻿namespace Pharmacy.Forms
{
    partial class frmMain
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmMain));
            this.defaultLookAndFeel1 = new DevExpress.LookAndFeel.DefaultLookAndFeel(this.components);
            this.pnlSmall = new DevExpress.XtraEditors.PanelControl();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.navBarControl1 = new DevExpress.XtraNavBar.NavBarControl();
            this.NBG_Sales = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBI_SL_Invoice = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_SL_Return = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_SL_Customer = new DevExpress.XtraNavBar.NavBarItem();
            this.NBG_Settings = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBI_Styles = new DevExpress.XtraNavBar.NavBarItem();
            this.NBG_Data = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBI_Item = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_Store = new DevExpress.XtraNavBar.NavBarItem();
            this.ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage1 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.menuStrip1 = new System.Windows.Forms.MenuStrip();
            this.mi_settings = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_ST_CompInfo = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_ShowHideChart = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_Styles = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator6 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_ST_SP_1 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_E_InvoiceSettings = new System.Windows.Forms.ToolStripMenuItem();
            this.الاعداداتToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.miExit = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_data = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_Item = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_UOM = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_DT_Sep1 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_Store = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_DT_Sep3 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_Sales = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_SL_Invoice = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_SL_InvoiceList = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_SL_Sep1Ret = new System.Windows.Forms.ToolStripSeparator();
            this.mi_SL_Return = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_SL_ReturnList = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_SL_Sep5IndSl = new System.Windows.Forms.ToolStripSeparator();
            this.mi_SL_Add = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_SL_AddList = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator8 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_SL_Customer = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_envoice = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_EinvoiceSync = new System.Windows.Forms.ToolStripMenuItem();
            this.eInvoicesUnsyncToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_unSyncedRtrnInces = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_UnSyncAddNotes = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator9 = new System.Windows.Forms.ToolStripSeparator();
            this.recievedDocuments = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator7 = new System.Windows.Forms.ToolStripSeparator();
            this.scr_Item_Codes = new System.Windows.Forms.ToolStripMenuItem();
            this.Scr_UnitOfMeaure = new System.Windows.Forms.ToolStripMenuItem();
            this.E_storesCodes = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.E_taxableTypes = new System.Windows.Forms.ToolStripMenuItem();
            this.taxSubTypesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.scr_Currency = new System.Windows.Forms.ToolStripMenuItem();
            this.src_Countries = new System.Windows.Forms.ToolStripMenuItem();
            this.E_activityTypes = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_reports = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_reportCenter = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_Help = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_GlobalGrid = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator32 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_ChangePass = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_globalERPRegistration = new System.Windows.Forms.ToolStripMenuItem();
            this.releaseNotesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_About = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_ImportExcelFiles = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_Windows = new System.Windows.Forms.ToolStripMenuItem();
            this.salesProfitabilityToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.profitabilityOfItemsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.topSellingCustomerCategoriesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.topCategoriesOfItemsSoldToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.salesRateToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.salesDelegatesForItemsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.itemsSalesToEachDelegateToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.customeAccountsListStatisticsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator31 = new System.Windows.Forms.ToolStripSeparator();
            this.mi_PreInvoice = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.pnlLarge = new DevExpress.XtraEditors.PanelControl();
            this.pnlImage = new DevExpress.XtraEditors.PanelControl();
            this.popupStyle = new DevExpress.XtraEditors.PopupContainerControl();
            this.rdoStyles = new DevExpress.XtraEditors.RadioGroup();
            this.btnClosePopup = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar3 = new DevExpress.XtraBars.Bar();
            this.barUser = new DevExpress.XtraBars.BarStaticItem();
            this.barUserName = new DevExpress.XtraBars.BarStaticItem();
            this.CTA_Facebook = new DevExpress.XtraBars.BarButtonItem();
            this.CTA_Twitter = new DevExpress.XtraBars.BarButtonItem();
            this.CTA_LinkedIn = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticItem1 = new DevExpress.XtraBars.BarStaticItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.CTA_Linkitsys = new DevExpress.XtraBars.BarLinkContainerItem();
            ((System.ComponentModel.ISupportInitialize)(this.pnlSmall)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).BeginInit();
            this.menuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pnlLarge)).BeginInit();
            this.pnlLarge.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pnlImage)).BeginInit();
            this.pnlImage.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.popupStyle)).BeginInit();
            this.popupStyle.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.rdoStyles.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            this.SuspendLayout();
            // 
            // defaultLookAndFeel1
            // 
            this.defaultLookAndFeel1.LookAndFeel.SkinName = "Coffee";
            // 
            // pnlSmall
            // 
            resources.ApplyResources(this.pnlSmall, "pnlSmall");
            this.pnlSmall.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("pnlSmall.Appearance.BackColor")));
            this.pnlSmall.Appearance.FontSizeDelta = ((int)(resources.GetObject("pnlSmall.Appearance.FontSizeDelta")));
            this.pnlSmall.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("pnlSmall.Appearance.FontStyleDelta")));
            this.pnlSmall.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("pnlSmall.Appearance.GradientMode")));
            this.pnlSmall.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("pnlSmall.Appearance.Image")));
            this.pnlSmall.Appearance.Options.UseBackColor = true;
            this.pnlSmall.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.pnlSmall.Name = "pnlSmall";
            this.pnlSmall.Paint += new System.Windows.Forms.PaintEventHandler(this.pnlSmall_Paint);
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.AccessibleDescription = resources.GetString("picLogo.Properties.AccessibleDescription");
            this.picLogo.Properties.AccessibleName = resources.GetString("picLogo.Properties.AccessibleName");
            this.picLogo.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("picLogo.Properties.Appearance.BackColor")));
            this.picLogo.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("picLogo.Properties.Appearance.FontSizeDelta")));
            this.picLogo.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("picLogo.Properties.Appearance.FontStyleDelta")));
            this.picLogo.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("picLogo.Properties.Appearance.GradientMode")));
            this.picLogo.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("picLogo.Properties.Appearance.Image")));
            this.picLogo.Properties.Appearance.Options.UseBackColor = true;
            this.picLogo.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.picLogo.Properties.ReadOnly = true;
            this.picLogo.Properties.ShowMenu = false;
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // navBarControl1
            // 
            resources.ApplyResources(this.navBarControl1, "navBarControl1");
            this.navBarControl1.ActiveGroup = this.NBG_Sales;
            this.navBarControl1.ContentButtonHint = null;
            this.navBarControl1.Groups.AddRange(new DevExpress.XtraNavBar.NavBarGroup[] {
            this.NBG_Settings,
            this.NBG_Data,
            this.NBG_Sales});
            this.navBarControl1.Items.AddRange(new DevExpress.XtraNavBar.NavBarItem[] {
            this.NBI_Styles,
            this.NBI_Item,
            this.NBI_SL_Customer,
            this.NBI_Store,
            this.NBI_SL_Invoice,
            this.NBI_SL_Return});
            this.navBarControl1.LinkSelectionMode = DevExpress.XtraNavBar.LinkSelectionModeType.OneInControl;
            this.navBarControl1.Name = "navBarControl1";
            this.navBarControl1.OptionsNavPane.CollapsedWidth = ((int)(resources.GetObject("resource.CollapsedWidth")));
            this.navBarControl1.OptionsNavPane.ExpandButtonMode = DevExpress.Utils.Controls.ExpandButtonMode.Inverted;
            this.navBarControl1.OptionsNavPane.ExpandedWidth = ((int)(resources.GetObject("resource.ExpandedWidth")));
            this.navBarControl1.OptionsNavPane.ShowOverflowButton = false;
            this.navBarControl1.OptionsNavPane.ShowOverflowPanel = false;
            this.navBarControl1.PaintStyleKind = DevExpress.XtraNavBar.NavBarViewKind.NavigationPane;
            this.navBarControl1.StoreDefaultPaintStyleName = true;
            // 
            // NBG_Sales
            // 
            resources.ApplyResources(this.NBG_Sales, "NBG_Sales");
            this.NBG_Sales.Expanded = true;
            this.NBG_Sales.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Sales.ItemLinks.AddRange(new DevExpress.XtraNavBar.NavBarItemLink[] {
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_SL_Invoice),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_SL_Return),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_SL_Customer)});
            this.NBG_Sales.Name = "NBG_Sales";
            // 
            // NBI_SL_Invoice
            // 
            resources.ApplyResources(this.NBI_SL_Invoice, "NBI_SL_Invoice");
            this.NBI_SL_Invoice.LargeImage = global::Pharmacy.Properties.Resources.N_sell;
            this.NBI_SL_Invoice.Name = "NBI_SL_Invoice";
            this.NBI_SL_Invoice.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_SL_Invoice_LinkClicked);
            // 
            // NBI_SL_Return
            // 
            resources.ApplyResources(this.NBI_SL_Return, "NBI_SL_Return");
            this.NBI_SL_Return.LargeImage = global::Pharmacy.Properties.Resources.N_return2;
            this.NBI_SL_Return.Name = "NBI_SL_Return";
            this.NBI_SL_Return.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_SL_Return_LinkClicked);
            // 
            // NBI_SL_Customer
            // 
            resources.ApplyResources(this.NBI_SL_Customer, "NBI_SL_Customer");
            this.NBI_SL_Customer.LargeImage = global::Pharmacy.Properties.Resources.N_customers;
            this.NBI_SL_Customer.Name = "NBI_SL_Customer";
            this.NBI_SL_Customer.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_SL_Customer_LinkClicked);
            // 
            // NBG_Settings
            // 
            resources.ApplyResources(this.NBG_Settings, "NBG_Settings");
            this.NBG_Settings.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Settings.ItemLinks.AddRange(new DevExpress.XtraNavBar.NavBarItemLink[] {
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_Styles)});
            this.NBG_Settings.Name = "NBG_Settings";
            // 
            // NBI_Styles
            // 
            resources.ApplyResources(this.NBI_Styles, "NBI_Styles");
            this.NBI_Styles.LargeImage = global::Pharmacy.Properties.Resources.N_Style;
            this.NBI_Styles.Name = "NBI_Styles";
            this.NBI_Styles.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_Styles_LinkClicked);
            // 
            // NBG_Data
            // 
            resources.ApplyResources(this.NBG_Data, "NBG_Data");
            this.NBG_Data.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Data.ItemLinks.AddRange(new DevExpress.XtraNavBar.NavBarItemLink[] {
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_Item),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_Store)});
            this.NBG_Data.Name = "NBG_Data";
            this.NBG_Data.SelectedLinkIndex = 2;
            // 
            // NBI_Item
            // 
            resources.ApplyResources(this.NBI_Item, "NBI_Item");
            this.NBI_Item.LargeImage = global::Pharmacy.Properties.Resources.N_items;
            this.NBI_Item.Name = "NBI_Item";
            this.NBI_Item.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_IC_Item_LinkClicked);
            // 
            // NBI_Store
            // 
            resources.ApplyResources(this.NBI_Store, "NBI_Store");
            this.NBI_Store.LargeImage = global::Pharmacy.Properties.Resources.N_Store;
            this.NBI_Store.Name = "NBI_Store";
            this.NBI_Store.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_IC_Store_LinkClicked);
            // 
            // ribbonPageGroup1
            // 
            this.ribbonPageGroup1.Name = "ribbonPageGroup1";
            resources.ApplyResources(this.ribbonPageGroup1, "ribbonPageGroup1");
            // 
            // ribbonPage1
            // 
            this.ribbonPage1.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup1});
            this.ribbonPage1.Name = "ribbonPage1";
            // 
            // menuStrip1
            // 
            resources.ApplyResources(this.menuStrip1, "menuStrip1");
            this.menuStrip1.BackColor = System.Drawing.Color.White;
            this.menuStrip1.ImageScalingSize = new System.Drawing.Size(28, 28);
            this.menuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_settings,
            this.mi_data,
            this.mi_Sales,
            this.mi_envoice,
            this.mi_reports,
            this.mi_Help,
            this.mi_Windows});
            this.menuStrip1.LayoutStyle = System.Windows.Forms.ToolStripLayoutStyle.HorizontalStackWithOverflow;
            this.menuStrip1.Name = "menuStrip1";
            this.menuStrip1.RenderMode = System.Windows.Forms.ToolStripRenderMode.System;
            // 
            // mi_settings
            // 
            resources.ApplyResources(this.mi_settings, "mi_settings");
            this.mi_settings.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_ST_CompInfo,
            this.mi_ShowHideChart,
            this.mi_Styles,
            this.toolStripSeparator6,
            this.mi_ST_SP_1,
            this.mi_E_InvoiceSettings,
            this.الاعداداتToolStripMenuItem,
            this.miExit});
            this.mi_settings.Name = "mi_settings";
            // 
            // mi_ST_CompInfo
            // 
            resources.ApplyResources(this.mi_ST_CompInfo, "mi_ST_CompInfo");
            this.mi_ST_CompInfo.BackColor = System.Drawing.Color.White;
            this.mi_ST_CompInfo.Name = "mi_ST_CompInfo";
            this.mi_ST_CompInfo.Click += new System.EventHandler(this.mi_ST_PharmacyInfo_Click);
            // 
            // mi_ShowHideChart
            // 
            resources.ApplyResources(this.mi_ShowHideChart, "mi_ShowHideChart");
            this.mi_ShowHideChart.Name = "mi_ShowHideChart";
            this.mi_ShowHideChart.Click += new System.EventHandler(this.mi_ShowHideChart_Click);
            // 
            // mi_Styles
            // 
            resources.ApplyResources(this.mi_Styles, "mi_Styles");
            this.mi_Styles.BackColor = System.Drawing.Color.White;
            this.mi_Styles.Name = "mi_Styles";
            this.mi_Styles.Click += new System.EventHandler(this.mi_Styles_Click);
            // 
            // toolStripSeparator6
            // 
            resources.ApplyResources(this.toolStripSeparator6, "toolStripSeparator6");
            this.toolStripSeparator6.BackColor = System.Drawing.Color.White;
            this.toolStripSeparator6.Name = "toolStripSeparator6";
            // 
            // mi_ST_SP_1
            // 
            resources.ApplyResources(this.mi_ST_SP_1, "mi_ST_SP_1");
            this.mi_ST_SP_1.BackColor = System.Drawing.Color.White;
            this.mi_ST_SP_1.Name = "mi_ST_SP_1";
            // 
            // mi_E_InvoiceSettings
            // 
            resources.ApplyResources(this.mi_E_InvoiceSettings, "mi_E_InvoiceSettings");
            this.mi_E_InvoiceSettings.Name = "mi_E_InvoiceSettings";
            this.mi_E_InvoiceSettings.Click += new System.EventHandler(this.mi_E_InvoiceSettings_Click);
            // 
            // الاعداداتToolStripMenuItem
            // 
            resources.ApplyResources(this.الاعداداتToolStripMenuItem, "الاعداداتToolStripMenuItem");
            this.الاعداداتToolStripMenuItem.Name = "الاعداداتToolStripMenuItem";
            this.الاعداداتToolStripMenuItem.Click += new System.EventHandler(this.mi_ST_Store_Click);
            // 
            // miExit
            // 
            resources.ApplyResources(this.miExit, "miExit");
            this.miExit.BackColor = System.Drawing.Color.White;
            this.miExit.Name = "miExit";
            this.miExit.Click += new System.EventHandler(this.miExit_Click);
            // 
            // mi_data
            // 
            resources.ApplyResources(this.mi_data, "mi_data");
            this.mi_data.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_Item,
            this.mi_UOM,
            this.mi_DT_Sep1,
            this.mi_Store,
            this.toolStripSeparator1,
            this.mi_DT_Sep3});
            this.mi_data.Name = "mi_data";
            // 
            // mi_Item
            // 
            resources.ApplyResources(this.mi_Item, "mi_Item");
            this.mi_Item.BackColor = System.Drawing.Color.White;
            this.mi_Item.Name = "mi_Item";
            this.mi_Item.Click += new System.EventHandler(this.mi_IC_Item_Click);
            // 
            // mi_UOM
            // 
            resources.ApplyResources(this.mi_UOM, "mi_UOM");
            this.mi_UOM.Name = "mi_UOM";
            this.mi_UOM.Click += new System.EventHandler(this.mi_UOM_Click);
            // 
            // mi_DT_Sep1
            // 
            resources.ApplyResources(this.mi_DT_Sep1, "mi_DT_Sep1");
            this.mi_DT_Sep1.BackColor = System.Drawing.Color.White;
            this.mi_DT_Sep1.Name = "mi_DT_Sep1";
            this.mi_DT_Sep1.Overflow = System.Windows.Forms.ToolStripItemOverflow.Never;
            // 
            // mi_Store
            // 
            resources.ApplyResources(this.mi_Store, "mi_Store");
            this.mi_Store.BackColor = System.Drawing.Color.White;
            this.mi_Store.Name = "mi_Store";
            this.mi_Store.Click += new System.EventHandler(this.mi_IC_Store_Click);
            // 
            // toolStripSeparator1
            // 
            resources.ApplyResources(this.toolStripSeparator1, "toolStripSeparator1");
            this.toolStripSeparator1.BackColor = System.Drawing.Color.White;
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Overflow = System.Windows.Forms.ToolStripItemOverflow.Never;
            // 
            // mi_DT_Sep3
            // 
            resources.ApplyResources(this.mi_DT_Sep3, "mi_DT_Sep3");
            this.mi_DT_Sep3.BackColor = System.Drawing.Color.White;
            this.mi_DT_Sep3.Name = "mi_DT_Sep3";
            this.mi_DT_Sep3.Overflow = System.Windows.Forms.ToolStripItemOverflow.Never;
            // 
            // mi_Sales
            // 
            resources.ApplyResources(this.mi_Sales, "mi_Sales");
            this.mi_Sales.BackColor = System.Drawing.Color.White;
            this.mi_Sales.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_SL_Invoice,
            this.mi_SL_InvoiceList,
            this.mi_SL_Sep1Ret,
            this.mi_SL_Return,
            this.mi_SL_ReturnList,
            this.mi_SL_Sep5IndSl,
            this.mi_SL_Add,
            this.mi_SL_AddList,
            this.toolStripSeparator8,
            this.mi_SL_Customer});
            this.mi_Sales.Name = "mi_Sales";
            // 
            // mi_SL_Invoice
            // 
            resources.ApplyResources(this.mi_SL_Invoice, "mi_SL_Invoice");
            this.mi_SL_Invoice.BackColor = System.Drawing.Color.White;
            this.mi_SL_Invoice.Name = "mi_SL_Invoice";
            this.mi_SL_Invoice.Click += new System.EventHandler(this.mi_SL_Invoice_Click);
            // 
            // mi_SL_InvoiceList
            // 
            resources.ApplyResources(this.mi_SL_InvoiceList, "mi_SL_InvoiceList");
            this.mi_SL_InvoiceList.BackColor = System.Drawing.Color.White;
            this.mi_SL_InvoiceList.Name = "mi_SL_InvoiceList";
            this.mi_SL_InvoiceList.Click += new System.EventHandler(this.mi_SL_InvoiceList_Click);
            // 
            // mi_SL_Sep1Ret
            // 
            resources.ApplyResources(this.mi_SL_Sep1Ret, "mi_SL_Sep1Ret");
            this.mi_SL_Sep1Ret.BackColor = System.Drawing.Color.White;
            this.mi_SL_Sep1Ret.Name = "mi_SL_Sep1Ret";
            // 
            // mi_SL_Return
            // 
            resources.ApplyResources(this.mi_SL_Return, "mi_SL_Return");
            this.mi_SL_Return.BackColor = System.Drawing.Color.White;
            this.mi_SL_Return.Name = "mi_SL_Return";
            this.mi_SL_Return.Click += new System.EventHandler(this.mi_SL_Return_Click);
            // 
            // mi_SL_ReturnList
            // 
            resources.ApplyResources(this.mi_SL_ReturnList, "mi_SL_ReturnList");
            this.mi_SL_ReturnList.BackColor = System.Drawing.Color.White;
            this.mi_SL_ReturnList.Name = "mi_SL_ReturnList";
            this.mi_SL_ReturnList.Click += new System.EventHandler(this.mi_SL_ReturnList_Click);
            // 
            // mi_SL_Sep5IndSl
            // 
            resources.ApplyResources(this.mi_SL_Sep5IndSl, "mi_SL_Sep5IndSl");
            this.mi_SL_Sep5IndSl.BackColor = System.Drawing.Color.White;
            this.mi_SL_Sep5IndSl.Name = "mi_SL_Sep5IndSl";
            // 
            // mi_SL_Add
            // 
            resources.ApplyResources(this.mi_SL_Add, "mi_SL_Add");
            this.mi_SL_Add.Name = "mi_SL_Add";
            this.mi_SL_Add.Click += new System.EventHandler(this.mi_SL_Add_Click);
            // 
            // mi_SL_AddList
            // 
            resources.ApplyResources(this.mi_SL_AddList, "mi_SL_AddList");
            this.mi_SL_AddList.Name = "mi_SL_AddList";
            this.mi_SL_AddList.Click += new System.EventHandler(this.mi_SL_AddList_Click);
            // 
            // toolStripSeparator8
            // 
            resources.ApplyResources(this.toolStripSeparator8, "toolStripSeparator8");
            this.toolStripSeparator8.Name = "toolStripSeparator8";
            // 
            // mi_SL_Customer
            // 
            resources.ApplyResources(this.mi_SL_Customer, "mi_SL_Customer");
            this.mi_SL_Customer.BackColor = System.Drawing.Color.White;
            this.mi_SL_Customer.Name = "mi_SL_Customer";
            this.mi_SL_Customer.Click += new System.EventHandler(this.mi_SL_Customer_Click_1);
            // 
            // mi_envoice
            // 
            resources.ApplyResources(this.mi_envoice, "mi_envoice");
            this.mi_envoice.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_EinvoiceSync,
            this.eInvoicesUnsyncToolStripMenuItem,
            this.toolStripSeparator5,
            this.mi_unSyncedRtrnInces,
            this.toolStripSeparator2,
            this.mi_UnSyncAddNotes,
            this.toolStripSeparator9,
            this.recievedDocuments,
            this.toolStripSeparator7,
            this.scr_Item_Codes,
            this.Scr_UnitOfMeaure,
            this.E_storesCodes,
            this.toolStripSeparator3,
            this.E_taxableTypes,
            this.taxSubTypesToolStripMenuItem,
            this.toolStripSeparator4,
            this.scr_Currency,
            this.src_Countries,
            this.E_activityTypes});
            this.mi_envoice.Name = "mi_envoice";
            // 
            // mi_EinvoiceSync
            // 
            resources.ApplyResources(this.mi_EinvoiceSync, "mi_EinvoiceSync");
            this.mi_EinvoiceSync.Name = "mi_EinvoiceSync";
            this.mi_EinvoiceSync.Click += new System.EventHandler(this.mi_EinvoiceSync_Click);
            // 
            // eInvoicesUnsyncToolStripMenuItem
            // 
            resources.ApplyResources(this.eInvoicesUnsyncToolStripMenuItem, "eInvoicesUnsyncToolStripMenuItem");
            this.eInvoicesUnsyncToolStripMenuItem.Name = "eInvoicesUnsyncToolStripMenuItem";
            this.eInvoicesUnsyncToolStripMenuItem.Click += new System.EventHandler(this.eInvoicesUnsyncToolStripMenuItem_Click);
            // 
            // toolStripSeparator5
            // 
            resources.ApplyResources(this.toolStripSeparator5, "toolStripSeparator5");
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            // 
            // mi_unSyncedRtrnInces
            // 
            resources.ApplyResources(this.mi_unSyncedRtrnInces, "mi_unSyncedRtrnInces");
            this.mi_unSyncedRtrnInces.Name = "mi_unSyncedRtrnInces";
            this.mi_unSyncedRtrnInces.Click += new System.EventHandler(this.mi_unSyncedRtrnInces_Click);
            // 
            // toolStripSeparator2
            // 
            resources.ApplyResources(this.toolStripSeparator2, "toolStripSeparator2");
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            // 
            // mi_UnSyncAddNotes
            // 
            resources.ApplyResources(this.mi_UnSyncAddNotes, "mi_UnSyncAddNotes");
            this.mi_UnSyncAddNotes.Name = "mi_UnSyncAddNotes";
            this.mi_UnSyncAddNotes.Click += new System.EventHandler(this.mi_UnSyncAddNotes_Click);
            // 
            // toolStripSeparator9
            // 
            resources.ApplyResources(this.toolStripSeparator9, "toolStripSeparator9");
            this.toolStripSeparator9.Name = "toolStripSeparator9";
            // 
            // recievedDocuments
            // 
            resources.ApplyResources(this.recievedDocuments, "recievedDocuments");
            this.recievedDocuments.Name = "recievedDocuments";
            this.recievedDocuments.Click += new System.EventHandler(this.recievedDocuments_Click);
            // 
            // toolStripSeparator7
            // 
            resources.ApplyResources(this.toolStripSeparator7, "toolStripSeparator7");
            this.toolStripSeparator7.Name = "toolStripSeparator7";
            // 
            // scr_Item_Codes
            // 
            resources.ApplyResources(this.scr_Item_Codes, "scr_Item_Codes");
            this.scr_Item_Codes.Name = "scr_Item_Codes";
            this.scr_Item_Codes.Click += new System.EventHandler(this.scr_Item_Codes_Click);
            // 
            // Scr_UnitOfMeaure
            // 
            resources.ApplyResources(this.Scr_UnitOfMeaure, "Scr_UnitOfMeaure");
            this.Scr_UnitOfMeaure.Name = "Scr_UnitOfMeaure";
            this.Scr_UnitOfMeaure.Click += new System.EventHandler(this.Scr_UnitOfMeaure_Click);
            // 
            // E_storesCodes
            // 
            resources.ApplyResources(this.E_storesCodes, "E_storesCodes");
            this.E_storesCodes.Name = "E_storesCodes";
            this.E_storesCodes.Click += new System.EventHandler(this.E_storesCodes_Click);
            // 
            // toolStripSeparator3
            // 
            resources.ApplyResources(this.toolStripSeparator3, "toolStripSeparator3");
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            // 
            // E_taxableTypes
            // 
            resources.ApplyResources(this.E_taxableTypes, "E_taxableTypes");
            this.E_taxableTypes.Name = "E_taxableTypes";
            this.E_taxableTypes.Click += new System.EventHandler(this.E_taxableTypes_Click);
            // 
            // taxSubTypesToolStripMenuItem
            // 
            resources.ApplyResources(this.taxSubTypesToolStripMenuItem, "taxSubTypesToolStripMenuItem");
            this.taxSubTypesToolStripMenuItem.Name = "taxSubTypesToolStripMenuItem";
            this.taxSubTypesToolStripMenuItem.Click += new System.EventHandler(this.E_TaxableSubtypes_Click);
            // 
            // toolStripSeparator4
            // 
            resources.ApplyResources(this.toolStripSeparator4, "toolStripSeparator4");
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            // 
            // scr_Currency
            // 
            resources.ApplyResources(this.scr_Currency, "scr_Currency");
            this.scr_Currency.Name = "scr_Currency";
            this.scr_Currency.Click += new System.EventHandler(this.scr_Currency_Click);
            // 
            // src_Countries
            // 
            resources.ApplyResources(this.src_Countries, "src_Countries");
            this.src_Countries.Name = "src_Countries";
            this.src_Countries.Click += new System.EventHandler(this.src_Countries_Click);
            // 
            // E_activityTypes
            // 
            resources.ApplyResources(this.E_activityTypes, "E_activityTypes");
            this.E_activityTypes.Name = "E_activityTypes";
            this.E_activityTypes.Click += new System.EventHandler(this.E_activityTypes_Click);
            // 
            // mi_reports
            // 
            resources.ApplyResources(this.mi_reports, "mi_reports");
            this.mi_reports.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_reportCenter});
            this.mi_reports.Name = "mi_reports";
            // 
            // mi_reportCenter
            // 
            resources.ApplyResources(this.mi_reportCenter, "mi_reportCenter");
            this.mi_reportCenter.BackColor = System.Drawing.Color.White;
            this.mi_reportCenter.Name = "mi_reportCenter";
            this.mi_reportCenter.Click += new System.EventHandler(this.mi_reportCenter_Click);
            // 
            // mi_Help
            // 
            resources.ApplyResources(this.mi_Help, "mi_Help");
            this.mi_Help.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_GlobalGrid,
            this.toolStripSeparator32,
            this.mi_ChangePass,
            this.mi_globalERPRegistration,
            this.releaseNotesToolStripMenuItem,
            this.mi_About,
            this.mi_ImportExcelFiles});
            this.mi_Help.Name = "mi_Help";
            // 
            // mi_GlobalGrid
            // 
            resources.ApplyResources(this.mi_GlobalGrid, "mi_GlobalGrid");
            this.mi_GlobalGrid.BackColor = System.Drawing.Color.White;
            this.mi_GlobalGrid.Name = "mi_GlobalGrid";
            this.mi_GlobalGrid.Click += new System.EventHandler(this.mi_GlobalGrid_Click);
            // 
            // toolStripSeparator32
            // 
            resources.ApplyResources(this.toolStripSeparator32, "toolStripSeparator32");
            this.toolStripSeparator32.BackColor = System.Drawing.Color.White;
            this.toolStripSeparator32.Name = "toolStripSeparator32";
            // 
            // mi_ChangePass
            // 
            resources.ApplyResources(this.mi_ChangePass, "mi_ChangePass");
            this.mi_ChangePass.BackColor = System.Drawing.Color.White;
            this.mi_ChangePass.Name = "mi_ChangePass";
            this.mi_ChangePass.Click += new System.EventHandler(this.mi_ChangePass_Click);
            // 
            // mi_globalERPRegistration
            // 
            resources.ApplyResources(this.mi_globalERPRegistration, "mi_globalERPRegistration");
            this.mi_globalERPRegistration.BackColor = System.Drawing.Color.White;
            this.mi_globalERPRegistration.Name = "mi_globalERPRegistration";
            this.mi_globalERPRegistration.Click += new System.EventHandler(this.mi_globalERPRegistration_Click);
            // 
            // releaseNotesToolStripMenuItem
            // 
            resources.ApplyResources(this.releaseNotesToolStripMenuItem, "releaseNotesToolStripMenuItem");
            this.releaseNotesToolStripMenuItem.Name = "releaseNotesToolStripMenuItem";
            this.releaseNotesToolStripMenuItem.Click += new System.EventHandler(this.releaseNotesToolStripMenuItem_Click);
            // 
            // mi_About
            // 
            resources.ApplyResources(this.mi_About, "mi_About");
            this.mi_About.BackColor = System.Drawing.Color.White;
            this.mi_About.Name = "mi_About";
            this.mi_About.Click += new System.EventHandler(this.mi_About_Click);
            // 
            // mi_ImportExcelFiles
            // 
            resources.ApplyResources(this.mi_ImportExcelFiles, "mi_ImportExcelFiles");
            this.mi_ImportExcelFiles.Name = "mi_ImportExcelFiles";
            this.mi_ImportExcelFiles.Click += new System.EventHandler(this.mi_ImportExcelFiles_Click);
            // 
            // mi_Windows
            // 
            resources.ApplyResources(this.mi_Windows, "mi_Windows");
            this.mi_Windows.Name = "mi_Windows";
            this.mi_Windows.DropDownOpening += new System.EventHandler(this.mi_Windows_DropDownOpening);
            // 
            // salesProfitabilityToolStripMenuItem
            // 
            resources.ApplyResources(this.salesProfitabilityToolStripMenuItem, "salesProfitabilityToolStripMenuItem");
            this.salesProfitabilityToolStripMenuItem.Name = "salesProfitabilityToolStripMenuItem";
            // 
            // profitabilityOfItemsToolStripMenuItem
            // 
            resources.ApplyResources(this.profitabilityOfItemsToolStripMenuItem, "profitabilityOfItemsToolStripMenuItem");
            this.profitabilityOfItemsToolStripMenuItem.Name = "profitabilityOfItemsToolStripMenuItem";
            // 
            // topSellingCustomerCategoriesToolStripMenuItem
            // 
            resources.ApplyResources(this.topSellingCustomerCategoriesToolStripMenuItem, "topSellingCustomerCategoriesToolStripMenuItem");
            this.topSellingCustomerCategoriesToolStripMenuItem.Name = "topSellingCustomerCategoriesToolStripMenuItem";
            // 
            // topCategoriesOfItemsSoldToolStripMenuItem
            // 
            resources.ApplyResources(this.topCategoriesOfItemsSoldToolStripMenuItem, "topCategoriesOfItemsSoldToolStripMenuItem");
            this.topCategoriesOfItemsSoldToolStripMenuItem.Name = "topCategoriesOfItemsSoldToolStripMenuItem";
            // 
            // salesRateToolStripMenuItem
            // 
            resources.ApplyResources(this.salesRateToolStripMenuItem, "salesRateToolStripMenuItem");
            this.salesRateToolStripMenuItem.Name = "salesRateToolStripMenuItem";
            // 
            // salesDelegatesForItemsToolStripMenuItem
            // 
            resources.ApplyResources(this.salesDelegatesForItemsToolStripMenuItem, "salesDelegatesForItemsToolStripMenuItem");
            this.salesDelegatesForItemsToolStripMenuItem.Name = "salesDelegatesForItemsToolStripMenuItem";
            // 
            // itemsSalesToEachDelegateToolStripMenuItem
            // 
            resources.ApplyResources(this.itemsSalesToEachDelegateToolStripMenuItem, "itemsSalesToEachDelegateToolStripMenuItem");
            this.itemsSalesToEachDelegateToolStripMenuItem.Name = "itemsSalesToEachDelegateToolStripMenuItem";
            // 
            // customeAccountsListStatisticsToolStripMenuItem
            // 
            resources.ApplyResources(this.customeAccountsListStatisticsToolStripMenuItem, "customeAccountsListStatisticsToolStripMenuItem");
            this.customeAccountsListStatisticsToolStripMenuItem.Name = "customeAccountsListStatisticsToolStripMenuItem";
            // 
            // toolStripSeparator31
            // 
            resources.ApplyResources(this.toolStripSeparator31, "toolStripSeparator31");
            this.toolStripSeparator31.Name = "toolStripSeparator31";
            // 
            // mi_PreInvoice
            // 
            resources.ApplyResources(this.mi_PreInvoice, "mi_PreInvoice");
            this.mi_PreInvoice.Name = "mi_PreInvoice";
            // 
            // toolStripMenuItem4
            // 
            resources.ApplyResources(this.toolStripMenuItem4, "toolStripMenuItem4");
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            // 
            // pnlLarge
            // 
            resources.ApplyResources(this.pnlLarge, "pnlLarge");
            this.pnlLarge.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("pnlLarge.Appearance.BackColor")));
            this.pnlLarge.Appearance.FontSizeDelta = ((int)(resources.GetObject("pnlLarge.Appearance.FontSizeDelta")));
            this.pnlLarge.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("pnlLarge.Appearance.FontStyleDelta")));
            this.pnlLarge.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("pnlLarge.Appearance.GradientMode")));
            this.pnlLarge.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("pnlLarge.Appearance.Image")));
            this.pnlLarge.Appearance.Options.UseBackColor = true;
            this.pnlLarge.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.pnlLarge.Controls.Add(this.pnlImage);
            this.pnlLarge.Name = "pnlLarge";
            // 
            // pnlImage
            // 
            resources.ApplyResources(this.pnlImage, "pnlImage");
            this.pnlImage.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("pnlImage.Appearance.BackColor")));
            this.pnlImage.Appearance.FontSizeDelta = ((int)(resources.GetObject("pnlImage.Appearance.FontSizeDelta")));
            this.pnlImage.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("pnlImage.Appearance.FontStyleDelta")));
            this.pnlImage.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("pnlImage.Appearance.GradientMode")));
            this.pnlImage.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("pnlImage.Appearance.Image")));
            this.pnlImage.Appearance.Options.UseBackColor = true;
            this.pnlImage.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.pnlImage.Controls.Add(this.picLogo);
            this.pnlImage.Controls.Add(this.pnlSmall);
            this.pnlImage.Name = "pnlImage";
            // 
            // popupStyle
            // 
            resources.ApplyResources(this.popupStyle, "popupStyle");
            this.popupStyle.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.popupStyle.Controls.Add(this.rdoStyles);
            this.popupStyle.Controls.Add(this.btnClosePopup);
            this.popupStyle.Controls.Add(this.labelControl1);
            this.popupStyle.Name = "popupStyle";
            // 
            // rdoStyles
            // 
            resources.ApplyResources(this.rdoStyles, "rdoStyles");
            this.rdoStyles.Name = "rdoStyles";
            this.rdoStyles.Properties.AccessibleDescription = resources.GetString("rdoStyles.Properties.AccessibleDescription");
            this.rdoStyles.Properties.AccessibleName = resources.GetString("rdoStyles.Properties.AccessibleName");
            this.rdoStyles.Properties.Columns = 2;
            this.rdoStyles.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Default;
            this.rdoStyles.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items"), resources.GetString("rdoStyles.Properties.Items1")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items2"), resources.GetString("rdoStyles.Properties.Items3")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items4"), resources.GetString("rdoStyles.Properties.Items5")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items6"), resources.GetString("rdoStyles.Properties.Items7")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items8"), resources.GetString("rdoStyles.Properties.Items9")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items10"), resources.GetString("rdoStyles.Properties.Items11")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items12"), resources.GetString("rdoStyles.Properties.Items13")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items14"), resources.GetString("rdoStyles.Properties.Items15")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items16"), resources.GetString("rdoStyles.Properties.Items17")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items18"), resources.GetString("rdoStyles.Properties.Items19")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items20"), resources.GetString("rdoStyles.Properties.Items21")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items22"), resources.GetString("rdoStyles.Properties.Items23")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items24"), resources.GetString("rdoStyles.Properties.Items25")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items26"), resources.GetString("rdoStyles.Properties.Items27")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items28"), resources.GetString("rdoStyles.Properties.Items29")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items30"), resources.GetString("rdoStyles.Properties.Items31")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items32"), resources.GetString("rdoStyles.Properties.Items33")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items34"), resources.GetString("rdoStyles.Properties.Items35")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items36"), resources.GetString("rdoStyles.Properties.Items37")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items38"), resources.GetString("rdoStyles.Properties.Items39")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items40"), resources.GetString("rdoStyles.Properties.Items41")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items42"), resources.GetString("rdoStyles.Properties.Items43")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items44"), resources.GetString("rdoStyles.Properties.Items45")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items46"), resources.GetString("rdoStyles.Properties.Items47")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items48"), resources.GetString("rdoStyles.Properties.Items49")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items50"), resources.GetString("rdoStyles.Properties.Items51")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items52"), resources.GetString("rdoStyles.Properties.Items53")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items54"), resources.GetString("rdoStyles.Properties.Items55")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items56"), resources.GetString("rdoStyles.Properties.Items57")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items58"), resources.GetString("rdoStyles.Properties.Items59")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items60"), resources.GetString("rdoStyles.Properties.Items61")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(resources.GetString("rdoStyles.Properties.Items62"), resources.GetString("rdoStyles.Properties.Items63"))});
            this.rdoStyles.SelectedIndexChanged += new System.EventHandler(this.rdoStyles_SelectedIndexChanged);
            // 
            // btnClosePopup
            // 
            resources.ApplyResources(this.btnClosePopup, "btnClosePopup");
            this.btnClosePopup.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnClosePopup.Image = global::Pharmacy.Properties.Resources.clse;
            this.btnClosePopup.Name = "btnClosePopup";
            this.btnClosePopup.Click += new System.EventHandler(this.btnClosePopup_Click);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // panelControl1
            // 
            resources.ApplyResources(this.panelControl1, "panelControl1");
            this.panelControl1.Controls.Add(this.popupStyle);
            this.panelControl1.Controls.Add(this.navBarControl1);
            this.panelControl1.Controls.Add(this.pnlLarge);
            this.panelControl1.Name = "panelControl1";
            // 
            // barManager1
            // 
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar3});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barUser,
            this.barUserName,
            this.CTA_Facebook,
            this.CTA_Twitter,
            this.CTA_LinkedIn,
            this.CTA_Linkitsys,
            this.barStaticItem1});
            this.barManager1.MaxItemId = 7;
            this.barManager1.StatusBar = this.bar3;
            // 
            // bar3
            // 
            this.bar3.BarName = "Status bar";
            this.bar3.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            this.bar3.DockCol = 0;
            this.bar3.DockRow = 0;
            this.bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            this.bar3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barUser),
            new DevExpress.XtraBars.LinkPersistInfo(this.barUserName),
            new DevExpress.XtraBars.LinkPersistInfo(this.CTA_Facebook),
            new DevExpress.XtraBars.LinkPersistInfo(this.CTA_Twitter),
            new DevExpress.XtraBars.LinkPersistInfo(this.CTA_LinkedIn),
            new DevExpress.XtraBars.LinkPersistInfo(this.barStaticItem1)});
            this.bar3.OptionsBar.AllowQuickCustomization = false;
            this.bar3.OptionsBar.DrawDragBorder = false;
            this.bar3.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar3, "bar3");
            // 
            // barUser
            // 
            resources.ApplyResources(this.barUser, "barUser");
            this.barUser.Id = 0;
            this.barUser.Name = "barUser";
            this.barUser.TextAlignment = System.Drawing.StringAlignment.Near;
            // 
            // barUserName
            // 
            resources.ApplyResources(this.barUserName, "barUserName");
            this.barUserName.Id = 1;
            this.barUserName.Name = "barUserName";
            this.barUserName.TextAlignment = System.Drawing.StringAlignment.Near;
            // 
            // CTA_Facebook
            // 
            resources.ApplyResources(this.CTA_Facebook, "CTA_Facebook");
            this.CTA_Facebook.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.CTA_Facebook.Glyph = global::Pharmacy.Properties.Resources.facebook;
            this.CTA_Facebook.Id = 2;
            this.CTA_Facebook.Name = "CTA_Facebook";
            this.CTA_Facebook.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.CTA_Facebook_ItemClick);
            // 
            // CTA_Twitter
            // 
            resources.ApplyResources(this.CTA_Twitter, "CTA_Twitter");
            this.CTA_Twitter.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.CTA_Twitter.Glyph = global::Pharmacy.Properties.Resources.twitter;
            this.CTA_Twitter.Id = 3;
            this.CTA_Twitter.Name = "CTA_Twitter";
            this.CTA_Twitter.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.CTA_Twitter_ItemClick);
            // 
            // CTA_LinkedIn
            // 
            resources.ApplyResources(this.CTA_LinkedIn, "CTA_LinkedIn");
            this.CTA_LinkedIn.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.CTA_LinkedIn.Glyph = global::Pharmacy.Properties.Resources.linkedin;
            this.CTA_LinkedIn.Id = 4;
            this.CTA_LinkedIn.Name = "CTA_LinkedIn";
            this.CTA_LinkedIn.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.CTA_LinkedIn_ItemClick);
            // 
            // barStaticItem1
            // 
            resources.ApplyResources(this.barStaticItem1, "barStaticItem1");
            this.barStaticItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItem1.Id = 6;
            this.barStaticItem1.Name = "barStaticItem1";
            this.barStaticItem1.TextAlignment = System.Drawing.StringAlignment.Near;
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // CTA_Linkitsys
            // 
            resources.ApplyResources(this.CTA_Linkitsys, "CTA_Linkitsys");
            this.CTA_Linkitsys.Id = 5;
            this.CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.FontSizeDelta")));
            this.CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.FontStyleDelta")));
            this.CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.GradientMode")));
            this.CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.Image")));
            this.CTA_Linkitsys.Name = "CTA_Linkitsys";
            // 
            // frmMain
            // 
            resources.ApplyResources(this, "$this");
            this.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("frmMain.Appearance.BackColor")));
            this.Appearance.FontSizeDelta = ((int)(resources.GetObject("frmMain.Appearance.FontSizeDelta")));
            this.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("frmMain.Appearance.FontStyleDelta")));
            this.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("frmMain.Appearance.GradientMode")));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("frmMain.Appearance.Image")));
            this.Appearance.Options.UseBackColor = true;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.menuStrip1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.HelpButton = true;
            this.KeyPreview = true;
            this.MainMenuStrip = this.menuStrip1;
            this.Name = "frmMain";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frmMain_FormClosing);
            this.Load += new System.EventHandler(this.frmMain_Load);
            this.Shown += new System.EventHandler(this.frmMain_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frmMain_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.pnlSmall)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).EndInit();
            this.menuStrip1.ResumeLayout(false);
            this.menuStrip1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pnlLarge)).EndInit();
            this.pnlLarge.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.pnlImage)).EndInit();
            this.pnlImage.ResumeLayout(false);
            this.pnlImage.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.popupStyle)).EndInit();
            this.popupStyle.ResumeLayout(false);
            this.popupStyle.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.rdoStyles.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.PanelControl pnlSmall;
        private DevExpress.XtraNavBar.NavBarControl navBarControl1;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Data;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Sales;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Settings;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup1;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage1;
        public DevExpress.LookAndFeel.DefaultLookAndFeel defaultLookAndFeel1;
        private DevExpress.XtraNavBar.NavBarItem NBI_Styles;
        private DevExpress.XtraNavBar.NavBarItem NBI_Item;
        private System.Windows.Forms.MenuStrip menuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_settings;
        private System.Windows.Forms.ToolStripMenuItem mi_ST_CompInfo;
        private System.Windows.Forms.ToolStripMenuItem mi_Styles;
        private System.Windows.Forms.ToolStripMenuItem mi_data;
        private System.Windows.Forms.ToolStripMenuItem mi_Item;
        private System.Windows.Forms.ToolStripMenuItem mi_Windows;
        private System.Windows.Forms.ToolStripMenuItem mi_Help;
        private System.Windows.Forms.ToolStripMenuItem mi_About;
        private System.Windows.Forms.ToolStripMenuItem mi_reports;
        private System.Windows.Forms.ToolStripMenuItem mi_reportCenter;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem mi_Sales;
        private System.Windows.Forms.ToolStripMenuItem mi_SL_Customer;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator6;
        private System.Windows.Forms.ToolStripMenuItem mi_Store;
        private System.Windows.Forms.ToolStripSeparator mi_DT_Sep1;
        private DevExpress.XtraNavBar.NavBarItem NBI_SL_Customer;
        private System.Windows.Forms.ToolStripSeparator mi_ST_SP_1;
        private DevExpress.XtraNavBar.NavBarItem NBI_Store;
        private DevExpress.XtraNavBar.NavBarItem NBI_SL_Invoice;
        private DevExpress.XtraNavBar.NavBarItem NBI_SL_Return;
        private System.Windows.Forms.ToolStripMenuItem mi_SL_Invoice;
        private System.Windows.Forms.ToolStripMenuItem mi_SL_InvoiceList;
        private System.Windows.Forms.ToolStripMenuItem mi_SL_Return;
        private System.Windows.Forms.ToolStripMenuItem miExit;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.PanelControl pnlLarge;
        private DevExpress.XtraEditors.PanelControl pnlImage;
        private DevExpress.XtraEditors.PopupContainerControl popupStyle;
        private DevExpress.XtraEditors.SimpleButton btnClosePopup;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.RadioGroup rdoStyles;
        private System.Windows.Forms.ToolStripSeparator mi_SL_Sep1Ret;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator32;
        private System.Windows.Forms.ToolStripMenuItem mi_GlobalGrid;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar3;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarStaticItem barUser;
        private DevExpress.XtraBars.BarStaticItem barUserName;
        private System.Windows.Forms.ToolStripSeparator mi_SL_Sep5IndSl;
        private System.Windows.Forms.ToolStripMenuItem mi_globalERPRegistration;
        private System.Windows.Forms.ToolStripMenuItem mi_ChangePass;
        private System.Windows.Forms.ToolStripSeparator mi_DT_Sep3;
        private System.Windows.Forms.ToolStripMenuItem salesProfitabilityToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem profitabilityOfItemsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem topSellingCustomerCategoriesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem topCategoriesOfItemsSoldToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem salesRateToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem salesDelegatesForItemsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem itemsSalesToEachDelegateToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem customeAccountsListStatisticsToolStripMenuItem;
        private DevExpress.XtraBars.BarButtonItem CTA_Facebook;
        private DevExpress.XtraBars.BarButtonItem CTA_Twitter;
        private DevExpress.XtraBars.BarButtonItem CTA_LinkedIn;
        private DevExpress.XtraBars.BarLinkContainerItem CTA_Linkitsys;
        private DevExpress.XtraBars.BarStaticItem barStaticItem1;
        private System.Windows.Forms.ToolStripMenuItem releaseNotesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem mi_PreInvoice;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator31;
        private System.Windows.Forms.ToolStripMenuItem mi_ImportExcelFiles;
        private System.Windows.Forms.ToolStripMenuItem mi_envoice;
        private System.Windows.Forms.ToolStripMenuItem Scr_UnitOfMeaure;
        private System.Windows.Forms.ToolStripMenuItem scr_Currency;
        private System.Windows.Forms.ToolStripMenuItem scr_Item_Codes;
        private System.Windows.Forms.ToolStripMenuItem src_Countries;
        private System.Windows.Forms.ToolStripMenuItem mi_EinvoiceSync;
        private System.Windows.Forms.ToolStripMenuItem eInvoicesUnsyncToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem E_taxableTypes;
        private System.Windows.Forms.ToolStripMenuItem E_activityTypes;
        private System.Windows.Forms.ToolStripMenuItem E_storesCodes;
        private System.Windows.Forms.ToolStripMenuItem taxSubTypesToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem mi_UOM;
        private System.Windows.Forms.ToolStripMenuItem mi_SL_ReturnList;
        private System.Windows.Forms.ToolStripMenuItem الاعداداتToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private System.Windows.Forms.ToolStripMenuItem mi_unSyncedRtrnInces;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator7;
        private System.Windows.Forms.ToolStripMenuItem mi_SL_Add;
        private System.Windows.Forms.ToolStripMenuItem mi_SL_AddList;
        private System.Windows.Forms.ToolStripMenuItem mi_UnSyncAddNotes;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem mi_ShowHideChart;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator8;
        private System.Windows.Forms.ToolStripMenuItem recievedDocuments;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator9;
        private System.Windows.Forms.ToolStripMenuItem mi_E_InvoiceSettings;
    }
}

