﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using System.IO;
using ExcelDataReader;

namespace Pharmacy.Forms
{
    public partial class frm_SL_CustomerList: DevExpress.XtraEditors.XtraForm
    {
        List<SL_Customer_Info> lst_Customers = new List<SL_Customer_Info>();
        List<SL_CustomerCategoryInfo> lst_groups = new List<SL_CustomerCategoryInfo>();
        
        public frm_SL_CustomerList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_IC_CustomerList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            ErpUtils.Tab_Enter_Process(grd_Customer);
                       
            ErpUtils.Load_Grid_Layout(grd_Customer, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grd_Customer);
            
            BindDataSources();
            Get_Customers();
            LoadPrivilege();
        }

        private void frm_SL_CustomerList_FormClosing(object sender, FormClosingEventArgs e)
        {
            ErpUtils.save_Grid_Layout(grd_Customer, this.Name.Replace("frm_", ""), true);
        }

        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_SL_Customer)))
                Application.OpenForms["frm_SL_Customer"].Close();

            new frm_SL_Customer().Show();            
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        
        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Get_Customers();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_customer();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_Customer.MinimumSize = grd_Customer.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grd_Customer, false).ShowPreview();
            grd_Customer.MinimumSize = new Size(0, 0);
        }        

        private void grd_Customer_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_customer();
        }


        private void Open_Selected_customer()
        {
            var view = grd_Customer.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_CustomerId));

            if (ErpUtils.IsFormOpen(typeof(frm_SL_Customer)))
                Application.OpenForms["frm_SL_Customer"].Close();

            new frm_SL_Customer(inv_id).Show();
        }


        private void Get_Customers()
        {
            int focusedIndex = (grd_Customer.FocusedView as GridView).FocusedRowHandle;
            //(grd_Customer.FocusedView as GridView).ClearSelection();
            int LastCustId = 0;
            MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);
            grd_Customer.DataSource = lst_Customers;            
            (grd_Customer.FocusedView as GridView).FocusedRowHandle = focusedIndex;
            //(grd_Customer.FocusedView as GridView).SelectRow(focusedIndex);            
        }

        void BindDataSources()
        {
            ERPDataContext DB = new ERPDataContext();

            //#region Price Level
            //lkpPriceLevel.Properties.ValueMember = "PriceLevelId";
            //lkpPriceLevel.Properties.DisplayMember = "PLName";
            //List<PriceLevel> lst_PriceLevel = (from st in DB.IC_PriceLevels.AsEnumerable()
            //                                   select new PriceLevel
            //                                   {
            //                                       PriceLevelId = st.PriceLevelId,
            //                                       PLName = st.PLName,
            //                                       IsRatio = st.IsRatio == true ?
            //                                       (Shared.IsEnglish ? ResICEn.FixedRatio : ResICAr.FixedRatio) :
            //                                       (Shared.IsEnglish ? ResICEn.PerItem : ResICAr.PerItem),
            //                                       IsRatioIncrease = st.IsRatioIncrease,
            //                                       Ratio = st.Ratio,
            //                                       Details = st.IsRatio == true && st.IsRatioIncrease == true ?
            //                                       (decimal.ToDouble(st.Ratio)).ToString() + "%" :
            //                                       (st.IsRatio == true && st.IsRatioIncrease == false ?
            //                                       (decimal.ToDouble((st.Ratio * -1))).ToString() + "%" :
            //                                       (Shared.IsEnglish ? ResICEn.variesPerItem : ResICAr.variesPerItem))
            //                                   }).ToList();

            //lst_PriceLevel.Insert(0, new PriceLevel
            //{
            //    PriceLevelId = -1,
            //    PLName = Shared.IsEnglish ? "No Change" : "عدم التغيير",
            //    IsRatio = string.Empty,
            //    IsRatioIncrease = false,
            //    Ratio = 0
            //});
            //lst_PriceLevel.Insert(1, new PriceLevel
            //{
            //    PriceLevelId = 0,
            //    PLName = Shared.IsEnglish ? "Without" : "بدون",
            //    IsRatio = string.Empty,
            //    IsRatioIncrease = false,
            //    Ratio = 0
            //});

            //lkpPriceLevel.Properties.DataSource = lst_PriceLevel;
            
            //#endregion
            
            #region group
            lkp_Category.Properties.ValueMember = "CustomerGroupId";
            lkp_Category.Properties.DisplayMember = "CGNameAr";

            lkp_Category.EditValue = 0;
            lst_groups = (from g in DB.SL_CustomerGroups
                          where DB.SL_CustomerGroups.Where(b => b.ParentGroupId == g.CustomerGroupId).Count() < 1
                          //join a in DB.ACC_Accounts
                          //on g.AccountId equals a.AccountId
                          //orderby a.AcNumber
                          select new SL_CustomerCategoryInfo
                          {
                              CustomerGroupId = g.CustomerGroupId,
                              CustomerGroupCode = g.CustomerGroupCode,
                              CGNameAr = g.CGNameAr,
                              CGNameEn = g.CGNameEn,
                              Desc = g.Desc,
                              MaxCredit = g.MaxCredit,
                              //AccountId = a.AccountId,
                              //AcNumber = a.AcNumber,
                              CustomersDefaultAccount = g.CustomersDefaultAccount,
                              CustomersHaveSeparateAccount = g.CustomersHaveSeparateAccount,
                          }).ToList();

            lst_groups.Insert(0, new SL_CustomerCategoryInfo
            {
                CustomerGroupId = -1,
                CustomerGroupCode = "",
                CGNameAr = Shared.IsEnglish? "No Change": "عدم التغيير",
                CGNameEn = "",
                Desc = "",
                MaxCredit = 0,
                AccountId = 0,
                AcNumber = "",
                CustomersDefaultAccount = 0,
                CustomersHaveSeparateAccount = false,
            });
            lst_groups.Insert(1, new SL_CustomerCategoryInfo
            {
                CustomerGroupId = 0,
                CustomerGroupCode = "",
                CGNameAr = Shared.IsEnglish ? "Without" : "بدون",
                CGNameEn = "",
                Desc = "",
                MaxCredit = 0,
                AccountId = 0,
                AcNumber = "",
                CustomersDefaultAccount = 0,
                CustomersHaveSeparateAccount = false,
            });

            lkp_Category.Properties.DataSource = lst_groups;

            #endregion

            #region SalesEmp
            List<SalesRep> lst_salesRep = new List<SalesRep>();
            lst_salesRep = (from e in DB.HR_Employees
                            where e.SalesRep
                            select new SalesRep
                            {
                                EmpId = e.EmpId,
                                EmpName = e.EmpName
                            }).ToList();

            lst_salesRep.Insert(0, new SalesRep
            {
                EmpId = -1,
                EmpName = Shared.IsEnglish ? "No Change" : "عدم التغيير",
            });
            lst_salesRep.Insert(1, new SalesRep
            {
                EmpId = 0,
                EmpName = Shared.IsEnglish ? "Without" : "بدون",
            });

            lkp_SalesEmp.Properties.DataSource = rep_salesEmp.DataSource = lst_salesRep;
            lkp_SalesEmp.Properties.DisplayMember = rep_salesEmp.DisplayMember = "EmpName";
            lkp_SalesEmp.Properties.ValueMember = rep_salesEmp.ValueMember = "EmpId";
            #endregion

            Get_Customers();
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Customer).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "تسجيل عميل جديد");
        }

        #region Update Customers Data
        private void mi_UpdateCust_Click(object sender, EventArgs e)
        {
            lkp_Category.EditValue = lkp_SalesEmp.EditValue = lkpPriceLevel.EditValue = -1;
            popupData.Show();
        }

        private void btnClosePopup_Click(object sender, EventArgs e)
        {
            popupData.Hide();
        }

        private void btn_Continue_Click(object sender, EventArgs e)
        {
            var view = grd_Customer.FocusedView as GridView;
            var selected_rows = view.GetSelectedRows();
            if (selected_rows.Count() == 0 || selected_rows[0] < 0)
                return;
            
            #region Update Customers
            if (XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResEn.MsgContinue : ResAr.MsgContinue,
                    Shared.IsEnglish == true ? ResEn.MsgTQues : ResAr.MsgTQues,
                     MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                return;

            ERPDataContext DB = new ERPDataContext();

            List<int> lst_SelectedIds = new List<int>();
            foreach (int index in selected_rows)
            {
                lst_SelectedIds.Add(Convert.ToInt32(view.GetRowCellValue(index, col_CustomerId)));
            }

            foreach (int Id in lst_SelectedIds)
            {
                SL_Customer cst = DB.SL_Customers.Where(x => x.CustomerId == Id).FirstOrDefault();


                if (Convert.ToInt32(lkp_Category.EditValue) > 0)//change group, so change customer code
                {
                    if (cst.CategoryId != Convert.ToInt32(lkp_Category.EditValue))//don't change customer code, if he has the same selected group
                    {

                        #region Change Customer Code
                        var last_custCode = (from d in DB.SL_Customers
                                             where d.CategoryId == Convert.ToInt32(lkp_Category.EditValue)
                                             select d.CusCode).ToList();
                        int x = last_custCode.DefaultIfEmpty(0).Max() + 1;

                        if (x == 1)
                        {
                            var group = lst_groups.Where(g => g.CustomerGroupId == Convert.ToInt32(lkp_Category.EditValue)).First();
                            cst.CusCode = Convert.ToInt32(group.CustomerGroupCode + "0001");
                        }
                        else
                            cst.CusCode = Convert.ToInt32(x.ToString());
                        #endregion

                        #region Change Customer Account Number
                        if (cst.HasSeparateAccount)
                        {
                            int grpAccId = Convert.ToInt32(lkp_Category.GetColumnValue("AccountId"));
                            string grpAcNumber = lkp_Category.GetColumnValue("AcNumber").ToString();

                            var Account = (from a in DB.ACC_Accounts
                                           where a.AccountId == cst.AccountId
                                           select a).Single();

                            //get new parent, than change account number upon
                            var NewParentAcc = (from a in DB.ACC_Accounts
                                                where a.AccountId == grpAccId
                                                select a).Single();

                            Account.ParentActId = NewParentAcc.AccountId;
                            Account.AcNumber = HelperAcc.AccNumGenerated(NewParentAcc);
                            
                        }
                        #endregion

                        cst.CategoryId = Convert.ToInt32(lkp_Category.EditValue);
                    }
                }
                else if (Convert.ToInt32(lkp_Category.EditValue) == 0)//change group, so change customer code
                {
                    if (cst.CategoryId.HasValue && Convert.ToInt32(lkp_Category.EditValue) == 0)//don't change customer code, if he has the same selected group
                    {
                        #region Change Customer Code
                        var last_custCode = (from d in DB.SL_Customers
                                             where d.CategoryId == null
                                             select d.CusCode).ToList();
                        int x = last_custCode.DefaultIfEmpty(0).Max() + 1;
                        cst.CusCode = Convert.ToInt32(x.ToString());
                        #endregion

                        #region Change Customer Account Number
                        if (cst.HasSeparateAccount)
                        {
                            int grpAccId = Shared.st_Store.CustomersAcc.Value;
                            string grpAcNumber = DB.ACC_Accounts.Where(a => a.AccountId == grpAccId).Select(a => a.AcNumber).First();

                            var Account = (from a in DB.ACC_Accounts
                                           where a.AccountId == cst.AccountId
                                           select a).Single();

                            //get new parent, than change account number upon
                            var NewParentAcc = (from a in DB.ACC_Accounts
                                                where a.AccountId == grpAccId
                                                select a).Single();

                            Account.ParentActId = NewParentAcc.AccountId;
                            Account.AcNumber = HelperAcc.AccNumGenerated(NewParentAcc);
                            
                        }
                        #endregion

                        cst.CategoryId = Convert.ToInt32(lkp_Category.EditValue);
                    }
                }


                if (Convert.ToInt32(lkp_SalesEmp.EditValue) >= 0)
                {
                    cst.SalesEmpId = Convert.ToInt32(lkp_SalesEmp.EditValue) == 0 ? (int?)null : Convert.ToInt32(lkp_SalesEmp.EditValue);
                }

                if (Convert.ToInt32(lkpPriceLevel.EditValue) >= 0)
                {
                    cst.PriceLevel = Convert.ToInt32(lkpPriceLevel.EditValue) == 0 ? (int?)null : Convert.ToInt32(lkpPriceLevel.EditValue);
                }
                //Mohammad 15-04-2019
                cst.Is_Active = chk_IsActive.Checked;
                cst.Is_Blocked = chk_IsBlocked.Checked;

                DB.SubmitChanges();
            }

            XtraMessageBox.Show(this.LookAndFeel,
              Shared.IsEnglish == true ? ResHREn.MsgSave : ResHRAr.MsgSave//"تم الحفظ بنجاح"                                                
              , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            #endregion

            popupData.Hide();
            Get_Customers();

        }
        #endregion

        private void contextMenuStrip1_Opening(object sender, CancelEventArgs e)
        {
            var view = grd_Customer.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)                
                e.Cancel = true;
        }

        private void frm_SL_CustomerList_Activated(object sender, EventArgs e)
        {
            Get_Customers();
        }

        private void navBarItem1_LinkClicked(object sender, NavBarLinkEventArgs e)
        {
            Customers c = new Customers();
            c.Show();
        }

        private void grd_Customer_Click(object sender, EventArgs e)
        {

        }

        private void barbtnImportCustomers_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //importCustomers();
        }

        //public void importCustomers()
        //{
        //    ERPDataContext DB = new ERPDataContext();
        //    OpenFileDialog ofd = new OpenFileDialog();
        //    ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
        //    if (ofd.ShowDialog() == DialogResult.OK)
        //    {
        //        try
        //        {
        //            //DataTable dt = ErpUtils.exceldata(ofd.FileName);

        //            FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

        //            IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
        //            DataSet result = excelReader.AsDataSet();

        //            int count = 1;
        //            List<SL_Customer> csList = new List<SL_Customer>();
        //            List<string> regions = new List<string>();
        //            foreach (DataRow d in result.Tables[0].Rows)
        //            {
        //                try
        //                {
        //                    if (result.Tables[0].Rows.IndexOf(d) == 0) { continue; }

        //                    SL_Customer customer = new SL_Customer();
                            
        //                    customer.CusCode = count++;
        //                    customer.CusNameAr = Convert.ToString(d[0]);
        //                    customer.CusNameEn = string.IsNullOrEmpty(Convert.ToString(d[1])) ? "" : Convert.ToString(d[1]);
        //                    customer.Tel = string.IsNullOrEmpty(Convert.ToString(d[2])) ? null : Convert.ToString(d[2]);
        //                    customer.Mobile = string.IsNullOrEmpty(Convert.ToString(d[3])) ? null : Convert.ToString(d[3]);
        //                    customer.Address = string.IsNullOrEmpty(Convert.ToString(d[4])) ? null : Convert.ToString(d[4]);
        //                    customer.City = string.IsNullOrEmpty(Convert.ToString(d[5])) ? null : Convert.ToString(d[5]);
        //                    //if (!string.IsNullOrEmpty(Convert.ToString(d[5])))
        //                    //{
        //                    //    if (regions.Contains(Convert.ToString(d[5])))
        //                    //    {
        //                    //        customer.IdRegion = DB.SL_CustomerRegions.Where(x => x.RegionName == Convert.ToString(d[5])).Select(x => x.IdRegion).FirstOrDefault();
        //                    //    }
        //                    //    else
        //                    //    {
        //                    //        regions.Add(Convert.ToString(d[5]));
        //                    //        SL_CustomerRegion region = new SL_CustomerRegion();
        //                    //        region.RegionName = Convert.ToString(d[5]);
        //                    //        DB.SL_CustomerRegions.InsertOnSubmit(region);
        //                    //        DB.SubmitChanges();
        //                    //        customer.IdRegion = region.IdRegion;
        //                    //    }
        //                    //}
        //                    customer.Email = string.IsNullOrEmpty(Convert.ToString(d[6])) ? null : Convert.ToString(d[6]);
        //                    customer.Fax = string.IsNullOrEmpty(Convert.ToString(d[7])) ? null : Convert.ToString(d[7]);
        //                    customer.Zip = string.IsNullOrEmpty(Convert.ToString(d[8])) ? null : Convert.ToString(d[8]);
        //                    customer.Shipping = string.IsNullOrEmpty(Convert.ToString(d[9])) ? null : Convert.ToString(d[9]);
        //                    if (!string.IsNullOrEmpty(Convert.ToString(d[10])))
        //                        customer.csType = (Convert.ToString(d[10]) == "طبيعي" ? 0 : (Convert.ToString(d[10]) == "اعتباري" ? 1: 2));
        //                    customer.Representative = string.IsNullOrEmpty(Convert.ToString(d[11])) ? null : Convert.ToString(d[11]);
        //                    //if (!string.IsNullOrEmpty(Convert.ToString(d[11])))
        //                    //{
        //                    //    customer.SalesEmpId = DB.HR_Employees.Where(x => x.EmpName == Convert.ToString(d[11])).Select(x => x.EmpId).FirstOrDefault();
        //                    //}
        //                    customer.IdNumber = string.IsNullOrEmpty(Convert.ToString(d[12])) ? null : Convert.ToString(d[12]);
        //                    customer.TradeRegistry = string.IsNullOrEmpty(Convert.ToString(d[13])) ? null : Convert.ToString(d[13]);
        //                    customer.TaxCardNumber = string.IsNullOrEmpty(Convert.ToString(d[14])) ? null : Convert.ToString(d[14]);
        //                    customer.TaxFileNumber = string.IsNullOrEmpty(Convert.ToString(d[15])) ? null : Convert.ToString(d[15]);
        //                    customer.Is_Active = true;
        //                    customer.Is_Blocked = false;
        //                    if (string.IsNullOrEmpty(Convert.ToString(d[16])))
        //                        customer.MaxCredit = 0;
        //                    else
        //                        customer.MaxCredit = Convert.ToDecimal(d[16]);

        //                    customer.BankName = string.IsNullOrEmpty(Convert.ToString(d[17])) ? null : Convert.ToString(d[17]);
        //                    customer.BankAccNum = string.IsNullOrEmpty(Convert.ToString(d[18])) ? null : Convert.ToString(d[18]);
        //                    if (string.IsNullOrEmpty(Convert.ToString(d[19])))
        //                        customer.IsTaxable = false;
        //                    else
        //                        customer.IsTaxable = (Convert.ToString(d[19]) == "ضريبي" ? true : false);

        //                    customer.HasSeparateAccount = true;
        //                    csList.Add(customer);
        //                }
        //                catch (Exception ex)
        //                {

        //                    XtraMessageBox.Show(
        //                        string.Format("حدث خطأ أثناء في تحميل العملاء")
        //                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //                }

        //            }
        //            excelReader.Close();
        //            DB.SL_Customers.InsertAllOnSubmit(csList);
        //            DB.SubmitChanges();

        //            foreach (var c in csList)
        //            {
        //                c.AccountId = HelperAcc.Get_CustomerAccount_Id(c.CustomerId, Shared.st_Store.CustomersAcc.Value, Shared.st_Store.CustomersAcc);
        //            }


        //            DB.SubmitChanges();
        //            XtraMessageBox.Show(
        //                string.Format("تم تحميل العملاء بشكل سليم")
        //                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //        }
        //        catch (Exception ex)
        //        {

        //            XtraMessageBox.Show(
        //                string.Format("حدث خطأ أثناء في تحميل الملف")
        //                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
        //        }
        //    }
        //    else
        //        return;
        //}
    }

    class PriceLevel
    {
        int priceLevelId;

        public int PriceLevelId
        {
            get { return priceLevelId; }
            set { priceLevelId = value; }
        }
        string pLName;

        public string PLName
        {
            get { return pLName; }
            set { pLName = value; }
        }
        string isRatio;

        public string IsRatio
        {
            get { return isRatio; }
            set { isRatio = value; }
        }
        bool isRatioIncrease;

        public bool IsRatioIncrease
        {
            get { return isRatioIncrease; }
            set { isRatioIncrease = value; }
        }
        decimal ratio;

        public decimal Ratio
        {
            get { return ratio; }
            set { ratio = value; }
        }
        string details;

        public string Details
        {
            get { return details; }
            set { details = value; }
        }
    }

    class SalesRep
    {
        int empId;

        public int EmpId
        {
            get { return empId; }
            set { empId = value; }
        }
        string empName;

        public string EmpName
        {
            get { return empName; }
            set { empName = value; }
        }
    }

}