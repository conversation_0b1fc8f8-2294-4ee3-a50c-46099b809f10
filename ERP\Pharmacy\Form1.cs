﻿using DAL;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            fix(1);//Fix Level

        }

        private static void fix(int lvl)
        {
            ERPDataContext db = new ERPDataContext(Properties.Settings.Default.ERPConnectionString);
            int i = 1;
            var acs = from a in db.ACC_Accounts
                      //where a.Level >= 4
                      where a.ParentActId !=null
                      orderby a.ParentActId
                      group a by a.ParentActId into grp
                      select grp;
            if (acs.Count() == 0) return;
            foreach (var g in acs)
            {
                //int indx = 1;
                foreach (var nn in g)
                {
                    nn.AcNumber = db.ACC_Accounts.Where(x => x.AccountId == nn.ParentActId).Select(x => x.AcNumber).FirstOrDefault();
                    var n = db.ACC_Accounts.Where(x => x.AccountId == nn.AccountId).First();
                    n.AcNumber = nn.AcNumber;
                    if (n.Level == 2)
                        n.AcNumber = n.AcNumber + "0" + i;
                    else if (n.Level == 3)
                        n.AcNumber = n.AcNumber + "00" + i;
                    else if (n.Level == 4)
                        n.AcNumber = n.AcNumber + (i.ToString().Length > 1 ? "000".Substring(0, n.Level-1 - (i.ToString().Length - 1)) + i : "000" + i);
                    else if (n.Level == 5)
                        n.AcNumber = n.AcNumber + (i.ToString().Length > 1 ? "0000".Substring(0, n.Level - 1 - (i.ToString().Length - 1)) + i : "0000" + i);
                    else if (n.Level == 6)
                        n.AcNumber = n.AcNumber + (i.ToString().Length > 1 ? "00000".Substring(0, n.Level - 1 - (i.ToString().Length - 1)) + i : "00000" + i);
                    else if (n.Level == 7)
                        n.AcNumber = n.AcNumber + (i.ToString().Length > 1 ? "000000".Substring(0, n.Level - 1 - (i.ToString().Length - 1)) + i : "000000" + i);
                    else if (n.Level == 8)
                        n.AcNumber = n.AcNumber + (i.ToString().Length > 1 ? "0000000".Substring(0, n.Level - 1 - (i.ToString().Length - 1)) + i : "0000000" + i);
                    else if (n.Level == 9)
                        n.AcNumber = n.AcNumber + (i.ToString().Length > 1 ? "********".Substring(0, n.Level - 1 - (i.ToString().Length - 1)) + i : "********" + i);
                    db.SubmitChanges();
                    i += 1;
                    //db.Refresh(System.Data.Linq.RefreshMode.OverwriteCurrentValues);
                }
                i = 1;
            }

            if (lvl >= 8) return;
            fix(++lvl);

            //acs = from a in db.ACC_Accounts
            //      where a.Level >= 5
            //      group a by a.ParentActId into grp
            //      select grp;
            //foreach (var g in acs)
            //{
            //    //int indx = 1;
            //    foreach (var nn in g)
            //    {
            //        nn.AcNumber = db.ACC_Accounts.Where(x => x.AccountId == nn.ParentActId).Select(x => x.AcNumber).FirstOrDefault();
            //        var n = db.ACC_Accounts.Where(x => x.AccountId == nn.AccountId).First();
            //        n.AcNumber = nn.AcNumber;
            //        //if (n.Level == 2)
            //        //    n.AcNumber = n.AcNumber + "0" + i;
            //        //else if (n.Level == 3)
            //        //    n.AcNumber = n.AcNumber + "00" + i;
            //        //else if (n.Level == 4)
            //        //    n.AcNumber = n.AcNumber + "000" + i;
            //         if (n.Level == 5)
            //            n.AcNumber = n.AcNumber + "0000" + i;
            //        else if (n.Level == 6)
            //            n.AcNumber = n.AcNumber + "00000" + i;
            //        else if (n.Level == 7)
            //            n.AcNumber = n.AcNumber + "000000" + i;
            //        else if (n.Level == 8)
            //            n.AcNumber = n.AcNumber + "0000000" + i;
            //        else if (n.Level == 9)
            //            n.AcNumber = n.AcNumber + "********" + i;
            //        db.SubmitChanges();
            //        i += 1;
            //        //db.Refresh(System.Data.Linq.RefreshMode.OverwriteCurrentValues);
            //    }
            //    //i = 1;
            //}
            db.SubmitChanges();
        }
    }
}
