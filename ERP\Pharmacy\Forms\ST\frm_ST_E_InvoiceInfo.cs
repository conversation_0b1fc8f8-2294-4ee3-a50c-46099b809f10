﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using System.Management;
using System.Collections;
using System.Net.Http;
using System.Threading.Tasks;
using System.Net;
using System.Runtime.InteropServices;
using Pharmacy.Properties;
using System.Security.AccessControl;
using McDRSigniture;
using System.IO;
using SignuatuerGenerator;

namespace Pharmacy.Forms
{
    public partial class frm_ST_E_InvoiceInfo : DevExpress.XtraEditors.XtraForm
    {
        bool DataModified;

        public frm_ST_E_InvoiceInfo()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_ST_Print_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
            var DB = new DAL.ERPDataContext();

            SignturerCompany signturerCompany1 = new SignturerCompany(1, "Egypt Trust");
            SignturerCompany signturerCompany2 = new SignturerCompany(2, "مصر للمقاصة");
            SignturerCompany signturerCompany3 = new SignturerCompany(1, "فيكسد مصر");
            SignturerCompany signturerCompany4 = new SignturerCompany(1, "الدلتا");
            lkpCompanyType.Properties.DataSource = new List<SignturerCompany> { signturerCompany1, signturerCompany2, signturerCompany3, signturerCompany4 };
            lkpCompanyType.Properties.ValueMember = "Id";
            lkpCompanyType.Properties.DisplayMember = "Name";
            #region COUNTRY
            lkp_country.Properties.DataSource = DB.HR_Countries;
            lkp_country.Properties.ValueMember = "CountryId";
            lkp_country.Properties.DisplayMember = "CountryName";
            #endregion

            Load_EInvoiceSettings();
        }

        private void frm_ST_Print_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }


        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Save_EInvoiceSettings();
        }

        private void barButtonClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }


        private void Save_EInvoiceSettings()
        {
            try
            {

                DAL.ERPDataContext DB = new DAL.ERPDataContext();
                var ST_CompanyInfo = DB.ST_CompanyInfos.FirstOrDefault();
                ST_CompanyInfo.CmpNameAr = txtCompanyNameAr.Text;
                ST_CompanyInfo.CmpNameEn = txtCompanyNameEn.Text;
                ST_CompanyInfo.ActivityType = txtActivityType.Text;
                ST_CompanyInfo.CommercialBook = txtCommercialBook.Text;
                ST_CompanyInfo.TaxCard = txtTaxCard.Text;
                ST_CompanyInfo.ClientId = Crypto.EncryptStringAES(txtClientId.Text, Crypto.Key);//Encryption
                ST_CompanyInfo.ClientSecret = Crypto.EncryptStringAES(txtClientSecret1.Text, Crypto.Key);//Encryption
                ST_CompanyInfo.DonglePIN = Crypto.EncryptStringAES(txtDonglePIN.Text, Crypto.Key);//Encryption
                ST_CompanyInfo.TokenSerial = txtTokenserial.Text;
                ST_CompanyInfo.ServerName = txtServerName.Text;
                ST_CompanyInfo.CertificateCompanyType = Convert.ToInt32(lkpCompanyType.EditValue);
                ST_CompanyInfo.LibraryPath = txtLibraryPath.Text;
                ST_CompanyInfo.PublicKey = txtPublickey.Text;

                DB.SubmitChanges();
                MyHelper.UpdateST_UserLog(DB, "", "", (int)FormAction.Edit, (int)FormsNames.ST_CompInfo);
                var ST_Store = DB.ST_Stores.FirstOrDefault();
                ST_Store.RoundValue = Convert.ToInt32(spinRoundValue.Value);
                ST_Store.DocumentThreshold = spinDocumentThreshold.Value;
                DB.SubmitChanges();
                MyHelper.UpdateST_UserLog(DB, "", "", (int)FormAction.Edit, (int)FormsNames.ST_Store);
                var Stores = DB.IC_Stores;
                foreach (var store in Stores)
                {
                    store.ECode = txtECode.Text;
                    store.Governate = txtGovernate.Text;
                    store.RegionCity = txtRegionCity.Text;
                    store.Street = txtStreet.Text;
                    store.BuildingNumber = txtBuildingNumber.Text;
                    store.CountryId = Convert.ToInt32(lkp_country.EditValue);
                }
                DB.SubmitChanges();

                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgSave : ResAccAr.MsgSave//"تم الحفظ بنجاح"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                DataModified = false;

            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgIncorrectData : ResAccAr.MsgIncorrectData//"تأكد من صحة البيانات"                    
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void Load_EInvoiceSettings()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var ST_CompanyInfo = DB.ST_CompanyInfos.FirstOrDefault();
            txtCompanyNameAr.Text = ST_CompanyInfo.CmpNameAr;
            txtCompanyNameEn.Text = ST_CompanyInfo.CmpNameEn;
            txtActivityType.Text = ST_CompanyInfo.ActivityType;
            txtCommercialBook.Text = ST_CompanyInfo.CommercialBook;
            txtTaxCard.Text = ST_CompanyInfo.TaxCard;
            txtClientId.Text = Crypto.DecryptStringAES(ST_CompanyInfo.ClientId, Crypto.Key);//Encryption
            txtClientSecret1.Text = Crypto.DecryptStringAES(ST_CompanyInfo.ClientSecret, Crypto.Key);//Encryption
            txtDonglePIN.Text = Crypto.DecryptStringAES(ST_CompanyInfo.DonglePIN,Crypto.Key);//Encryption
            txtTokenserial.Text = ST_CompanyInfo.TokenSerial;
            txtServerName.Text = ST_CompanyInfo.ServerName;
            lkpCompanyType.EditValue =ST_CompanyInfo.CertificateCompanyType??1;
            txtLibraryPath.Text = ST_CompanyInfo.LibraryPath;
            txtPublickey.Text=ST_CompanyInfo.PublicKey;

            var ST_Store = DB.ST_Stores.FirstOrDefault();
            spinRoundValue.Value =ST_Store.RoundValue??5;
            spinDocumentThreshold.Value =ST_Store.DocumentThreshold??25000 ;
            var store = DB.IC_Stores.FirstOrDefault();

            txtECode.Text = store.ECode;
            txtGovernate.Text = store.Governate;
            txtRegionCity.Text = store.RegionCity;
            txtStreet.Text = store.Street;
            txtBuildingNumber.Text = store.BuildingNumber;
            lkp_country.EditValue =store.CountryId;
            txtuuid.Text = "Z74TEDXHFYN2G91FRNJFHEQF10";
        }

        private void controls_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        DialogResult ChangesMade()
        {

            if (DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgDataModified : ResAccAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "                                        
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    Save_EInvoiceSettings();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void DoValidate()
        {

        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "إعدادات طباعة الباركود");
        }

        private async void btnTestAPI_Click(object sender, EventArgs e)
        {
            btnTestAPI.Enabled = false;
           if (string.IsNullOrWhiteSpace(Settings.Default.BackEndPoint))
            {
                MessageBox.Show("Backend URL is not configured.");
                return;
            }
            if (string.IsNullOrWhiteSpace(Settings.Default.BackEndPort.ToString()))
            {
                MessageBox.Show("Backend port is not configured.");
                return;
            }
            MessageBox.Show("Please Wait, till checking ");
            var baseUrl = $"{Settings.Default.BackEndPoint}:{Settings.Default.BackEndPort}";
            var uuid = txtuuid.Text;
            var url = $"{baseUrl}/api/Einvoice/GetDocument?uuid={uuid}";
            var msg = await CheckIfDocumentExists(url);
            XtraMessageBox.Show(msg, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            btnTestAPI.Enabled = true;
        }
        private async Task<string> CheckIfDocumentExists(string url)
        {
            using (var client = new HttpClient())
            {
                try
                {
                    client.Timeout = TimeSpan.FromSeconds(30);
                    var response = await client.GetAsync(url);

                    if (response.StatusCode == HttpStatusCode.NotFound)
                        return "Document not found.";

                    response.EnsureSuccessStatusCode(); 
                    return "Document exists.";
                }
                catch (HttpRequestException ex)
                {
                    if (ex.InnerException is System.Net.Sockets.SocketException socketEx)
                    {
                        return $"Socket error: {socketEx.ErrorCode}";
                    }

                    if (Marshal.GetHRForException(ex) == unchecked((int)0x80070005))
                        return "Error: Access denied (0x80070005). Check publish folder permissions.";
                    if (Marshal.GetHRForException(ex) == unchecked((int)0x8007000D))
                        return "Error: Invalid data (0x8007000D). .NET hosting may not be installed.";

                    return $"Request error: {ex.Message}";
                }
                catch (Exception ex)
                {
                    return $"Unexpected error: {ex.Message}";
                }
            }
        }

        private void btnTestlibrary_Click(object sender, EventArgs e)
        {
            try
            {
                var libPath = txtLibraryPath.Text;
                if (string.IsNullOrWhiteSpace(libPath))
                {
                    MessageBox.Show("signuatuer dll is not configured.");
                    return;
                }
                if (!File.Exists(libPath))
                {
                    MessageBox.Show("DLL file not found at specified path.");
                    return;
                }
                if (lkpCompanyType.EditValue == null)
                {
                    MessageBox.Show("Please select a company type.");
                    return;
                }
                bool result = false;
               //if (Environment.Is64BitProcess)
                if (Convert.ToInt32(lkpCompanyType.EditValue) == 1)
                {
                    GetSignatuerEgyptTrust egyptTrust = new GetSignatuerEgyptTrust();
                    result = egyptTrust.CheckDll(libPath);
                }
                if (Convert.ToInt32(lkpCompanyType.EditValue) == 2)
                {
                    result = MCDRSignature.CheckDLL(libPath);
                }
                    

                if (result)
                    MessageBox.Show("Signature DLL is configured successfully.");
                else
                    MessageBox.Show("Signature DLL is not configured successfully.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Unexpected error: {ex.Message}");
            }
        }

        private void Btnslots_Click(object sender, EventArgs e)
        {
            try
            {
                var libPath = txtLibraryPath.Text;
                if (string.IsNullOrWhiteSpace(libPath))
                {
                    MessageBox.Show("signuatuer dll is not configured.");
                    return;
                }
                if (!File.Exists(libPath))
                {
                    MessageBox.Show("DLL file not found at specified path.");
                    return;
                }
                McDRSigniture.MCDRSignature signature = new McDRSigniture.MCDRSignature();
                var slots = signature.SeeSlots(libPath);
                MessageBox.Show(slots);
            }
            catch (Exception)
            {

            }

        }

        private void BtnShowClientId_Click(object sender, EventArgs e)
        {
            ChangePasswordShow(txtClientId);
        }

        private void BtnShowClientScrt_Click(object sender, EventArgs e)
        {
            ChangePasswordShow(txtClientSecret1);
        }

        private void BtnShowDnglPIN_Click(object sender, EventArgs e)
        {
            ChangePasswordShow(txtDonglePIN);
        }
        void ChangePasswordShow(TextEdit textEdit)
        {
            if (textEdit.Properties.UseSystemPasswordChar)
            {
                textEdit.Properties.UseSystemPasswordChar = false;
                textEdit.Properties.PasswordChar = '\0';
            }
            else
            {
                textEdit.Properties.UseSystemPasswordChar = true;
                textEdit.Properties.PasswordChar = '*';
            }
        }
    }
}
public class SignturerCompany
{
    public SignturerCompany()
    {

    }
    public SignturerCompany(int Id, string Name)
    {
        this.Id = Id;
        this.Name = Name;
    }
    public int Id { get; set; }
    public string Name { get; set; }
}