﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using DevExpress.XtraPrinting;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;
using System.IO;
using ExcelDataReader;

namespace Pharmacy.Forms
{
    public partial class frm_E_ItemCodes : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();
        DataTable dt_Details = new DataTable();
        string taxNumber = "";
        public frm_E_ItemCodes()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_E_ItemCodes_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            BindDataSources();
            LoadItems();
            taxNumber = DB.ST_CompanyInfos.FirstOrDefault().TaxCard;
        }


        private void BindDataSources()
        {

            #region dt_Details
            dt_Details.Columns.Clear();
            dt_Details.Columns.Add("ItemId");
            dt_Details.Columns.Add("ItemCode1");
            dt_Details.Columns.Add("ItemCode2");
            dt_Details.Columns.Add("ItemNameAr");
            dt_Details.Columns.Add("ItemNameEn");
            dt_Details.Columns.Add("Category");
            dt_Details.Columns.Add("Company");
            dt_Details.Columns.Add("ItemEType");
            dt_Details.Columns.Add("ItemECode");
            grItemCodes.DataSource = dt_Details;
            #endregion
        }
        private void LoadItems()
        {

            var lstItems = (from i in DB.IC_Items
                            select new
                            {
                                ItemCode1 = i.ItemCode1,
                                ItemCode2 = i.ItemCode2,
                                ItemId = i.ItemId,
                                ItemNameAr = i.ItemNameAr,
                                ItemNameEn = i.ItemNameEn,
                                MaxQty = i.MaxQty,
                                MinQty = i.MinQty,
                                ReorderLevel = i.ReorderLevel,
                                IsExpire = i.IsExpire,
                                PurchasePrice = i.PurchasePrice,
                                SellPrice = i.SmallUOMPrice,
                                PicPath = i.PicPath,
                                MediumUOM = i.MediumUOM,
                                LargeUOM = i.LargeUOM,
                                CategoryNameAr = i.IC_Category.CategoryNameAr,
                                //CompanyNameAr = i.IC_Company.CompanyNameAr,
                                SellDiscountRatio = i.SalesDiscRatio,
                                CategoryId = i.Category,
                                i.ItemECode,
                                i.ItemEType

                            }).ToList();


            dt_Details.Rows.Clear();

            if (lstItems.Count() > 0)
            {

                foreach (var item in lstItems)
                {
                    DataRow row = dt_Details.NewRow();
                    row["ItemId"] = item.ItemId;
                    row["ItemCode1"] = item.ItemCode1;
                    row["ItemCode2"] = item.ItemCode2;
                    row["ItemNameAr"] = item.ItemNameAr;
                    row["ItemNameEn"] = item.ItemNameEn;
                    row["Category"] = item.CategoryNameAr;
                    //row["Company"] = item.CompanyNameAr;
                    row["ItemEType"] = item.ItemEType;
                    row["ItemECode"] = item.ItemECode;
                    dt_Details.Rows.Add(row);
                }

            }
            dt_Details.AcceptChanges();

        }
        private void barBtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                SaveData();
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgSave : ResSLAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        private void SaveData()
        {
            for (int x = 0; x < dt_Details.Rows.Count; x++)
            {
                IC_Item item;
                #region PrInvDetails
                if (dt_Details.Rows[x].RowState == DataRowState.Deleted)
                    continue;
                var c = dt_Details.Rows[x].Table;
                if (dt_Details.Rows[x]["ItemId"] != DBNull.Value)
                {

                   item = DB.IC_Items.FirstOrDefault(a=>a.ItemId==Convert.ToInt32(dt_Details.Rows[x]["ItemId"]));

                    if (dt_Details.Rows[x]["ItemEType"] != null)
                        item.ItemEType = dt_Details.Rows[x]["ItemEType"].ToString();
                    else
                        item.ItemEType = "";
                    if (dt_Details.Rows[x]["ItemECode"] != null)
                        item.ItemECode = dt_Details.Rows[x]["ItemECode"].ToString();
                    else
                        item.ItemECode = "";
                    DB.SubmitChanges();
                }

                #endregion
            }
           
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, //"حذف صف ؟"
                    Shared.IsEnglish == true ? ResICEn.MsgTQues : ResICAr.MsgTQues,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;

                GridView view = sender as GridView;

                if (view.GetFocusedRowCellValue(col_ItemId) == null)
                    return;
                else
                    view.DeleteRow(view.FocusedRowHandle);
            }
        }

        private void gvPriority_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            try
            {
                ColumnView view = sender as ColumnView;

                if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemEType"]).ToString() == "GS1")
                {
                    if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemECode"]).ToString() == "")
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["ItemECode"], "*");
                    }
                }
            }
            catch
            {
                e.Valid = false;
            }
        }

        private void gvPriority_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "تسجيل عميل جديد");
        }

        private void gvUom_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {
            {

                GridView view = grItemCodes.FocusedView as GridView;
                if (e.Column.FieldName == "ItemEType")
                {
                    if (!string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "ItemEType").ToString()))
                    {

                        var type = view.GetRowCellValue(e.RowHandle, "ItemEType").ToString();
                        var code1 = view.GetRowCellValue(e.RowHandle, "ItemCode1").ToString();
                        if (type == "EGS")
                        {
                            var code = "EG" + "-" + taxNumber + "-" + code1;
                            view.SetRowCellValue(e.RowHandle, "ItemECode", code);
                        }
                        else
                        {
                            var code ="";
                            view.SetRowCellValue(e.RowHandle, "ItemECode", code);
                        }

                    }

                }

            }
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void frm_E_ItemCodes_FormClosed(object sender, FormClosedEventArgs e)
        {
            this.Close();
        }

        private void barbtn_ImpotItems_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            ImportItems();
            LoadItems();
        }

        public void ImportItems()
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xlsx)|*.xlsx|Excel File(*.xls)|*.xls";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    int count = 1;
                    List<IC_Item> ItemList = new List<IC_Item>();
                    List<string> CategoryList = new List<string>();
                    List<string> CompanyList = new List<string>();
                    List<string> UOMList = new List<string>();
                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;
                            if (result.Tables[0].Rows.IndexOf(d) == 0) { continue; }

                            #region Category
                            CategoryList = DB.IC_Categories.Select(x => x.CategoryNameAr).ToList();
                            string category = string.IsNullOrEmpty(Convert.ToString(d[4])) ? "الفئة العامة" : Convert.ToString(d[4]);
                            IC_Category cat = new IC_Category();
                            if (!CategoryList.Contains(category))
                            {
                                cat.CategoryNameAr = category;
                                cat.CategoryNameEn = string.Empty;
                                cat.CatNumber = (DB.IC_Categories.Count() + 1).ToString();
                                cat.Level = 2;
                                cat.ParentId = 2;
                                //cat.CategoryId = (DB.IC_Categories.Count() + 1);
                                DB.IC_Categories.InsertOnSubmit(cat);
                                DB.SubmitChanges();
                                CategoryList.Add(category);
                            }
                            else
                            {
                                cat = DB.IC_Categories.Where(c => c.CategoryNameAr == category).FirstOrDefault();
                            }
                            #endregion

                            #region Company
                            CompanyList = DB.IC_Companies.Select(x => x.CompanyNameAr).ToList();
                            string company = string.IsNullOrEmpty(Convert.ToString(d[5])) ? "شركه عامه" : Convert.ToString(d[5]);
                            IC_Company com = new IC_Company();
                            if (!CompanyList.Contains(company))
                            {
                                com.CompanyNameAr = company;
                                com.CompanyNameEn = string.Empty;
                                com.CompanyCode = (DB.IC_Companies.Count() + 1);
                                com.Address = string.Empty;
                                com.Tel = string.Empty;
                                DB.IC_Companies.InsertOnSubmit(com);
                                DB.SubmitChanges();
                                CompanyList.Add(company);
                            }
                            else
                            {
                                com = DB.IC_Companies.Where(c => c.CompanyNameAr == company).FirstOrDefault();
                            }
                            #endregion
                        
                            #region UOM
                            // small uom
                            UOMList = DB.IC_UOMs.Select(x => x.UOM).ToList();
                            IC_UOM uom = new IC_UOM();
                            string um = string.IsNullOrEmpty(Convert.ToString(d[7])) ? "وحدة" : Convert.ToString(d[7]);
                            if (!UOMList.Contains(um))
                            {
                                uom.UOM = um;
                                DB.IC_UOMs.InsertOnSubmit(uom);
                               // UOMList.Add(um);
                                DB.SubmitChanges();
                            }
                            d[7] = um;
                            // mediuom uom
                            if (!string.IsNullOrEmpty(Convert.ToString(d[9])))
                            {
                                if (!UOMList.Contains(Convert.ToString(d[9])))
                                  {
                                    IC_UOM uom2 = new IC_UOM();
                                    uom2.UOM = Convert.ToString(d[9]);
                                    DB.IC_UOMs.InsertOnSubmit(uom2);
                                   // UOMList.Add(uom2);
                                    DB.SubmitChanges();
                                }
                            }
                            // large uom
                            if (!string.IsNullOrEmpty(Convert.ToString(d[12])))
                            {
                                if (!UOMList.Contains(Convert.ToString(d[12])))
                                {
                                    IC_UOM uom3 = new IC_UOM();
                                    uom3.UOM = Convert.ToString(d[12]);
                                    DB.IC_UOMs.InsertOnSubmit(uom3);
                                  //  UOMList.Add(um);
                                    DB.SubmitChanges();
                                }
                            }
                            #endregion

                            

                            #region Item
                            IC_Item item = new IC_Item();
                        
                            item.ItemCode1 = string.IsNullOrEmpty(Convert.ToString(d[0])) ? (DB.IC_Items.OrderByDescending(x => x.ItemId).Select(x => x.ItemCode1).FirstOrDefault() + 1) : int.Parse(Convert.ToString(d[0]));
                            item.ItemCode2 = string.IsNullOrEmpty(Convert.ToString(d[1])) ? null : Convert.ToString(d[1]);
                            item.ItemNameAr = Convert.ToString(d[2]);
                            item.ItemNameEn = string.IsNullOrEmpty(Convert.ToString(d[3])) ? "" : Convert.ToString(d[3]);
                            item.Category = cat.CategoryId;
                            item.Company = com.CompanyId;
                            item.PurchasePrice = string.IsNullOrEmpty(Convert.ToString(d[6])) ? 0 : decimal.Parse(Convert.ToString(d[6]));
                            item.SmallUOM = (byte)DB.IC_UOMs.Where(u => u.UOM == Convert.ToString(d[7])).Select(u => u.UOMId).FirstOrDefault();
                            item.SmallUOMPrice = string.IsNullOrEmpty(Convert.ToString(d[8])) ? 0 : decimal.Parse(Convert.ToString(d[8]));

                            if (!string.IsNullOrEmpty(Convert.ToString(d[9])))
                            {
                                item.MediumUOM = (byte)DB.IC_UOMs.Where(u => u.UOM == Convert.ToString(d[9])).Select(u => u.UOMId).FirstOrDefault();
                                item.MediumUOMPrice = string.IsNullOrEmpty(Convert.ToString(d[11])) ? 0 : decimal.Parse(Convert.ToString(d[11]));
                                item.MediumUOMFactor = string.IsNullOrEmpty(Convert.ToString(d[10])) ? "1" : Convert.ToString(d[10]);
                            }

                            if (!string.IsNullOrEmpty(Convert.ToString(d[12])))
                            {
                                item.LargeUOM = (byte)DB.IC_UOMs.Where(u => u.UOM == Convert.ToString(d[12])).Select(u => u.UOMId).FirstOrDefault();
                                item.LargeUOMPrice = string.IsNullOrEmpty(Convert.ToString(d[14])) ? 0 : decimal.Parse(Convert.ToString(d[14]));
                                item.LargeUOMFactor = string.IsNullOrEmpty(Convert.ToString(d[13])) ? "1" : Convert.ToString(d[13]);
                            }

                            item.Length = string.IsNullOrEmpty(Convert.ToString(d[15])) ? 1 : decimal.Parse(Convert.ToString(d[15]));
                            item.Width = string.IsNullOrEmpty(Convert.ToString(d[16])) ? 1 : decimal.Parse(Convert.ToString(d[16]));
                            item.Height = string.IsNullOrEmpty(Convert.ToString(d[17])) ? 1 : decimal.Parse(Convert.ToString(d[17]));

                            if (!string.IsNullOrEmpty(Convert.ToString(d[18])))
                                item.Expiry = (Convert.ToString(d[18]) == "نعم" ? 1 : 0);

                            item.ItemType = (Convert.ToString(d[19]) == "خام" ? (int)ItemType.Inventory : Convert.ToString(d[19]) == "تام" ? (int)ItemType.Assembly : Convert.ToString(d[19]) == "خدمة" ? (int)ItemType.Service : (int)ItemType.Inventory);

                            item.MinQty = string.IsNullOrEmpty(Convert.ToString(d[20])) ? 0 : int.Parse(Convert.ToString(d[20]));
                            item.ReorderLevel = string.IsNullOrEmpty(Convert.ToString(d[21])) ? 0 : int.Parse(Convert.ToString(d[21]));
                            if (!string.IsNullOrEmpty(Convert.ToString(d[22])))
                            {
                                item.ItemEType = Convert.ToString(d[22]);
                                item.ItemECode = Convert.ToString(d[24]);
                            }
                            if (string.IsNullOrEmpty(Convert.ToString(d[4])))
                            {

                                if (DB.IC_Items.Where(x => x.ItemNameAr == item.ItemNameAr).Count() > 0)
                                    continue;
                            }
                            else
                            {
                                if (DB.IC_Items.Where(x => x.ItemNameAr == item.ItemNameAr && x.Category == item.Category
                                                    && item.Company == x.Company && x.Length == item.Length && x.Width == item.Width
                                                    && x.Height == item.Height && item.ItemCode2 == x.ItemCode2).Count() > 0)
                                    continue;
                            }

                            DB.IC_Items.InsertOnSubmit(item);
                            #endregion

                            DB.SubmitChanges();
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الأصناف")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.IC_Items.InsertAllOnSubmit(ItemList);
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الأصناف بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }
    }
}
