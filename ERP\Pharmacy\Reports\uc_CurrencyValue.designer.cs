﻿namespace Reports
{
    partial class uc_CurrencyValue_Reports
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(uc_CurrencyValue_Reports));
            this.lkp_Crnc = new DevExpress.XtraEditors.LookUpEdit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Crnc.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // lkp_Crnc
            // 
            resources.ApplyResources(this.lkp_Crnc, "lkp_Crnc");
            this.lkp_Crnc.EnterMoveNextControl = true;
            this.lkp_Crnc.Name = "lkp_Crnc";
            this.lkp_Crnc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Crnc.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("lkp_Crnc.Properties.AppearanceDisabled.BackColor")));
            this.lkp_Crnc.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("lkp_Crnc.Properties.AppearanceDisabled.ForeColor")));
            this.lkp_Crnc.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.lkp_Crnc.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.lkp_Crnc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Crnc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Crnc.Properties.Buttons"))))});
            this.lkp_Crnc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Crnc.Properties.Columns"), resources.GetString("lkp_Crnc.Properties.Columns1"), ((int)(resources.GetObject("lkp_Crnc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Crnc.Properties.Columns3"))), resources.GetString("lkp_Crnc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Crnc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Crnc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Crnc.Properties.Columns7"), resources.GetString("lkp_Crnc.Properties.Columns8"))});
            this.lkp_Crnc.Properties.NullText = resources.GetString("lkp_Crnc.Properties.NullText");
            this.lkp_Crnc.EditValueChanged += new System.EventHandler(this.lkp_Crnc_EditValueChanged);
            // 
            // uc_CurrencyValue
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.lkp_Crnc);
            this.Name = "uc_CurrencyValue";
            this.Load += new System.EventHandler(this.uc_LinkAccount_Load);
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Crnc.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        public DevExpress.XtraEditors.LookUpEdit lkp_Crnc;
    }
}
