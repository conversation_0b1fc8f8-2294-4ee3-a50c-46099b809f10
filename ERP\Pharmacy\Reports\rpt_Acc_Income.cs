using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Linq;
using DAL;
using BL;
using Pharmacy.Forms;
using DevExpress.XtraEditors;
using System.Windows.Forms;
using System.Data;

namespace Pharmacy.Reports
{
    public partial class rpt_Acc_Income : DevExpress.XtraReports.UI.XtraReport
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        
        byte fltrTyp_Date;
        DateTime date1, date2;
        

        public rpt_Acc_Income(string reportName, string dateFilter, string otherFilters,            
            byte fltrTyp_Date, DateTime date1, DateTime date2)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            RTL.EnCulture(RTL.IsEnglish);
            InitializeComponent();
            if (RTL.IsEnglish)
                RTL.MirrorGridControl(this.grid_openBalance);

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.fltrTyp_Date = fltrTyp_Date;

            this.date1 = date1;
            this.date2 = date2;

            

            getReportHeader();

            LoadData();
        }

        void getReportHeader()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var comp = DB.ST_CompanyInfos.FirstOrDefault();
            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = Utilities.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            
            decimal expenses;
            decimal revenues;
            decimal profit;
            decimal netProfit;
            DataTable dt = HelperAcc.GetIncomeSheet(date1, date2, out expenses, out revenues, out profit, out netProfit);
            grid_openBalance.DataSource = dt;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if(e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle + 1;
        }

        bool LoadPrivilege()
        {
            if (frmMain.LstUserPrvlg != null)
            {
                UserPriv p = frmMain.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_Acc_Income).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }
    }
}
