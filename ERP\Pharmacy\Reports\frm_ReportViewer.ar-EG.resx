﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="barBtn_Help.Caption" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barBtn_Help.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEgAACxIB0t1+/AAAAtpJREFUOE9tkklME2EU
        xxuNR08E48EQY0yM0YNXDh68G64evBiXRBQjJ5YCKYVSNtlFgbJathbKWnYLBLCIskOU3RZoujClnQ4U
        Bgh/3zcFDGqTX6bvvf/7f+97MzIA58hu81wkwvM6vP1Fn4X90oEdFPUJfK7Ra6R82N/6cwEJbpFwvHZM
        RPPMEXp+HqF34VB6srjavA+qt5Iu+B8DSoYWdvOiYeoQ2tE9lAwKyO3gkNXukige8En5holD5HfybtKH
        SAZPkoZkGU1bl7Na3R7ddxEaavxo8iHHuIW5jQPSADPrIrIp/tDHk5GA2q97eNfCzZwZpBtcKZp+H8qH
        d1HYy+M9kWPkYJr3Y+/gGL2zuzQFh/xuL/K7PCgbIl2PF9T3SDJQ6xzLldTMinmMzgBJNRYotStI0W2Q
        4TZNQbQHKKNJ1Hr7gExVbwvKMNhRQhOcFrMYbW6YF/3SFUw0QWaLm+CQwWjm6M3wSKmzeWTKmvU7ar0N
        OTTiaTGd0cRh5McOjo7oTUz5kGbYQmpjADVBO6AJrYcyhdZ6RaG10H2cZ0V1A8OFwTkfRFFE1zgPld4F
        lS5Ass6JtEYn4spXeWkHUcULXrXeAWWtXSom1wcwTXvh9/thHKN91DmhZNQ6oai2QVVnQ4xmaU4yiC5Z
        1Cd8siK+ah2JNXYSOZBI9E1sQxAEtI26KU85QlFtR1zlOmI1S4gtXUqVDORlyyFv82ZEhXYTMWUWJFRt
        SmLDtA8dCztopB2wOL5yAzGlv8jAgsiCWS6+YvWSZEBFmmLhVUTO5LG8worYciuiNWuIJTN5BZ1GzyiK
        5ZSPKllBRPbkAY1/n/WdGTBoF2Gvs8aFyIJ5MrHQlTaITTqRnbxGjVN4kztlo8Punfaw34WTP9eJu0FX
        bzx8oTINh2d+8z1VjeC52oxnKV/wMt3sfRxV10OaB0Qo00o97Hs+dTsxYoXbxE3i2n/i4D96h+w3curF
        81pcic0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="barBtn_Open.Caption" xml:space="preserve">
    <value>فتح</value>
  </data>
  <data name="barBtn_Open.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEgAACxIB0t1+/AAAAU9JREFUOE9j+P//P0UY
        qyApmKFx+eOF/Wvv/kfH9gnzGLFpQMcM5XNv/X/9/d//Hbe/wfGW658xDETG0fW72VAMmHXk4/9Je7+h
        4JXnEXjJaQhecOLb/9Z1z/8ntB6SBGkGupKVoWzOzf+9O178b17z7H/JrCsEcfn8m/9d05cmADU7A7Es
        0IAb/9s3PPpfNu8mGH/89R/FO/gw0ABfhtLZN/6Xz7sINn3hiS9A13wmCtctf/QzqeOoHtCA6//zppz4
        P2Pvq/892z7+79pKHM6beukYOBBLZ13/n9Z94P/8Yx//t298TxRuWPn8f2r3yTSwASWzrv2fsOXR/+4t
        7/83r31HFM6beuUlPBpBBiw+8eF/58Z3//u2fyCIe7e++5/Re2Yl3ACf9Emx1Qtv/s2ZfJkonNF37pei
        nrMx3AAgsCAHww2AMcjFWAWJx/8ZAEYM2PK7ULaRAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="barBtnClose.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEwAACxMBAJqcGAAAAzJJREFUOE9tkGtMknEY
        xd8yIjNJRE1nIOlqucL5Qde9NhNBEJEVrizLMqulI7WWecvVWrHMmpYmrszSmrrUpFVaKRq6ghdTaRkS
        dAHL7LYuX2zVTi/U6Pps57+z5znn9+FPACAqYzmEShRE1MbPItQJbEKT4DePlDFTTHJmtkXulT0g80rp
        kfmFXpPNJOokQUQVlXV0HN0/AOclXKJJHLBRmxxKmgrixp+Xbfs6Wrb926MCyfidDaE6dZz/+to4zv8B
        lUI2/ZyYqyJzpXh1oQgfW0vwqVnp1AfKO3aG3HjUxgVVUFnaH4ByAZuoFHCU9/IleFOdibHyzRg7tgYv
        GpUYbTiMlyVrqF0qXlO3vvx4qIScg46OC1C6MjCiMy3ys/1oIh4XxeDxKQVsnQ0YI2/iFdlO+UZYKzKo
        Gx/24kTc2hIxXhodGO4C1IiDSod2L4UpOxLm0q0Ytdtga1BipL0GI23VsDUewajtGYaPpzozQ7uW4KyY
        U+wCqOWzyAfpYTCmh2PkPgnrmRwMyBl4qq7Ek8vl6Ke8tToPdqMOxh1heJDBQ+tq7h0XoC3B33x3FQsD
        hVLYDbehTwqEXjYd1pZTsDSdgE7GgH4dG/Y+LfrzxHBkr0tnDLkA18RMcwd/Msj8VbB2tEAjcEeX2BN6
        xQroFMucXiOcCqumFfq9UnTw6bgqYv4CqGO9e7pF09CzORwWvRZdiVx0Cj3QIZz2Ux7oXhsMK6mFNoWH
        2yJPqIXeXS5AQwzrgEEegB7BFDysL8PgpSpoRAx082nojqZBI/aCsbkaQ3Ul6BXQ0Udl6/msQhegJmr6
        7MFNc98PJAfDsD4E5qsXYGxrgu5oJvQlWTC2X4b5ynkYkrgYTA6BfsPct6rljGAXQDCTRuREMDMsmYvx
        ULGA+ulIDJ/Mgrm5ipIKw2WZ1C4CJsVCmHYuwuko1nZlpLuz++P5MYxNYb4Xh3NjMVachCf7E2DZJ3HK
        4R07U64AyTyfE/SJBOHuNuEfAJ3SnDDfKdnHpbyu3hzJO9OhpC8O9e6RvDkWz7vB86GnURmWM03N34Df
        x82HRnDnMycJw5mTYvwnE2zaz8PvA4D4Do7rKQv5AfG9AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1079, 24</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 647</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1079, 0</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 623</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1079, 24</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 623</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1079, 647</value>
  </data>
  <data name="txtDescription.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 24</value>
  </data>
  <data name="txtDescription.Size" type="System.Drawing.Size, System.Drawing">
    <value>8, 160</value>
  </data>
  <data name="groupControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>12, 186</value>
  </data>
  <data name="groupControl3.Text" xml:space="preserve">
    <value>الوصف</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="xtraScrollableControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="xtraScrollableControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 23</value>
  </data>
  <data name="xtraScrollableControl1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="xtraScrollableControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>715, 580</value>
  </data>
  <data name="groupControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 30</value>
  </data>
  <data name="groupControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>745, 605</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="groupControl2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="groupControl2.Text" xml:space="preserve">
    <value>الفلاتر</value>
  </data>
  <data name="lstBxReports.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 24</value>
  </data>
  <data name="lstBxReports.Size" type="System.Drawing.Size, System.Drawing">
    <value>208, 579</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>761, 30</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>212, 605</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>التقارير</value>
  </data>
  <data name="NBG_Dept.Caption" xml:space="preserve">
    <value>الأقسام</value>
  </data>
  <data name="NBI_IC.Caption" xml:space="preserve">
    <value>المخازن</value>
  </data>
  <data name="NBI_SL.Caption" xml:space="preserve">
    <value>المبيعات</value>
  </data>
  <data name="NBI_PR.Caption" xml:space="preserve">
    <value>المشتريات</value>
  </data>
  <data name="NBI_Acc.Caption" xml:space="preserve">
    <value>الحسابات</value>
  </data>
  <data name="NBI_Special.Caption" xml:space="preserve">
    <value>تقارير خاصة</value>
  </data>
  <data name="NBI_HR.Caption" xml:space="preserve">
    <value>الموارد البشرية</value>
  </data>
  <data name="navBarControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>979, 30</value>
  </data>
  <data name="resource.ExpandedWidth" type="System.Int32, mscorlib">
    <value>98</value>
  </data>
  <data name="navBarControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 603</value>
  </data>
  <data name="barDockControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 647</value>
  </data>
  <data name="barDockControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>1079, 0</value>
  </data>
  <data name="barDockControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 647</value>
  </data>
  <data name="barDockControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 647</value>
  </data>
  <data name="barDockControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1079, 0</value>
  </data>
  <data name="barDockControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1079, 0</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>مركز التقارير</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="lkpVenGroup.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpVenGroup.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtQC.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="lkpEmpGroup.Properties.Columns1" xml:space="preserve">
    <value>المجموعة</value>
  </data>
  <data name="lkpEmpGroup.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpEmpGroup.TabIndex" type="System.Int32, mscorlib">
    <value>39</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="chkLstInvBooks.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chkLstInvBooks.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="chkLstInvBooks.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Properties.Items3" xml:space="preserve">
    <value>كلا من</value>
  </data>
  <data name="cmbFltrTyp_InvBook.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_InvBook.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="txtSellPrice2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="txtSellPrice2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtSellPrice2.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="lkpCategory.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpCategory.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_Cat.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_Cat.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_Cat.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_Cat.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtSellPrice1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="txtSellPrice1.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="txtSellPrice1.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="txtSellPrice1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtSellPrice1.TabIndex" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items6" xml:space="preserve">
    <value>يتراوح بين</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="lkpJoDept.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpJoDept.TabIndex" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="lkpJoState.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpJoState.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="lkpJoPriority.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpJoPriority.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="txtWidth.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtWidth.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="cmbItemType.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbItemType.Properties.Items3" xml:space="preserve">
    <value>مخزني</value>
  </data>
  <data name="cmbItemType.Properties.Items4" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbItemType.Properties.Items6" xml:space="preserve">
    <value>خدمة</value>
  </data>
  <data name="cmbItemType.Properties.Items7" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbItemType.Properties.Items9" xml:space="preserve">
    <value>منتج</value>
  </data>
  <data name="cmbItemType.Properties.Items10" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="cmbItemType.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbItemType.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="lkpAccount.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpAccount.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="txtLength.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtLength.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="lkpMtrxParentId.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpMtrxParentId.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lkpCustGroup.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpCustGroup.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="txtBatch.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="txtBatch.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="lkpSalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns1" xml:space="preserve">
    <value>الموظف</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns2" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns3" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkpSalesEmp.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="lkpUser.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpUser.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="cmbFltrTyp_User.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_User.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_User.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_User.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="lkpCustomList.Properties.Columns8" xml:space="preserve">
    <value>اسم قائمة الحسابات</value>
  </data>
  <data name="lkpCustomList.Properties.Columns15" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="lkpCustomList.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpCustomList.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="txtHeight.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtHeight.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="cmbProcess.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbProcess.Properties.Items3" xml:space="preserve">
    <value>فاتورة شراء</value>
  </data>
  <data name="cmbProcess.Properties.Items6" xml:space="preserve">
    <value>فاتورة بيع</value>
  </data>
  <data name="cmbProcess.Properties.Items9" xml:space="preserve">
    <value>مردود شراء</value>
  </data>
  <data name="cmbProcess.Properties.Items12" xml:space="preserve">
    <value>مردود بيع</value>
  </data>
  <data name="cmbProcess.Properties.Items15" xml:space="preserve">
    <value>تسويه اضافه</value>
  </data>
  <data name="cmbProcess.Properties.Items18" xml:space="preserve">
    <value>تسويه خصم</value>
  </data>
  <data name="cmbProcess.Properties.Items21" xml:space="preserve">
    <value>تالف/هالك</value>
  </data>
  <data name="cmbProcess.Properties.Items24" xml:space="preserve">
    <value>نقل من</value>
  </data>
  <data name="cmbProcess.Properties.Items27" xml:space="preserve">
    <value>نقل الي</value>
  </data>
  <data name="cmbProcess.Properties.Items30" xml:space="preserve">
    <value>رصيد افتتاحي</value>
  </data>
  <data name="cmbProcess.Properties.Items33" xml:space="preserve">
    <value>التشغيل</value>
  </data>
  <data name="cmbProcess.Properties.Items36" xml:space="preserve">
    <value>اذن اضافة</value>
  </data>
  <data name="cmbProcess.Properties.Items39" xml:space="preserve">
    <value>اذن صرف</value>
  </data>
  <data name="cmbProcess.Properties.Items42" xml:space="preserve">
    <value>أمر بيع</value>
  </data>
  <data name="cmbProcess.Properties.Items45" xml:space="preserve">
    <value>مراقبة جودة</value>
  </data>
  <data name="cmbProcess.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbProcess.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="lkpCostCenters.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns8" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns10" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="lkpCostCenters.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpCostCenters.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="lkpCustomer2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpCustomer2.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items6" xml:space="preserve">
    <value>يتراوح مابين</value>
  </data>
  <data name="cmbFltrTyp_Customer.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_Customer.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="lkpCustomer1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpCustomer1.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="cmbFltrTyp_Comp.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_Comp.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_Comp.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_Comp.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items6" xml:space="preserve">
    <value>يتراوح مابين</value>
  </data>
  <data name="cmbFltrTyp_Vendor.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_Vendor.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items6" xml:space="preserve">
    <value>يتراوح مابين</value>
  </data>
  <data name="cmbFltrTyp_Store.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_Store.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="dtExpDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtExpDate.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="dtExpDate.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="lkpVendor2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpVendor2.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="lkpStore2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpStore2.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items6" xml:space="preserve">
    <value>يتراوح مابين</value>
  </data>
  <data name="cmbFltrTyp_Date.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_Date.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="lkpItem2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpItem2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items6" xml:space="preserve">
    <value>يتراوح مابين</value>
  </data>
  <data name="cmbFltrTyp_Item.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="cmbFltrTyp_Item.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lkpItem1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpItem1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lkpVendor1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpVendor1.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="dt1.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="dt1.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="lkpStore1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpStore1.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="dt2.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dt2.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dt2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="dt2.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="lkpCompany.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpCompany.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="cmbFltrTyp_Car.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="cmbFltrTyp_Car.Properties.Items3" xml:space="preserve">
    <value>يساوي</value>
  </data>
  <data name="cmbFltrTyp_Car.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpCars.Properties.Columns8" xml:space="preserve">
    <value>السيارة</value>
  </data>
  <data name="lkpCars.Properties.Columns10" xml:space="preserve">
    <value>رقم اللوحة</value>
  </data>
  <data name="lkpCars.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="layoutControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 3</value>
  </data>
  <data name="layoutControl1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="layoutControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>687, 577</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 6</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>13, 13</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>م3</value>
  </data>
  <data name="lkpMtrx3.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpMtrx3.Size" type="System.Drawing.Size, System.Drawing">
    <value>164, 20</value>
  </data>
  <data name="lkpMtrx3.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn27.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn27.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn26.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn26.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>171, 29</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>البند</value>
  </data>
  <data name="lkpMtrxD3.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpMtrxD3.Size" type="System.Drawing.Size, System.Drawing">
    <value>164, 20</value>
  </data>
  <data name="lkpMtrxD3.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn51.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn51.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn50.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn50.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 8</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>13, 13</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>م2</value>
  </data>
  <data name="lkpMtrx2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpMtrx2.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 20</value>
  </data>
  <data name="lkpMtrx2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn25.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn25.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn24.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn24.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>146, 31</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>البند</value>
  </data>
  <data name="lkpMtrxD2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpMtrxD2.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 20</value>
  </data>
  <data name="lkpMtrxD2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn48.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn48.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn47.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn47.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lblM1.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 6</value>
  </data>
  <data name="lblM1.Size" type="System.Drawing.Size, System.Drawing">
    <value>13, 13</value>
  </data>
  <data name="lblM1.Text" xml:space="preserve">
    <value>م1</value>
  </data>
  <data name="lkpMtrx1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpMtrx1.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 20</value>
  </data>
  <data name="lkpMtrx1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn45.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn45.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn44.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn44.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lblM1D.Location" type="System.Drawing.Point, System.Drawing">
    <value>115, 29</value>
  </data>
  <data name="lblM1D.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="lblM1D.Text" xml:space="preserve">
    <value>البند</value>
  </data>
  <data name="lkpMtrxD1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpMtrxD1.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 20</value>
  </data>
  <data name="lkpMtrxD1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn23.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn23.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn22.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn42.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn42.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn39.Caption" xml:space="preserve">
    <value>فئة المورد</value>
  </data>
  <data name="gridColumn39.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn43.Caption" xml:space="preserve">
    <value>VendorGroupId</value>
  </data>
  <data name="gridColumn40.Caption" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="gridColumn37.Caption" xml:space="preserve">
    <value>الحالة</value>
  </data>
  <data name="gridColumn34.Caption" xml:space="preserve">
    <value>الاهمية</value>
  </data>
  <data name="col_AcNumber.Caption" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="col_AcNumber.Width" type="System.Int32, mscorlib">
    <value>323</value>
  </data>
  <data name="col_AcNameAr.Caption" xml:space="preserve">
    <value>الحساب</value>
  </data>
  <data name="col_AcNameAr.Width" type="System.Int32, mscorlib">
    <value>739</value>
  </data>
  <data name="gridColumn30.Caption" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn20.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn17.Caption" xml:space="preserve">
    <value>اسم الفئة</value>
  </data>
  <data name="gridColumn17.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn18.Caption" xml:space="preserve">
    <value>اسم المستخدم</value>
  </data>
  <data name="gridColumn14.Caption" xml:space="preserve">
    <value>كود العميل</value>
  </data>
  <data name="gridColumn14.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn15.Caption" xml:space="preserve">
    <value>اسم العميل</value>
  </data>
  <data name="gridColumn15.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn16.Caption" xml:space="preserve">
    <value>اسم العميل ج</value>
  </data>
  <data name="gridColumn16.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn13.Caption" xml:space="preserve">
    <value>كود العميل</value>
  </data>
  <data name="gridColumn13.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn13.Width" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>اسم العميل</value>
  </data>
  <data name="gridColumn12.Width" type="System.Int32, mscorlib">
    <value>162</value>
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>اسم العميل ج</value>
  </data>
  <data name="gridColumn11.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn11.Width" type="System.Int32, mscorlib">
    <value>162</value>
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>كود المورد</value>
  </data>
  <data name="gridColumn10.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn9.Caption" xml:space="preserve">
    <value>اسم المورد</value>
  </data>
  <data name="gridColumn9.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>اسم المورد ج</value>
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn3.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn3.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>اسم الفرع/ المخزن</value>
  </data>
  <data name="gridColumn4.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="colItemCode11.Caption" xml:space="preserve">
    <value>كود1</value>
  </data>
  <data name="colItemCode11.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn36.Caption" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="gridColumn36.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="colItemNameAr1.Caption" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="colItemNameAr1.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colItemNameEn1.Caption" xml:space="preserve">
    <value>اسم الصنف ج</value>
  </data>
  <data name="colItemNameEn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="colItemCode1.Caption" xml:space="preserve">
    <value>كود1</value>
  </data>
  <data name="colItemCode1.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colItemCode2.Caption" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="colItemCode2.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="colItemNameAr.Caption" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="colItemNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colItemNameEn.Caption" xml:space="preserve">
    <value>اسم الصنف ج</value>
  </data>
  <data name="colItemNameEn.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>كود المورد</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>اسم المورد</value>
  </data>
  <data name="gridColumn6.Width" type="System.Int32, mscorlib">
    <value>162</value>
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>اسم المورد ج</value>
  </data>
  <data name="gridColumn5.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn5.Width" type="System.Int32, mscorlib">
    <value>162</value>
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>اسم الفرع/ المخزن</value>
  </data>
  <data name="gridColumn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="comp1.CustomizationFormText" xml:space="preserve">
    <value>الشركه</value>
  </data>
  <data name="comp1.Text" xml:space="preserve">
    <value>مجموعة الصنف</value>
  </data>
  <data name="item1.Text" xml:space="preserve">
    <value>الصنف</value>
  </data>
  <data name="date1.CustomizationFormText" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="date1.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="store1.CustomizationFormText" xml:space="preserve">
    <value>المخزن</value>
  </data>
  <data name="store1.Text" xml:space="preserve">
    <value>الفرع/ المخزن</value>
  </data>
  <data name="vendor1.CustomizationFormText" xml:space="preserve">
    <value>المورد</value>
  </data>
  <data name="vendor1.Text" xml:space="preserve">
    <value>المورد</value>
  </data>
  <data name="expDate1.CustomizationFormText" xml:space="preserve">
    <value>التاريخ اقل من</value>
  </data>
  <data name="expDate1.Text" xml:space="preserve">
    <value>التاريخ اقل من</value>
  </data>
  <data name="lblFltrName.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem2</value>
  </data>
  <data name="lblFltrName.Text" xml:space="preserve">
    <value>colName</value>
  </data>
  <data name="lblTo.CustomizationFormText" xml:space="preserve">
    <value>الي</value>
  </data>
  <data name="lblTo.Text" xml:space="preserve">
    <value>الي</value>
  </data>
  <data name="lblFrom.CustomizationFormText" xml:space="preserve">
    <value>من</value>
  </data>
  <data name="lblFrom.Text" xml:space="preserve">
    <value>من</value>
  </data>
  <data name="lblFltrType.CustomizationFormText" xml:space="preserve">
    <value>نوع الفلتر</value>
  </data>
  <data name="lblFltrType.Text" xml:space="preserve">
    <value>نوع الفلتر</value>
  </data>
  <data name="Customer1.CustomizationFormText" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="Customer1.Text" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="process1.CustomizationFormText" xml:space="preserve">
    <value>نوع العملية</value>
  </data>
  <data name="process1.Text" xml:space="preserve">
    <value>نوع العملية</value>
  </data>
  <data name="cost1.CustomizationFormText" xml:space="preserve">
    <value>مركز التكلفة</value>
  </data>
  <data name="cost1.Text" xml:space="preserve">
    <value>مركز التكلفة</value>
  </data>
  <data name="acc1.CustomizationFormText" xml:space="preserve">
    <value>الحساب</value>
  </data>
  <data name="acc1.Text" xml:space="preserve">
    <value>الحساب</value>
  </data>
  <data name="cstmLst1.Text" xml:space="preserve">
    <value>قائمة حسابات مخصصة</value>
  </data>
  <data name="user1.CustomizationFormText" xml:space="preserve">
    <value>المستخدم</value>
  </data>
  <data name="user1.Text" xml:space="preserve">
    <value>المستخدم</value>
  </data>
  <data name="salesEmp1.Text" xml:space="preserve">
    <value>الموظف</value>
  </data>
  <data name="Batch1.Text" xml:space="preserve">
    <value>التشغيلة</value>
  </data>
  <data name="custGroup1.Text" xml:space="preserve">
    <value>فئة العميل</value>
  </data>
  <data name="ParentItem3.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem8</value>
  </data>
  <data name="dim4.Text" xml:space="preserve">
    <value>الطول</value>
  </data>
  <data name="dim2.Text" xml:space="preserve">
    <value>الارتفاع</value>
  </data>
  <data name="dim3.Text" xml:space="preserve">
    <value>العرض</value>
  </data>
  <data name="ParentItem1.Text" xml:space="preserve">
    <value>صنف رئيسي</value>
  </data>
  <data name="Mtrx0.Text" xml:space="preserve">
    <value>المصفوفة</value>
  </data>
  <data name="dim1.Text" xml:space="preserve">
    <value>الابعاد</value>
  </data>
  <data name="jo1.Text" xml:space="preserve">
    <value>امر عمل</value>
  </data>
  <data name="jo2.Text" xml:space="preserve">
    <value>الاهمية</value>
  </data>
  <data name="jo3.Text" xml:space="preserve">
    <value>الحالة</value>
  </data>
  <data name="jo4.Text" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="itmTyp1.Text" xml:space="preserve">
    <value>نوع الصنف</value>
  </data>
  <data name="sell1.CustomizationFormText" xml:space="preserve">
    <value>Sell Price</value>
  </data>
  <data name="sell1.Text" xml:space="preserve">
    <value>سعر البيع</value>
  </data>
  <data name="sell2.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="sell3.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem2</value>
  </data>
  <data name="sell4.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem3</value>
  </data>
  <data name="cat1.CustomizationFormText" xml:space="preserve">
    <value>فئة الصنف</value>
  </data>
  <data name="cat1.Text" xml:space="preserve">
    <value>فئة الصنف</value>
  </data>
  <data name="InvoiceBook1.CustomizationFormText" xml:space="preserve">
    <value>دفاتر الفواتير</value>
  </data>
  <data name="InvoiceBook1.Text" xml:space="preserve">
    <value>دفاتر الفواتير</value>
  </data>
  <data name="EmpGroup4.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem5</value>
  </data>
  <data name="EmpGroup1.Text" xml:space="preserve">
    <value>مجموعة الموظف</value>
  </data>
  <data name="EmpGroup2.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="EmpGroup3.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem2</value>
  </data>
  <data name="QC1.Text" xml:space="preserve">
    <value>م.ج</value>
  </data>
  <data name="VenGroup1.CustomizationFormText" xml:space="preserve">
    <value>فئة المورد</value>
  </data>
  <data name="VenGroup1.Text" xml:space="preserve">
    <value>فئة المورد</value>
  </data>
  <data name="cat5.CustomizationFormText" xml:space="preserve">
    <value>السيارات</value>
  </data>
  <data name="cat5.Text" xml:space="preserve">
    <value>السيارات</value>
  </data>
  <data name="memoEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 24</value>
  </data>
  <data name="memoEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>9, 3</value>
  </data>
  <data name="xtraScrollableControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 24</value>
  </data>
  <data name="xtraScrollableControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>702, 600</value>
  </data>
  <data name="imageListBoxControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 24</value>
  </data>
  <data name="imageListBoxControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>196, 602</value>
  </data>
</root>