﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using DevExpress.XtraPrinting;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;

namespace Pharmacy.Forms
{
    public partial class frm_E_StoreCode : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();
        DataTable dt_Details = new DataTable();
        string taxNumber = "";
        public frm_E_StoreCode()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_E_StoreCode_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            BindDataSources();
            LoadStores();
       
        }


        private void BindDataSources()
        {

            #region dt_Details
            dt_Details.Columns.Clear();
            dt_Details.Columns.Add("StoreId");
            dt_Details.Columns.Add("StoreCode");
            dt_Details.Columns.Add("StoreNameAr");
            dt_Details.Columns.Add("StoreNameEn");
            dt_Details.Columns.Add("ECode");
            dt_Details.Columns.Add("Address");
            //dt_Details.Columns.Add("Company");
            //dt_Details.Columns.Add("ItemEType");
            //dt_Details.Columns.Add("ItemECode");
            grdStoreCodes.DataSource = dt_Details;
            #endregion
        }
        private void LoadStores()
        {

            var lstItems = (from i in DB.IC_Stores
                            select new
                            {
                                i.StoreId,
                                i.StoreCode,
                                i.StoreNameAr,
                                i.StoreNameEn,
                                i.ECode,
                                i.Address
                            }).ToList();


            dt_Details.Rows.Clear();

            if (lstItems.Count() > 0)
            {

                foreach (var item in lstItems)
                {
                    DataRow row = dt_Details.NewRow();
                    row["StoreId"] = item.StoreId;
                    row["StoreCode"] = item.StoreCode;
                    row["StoreNameAr"] = item.StoreNameAr;
                    row["StoreNameEn"] = item.StoreNameEn;
                    row["ECode"] = item.ECode;
                    row["Address"] = item.Address;
                    //row["Company"] = item.CompanyNameAr;
                    //row["ItemEType"] = item.ItemEType;
                    //row["ItemECode"] = item.ItemECode;
                    dt_Details.Rows.Add(row);
                }

            }
            dt_Details.AcceptChanges();

        }
        private void barBtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                SaveData();
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgSave : ResSLAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        private void SaveData()
        {
            for (int x = 0; x < dt_Details.Rows.Count; x++)
            {
                IC_Store store;
                #region PrInvDetails
                if (dt_Details.Rows[x].RowState == DataRowState.Deleted)
                    continue;
                var c = dt_Details.Rows[x].Table;
                if (dt_Details.Rows[x]["StoreId"] != DBNull.Value)
                {

                    store = DB.IC_Stores.FirstOrDefault(a=>a.StoreId==Convert.ToInt32(dt_Details.Rows[x]["StoreId"]));    
                    if (dt_Details.Rows[x]["ECode"] != null)
                        store.ECode = dt_Details.Rows[x]["ECode"].ToString();
               
                    DB.SubmitChanges();
                }

                #endregion
            }
           
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, //"حذف صف ؟"
                    Shared.IsEnglish == true ? ResICEn.MsgTQues : ResICAr.MsgTQues,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;

                GridView view = sender as GridView;

                if (view.GetFocusedRowCellValue(col_StoreId) == null)
                    return;
                else
                    view.DeleteRow(view.FocusedRowHandle);
            }
        }

      
        private void gvPriority_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }


        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void frm_E_ItemCodes_FormClosed(object sender, FormClosedEventArgs e)
        {
            this.Close();
        }
    }
}
