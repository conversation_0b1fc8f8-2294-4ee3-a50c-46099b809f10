﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_CustomerTotal_Invoices : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int store_id1, store_id2, CustomerId1, CustomerId2, custGroupId, salesEmpId;

        string custGroupAccNumber;

        byte FltrTyp_Store, fltrTyp_Date, FltrTyp_Customer, FltrTyp_InvBook;
        DateTime date1, date2;

        List<int> lst_invBooksId = new List<int>();

        private void btn_Landscape_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void btn_Portrait_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        public frm_SL_CustomerTotal_Invoices(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Store, int store_id1, int store_id2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2, int custGroupId, string custGroupAccNumber,
                int salesEmpId, byte FltrTyp_InvBook, string InvBooks)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Store = fltrTyp_Store;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;

            this.store_id1 = store_id1;
            this.store_id2 = store_id2;

            this.date1 = date1;
            this.date2 = date2;

            this.CustomerId1 = customerId1;
            this.CustomerId2 = customerId2;
            this.salesEmpId = salesEmpId;
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();

            LoadData();

            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));

            ERPDataContext DB = new DAL.ERPDataContext();


            rep_CategoryId.DataSource = DB.SL_CustomerGroups;
            rep_CategoryId.ValueMember = "CustomerGroupId";
            rep_CategoryId.DisplayMember = Shared.IsEnglish ? "CGNameEn" : "CGNameAr";
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //grdCategory.MinimumSize = grdCategory.Size;
            //new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
            //        lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            //grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            try
            {
                var data = (from c in DB.SL_Invoices
                            join v in DB.SL_Customers on c.CustomerId equals v.CustomerId
                            //join a in DB.ACC_Accounts on v.AccountId equals a.AccountId
                            //where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                            where FltrTyp_Customer == 1 ? v.CustomerId == CustomerId1 : true
                            where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 != 0) ?
                            v.CustomerId >= CustomerId1 && v.CustomerId <= CustomerId2 : true
                            where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 == 0) ?
                            v.CustomerId >= CustomerId1 : true
                            where (FltrTyp_Customer == 2 && CustomerId1 == 0 && CustomerId2 != 0) ?
                            v.CustomerId <= CustomerId2 : true

                            where fltrTyp_Date == 1 ? c.InvoiceDate.Date == date1.Date : true
                            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                            c.InvoiceDate.Date >= date1.Date && c.InvoiceDate.Date <= date2.Date : true
                            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                            c.InvoiceDate.Date >= date1.Date : true
                            where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                            c.InvoiceDate.Date <= date2.Date : true

                            where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                 x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(c.StoreId)// c.StoreId == Shared.user.DefaultStore
                            where Shared.user.AccessOtherUserTrns ? true : c.UserId == Shared.UserId

                            //orderby c.InvoiceDate
                            group c by new { c.CustomerId, Name = Shared.IsEnglish ? v.CusNameEn : v.CusNameAr, v.CategoryId, v.City, v.IdRegion } into grp
                            //let custGroup = DB.SL_Group_Customers
                            select new
                            {
                                DiscountRatio = grp.ToList().DefaultIfEmpty().Sum(x => x.DiscountRatio * x.CrncRate),
                                DiscountValue = grp.ToList().DefaultIfEmpty().Sum(x => x.DiscountValue * x.CrncRate),
                                Expenses = grp.ToList().DefaultIfEmpty().Sum(x => x.Expenses),
                                Net = grp.ToList().DefaultIfEmpty().Sum(x => x.Net * x.CrncRate),
                                Paid = grp.ToList().DefaultIfEmpty().Sum(x => x.Paid * x.CrncRate),
                                Remains = grp.ToList().DefaultIfEmpty().Sum(x => x.Remains * x.CrncRate),
                                Total = Math.Round(grp.Select(x => (x.Net + x.DiscountValue +
                                x.CustomTaxValue + x.AddTaxValue + x.DeductTaxValue + x.RetentionValue + x.AdvancePaymentValue -
                                x.Expenses - x.HandingValue - x.TransportationValue - x.ShiftAdd) * x.CrncRate).ToList().Sum(x => x.HasValue ? (double)x.Value : 0), 3, MidpointRounding.AwayFromZero),
                                CustomerId = grp.Key.Name,
                                CustId = grp.Key.CustomerId,
                                grp.Key.CategoryId,
                                TotalCostPrice = grp.ToList().DefaultIfEmpty().Sum(x => x.TotalCostPrice),
                                Profit = grp.ToList().DefaultIfEmpty().Sum(x => x.Net - x.TotalCostPrice),
                                profitRatio = grp.ToList().DefaultIfEmpty().Sum(x => x.TotalCostPrice) == 0 ? 1 : (grp.Sum(x => x.Net) > 0 ? (grp.Sum(x => x.Net - x.TotalCostPrice)) / grp.Sum(x => x.Net) : 0),
                                ProfitCostRatio = grp.ToList().DefaultIfEmpty().Sum(x => x.TotalCostPrice) == 0 ? 1 : (grp.Sum(x => x.TotalCostPrice) > 0 ? (grp.Sum(x => x.Net - x.TotalCostPrice)) / grp.Sum(x => x.TotalCostPrice) : 0),

                                City = grp.Key.City,
                                //Area = DB.SL_CustomerRegions.Where(x => x.IdRegion == grp.Key.IdRegion).Select(x => x.RegionName).FirstOrDefault()
                            }).Distinct().ToList().OrderByDescending(x => x.Net);

                grdCategory.DataSource = data;

            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;
        }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_CustomerTotal_Invoices).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }



    }
}