﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_ItemsSalesTotals : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, storeId1, storeId2;
        byte FltrTyp_item, fltrTyp_Date, FltrTyp_Customer, FltrTyp_Category, FltrTyp_InvBook, FltrTyp_Store;
        DateTime date1, date2;

        byte FltrTyp_Company;
        int companyId;

        int customerId1, customerId2, custGroupId, salesEmpId, EmpGroupId;
        string custGroupAccNumber;

        private void btn_Landscape_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void btn_Portrait_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        string categoryNum;
        bool UsingTaxRatio = false;

        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();

        public frm_SL_ItemsSalesTotals(
            string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            int custGroupId, string custGroupAccNumber,
            byte FltrTyp_Category, string categoryNum, int salesEmpId,
            byte FltrTyp_InvBook, string InvBooks, int EmpGroupId, bool UsingTaxRatio,
            byte _fltrTyp_Store, int _storeId1, int _storeId2, byte FltrTyp_Company, int companyId)
        {
            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_Category = FltrTyp_Category;
            this.FltrTyp_Store = _fltrTyp_Store;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;
            this.date1 = date1;
            this.date2 = date2;
            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;
            this.categoryNum = categoryNum;
            this.salesEmpId = salesEmpId;
            this.EmpGroupId = EmpGroupId;

            this.UsingTaxRatio = UsingTaxRatio;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            this.storeId1 = _storeId1;
            this.storeId2 = _storeId2;

            getReportHeader();
            LoadData();
            ReportsUtils.ColumnChooser(grdCategory);
        }


        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            if (Shared.st_Store.UseLengthDimension)
                col_length.Visible = true;
            if (Shared.st_Store.UseHeightDimension)
                col_height.Visible = true;
            if (Shared.st_Store.UseWidthDimension)
                col_width.Visible = true;

            col_LibraQty.OptionsColumn.ShowInCustomizationForm = col_Kg_Weight_Libra.OptionsColumn.ShowInCustomizationForm = Shared.LibraAvailabe;
            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //grdCategory.MinimumSize = grdCategory.Size;
            //new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
            //        lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            //grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            ERPDataContext DB = new ERPDataContext();

            var stores = DB.IC_Stores.ToList();
            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= storeId2 && store.StoreId >= storeId1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (storeId1 > 0 && (store.StoreId == storeId1 || store.ParentId == storeId1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }
            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var sales_data = (
                from c in DB.SL_Customers
                join a in DB.ACC_Accounts
                on c.AccountId equals a.AccountId
                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                c.CustomerId >= customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                c.CustomerId <= customerId2 : true

                join i in DB.SL_Invoices on c.CustomerId equals i.CustomerId
                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true

                where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId
                where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate >= date1 && i.InvoiceDate <= date2 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                i.InvoiceDate >= date1 : true
                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate <= date2 : true

                join hh in DB.HR_Employees on i.SalesEmpId equals hh.EmpId into hq
                from xx in hq.DefaultIfEmpty().Where(x => EmpGroupId == 0 ? true : x.GroupId == EmpGroupId)

                join s in DB.SL_InvoiceDetails on i.SL_InvoiceId equals s.SL_InvoiceId

                where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                s.ItemId >= itemId1 : true
                where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                s.ItemId <= itemId2 : true

                join t in DB.IC_Items on s.ItemId equals t.ItemId
                where FltrTyp_Company == 1 ? t.Company == companyId : true

                join g in DB.IC_Categories on t.Category equals g.CategoryId
                where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true
           
                select s).ToList();

            var sales_data1 = (from s in sales_data
                               group s by new { s.ItemId, SalesTaxRatio = UsingTaxRatio ? s.SalesTaxRatio : 0 } into grp
                               from t in DB.IC_Items.Where(x => x.ItemId == grp.Key.ItemId)
                               join g in DB.IC_Categories on t.Category equals g.CategoryId
                               where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                               join p in DB.IC_Companies on t.Company equals p.CompanyId
                               let Category = Shared.IsEnglish ? DB.IC_Categories.Where(a => a.CategoryId == t.Category).FirstOrDefault().CategoryNameEn : DB.IC_Categories.Where(a => a.CategoryId == t.Category).FirstOrDefault().CategoryNameAr
                               let MediumUOMFactor = MyHelper.FractionToDouble(t.MediumUOMFactor)
                               let LargeUOMFactor = MyHelper.FractionToDouble(t.LargeUOMFactor)
                               let smallQty = grp.Select(x => new { Qty = MyHelper.CalculateUomQty(x.Qty, x.UOMIndex, MediumUOMFactor, LargeUOMFactor) }).Select(x => x.Qty).Sum()
                               select new
                               {
                                   t.ItemCode1,
                                   t.ItemCode2,
                                   t.ItemNameAr,
                                   t.ItemId,
                                   Qty = smallQty,
                                   Category,
                                   DiscountValue = grp.Select(x => x.DiscountValue).Sum(),
                                   SalesTax = grp.Select(x => x.SalesTax).Sum(),
                                   SalesTaxRatio = grp.Key.SalesTaxRatio != 0 ? grp.Key.SalesTaxRatio : (t.SalesTaxRatio / 100),
                                   TotalBeforeDisc = grp.Select(x => x.TotalSellPrice + x.DiscountValue).Sum(),
                                   TotalSellPrice = grp.Select(x => x.TotalSellPrice).Sum(),
                                   Net = grp.Select(x => x.TotalSellPrice + x.SalesTax).Sum(),
                                   p.CompanyNameAr,
                                   AveSlPrice = smallQty > 0 ? grp.Select(x => x.TotalSellPrice + x.SalesTax).Sum() / smallQty : 0,
                                   t.Height,
                                   t.Length,
                                   t.Width,
                                   PiecesCount =(double)grp.Select(x=>x.PiecesCount).DefaultIfEmpty(0).Sum(),
                                   LibraQty = t.is_libra == true ? grp.Select(x=>x.LibraQty).DefaultIfEmpty(0).Sum() : null,
                                   Kg_Weight_Libra = (double) grp.Select(x=>x.kg_Weight_libra).DefaultIfEmpty(0).Sum()
                               }).OrderBy(x => x.ItemCode1).ToList();

            grdCategory.DataSource = sales_data1;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {

            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;

        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SL_ItemsSalesTotals).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }
    }
}