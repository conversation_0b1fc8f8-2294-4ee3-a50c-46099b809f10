﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;

namespace Pharmacy.Forms
{
    public partial class frm_SelectExpire : XtraForm
    {
        public static object Expire;
        public static object Batch;

        public frm_SelectExpire(int ItemId, int StoreId, DateTime InvDate)
        {
            Expire = DBNull.Value;
            Batch = null;

            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            DataTable dtExpireQty = new DataTable();
            gridControl1.DataSource = MyHelper.GetExpireQtyDataTable(dtExpireQty);
            MyHelper.Get_Expire_Qtys(ItemId, StoreId, InvDate, dtExpireQty);
        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            if (gridView1.FocusedRowHandle < 0)
                return;

            Expire = gridView1.GetFocusedRowCellValue(col_Expire);
            Batch = gridView1.GetFocusedRowCellValue(col_Batch);

            this.Close();
        }

        private void gridView1_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            #region Expire
            if (e.Column.FieldName == "Expire")
            {
                if (e.Value == null || e.Value == DBNull.Value)
                    return;
                try
                {
                    DateTime date = Convert.ToDateTime(e.Value);
                    e.DisplayText = date.Month + "-" + date.Year;
                }
                catch
                { }
            }
            #endregion
        }
    }
}
