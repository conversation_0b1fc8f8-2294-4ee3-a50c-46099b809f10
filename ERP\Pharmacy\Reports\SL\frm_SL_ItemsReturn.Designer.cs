﻿namespace Reports
{
    partial class frm_SL_ItemsReturn
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_ItemsReturn));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdCategory = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_RetunPiecesCount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldSQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.repositoryItemMemoEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.col_SoldMQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldLQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_SoldQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentPiecesCount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentSQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_currentKG = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentLibra = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentMQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentLQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_length = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_width = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_height = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_Category = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ItemId = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colIndex = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CurrentQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_Store = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPreview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnPreview
            // 
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPreview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Preview_ItemClick);
            // 
            // barBtnRefresh
            // 
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdCategory
            // 
            resources.ApplyResources(this.grdCategory, "grdCategory");
            this.grdCategory.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdCategory.EmbeddedNavigator.AccessibleDescription");
            this.grdCategory.EmbeddedNavigator.AccessibleName = resources.GetString("grdCategory.EmbeddedNavigator.AccessibleName");
            this.grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdCategory.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdCategory.EmbeddedNavigator.Anchor")));
            this.grdCategory.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdCategory.EmbeddedNavigator.BackgroundImage")));
            this.grdCategory.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdCategory.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdCategory.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdCategory.EmbeddedNavigator.ImeMode")));
            this.grdCategory.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdCategory.EmbeddedNavigator.MaximumSize")));
            this.grdCategory.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdCategory.EmbeddedNavigator.TextLocation")));
            this.grdCategory.EmbeddedNavigator.ToolTip = resources.GetString("grdCategory.EmbeddedNavigator.ToolTip");
            this.grdCategory.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdCategory.EmbeddedNavigator.ToolTipIconType")));
            this.grdCategory.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdCategory.EmbeddedNavigator.ToolTipTitle");
            this.grdCategory.MainView = this.bandedGridView1;
            this.grdCategory.Name = "grdCategory";
            this.grdCategory.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemMemoEdit1});
            this.grdCategory.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1});
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Appearance.BandPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.Appearance.BandPanel.Font")));
            this.bandedGridView1.Appearance.BandPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.Appearance.BandPanel.FontSizeDelta")));
            this.bandedGridView1.Appearance.BandPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.Appearance.BandPanel.FontStyleDelta")));
            this.bandedGridView1.Appearance.BandPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.Appearance.BandPanel.GradientMode")));
            this.bandedGridView1.Appearance.BandPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.Appearance.BandPanel.Image")));
            this.bandedGridView1.Appearance.BandPanel.Options.UseFont = true;
            this.bandedGridView1.Appearance.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.BandPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.GroupPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.Appearance.GroupPanel.FontSizeDelta")));
            this.bandedGridView1.Appearance.GroupPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.Appearance.GroupPanel.FontStyleDelta")));
            this.bandedGridView1.Appearance.GroupPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.Appearance.GroupPanel.GradientMode")));
            this.bandedGridView1.Appearance.GroupPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.Appearance.GroupPanel.Image")));
            this.bandedGridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.Font")));
            this.bandedGridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.bandedGridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.bandedGridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.GradientMode")));
            this.bandedGridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.Image")));
            this.bandedGridView1.Appearance.HeaderPanel.Options.UseFont = true;
            this.bandedGridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.Appearance.Row.FontSizeDelta")));
            this.bandedGridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.Appearance.Row.FontStyleDelta")));
            this.bandedGridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.Appearance.Row.GradientMode")));
            this.bandedGridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.Appearance.Row.Image")));
            this.bandedGridView1.Appearance.Row.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.AppearancePrint.BandPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.Font")));
            this.bandedGridView1.AppearancePrint.BandPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.BandPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.BandPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.GradientMode")));
            this.bandedGridView1.AppearancePrint.BandPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.Image")));
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.BandPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.AppearancePrint.FooterPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.Font")));
            this.bandedGridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.Image")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.GradientMode")));
            this.bandedGridView1.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.Image")));
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.GradientMode")));
            this.bandedGridView1.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.Image")));
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.Font")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.Image")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.BackColor")));
            this.bandedGridView1.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.ForeColor")));
            this.bandedGridView1.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.GradientMode")));
            this.bandedGridView1.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.Image")));
            this.bandedGridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.BorderColor")));
            this.bandedGridView1.AppearancePrint.Row.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.Row.Font")));
            this.bandedGridView1.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.Row.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.Row.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.ForeColor")));
            this.bandedGridView1.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.Row.GradientMode")));
            this.bandedGridView1.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.Row.Image")));
            this.bandedGridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand2,
            this.gridBand3,
            this.gridBand1});
            resources.ApplyResources(this.bandedGridView1, "bandedGridView1");
            this.bandedGridView1.ColumnPanelRowHeight = 35;
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.col_width,
            this.col_height,
            this.col_length,
            this.bandedGridColumn4,
            this.bandedGridColumn5,
            this.col_SoldLQty,
            this.col_SoldMQty,
            this.col_SoldSQty,
            this.col_CurrentLQty,
            this.col_CurrentMQty,
            this.col_CurrentSQty,
            this.col_ItemId,
            this.colIndex,
            this.col_CurrentQty,
            this.col_SoldQty,
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.bandedGridColumn3,
            this.col_RetunPiecesCount,
            this.col_CurrentPiecesCount,
            this.col_Store,
            this.col_CurrentLibra,
            this.col_currentKG,
            this.col_Category});
            this.bandedGridView1.GridControl = this.grdCategory;
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsBehavior.ReadOnly = true;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.bandedGridView1.OptionsView.RowAutoHeight = true;
            this.bandedGridView1.OptionsView.ShowAutoFilterRow = true;
            this.bandedGridView1.OptionsView.ShowFooter = true;
            this.bandedGridView1.OptionsView.ShowGroupPanel = false;
            this.bandedGridView1.OptionsView.ShowIndicator = false;
            this.bandedGridView1.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            // 
            // gridBand2
            // 
            this.gridBand2.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("gridBand2.AppearanceHeader.Font")));
            this.gridBand2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridBand2.AppearanceHeader.FontSizeDelta")));
            this.gridBand2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridBand2.AppearanceHeader.FontStyleDelta")));
            this.gridBand2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridBand2.AppearanceHeader.GradientMode")));
            this.gridBand2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridBand2.AppearanceHeader.Image")));
            this.gridBand2.AppearanceHeader.Options.UseFont = true;
            this.gridBand2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            resources.ApplyResources(this.gridBand2, "gridBand2");
            this.gridBand2.Columns.Add(this.col_RetunPiecesCount);
            this.gridBand2.Columns.Add(this.col_SoldSQty);
            this.gridBand2.Columns.Add(this.col_SoldMQty);
            this.gridBand2.Columns.Add(this.col_SoldLQty);
            this.gridBand2.VisibleIndex = 0;
            // 
            // col_RetunPiecesCount
            // 
            resources.ApplyResources(this.col_RetunPiecesCount, "col_RetunPiecesCount");
            this.col_RetunPiecesCount.FieldName = "ReturnPiecesCount";
            this.col_RetunPiecesCount.Name = "col_RetunPiecesCount";
            this.col_RetunPiecesCount.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_SoldSQty
            // 
            resources.ApplyResources(this.col_SoldSQty, "col_SoldSQty");
            this.col_SoldSQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_SoldSQty.FieldName = "SoldSQty";
            this.col_SoldSQty.Name = "col_SoldSQty";
            this.col_SoldSQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldSQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // repositoryItemMemoEdit1
            // 
            resources.ApplyResources(this.repositoryItemMemoEdit1, "repositoryItemMemoEdit1");
            this.repositoryItemMemoEdit1.Name = "repositoryItemMemoEdit1";
            // 
            // col_SoldMQty
            // 
            resources.ApplyResources(this.col_SoldMQty, "col_SoldMQty");
            this.col_SoldMQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_SoldMQty.FieldName = "SoldMQty";
            this.col_SoldMQty.Name = "col_SoldMQty";
            this.col_SoldMQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldMQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_SoldLQty
            // 
            resources.ApplyResources(this.col_SoldLQty, "col_SoldLQty");
            this.col_SoldLQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_SoldLQty.FieldName = "SoldLQty";
            this.col_SoldLQty.Name = "col_SoldLQty";
            this.col_SoldLQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldLQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // gridBand3
            // 
            this.gridBand3.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("gridBand3.AppearanceHeader.Font")));
            this.gridBand3.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridBand3.AppearanceHeader.FontSizeDelta")));
            this.gridBand3.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridBand3.AppearanceHeader.FontStyleDelta")));
            this.gridBand3.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridBand3.AppearanceHeader.GradientMode")));
            this.gridBand3.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridBand3.AppearanceHeader.Image")));
            this.gridBand3.AppearanceHeader.Options.UseFont = true;
            this.gridBand3.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridBand3.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            resources.ApplyResources(this.gridBand3, "gridBand3");
            this.gridBand3.Columns.Add(this.col_SoldQty);
            this.gridBand3.Columns.Add(this.col_CurrentPiecesCount);
            this.gridBand3.Columns.Add(this.col_CurrentSQty);
            this.gridBand3.Columns.Add(this.col_currentKG);
            this.gridBand3.Columns.Add(this.col_CurrentLibra);
            this.gridBand3.Columns.Add(this.col_CurrentMQty);
            this.gridBand3.Columns.Add(this.col_CurrentLQty);
            this.gridBand3.Columns.Add(this.col_length);
            this.gridBand3.Columns.Add(this.col_width);
            this.gridBand3.VisibleIndex = 1;
            // 
            // col_SoldQty
            // 
            resources.ApplyResources(this.col_SoldQty, "col_SoldQty");
            this.col_SoldQty.FieldName = "SoldQty";
            this.col_SoldQty.Name = "col_SoldQty";
            this.col_SoldQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_CurrentPiecesCount
            // 
            resources.ApplyResources(this.col_CurrentPiecesCount, "col_CurrentPiecesCount");
            this.col_CurrentPiecesCount.FieldName = "CurrentPiecesCount";
            this.col_CurrentPiecesCount.Name = "col_CurrentPiecesCount";
            this.col_CurrentPiecesCount.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_CurrentSQty
            // 
            resources.ApplyResources(this.col_CurrentSQty, "col_CurrentSQty");
            this.col_CurrentSQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_CurrentSQty.FieldName = "CurrentSQty";
            this.col_CurrentSQty.Name = "col_CurrentSQty";
            this.col_CurrentSQty.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_CurrentSQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_CurrentSQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_currentKG
            // 
            resources.ApplyResources(this.col_currentKG, "col_currentKG");
            this.col_currentKG.FieldName = "currentKG";
            this.col_currentKG.Name = "col_currentKG";
            // 
            // col_CurrentLibra
            // 
            resources.ApplyResources(this.col_CurrentLibra, "col_CurrentLibra");
            this.col_CurrentLibra.FieldName = "CurrentLibra";
            this.col_CurrentLibra.Name = "col_CurrentLibra";
            // 
            // col_CurrentMQty
            // 
            resources.ApplyResources(this.col_CurrentMQty, "col_CurrentMQty");
            this.col_CurrentMQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_CurrentMQty.FieldName = "CurrentMQty";
            this.col_CurrentMQty.Name = "col_CurrentMQty";
            this.col_CurrentMQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_CurrentMQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_CurrentLQty
            // 
            resources.ApplyResources(this.col_CurrentLQty, "col_CurrentLQty");
            this.col_CurrentLQty.ColumnEdit = this.repositoryItemMemoEdit1;
            this.col_CurrentLQty.FieldName = "CurrentLQty";
            this.col_CurrentLQty.Name = "col_CurrentLQty";
            this.col_CurrentLQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_CurrentLQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_length
            // 
            resources.ApplyResources(this.col_length, "col_length");
            this.col_length.FieldName = "Length";
            this.col_length.Name = "col_length";
            // 
            // col_width
            // 
            resources.ApplyResources(this.col_width, "col_width");
            this.col_width.FieldName = "Width";
            this.col_width.Name = "col_width";
            this.col_width.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // gridBand1
            // 
            resources.ApplyResources(this.gridBand1, "gridBand1");
            this.gridBand1.Columns.Add(this.bandedGridColumn4);
            this.gridBand1.Columns.Add(this.bandedGridColumn5);
            this.gridBand1.Columns.Add(this.col_height);
            this.gridBand1.Columns.Add(this.col_Category);
            this.gridBand1.Columns.Add(this.col_ItemId);
            this.gridBand1.Columns.Add(this.colIndex);
            this.gridBand1.VisibleIndex = 2;
            // 
            // bandedGridColumn4
            // 
            resources.ApplyResources(this.bandedGridColumn4, "bandedGridColumn4");
            this.bandedGridColumn4.FieldName = "LargeUOMFactor";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            this.bandedGridColumn4.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn5
            // 
            resources.ApplyResources(this.bandedGridColumn5, "bandedGridColumn5");
            this.bandedGridColumn5.FieldName = "MediumUOMFactor";
            this.bandedGridColumn5.Name = "bandedGridColumn5";
            this.bandedGridColumn5.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_height
            // 
            resources.ApplyResources(this.col_height, "col_height");
            this.col_height.FieldName = "Height";
            this.col_height.Name = "col_height";
            // 
            // col_Category
            // 
            resources.ApplyResources(this.col_Category, "col_Category");
            this.col_Category.FieldName = "Category";
            this.col_Category.Name = "col_Category";
            this.col_Category.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_ItemId
            // 
            resources.ApplyResources(this.col_ItemId, "col_ItemId");
            this.col_ItemId.FieldName = "ItemNameAr";
            this.col_ItemId.Name = "col_ItemId";
            this.col_ItemId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemId.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colIndex
            // 
            resources.ApplyResources(this.colIndex, "colIndex");
            this.colIndex.FieldName = "colIndex";
            this.colIndex.Name = "colIndex";
            this.colIndex.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.colIndex.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // col_CurrentQty
            // 
            resources.ApplyResources(this.col_CurrentQty, "col_CurrentQty");
            this.col_CurrentQty.FieldName = "CurrentQty";
            this.col_CurrentQty.Name = "col_CurrentQty";
            this.col_CurrentQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn1
            // 
            resources.ApplyResources(this.bandedGridColumn1, "bandedGridColumn1");
            this.bandedGridColumn1.FieldName = "SUom";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn2
            // 
            resources.ApplyResources(this.bandedGridColumn2, "bandedGridColumn2");
            this.bandedGridColumn2.FieldName = "MUom";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn3
            // 
            resources.ApplyResources(this.bandedGridColumn3, "bandedGridColumn3");
            this.bandedGridColumn3.FieldName = "LUom";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            this.bandedGridColumn3.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_Store
            // 
            resources.ApplyResources(this.col_Store, "col_Store");
            this.col_Store.FieldName = "Store";
            this.col_Store.Name = "col_Store";
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.MenuManager = this.barManager1;
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.AccessibleDescription = resources.GetString("picLogo.Properties.AccessibleDescription");
            this.picLogo.Properties.AccessibleName = resources.GetString("picLogo.Properties.AccessibleName");
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.AccessibleDescription = resources.GetString("lblReportName.Properties.AccessibleDescription");
            this.lblReportName.Properties.AccessibleName = resources.GetString("lblReportName.Properties.AccessibleName");
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblReportName.Properties.Appearance.FontSizeDelta")));
            this.lblReportName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblReportName.Properties.Appearance.FontStyleDelta")));
            this.lblReportName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblReportName.Properties.Appearance.GradientMode")));
            this.lblReportName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblReportName.Properties.Appearance.Image")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblReportName.Properties.AutoHeight = ((bool)(resources.GetObject("lblReportName.Properties.AutoHeight")));
            this.lblReportName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblReportName.Properties.Mask.AutoComplete")));
            this.lblReportName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblReportName.Properties.Mask.BeepOnError")));
            this.lblReportName.Properties.Mask.EditMask = resources.GetString("lblReportName.Properties.Mask.EditMask");
            this.lblReportName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblReportName.Properties.Mask.IgnoreMaskBlank")));
            this.lblReportName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblReportName.Properties.Mask.MaskType")));
            this.lblReportName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblReportName.Properties.Mask.PlaceHolder")));
            this.lblReportName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblReportName.Properties.Mask.SaveLiteral")));
            this.lblReportName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblReportName.Properties.Mask.ShowPlaceHolders")));
            this.lblReportName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblReportName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblReportName.Properties.NullValuePrompt = resources.GetString("lblReportName.Properties.NullValuePrompt");
            this.lblReportName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblReportName.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.AccessibleDescription = resources.GetString("lblDateFilter.Properties.AccessibleDescription");
            this.lblDateFilter.Properties.AccessibleName = resources.GetString("lblDateFilter.Properties.AccessibleName");
            this.lblDateFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblDateFilter.Properties.Appearance.FontSizeDelta")));
            this.lblDateFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblDateFilter.Properties.Appearance.FontStyleDelta")));
            this.lblDateFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblDateFilter.Properties.Appearance.GradientMode")));
            this.lblDateFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblDateFilter.Properties.Appearance.Image")));
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblDateFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblDateFilter.Properties.AutoHeight")));
            this.lblDateFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblDateFilter.Properties.Mask.AutoComplete")));
            this.lblDateFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.BeepOnError")));
            this.lblDateFilter.Properties.Mask.EditMask = resources.GetString("lblDateFilter.Properties.Mask.EditMask");
            this.lblDateFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblDateFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblDateFilter.Properties.Mask.MaskType")));
            this.lblDateFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblDateFilter.Properties.Mask.PlaceHolder")));
            this.lblDateFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.SaveLiteral")));
            this.lblDateFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblDateFilter.Properties.NullValuePrompt = resources.GetString("lblDateFilter.Properties.NullValuePrompt");
            this.lblDateFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblDateFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.AccessibleDescription = resources.GetString("lblFilter.Properties.AccessibleDescription");
            this.lblFilter.Properties.AccessibleName = resources.GetString("lblFilter.Properties.AccessibleName");
            this.lblFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblFilter.Properties.Appearance.FontSizeDelta")));
            this.lblFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblFilter.Properties.Appearance.FontStyleDelta")));
            this.lblFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblFilter.Properties.Appearance.GradientMode")));
            this.lblFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblFilter.Properties.Appearance.Image")));
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblFilter.Properties.AutoHeight")));
            this.lblFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblFilter.Properties.Mask.AutoComplete")));
            this.lblFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblFilter.Properties.Mask.BeepOnError")));
            this.lblFilter.Properties.Mask.EditMask = resources.GetString("lblFilter.Properties.Mask.EditMask");
            this.lblFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblFilter.Properties.Mask.MaskType")));
            this.lblFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblFilter.Properties.Mask.PlaceHolder")));
            this.lblFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblFilter.Properties.Mask.SaveLiteral")));
            this.lblFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblFilter.Properties.NullValuePrompt = resources.GetString("lblFilter.Properties.NullValuePrompt");
            this.lblFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // frm_SL_ItemsReturn
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.grdCategory);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_ItemsReturn";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_Rep_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_InvoiceList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdCategory;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_RetunPiecesCount;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldSQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldMQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldLQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentPiecesCount;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentSQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentMQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentLQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ItemId;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colIndex;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn5;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Store;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_length;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_width;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_height;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CurrentLibra;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_currentKG;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Category;
    }
}