﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_SalesOrderItems: DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, customerId1, customerId2, store_id1, store_id2;
        int MtrxParentId, M1, M2, M3;
        decimal ItmHeight, ItmWidth, ItmLength;

        byte FltrTyp_Store, FltrTyp_item, fltrTyp_Date, FltrTyp_Customer;        
        DateTime date1, date2;
        List<int> lstStores = new List<int>();

        public frm_SL_SalesOrderItems(
            string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            int MtrxParentId, int M1, int M2, int M3, 
            decimal Height, decimal Width, decimal Length,byte FltrTyp_Store, int store_id1, int store_id2)
        {


            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.itemId1 = itemId1;
            this.itemId2 = itemId2;
            this.date1 = date1;
            this.date2 = date2;
            this.customerId1 = customerId1;
            this.customerId2 = customerId2;

            this.MtrxParentId = MtrxParentId;
            this.M1 = M1;
            this.M2 = M2;
            this.M3 = M3;
            this.ItmHeight = Height;
            this.ItmWidth = Width;
            this.ItmLength = Length;
            this.store_id1 = store_id1;
            this.FltrTyp_Store = FltrTyp_Store;
            this.store_id2 = store_id2;

            getReportHeader();

            ERPDataContext DB = new ERPDataContext();
            rep_MtrxParent.DataSource = DB.IC_Items.
                Where(i => i.ItemType == (int)ItemType.MatrixParent).
                Select(x => new { x.ItemId, x.ItemNameAr }).ToList();
            rep_MtrxParent.ValueMember = "ItemId";
            rep_MtrxParent.DisplayMember = "ItemNameAr";
            
            rep_mtrx.DataSource = DB.IC_MatrixDetails.
               Select(x => new { x.MatrixDetailId, x.MDName}).ToList();
            rep_mtrx.ValueMember = "MatrixDetailId";
            rep_mtrx.DisplayMember = "MDName";

            rep_Cust.DataSource = DB.SL_Customers.
               Select(x => new { x.CustomerId, x.CusNameAr}).ToList();
            rep_Cust.ValueMember = "CustomerId";
            rep_Cust.DisplayMember = "CusNameAr";

            LoadData();
            ReportsUtils.ColumnChooser(grdCategory);     
        }


        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));


            col_Width.OptionsColumn.ShowInCustomizationForm = Shared.st_Store.UseWidthDimension;
            col_Height.OptionsColumn.ShowInCustomizationForm = Shared.st_Store.UseHeightDimension;
            col_Length.OptionsColumn.ShowInCustomizationForm = Shared.st_Store.UseLengthDimension;
            col_PcsCount.OptionsColumn.ShowInCustomizationForm = Shared.st_Store.PiecesCount;
            col_Expire.OptionsColumn.ShowInCustomizationForm = Shared.st_Store.ExpireDate;
            col_Batch.OptionsColumn.ShowInCustomizationForm = Shared.st_Store.Batch;

            col_MtrxParentId.OptionsColumn.ShowInCustomizationForm = Shared.ItemMatrixAvailable;
            col_Mtrx1.OptionsColumn.ShowInCustomizationForm = Shared.ItemMatrixAvailable;
            col_Mtrx2.OptionsColumn.ShowInCustomizationForm = Shared.ItemMatrixAvailable;
            col_Mtrx3.OptionsColumn.ShowInCustomizationForm = Shared.ItemMatrixAvailable;
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }
        
        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stores = DB.IC_Stores.ToList();

            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= store_id2 && store.StoreId >= store_id1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (store_id1 > 0 && (store.StoreId == store_id1 || store.ParentId == store_id1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }
            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();

            var data_sl = (from p in DB.SL_SalesOrders.Where(p=> !p.Is_OutTrns.HasValue || p.Is_OutTrns != true)
                           where lstStores.Count > 0 ? lstStores.Contains(p.StoreId) : true

                           join pd in DB.SL_SalesOrderDetails on p.SL_SalesOrderId equals pd.SL_SalesOrderId

                        where fltrTyp_Date == 1 ? p.SalesOrderDate.Date == date1 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        p.SalesOrderDate >= date1 && p.SalesOrderDate <= date2 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        p.SalesOrderDate >= date1 : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        p.SalesOrderDate <= date2 : true

                        where FltrTyp_Customer == 1 ? p.CustomerId == customerId1 : true
                        where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                        p.CustomerId >= customerId1 && p.CustomerId <= customerId2 : true
                        where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                        p.CustomerId >= customerId1 : true
                        where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                        p.CustomerId <= customerId2 : true

                        where FltrTyp_item == 1 ? pd.ItemId == itemId1 : true
                        where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                        pd.ItemId >= itemId1 && pd.ItemId <= itemId2 : true
                        where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                        pd.ItemId >= itemId1 : true
                        where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                        pd.ItemId <= itemId2 : true

                        where ItmHeight == 0 ? true : pd.Height == ItmHeight
                        where ItmWidth == 0 ? true : pd.Width == ItmWidth
                        where ItmLength == 0 ? true : pd.Length == ItmLength

                        join t in DB.IC_Items on pd.ItemId equals t.ItemId
                        join g in DB.IC_Categories on t.Category equals g.CategoryId
                          
                        where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                        where MtrxParentId == 0 ? true : t.mtrxParentItem == MtrxParentId
                        where M1 == 0 ? true : t.mtrxId1 == M1
                        where M2 == 0 ? true : t.mtrxId2 == M2
                        where M3 == 0 ? true : t.mtrxId3 == M3

                        join u in DB.IC_UOMs on pd.UOMId equals u.UOMId
                        select new
                        {
                            CustomerId = p.CustomerId,
                            InvoiceCode = p.SalesOrderCode,
                            InvoiceDate = p.SalesOrderDate,
                            DeliverDate = p.DeliverDate,
                            ItemCode1 = t.ItemCode1,
                            ItemCode2 = t.ItemCode2,
                            ItemNameAr = t.ItemNameAr,
                            mtrxParentItem = t.mtrxParentItem,
                            t.mtrxAttribute1,
                            t.mtrxAttribute2,
                            t.mtrxAttribute3,
                            pd.Expire,
                            pd.Batch,
                            pd.Height,
                            pd.Width,
                            pd.Length,
                            SellPrice = pd.SellPrice,
                            TotalSellPrice = pd.TotalSellPrice,
                            Qty = pd.Qty,
                            UOM = u.UOM,
                            PiecesCount = pd.PiecesCount
                        }).ToList().OrderBy(x => x.CustomerId).ThenBy(x => x.InvoiceDate).ToList();

            grdCategory.DataSource = data_sl;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;
            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;

        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_frm_SL_SalesOrderItems).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }        
    }
}