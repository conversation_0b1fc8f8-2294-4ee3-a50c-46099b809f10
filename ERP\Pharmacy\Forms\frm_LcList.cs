﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using Pharmacy.Forms;
using DevExpress.Utils.Menu;
using DevExpress.XtraGrid.Localization;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_LcList : DevExpress.XtraEditors.XtraForm
    {
        public frm_LcList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
        }

        private void frm_FA_FixedAssetList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            ErpUtils.Tab_Enter_Process(grd_Customer);
            ErpUtils.ColumnChooser(grd_Customer);

            repCrnc.DataSource = Shared.lstCurrency;
            repCrnc.ValueMember = "CrncId";
            repCrnc.DisplayMember = "crncName";

            LoadPrivilege();
            GetFixedAssets();

        }

        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_LC)))
                Application.OpenForms["frm_LC"].Close();

            new frm_LC(0).Show();
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GetFixedAssets();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            OpenSelectedFA();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_Customer.MinimumSize = grd_Customer.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grd_Customer, false).ShowPreview();
            grd_Customer.MinimumSize = new Size(0, 0);
        }


        private void grd_Customer_DoubleClick(object sender, EventArgs e)
        {
            OpenSelectedFA();
        }


        private void OpenSelectedFA()
        {
            var view = grd_Customer.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            if (ErpUtils.IsFormOpen(typeof(frm_LC)))
                Application.OpenForms["frm_LC"].Close();

            new frm_LC(Convert.ToInt32(view.GetFocusedRowCellValue(colPR_LcId))).Show();
        }

        private void GetFixedAssets()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var emps = (from e in DB.PR_LCs
                        join v in DB.PR_Vendors
                        on e.VendorId equals v.VendorId

                        let Value = HelperAcc.Get_TotalDebit(e.LcAccountId)
                             
                        select new
                        {
                            e.PR_LcId,
                            e.LcCode,
                            e.LcIsOpen,
                            e.LcName,
                            v.VenNameAr,
                            e.LcValue,
                            e.CrncId,

                            e.OpenDate,
                            e.CloseDate,
                            e.ShipDate,
                            e.DeliverDate,

                            e.ShipMethod,
                            e.ShipPort,
                            e.PayMethod,
                            e.BillOfLading,
                            e.Notes,

                            NetValue = Value,                            
                        }).ToList();

            grd_Customer.DataSource = emps;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_PR_LC).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "تسجيل موظف جديد");
        }
    }    
}