﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_CustomerItemsSales : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, customerId1, customerId2, custGroupId,
            storeId1, storeId2, salesEmpId, companyId;
        byte FltrTyp_item, fltrTyp_Date, FltrTyp_Customer, FltrTyp_SellPrice, FltrTyp_Category,
            FltrTyp_Store, FltrTyp_InvBook, FltrTyp_Company;
        string categoryNum;
        DateTime date1, date2;

        decimal SellPrice1, SellPrice2;
        string custGroupAccNumber;
        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();

        Process process;

        public frm_SL_CustomerItemsSales(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            byte FltrTyp_SellPrice, decimal SellPrice1, decimal SellPrice2,
            byte FltrTyp_Category, string categoryNum,
            int custGroupId, string custGroupAccNumber,
            byte FltrTyp_Store, int storeId1, int storeId2,
            int salesEmpId, Process process,
            byte FltrTyp_InvBook, string InvBooks, byte FltrTyp_Company, int companyId)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_SellPrice = FltrTyp_SellPrice;
            this.FltrTyp_Category = FltrTyp_Category;
            this.FltrTyp_Store = FltrTyp_Store;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;

            this.date1 = date1;
            this.date2 = date2;

            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            this.categoryNum = categoryNum;

            this.SellPrice1 = SellPrice1;
            this.SellPrice2 = SellPrice2;

            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;
            this.storeId1 = storeId1;
            this.storeId2 = storeId2;
            this.salesEmpId = salesEmpId;
            this.process = process;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();


            LoadData();

            col_PiecesCount.Visible = Shared.st_Store.PiecesCount;
            col_Serial.OptionsColumn.ShowInCustomizationForm = col_Serial.Visible = Shared.st_Store.Serial;
            col_Batch.OptionsColumn.ShowInCustomizationForm = col_Batch.Visible = Shared.st_Store.Serial;

            col_Serial.Caption = Shared.IsEnglish ? Shared.st_Store.SerialNameEn : Shared.st_Store.SerialNameAr;
            col_Serial2.Caption = Shared.IsEnglish ? Shared.st_Store.Serial2NameEn : Shared.st_Store.Serial2NameAr;
            col_Batch.Caption = Shared.IsEnglish ? Shared.st_Store.BatchNameEn : Shared.st_Store.BatchNameAr;
            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;

            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }


        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void barbtnPrintP_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, false, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void barbtnPrint_P_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            ERPDataContext DB = new ERPDataContext();

            rep_Currency.DataSource = Shared.lstCurrency;
            rep_Currency.ValueMember = "CrncId";
            rep_Currency.DisplayMember = "crncName";

            var stores = DB.IC_Stores.ToList();
            rep_Stores1.DataSource = stores;
            rep_Stores1.ValueMember = "StoreId";
            rep_Stores1.DisplayMember = Shared.IsEnglish ? "StoreNameEn" : "StoreNameAr";

            rep_ItemCategory1.DataSource = DB.IC_Categories;
            rep_ItemCategory1.DisplayMember = Shared.IsEnglish ? "CategoryNameEn" : "CategoryNameAr";
            rep_ItemCategory1.ValueMember = "CategoryId";

            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= storeId2 && store.StoreId >= storeId1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (storeId1 > 0 && (store.StoreId == storeId1 || store.ParentId == storeId1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }
            //var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            if (process == Process.SellInvoice)
            {
                #region sales
                var data = (
                    from c in DB.SL_Customers
                    //join a in DB.ACC_Accounts
                    //on c.AccountId equals a.AccountId
                    //where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                    where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                    where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                    c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                    where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                    c.CustomerId >= customerId1 : true
                    where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                    c.CustomerId <= customerId2 : true

                    join i in DB.SL_Invoices on c.CustomerId equals i.CustomerId
                    ////update
                    //join d in DB.SL_InvoiceDetails on i.SL_InvoiceId equals d.SL_InvoiceId
                    //where lstStores.Count > 0 ? lstStores.Contains(i.StoreId)|| lstStores.Contains(d.StoreId.Value) : true

                    where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                    where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                    where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1 : true
                    where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                    i.InvoiceDate >= date1 && i.InvoiceDate <= date2 : true
                    where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                    i.InvoiceDate >= date1 : true
                    where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                    i.InvoiceDate <= date2 : true

                    join s in DB.SL_InvoiceDetails on i.SL_InvoiceId equals s.SL_InvoiceId
                    where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) || lstStores.Contains(s.StoreId.Value) : true


                    where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                    where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                    s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                    where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                    s.ItemId >= itemId1 : true
                    where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                    s.ItemId <= itemId2 : true

                    where FltrTyp_SellPrice == 1 ? s.SellPrice == SellPrice1 : true
                    where (FltrTyp_SellPrice == 2 && SellPrice1 != 0 && SellPrice2 != 0) ?
                    s.SellPrice >= SellPrice1 && s.SellPrice <= SellPrice2 : true
                    where (FltrTyp_SellPrice == 2 && SellPrice1 != 0 && SellPrice2 == 0) ?
                    s.SellPrice >= SellPrice1 : true
                    where (FltrTyp_SellPrice == 2 && SellPrice1 == 0 && SellPrice2 != 0) ?
                    s.SellPrice <= SellPrice2 : true

                    join t in DB.IC_Items on s.ItemId equals t.ItemId
                    where FltrTyp_Company == 1 ? t.Company == companyId : true

                    join g in DB.IC_Categories on t.Category equals g.CategoryId
                    //where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                    where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true

                    join u in DB.IC_UOMs on s.UOMId equals u.UOMId
                    join p in DB.IC_Companies on t.Company equals p.CompanyId

                    from emp in DB.HR_Employees.Where(emp => emp.SalesRep && emp.EmpId == i.SalesEmpId).DefaultIfEmpty()

                    let Cust_Category = DB.SL_CustomerGroups
                    //let Cust_group = DB.SL_Group_Customers
                    let Users = DB.HR_Users
                    select new
                    {
                        CusNameAr = c.CusNameAr,
                        InvoiceCode = i.InvoiceCode,
                        InvoiceDate = i.InvoiceDate,
                        t.ItemCode1,
                        t.ItemCode2,
                        ItemNameAr = t.ItemNameAr,
                        Qty = s.Qty,
                        UOM = u.UOM,
                        PiecesCount = s.PiecesCount,
                        kg_Weight_libra = s.kg_Weight_libra == null ? 0 : (double)Math.Round(s.kg_Weight_libra.Value, 3, MidpointRounding.AwayFromZero),
                        LibraQty = t.is_libra == true ? ((double)Math.Round(s.LibraQty.Value, 3, MidpointRounding.AwayFromZero)).ToString() : string.Empty,
                        SellPrice = (double)Math.Round(s.SellPrice, 2, MidpointRounding.AwayFromZero),
                        TotalSellValue = (double)Math.Round(s.Qty * s.SellPrice, 2, MidpointRounding.AwayFromZero),
                        s.DiscountRatio,
                        s.DiscountRatio2,
                        s.DiscountRatio3,
                        DicVal1 = (s.Qty * s.SellPrice) * s.DiscountRatio,
                        DicVal2 = ((s.Qty * s.SellPrice) - ((s.Qty * s.SellPrice) * s.DiscountRatio)) * s.DiscountRatio2,
                        DicVal3 =
                        ((s.Qty * s.SellPrice) -
                        ((s.Qty * s.SellPrice) * s.DiscountRatio) -
                        (((s.Qty * s.SellPrice) - ((s.Qty * s.SellPrice) * s.DiscountRatio)) * s.DiscountRatio2)) * s.DiscountRatio3,
                        s.DiscountValue,
                        s.SalesTax,
                        //TotalSellPrice = ((s.Qty * s.SellPrice) - s.DiscountValue) +
                        //(Shared.st_Store.PriceIncludeSalesTax ? s.SalesTax * -1 : s.SalesTax)
                        TotalSellPrice = (double)Math.Round(s.TotalSellPrice, 2, MidpointRounding.AwayFromZero),
                        s.ItemDescription,
                        s.ItemDescriptionEn,
                        i.DriverName,
                        i.VehicleNumber,
                        i.Destination,
                        SalesEmp = emp == null ? null : emp.EmpName,
                        s.ItemId,
                        s.Height,
                        s.Length,
                        s.Width,
                        s.Expire,
                        s.Batch,
                        s.Serial,
                        s.Serial2,
                        p.CompanyNameAr,
                        Store = i.StoreId,
                        i.ProcessId,
                        i.CrncId,
                        i.CrncRate,
                        TotalSellPrice_Local = (double)Math.Round(s.TotalSellPrice * i.CrncRate, 2, MidpointRounding.AwayFromZero),
                        CustCat = Cust_Category.Where(x => x.CustomerGroupId == c.CategoryId).Select(x => Shared.IsEnglish ? x.CGNameEn : x.CGNameAr).FirstOrDefault(),
                        //CustGroup = Cust_group.Where(x => x.GroupId == c.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault(),
                        PaymentMethod = i.PayMethod,
                        i.Notes,
                        t.Category,
                        UserName = Users.Where(x => x.UserId == i.UserId).Select(x => x.UserName).FirstOrDefault(),

                        City = c.City,
                        //Area = DB.SL_CustomerRegions.Where(x => x.IdRegion == c.IdRegion).Select(x => x.RegionName).FirstOrDefault()
                    }).Distinct().ToList().OrderBy(x => x.InvoiceDate).ThenBy(x => x.CusNameAr).ThenBy(x => x.ItemNameAr).ToList();

                grdCategory.DataSource = data;
                #endregion
            }
            else if (process == Process.SellReturn)
            {
                #region return
                var data = (
                  from c in DB.SL_Customers
                  //join a in DB.ACC_Accounts
                  //on c.AccountId equals a.AccountId
                  //where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                  where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                  where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                  c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                  where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                  c.CustomerId >= customerId1 : true
                  where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                  c.CustomerId <= customerId2 : true

                  join i in DB.SL_Returns on c.CustomerId equals i.CustomerId

                  where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true

                  where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                  where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                  where fltrTyp_Date == 1 ? i.ReturnDate.Date == date1 : true
                  where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                  i.ReturnDate >= date1 && i.ReturnDate <= date2 : true
                  where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                  i.ReturnDate >= date1 : true
                  where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                  i.ReturnDate <= date2 : true

                  join s in DB.SL_ReturnDetails on i.SL_ReturnId equals s.SL_ReturnId

                  where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                  where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                  s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                  where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                  s.ItemId >= itemId1 : true
                  where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                  s.ItemId <= itemId2 : true

                  where FltrTyp_SellPrice == 1 ? s.SellPrice == SellPrice1 : true
                  where (FltrTyp_SellPrice == 2 && SellPrice1 != 0 && SellPrice2 != 0) ?
                  s.SellPrice >= SellPrice1 && s.SellPrice <= SellPrice2 : true
                  where (FltrTyp_SellPrice == 2 && SellPrice1 != 0 && SellPrice2 == 0) ?
                  s.SellPrice >= SellPrice1 : true
                  where (FltrTyp_SellPrice == 2 && SellPrice1 == 0 && SellPrice2 != 0) ?
                  s.SellPrice <= SellPrice2 : true

                  join t in DB.IC_Items on s.ItemId equals t.ItemId
                  where FltrTyp_Company == 1 ? t.Company == companyId : true

                  join g in DB.IC_Categories on t.Category equals g.CategoryId
                  //where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                  where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true

                  join u in DB.IC_UOMs on s.UOMId equals u.UOMId
                  join p in DB.IC_Companies on t.Company equals p.CompanyId

                  from emp in DB.HR_Employees.Where(emp => emp.SalesRep && emp.EmpId == i.SalesEmpId).DefaultIfEmpty()

                  let Cust_Category = DB.SL_CustomerGroups
                  //let Cust_group = DB.SL_Group_Customers
                  let Users = DB.HR_Users

                  select new
                  {
                      CusNameAr = c.CusNameAr,
                      InvoiceCode = i.ReturnCode,
                      InvoiceDate = i.ReturnDate,
                      t.ItemCode1,
                      t.ItemCode2,
                      ItemNameAr = t.ItemNameAr,
                      Qty = s.Qty,
                      UOM = u.UOM,
                      PiecesCount = s.PiecesCount,
                      kg_Weight_libra = (double)Math.Round(s.kg_Weight_libra.GetValueOrDefault(), 3, MidpointRounding.AwayFromZero),
                      LibraQty = t.is_libra == true ? ((double)Math.Round(s.LibraQty.Value, 3, MidpointRounding.AwayFromZero)).ToString() : string.Empty,
                      SellPrice = (double)Math.Round(s.SellPrice, 2, MidpointRounding.AwayFromZero),
                      TotalSellValue = (double)Math.Round(s.Qty * s.SellPrice, 2, MidpointRounding.AwayFromZero),
                      s.DiscountRatio,
                      s.DiscountRatio2,
                      s.DiscountRatio3,
                      s.DiscountValue,
                      s.SalesTax,
                      //TotalSellPrice = ((s.Qty * s.SellPrice) - s.DiscountValue) +
                      //(Shared.st_Store.PriceIncludeSalesTax ? s.SalesTax * -1 : s.SalesTax)
                      TotalSellPrice = (double)Math.Round(s.TotalSellPrice, 2, MidpointRounding.AwayFromZero),
                      s.ItemDescription,
                      s.ItemDescriptionEn,
                      i.DriverName,
                      i.VehicleNumber,
                      i.Destination,
                      SalesEmp = emp == null ? null : emp.EmpName,
                      s.ItemId,
                      s.Height,
                      s.Length,
                      s.Width,
                      s.Expire,
                      s.Batch,
                      s.Serial,
                      s.Serial2,
                      p.CompanyNameAr,
                      Store = i.StoreId,
                      i.ProcessId,
                      i.CrncId,
                      i.CrncRate,
                      //TotalSellPrice_Local = (double)Math.Round(s.TotalSellPrice * i.CrncRate, 2, MidpointRounding.AwayFromZero),
                      CustCat = Cust_Category.Where(x => x.CustomerGroupId == c.CategoryId).Select(x => Shared.IsEnglish ? x.CGNameEn : x.CGNameAr).FirstOrDefault(),
                      //CustGroup = Cust_group.Where(x => x.GroupId == c.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault(),
                      PaymentMethod = i.PayMethod,
                      i.Notes,
                      t.Category,
                      UserName = Users.Where(x => x.UserId == i.UserId).Select(x => x.UserName).FirstOrDefault()

                  }).ToList().OrderBy(x => x.InvoiceDate).ThenBy(x => x.CusNameAr).ThenBy(x => x.ItemNameAr).ToList();

                grdCategory.DataSource = data;
                #endregion
            }
            if (FltrTyp_Customer == 1)
                col_CusNameAr.Visible = false;

            if (FltrTyp_item == 1)
                col_ItemNameAr.Visible = false;
        }

        private void mi_CopyRows_Click(object sender, EventArgs e)
        {
            ReportsUtils.dt_Copied_Rows.Clear();
            foreach (int i in gridView1.GetSelectedRows())
            {
                DataRow row = ReportsUtils.dt_Copied_Rows.NewRow();
                row["ItemId"] = Convert.ToInt32(gridView1.GetRowCellValue(i, "ItemId"));
                row["Qty"] = Convert.ToDouble(gridView1.GetRowCellValue(i, col_Qty));

                if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply && Convert.ToDouble(gridView1.GetRowCellValue(i, "Height")) <= 0)
                    row["Height"] = 1;
                else
                    row["Height"] = Convert.ToDouble(gridView1.GetRowCellValue(i, "Height"));

                if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply && Convert.ToDouble(gridView1.GetRowCellValue(i, "Length")) <= 0)
                    row["Length"] = 1;
                else
                    row["Length"] = Convert.ToDouble(gridView1.GetRowCellValue(i, "Length"));

                if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply && Convert.ToDouble(gridView1.GetRowCellValue(i, "Width")) <= 0)
                    row["Width"] = 1;
                else
                    row["Width"] = Convert.ToDouble(gridView1.GetRowCellValue(i, "Width"));

                row["PiecesCount"] = Convert.ToDouble(gridView1.GetRowCellValue(i, "PiecesCount"));

                if (gridView1.GetRowCellValue(i, "Expire") != null)
                    row["Expire"] = Convert.ToDateTime(gridView1.GetRowCellValue(i, "Expire"));
                if (gridView1.GetRowCellValue(i, "Batch") != null)
                    row["Batch"] = gridView1.GetRowCellValue(i, "Batch").ToString();

                row["SellPrice"] = gridView1.GetRowCellValue(i, "SellPrice");
                row["Serial"] = gridView1.GetRowCellValue(i, "Serial");
                row["Serial2"] = gridView1.GetRowCellValue(i, "Serial2");

                ReportsUtils.dt_Copied_Rows.Rows.Add(row);
            }
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "Index")
                e.Value = e.RowHandle() + 1;
        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_CustomerItemsSales).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }

    }
}