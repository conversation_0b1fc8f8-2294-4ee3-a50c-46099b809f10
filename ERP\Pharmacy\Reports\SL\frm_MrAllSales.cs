﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_MrAllSales : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        byte fltrTyp_Date;

        byte FltrTyp_Company;
        int companyId;

        int custGroupId;
        DateTime date1, date2;
        string custGroupAccNumber;
        List<SL_CustomerCategoryInfo> lstCustGrp = new List<SL_CustomerCategoryInfo>();
        List<IC_Item> lstItem = new List<IC_Item>();
        DataTable dt = new DataTable();

        public frm_MrAllSales(string reportName, string dateFilter, string otherFilters,

            byte fltrTyp_Date, DateTime date1, DateTime date2, int custGroupId, string custGroupAccNumber, byte FltrTyp_Company, int companyId)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;


            this.fltrTyp_Date = fltrTyp_Date;
            this.date1 = date1;
            this.date2 = date2;
            if (this.date2 == Shared.minDate)
                this.date2 = Shared.maxDate;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;
            
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            getReportHeader();

            ERPDataContext DB = new ERPDataContext();
            lstItem = DB.IC_Items.Where(x => x.UsedInMarketing).ToList();
            rep_item.DataSource = lstItem;
            rep_item.ValueMember = "ItemId";
            rep_item.DisplayMember = "ItemNameAr";

            lstCustGrp = (from g in DB.SL_CustomerGroups
                          join a in DB.ACC_Accounts
                          on g.AccountId equals a.AccountId
                          orderby a.AcNumber
                          select new SL_CustomerCategoryInfo
                          {
                              CustomerGroupId = g.CustomerGroupId,
                              CustomerGroupCode = g.CustomerGroupCode,
                              CGNameAr = g.CGNameAr,
                              CGNameEn = g.CGNameEn,
                              Desc = g.Desc,
                              MaxCredit = g.MaxCredit,
                              AccountId = a.AccountId,
                              AcNumber = a.AcNumber
                          }).ToList();
                
            repGrp.DataSource = lstCustGrp;
            repGrp.ValueMember = "AcNumber";
            repGrp.DisplayMember = "CGNameAr";

            bandedGridView1.OptionsView.ColumnAutoWidth = false;

        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ERPDataContext DB = new ERPDataContext();

            #region Init_DataTable
            dt = new DataTable();
            dt.Columns.Add("ParentGrpId", typeof(int));
            dt.Columns.Add("ParentAcNum");
            dt.Columns.Add("CustomerGroupId", typeof(int));
            dt.Columns.Add("AcNumber");
            dt.Columns.Add("ItemId", typeof(int));

            dt.Columns.Add("SellPrice", typeof(decimal));
            dt.Columns.Add("DiscR", typeof(decimal));
            dt.Columns.Add("SellPriceBeforeDiscR", typeof(decimal));
            dt.Columns.Add("TotalSellPrice", typeof(decimal));

            dt.Columns.Add("TargetQty", typeof(decimal));
            dt.Columns.Add("TargetValue", typeof(decimal));
            dt.Columns.Add("Ratio", typeof(decimal));
            dt.Columns.Add("Weight", typeof(decimal));
            dt.Columns.Add("Achieve", typeof(decimal));

            dt.Columns.Add("DirectSoldQty", typeof(decimal));
            dt.Columns.Add("DirectSoldValue", typeof(decimal));
            dt.Columns.Add("TotalSoldQty", typeof(decimal));

            var indirectComps = DB.Mr_InDirectSalesComps.ToList();
            foreach (var indir in indirectComps)
            {
                var col_ = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
                col_.Name = "col_" + indir.CompId;
                col_.FieldName = "InDirect_" + indir.CompId;
                col_.Caption = indir.CompName + " " + (Shared.IsEnglish ? ResEn.txt_QtyText : ResAr.txt_QtyText);
                col_.Visible = true;
                col_.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
                col_.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                col_.DisplayFormat.FormatString = "n2";
                bandedGridView1.GroupSummary.Add(DevExpress.Data.SummaryItemType.Sum, col_.FieldName, col_, "{0:n2}");
                col_.SummaryItem.DisplayFormat = "{0:n2}";
                col_.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;
                bandedGridView1.Columns.Add(col_);
                gridBand_InDirect.Columns.Add(col_);
                dt.Columns.Add(col_.FieldName, typeof(decimal));

                var colValue_ = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
                colValue_.Name = "colValue_" + indir.CompId;
                colValue_.FieldName = "InDirectValue_" + indir.CompId;
                colValue_.Caption = indir.CompName + " " + (Shared.IsEnglish ? ResEn.txt_ValueText : ResAr.txt_ValueText);
                colValue_.Visible = true;
                colValue_.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
                colValue_.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                colValue_.DisplayFormat.FormatString = "n2";
                bandedGridView1.GroupSummary.Add(DevExpress.Data.SummaryItemType.Sum, colValue_.FieldName, colValue_, "{0:n2}");
                colValue_.SummaryItem.DisplayFormat = "{0:n2}";
                colValue_.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;
                bandedGridView1.Columns.Add(colValue_);
                gridBand_InDirect.Columns.Add(colValue_);
                dt.Columns.Add(colValue_.FieldName, typeof(decimal));
            }
            #endregion

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));

            LoadData();
            ReportsUtils.ColumnChooser(grdCategory);

            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        void LoadData()
        {
            ERPDataContext DB = new ERPDataContext();

            int level = Convert.ToInt32(cmbLevel.EditValue);                    //0 All    1 Only Direct  2 Only InDirect

            if (custGroupAccNumber == null)
                custGroupAccNumber = string.Empty;
            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var DirectData = (
                    from c in DB.SL_Customers
                    join a in DB.ACC_Accounts
                    on c.AccountId equals a.AccountId
                    where level != 2
                    where a.AcNumber.StartsWith(custGroupAccNumber)
                    join g in DB.SL_CustomerGroups on c.CategoryId equals g.CustomerGroupId
                    join i in DB.SL_Invoices on c.CustomerId equals i.CustomerId
                    where i.InvoiceDate.Date >= date1.Date && i.InvoiceDate.Date <= date2.Date
                    join s in DB.SL_InvoiceDetails on i.SL_InvoiceId equals s.SL_InvoiceId
                    where s.SellPrice > 0
                    join t in DB.IC_Items on s.ItemId equals t.ItemId
                    where t.UsedInMarketing == true
                    join ic in DB.IC_Categories on t.Category equals ic.CategoryId
                    where defaultCategories.Count() > 0 ? defaultCategories.Contains(ic.CategoryId) : true
                    select new
                    {
                        g.CustomerGroupId,
                        t.ItemId,
                        Qty = s.Qty,
                        s.UOMId,
                        s.UOMIndex,
                        t.MediumUOMFactor,
                        t.LargeUOMFactor,
                    }).Concat(
                    from c in DB.SL_Customers
                    join a in DB.ACC_Accounts
                    on c.AccountId equals a.AccountId
                    where level != 2
                    where a.AcNumber.StartsWith(custGroupAccNumber)
                    join g in DB.SL_CustomerGroups on c.CategoryId equals g.CustomerGroupId
                    join i in DB.SL_Returns on c.CustomerId equals i.CustomerId
                    where i.ReturnDate.Date >= date1.Date && i.ReturnDate.Date <= date2.Date
                    join s in DB.SL_ReturnDetails on i.SL_ReturnId equals s.SL_ReturnId
                    where s.SellPrice > 0
                    join t in DB.IC_Items on s.ItemId equals t.ItemId
                    where t.UsedInMarketing == true

                    select new
                    {
                        g.CustomerGroupId,
                        t.ItemId,
                        Qty = s.Qty * -1,
                        s.UOMId,
                        s.UOMIndex,
                        t.MediumUOMFactor,
                        t.LargeUOMFactor
                    }).ToList();

            var InDirectData = (
                    from i in DB.Mr_IndirectSales
                    where level != 1
                    where i.IndDate.Date >= date1.Date && i.IndDate.Date <= date2.Date
                    join c in DB.Mr_InDirectSalesComps on i.CompId equals c.CompId
                    join g in DB.SL_CustomerGroups on i.CustGrpId equals g.CustomerGroupId
                    join a in DB.ACC_Accounts on g.AccountId equals a.AccountId
                    join s in DB.Mr_IndirectSaleDetails on i.Mr_IndId equals s.Mr_IndId
                    join t in DB.IC_Items on s.ItemId equals t.ItemId
                    where t.UsedInMarketing == true
                    join ic in DB.IC_Categories on t.Category equals ic.CategoryId
                    where defaultCategories.Count() > 0 ? defaultCategories.Contains(ic.CategoryId) : true
                    where a.AcNumber.StartsWith(custGroupAccNumber)

                    join u in DB.IC_UOMs on s.UOMId equals u.UOMId

                    select new
                    {
                        CustomerGroupId = i.CustGrpId,
                        i.CompId,
                        t.ItemId,
                        Qty = s.Qty,
                        s.UOMId,
                        s.UOMIndex,

                        t.MediumUOMFactor,
                        t.LargeUOMFactor
                    }).ToList();

            var DirectData1 = from d in DirectData
                              select new
                              {
                                  d.CustomerGroupId,
                                  d.ItemId,
                                  SoldQty = MyHelper.CalculateUomQty(d.Qty, d.UOMIndex, MyHelper.FractionToDouble(d.MediumUOMFactor),
                                      MyHelper.FractionToDouble(d.LargeUOMFactor))
                              };

            var InDirectData1 = from d in InDirectData
                                select new
                                {
                                    d.CompId,
                                    d.CustomerGroupId,
                                    d.ItemId,
                                    SoldQty = MyHelper.CalculateUomQty(d.Qty, d.UOMIndex, MyHelper.FractionToDouble(d.MediumUOMFactor),
                                        MyHelper.FractionToDouble(d.LargeUOMFactor))
                                };


            var mrCustGrpItems = (from m in DB.Mr_CustGrpItems
                                  join g in DB.SL_CustomerGroups on m.CustGrpId equals g.CustomerGroupId
                                  join a in DB.ACC_Accounts on g.AccountId equals a.AccountId
                                  where m.Date.Date >= date1.Date
                                  && m.Date.Date <= date2.Date
                                  where a.AcNumber.StartsWith(custGroupAccNumber)

                                  group m by new { m.CustGrpId, m.ItemId, m.DiscR } into grp
                                  join t in DB.IC_Items on grp.Key.ItemId equals t.ItemId
                                  join ic in DB.IC_Categories on t.Category equals ic.CategoryId
                                  where defaultCategories.Count() > 0 ? defaultCategories.Contains(ic.CategoryId) : true
                                  select new
                                  {
                                      CustGrpId = grp.Key.CustGrpId,
                                      ItemId = grp.Key.ItemId,
                                      TargetQty = grp.Sum(x => x.TargetQty),
                                      Weight = grp.Average(x => x.Weight),
                                      SellPriceBeforeDiscR = t.SmallUOMPrice,
                                      DiscR = grp.Key.DiscR,
                                      SellPrice = t.SmallUOMPrice * grp.Key.DiscR,
                                  }).ToList();

            var ShowData = (((from m in mrCustGrpItems
                              join l in lstCustGrp
                              on m.CustGrpId equals l.CustomerGroupId
                              join gParent in lstCustGrp on l.ParentGroupId equals gParent.CustomerGroupId
                              from d in DirectData1.Where(x => x.CustomerGroupId == m.CustGrpId && x.ItemId == m.ItemId).DefaultIfEmpty()
                              select new
                              {
                                  CompId = 0,
                                  ParentGrpId = gParent.CustomerGroupId,
                                  ParentAcNum = gParent.AcNumber,
                                  CustomerGroupId = l.CustomerGroupId,
                                  AcNumber = l.AcNumber,
                                  ItemId = m.ItemId,
                                  m.SellPriceBeforeDiscR,
                                  m.DiscR,
                                  m.SellPrice,
                                  SoldQty = d == null ? 0 : d.SoldQty,
                                  m.TargetQty,
                                  m.Weight,
                              }).Concat
                                  (from m in mrCustGrpItems
                                   join l in lstCustGrp
                                   on m.CustGrpId equals l.CustomerGroupId
                                   join gParent in lstCustGrp on l.ParentGroupId equals gParent.CustomerGroupId
                                   from d in InDirectData1.Where(x => x.CustomerGroupId == m.CustGrpId && x.ItemId == m.ItemId).DefaultIfEmpty()
                                   select new
                                   {
                                       CompId = d == null ? 0 : d.CompId,
                                       ParentGrpId = gParent.CustomerGroupId,
                                       ParentAcNum = gParent.AcNumber,
                                       CustomerGroupId = l.CustomerGroupId,
                                       AcNumber = l.AcNumber,
                                       ItemId = m.ItemId,
                                       m.SellPriceBeforeDiscR,
                                       m.DiscR,
                                       m.SellPrice,
                                       SoldQty = d == null ? 0 : d.SoldQty,
                                       m.TargetQty,
                                       m.Weight,
                                   })).OrderBy(x => x.AcNumber)).ToList();


            var data_grouped = (from d in ShowData
                                group d by new
                                {
                                    d.CompId,
                                    d.ParentGrpId,
                                    d.ParentAcNum,
                                    d.CustomerGroupId,
                                    d.AcNumber,
                                    d.ItemId,
                                    d.SellPrice,
                                    d.DiscR,
                                    d.SellPriceBeforeDiscR,
                                    d.TargetQty,
                                    d.Weight,
                                } into grp
                                select new
                                {
                                    grp.Key.CompId,
                                    grp.Key.ParentGrpId,
                                    grp.Key.ParentAcNum,
                                    grp.Key.CustomerGroupId,
                                    grp.Key.AcNumber,
                                    grp.Key.ItemId,
                                    grp.Key.SellPrice,
                                    grp.Key.DiscR,
                                    grp.Key.SellPriceBeforeDiscR,
                                    grp.Key.TargetQty,
                                    grp.Key.Weight,
                                    SoldQty = grp.Select(x => x.SoldQty).Sum(),
                                }).ToList();

            var data_grouped1 = (from d in data_grouped
                                 group d by new
                                 {
                                     d.ParentGrpId,
                                     d.ParentAcNum,
                                     d.CustomerGroupId,
                                     d.AcNumber,
                                     d.ItemId,
                                     d.SellPrice,
                                     d.DiscR,
                                     d.SellPriceBeforeDiscR,
                                     d.TargetQty,
                                     d.Weight,
                                 } into grp
                                 select grp).ToList();

            dt.Rows.Clear();
            foreach (var d in data_grouped1)
            {
                DataRow dr = dt.NewRow();
                dr["ParentGrpId"] = d.Key.ParentGrpId;
                dr["ParentAcNum"] = d.Key.ParentAcNum;
                dr["CustomerGroupId"] = d.Key.CustomerGroupId;
                dr["AcNumber"] = d.Key.AcNumber;
                dr["ItemId"] = d.Key.ItemId;
                dr["TargetQty"] = d.Key.TargetQty;
                dr["TargetValue"] = d.Key.TargetQty * d.Key.SellPrice;

                dr["Weight"] = d.Key.Weight;
                dr["SellPrice"] = d.Key.SellPrice;
                dr["DiscR"] = d.Key.DiscR;
                dr["SellPriceBeforeDiscR"] = d.Key.SellPriceBeforeDiscR;

                decimal total_sold = d.Select(x => x.SoldQty).Sum();
                dr["TotalSoldQty"] = total_sold;

                dr["TotalSellPrice"] = total_sold * d.Key.SellPrice;

                foreach (var pd in d)
                {
                    if (pd.CompId == 0)
                    {
                        dr["DirectSoldQty"] = pd.SoldQty;
                        dr["DirectSoldValue"] = pd.SoldQty * d.Key.SellPrice;
                    }
                    else
                    {
                        dr["InDirect_" + pd.CompId] = pd.SoldQty;
                        dr["InDirectValue_" + pd.CompId] = pd.SoldQty * d.Key.SellPrice;
                    }
                }

                if (d.Key.TargetQty != 0)
                {
                    dr["Ratio"] = total_sold / d.Key.TargetQty;
                    dr["Achieve"] = (d.Key.Weight / 100) * (total_sold / d.Key.TargetQty);
                }

                dt.Rows.Add(dr);
            }

            grdCategory.DataSource = dt;
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_MrAllSales).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPreview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void gridView1_CustomColumnSort(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnSortEventArgs e)
        {
            //try
            //{
            //    if (e.Column.FieldName == "ParentGrpId")
            //    {
            //        DataRowView dr1 = (gridView1.DataSource as DataView)[e.ListSourceRowIndex1];
            //        DataRowView dr2 = (gridView1.DataSource as DataView)[e.ListSourceRowIndex2];
            //        e.Handled = true;
            //        if (dr1["ParentAcNum"] != null && dr2["ParentAcNum"] != null)
            //            e.Result = System.Collections.Comparer.Default.Compare(dr1["ParentAcNum"].ToString(), dr2["ParentAcNum"].ToString());
            //    }
            //    else if (e.Column.FieldName == "CustomerGroupId")
            //    {
            //        DataRowView dr1 = (gridView1.DataSource as DataView)[e.ListSourceRowIndex1];
            //        DataRowView dr2 = (gridView1.DataSource as DataView)[e.ListSourceRowIndex2];
            //        e.Handled = true;
            //        if (dr1["AcNumber"] != null && dr2["AcNumber"] != null)
            //            e.Result = System.Collections.Comparer.Default.Compare(dr1["AcNumber"].ToString(), dr2["AcNumber"].ToString());
            //    }

            //}
            //catch { }
        }
    }
}