﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using DAL.Res;

using System.Linq;
using System.Data;
using System.Data.Linq;
using DevExpress.XtraEditors;

using System.Windows.Forms;
using System.Collections.Generic;
using DevExpress.XtraPivotGrid;
using System.Drawing.Printing;

namespace Reports
{
    public partial class rpt_SL_CustomerTrans : DevExpress.XtraReports.UI.XtraReport
    {
        public bool UserCanOpen;
        string ReportName, dateFilter, otherFilters;
        byte fltrTyp_Date;

        DateTime date1, date2;
        int cusId;

        public rpt_SL_CustomerTrans(
            string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Date, DateTime date1, DateTime date2, int cusId)
        {

            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            //if (Shared.IsEnglish)
            //    RTL.MirrorGridControl(this.gridControl1);

            this.ReportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.fltrTyp_Date = fltrTyp_Date;
            this.date1 = date1;
            this.date2 = date2;
            this.cusId = cusId;
            getReportHeader();
            LoadData();
        }

        private void LoadData()
        {
            double totalDueChecks = 0, totalSell = 0, totalSellReturn = 0, totalCashIn = 0;
            ERPDataContext DB = new ERPDataContext();

            var emps = DB.HR_Employees.ToList();
            var process = DB.LKP_Processes.ToList();
            var cust = DB.SL_Customers.Where(x => x.CustomerId == cusId).First();

            var checks = (from n in DB.ACC_NotesReceivables
                          where n.IsVendor.HasValue && n.IsVendor.Value == false
                          where n.DealerId == cust.CustomerId
                          //where n.ResponseType == (byte)ReceiveNoteResponseType.Still

                          select new
                          {
                              NoteType = n.NoteType == (int)NoteType.check ? "شيك" : "كمبيالة",
                              n.NoteSerial,
                              n.NoteNumber,
                              n.RegDate,
                              n.DueDate,
                              n.Notes,
                              Amount = decimal.ToDouble(n.Amount),
                              n.ResponseType
                          }).ToList();

            gridControl1.DataSource = checks.Where(x => x.ResponseType == (byte)ReceiveNoteResponseType.Still).ToList();

            if (checks != null && checks.Count > 0)
            {
                totalDueChecks = checks.Where(x => x.ResponseType == (byte)ReceiveNoteResponseType.Still).Sum(x => x.Amount);
                totalCashIn += checks.Where(x => x.ResponseType == (byte)ReceiveNoteResponseType.PayedToBank ||
                    x.ResponseType == (byte)ReceiveNoteResponseType.PayedToDrawer).Sum(x => x.Amount);
            }

            var balance = (from c in DB.ACC_JournalDetails
                           join j in DB.ACC_Journals.Where(j => j.IsPosted)
                           on c.JournalId equals j.JournalId
                           group c by c.AccountId into grp
                           where grp.Key == cust.AccountId
                           select new
                           {
                               Credit = (grp.Where(c => c.Credit > 0).Count() > 0 ?
                                grp.Where(c => c.Credit > 0).Sum(x => x.Credit) : 0),
                               Debit = (grp.Where(c => c.Debit > 0).Count() > 0 ?
                                grp.Where(c => c.Debit > 0).Sum(x => x.Debit) : 0),

                               balance = (grp.Where(c => c.Credit > 0).Count() > 0 ?
                                grp.Where(c => c.Credit > 0).Sum(x => x.Credit) : 0)
                                    -
                                (grp.Where(c => c.Debit > 0).Count() > 0 ?
                                grp.Where(c => c.Debit > 0).Sum(x => x.Debit) : 0)
                           }).FirstOrDefault();

            var data1 = ((from qot in DB.SL_Quotes
                          join s in DB.IC_Stores
                          on qot.StoreId equals s.StoreId
                          join qotD in DB.SL_QuoteDetails
                          on qot.SL_QuoteId equals qotD.SL_QuoteId

                          where qot.CustomerId == cusId

                          where fltrTyp_Date == 1 ? qot.QuoteDate.Date == date1.Date : true
                          where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                          qot.QuoteDate >= date1 && qot.QuoteDate <= date2 : true
                          where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                          qot.QuoteDate >= date1 : true
                          where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                          qot.QuoteDate <= date2 : true

                          select new
                          {
                              Process = "101",
                              ProcessName = "عرض أسعار",
                              Id = qot.SL_QuoteId,
                              Code = qot.QuoteCode,
                              Date = qot.QuoteDate,
                              Emp = DB.HR_Employees.Where(x => x.EmpId == qot.SalesEmpId).Select(x => x.EmpName).First(),
                              Total = decimal.ToDouble(qot.Net + qot.DiscountValue),
                              DiscountRatio = decimal.ToDouble(qot.DiscountRatio),
                              DiscountValue = decimal.ToDouble(qot.DiscountValue),
                              Net = decimal.ToDouble(qot.Net),
                              Notes = qot.Notes,
                              Store = s.StoreNameAr
                          }).ToList().Union(
                        from qot in DB.SL_SalesOrders
                        join s in DB.IC_Stores
                          on qot.StoreId equals s.StoreId
                        join qotD in DB.SL_SalesOrderDetails
                        on qot.SL_SalesOrderId equals qotD.SL_SalesOrderId

                        where qot.CustomerId == cusId

                        where fltrTyp_Date == 1 ? qot.SalesOrderDate.Date == date1.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        qot.SalesOrderDate >= date1 && qot.SalesOrderDate <= date2 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        qot.SalesOrderDate >= date1 : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        qot.SalesOrderDate <= date2 : true
                        select new
                        {
                            Process = "102",
                            ProcessName = "أمر بيع",
                            Id = qot.SL_SalesOrderId,
                            Code = qot.SalesOrderCode,
                            Date = qot.SalesOrderDate,
                            Emp = DB.HR_Employees.Where(x => x.EmpId == qot.SalesEmpId).Select(x => x.EmpName).First(),
                            Total = decimal.ToDouble(qot.Net + qot.DiscountValue),
                            DiscountRatio = decimal.ToDouble(qot.DiscountRatio),
                            DiscountValue = decimal.ToDouble(qot.DiscountValue),
                            Net = decimal.ToDouble(qot.Net),
                            Notes = qot.Notes,
                            Store = s.StoreNameAr
                        }
                        ).ToList().Union(
                        from qot in DB.SL_Invoices
                        join s in DB.IC_Stores
                        on qot.StoreId equals s.StoreId
                        join qotD in DB.SL_InvoiceDetails
                        on qot.SL_InvoiceId equals qotD.SL_InvoiceId

                        where qot.CustomerId == cusId

                        where fltrTyp_Date == 1 ? qot.InvoiceDate.Date == date1.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        qot.InvoiceDate >= date1 && qot.InvoiceDate <= date2 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        qot.InvoiceDate >= date1 : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        qot.InvoiceDate <= date2 : true
                        select new
                        {
                            Process = ((int)Process.SellInvoice).ToString(),
                            ProcessName = "فاتورة بيع",
                            Id = qot.SL_InvoiceId,
                            Code = qot.InvoiceCode,
                            Date = qot.InvoiceDate,
                            Emp = DB.HR_Employees.Where(x => x.EmpId == qot.SalesEmpId).Select(x => x.EmpName).First(),
                            Total = decimal.ToDouble(qot.Net + qot.DiscountValue),
                            DiscountRatio = decimal.ToDouble(qot.DiscountRatio),
                            DiscountValue = decimal.ToDouble(qot.DiscountValue),
                            Net = decimal.ToDouble(qot.Net),
                            Notes = qot.Notes,
                            Store = s.StoreNameAr
                        }
                        ).ToList().Union(
                        from qot in DB.IC_OutTrns
                        join s in DB.IC_Stores
                        on qot.StoreId equals s.StoreId
                        join qotD in DB.IC_OutTrnsDetails
                        on qot.OutTrnsId equals qotD.OutTrnsId

                        where qot.CustomerId == cusId
                        where qot.IsVendor == (byte)IsVendor.Customer
                        where fltrTyp_Date == 1 ? qot.OutTrnsDate.Date == date1.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        qot.OutTrnsDate >= date1 && qot.OutTrnsDate <= date2 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        qot.OutTrnsDate >= date1 : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        qot.OutTrnsDate <= date2 : true
                        select new
                        {
                            Process = ((int)Process.OutTrns).ToString(),
                            ProcessName = "إذن صرف",
                            Id = qot.OutTrnsId,
                            Code = qot.OutTrnsCode,
                            Date = qot.OutTrnsDate,
                            Emp = DB.HR_Employees.Where(x => x.EmpId == qot.DeliveryEmpId).Select(x => x.EmpName).First(),
                            Total = decimal.ToDouble(0),
                            DiscountRatio = decimal.ToDouble(0),
                            DiscountValue = decimal.ToDouble(0),
                            Net = decimal.ToDouble(0),
                            Notes = qot.Notes,
                            Store = s.StoreNameAr
                        }
                        ).ToList().Union(
                        from qot in DB.IC_InTrns
                        join s in DB.IC_Stores
                        on qot.StoreId equals s.StoreId
                        join qotD in DB.IC_OutTrnsDetails
                        on qot.InTrnsId equals qotD.OutTrnsId

                        where qot.VendorId == cusId
                        where qot.IsVendor == (byte)IsVendor.Customer
                        where fltrTyp_Date == 1 ? qot.InTrnsDate.Date == date1.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        qot.InTrnsDate >= date1 && qot.InTrnsDate <= date2 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        qot.InTrnsDate >= date1 : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        qot.InTrnsDate <= date2 : true
                        select new
                        {
                            Process = ((int)Process.InTrns).ToString(),
                            ProcessName = "إذن إضافة",
                            Id = qot.InTrnsId,
                            Code = qot.InTrnsCode,
                            Date = qot.InTrnsDate,
                            Emp = "",
                            Total = decimal.ToDouble(0),
                            DiscountRatio = decimal.ToDouble(0),
                            DiscountValue = decimal.ToDouble(0),
                            Net = decimal.ToDouble(0),
                            Notes = qot.Notes,
                            Store = s.StoreNameAr
                        }).ToList().Union(
                        from qot in DB.SL_Returns
                        join s in DB.IC_Stores
                        on qot.StoreId equals s.StoreId
                        join qotD in DB.SL_ReturnDetails
                        on qot.SL_ReturnId equals qotD.SL_ReturnId

                        where qot.CustomerId == cusId

                        where fltrTyp_Date == 1 ? qot.ReturnDate.Date == date1.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        qot.ReturnDate >= date1 && qot.ReturnDate <= date2 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        qot.ReturnDate >= date1 : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        qot.ReturnDate <= date2 : true
                        select new
                        {
                            Process = ((int)Process.SellReturn).ToString(),
                            ProcessName = "مردود بيع",
                            Id = qot.SL_ReturnId,
                            Code = qot.ReturnCode,
                            Date = qot.ReturnDate,
                            Emp = "",
                            Total = decimal.ToDouble(qot.Net + qot.DiscountValue),
                            DiscountRatio = decimal.ToDouble(qot.DiscountRatio),
                            DiscountValue = decimal.ToDouble(qot.DiscountValue),
                            Net = decimal.ToDouble(qot.Net),
                            Notes = qot.Notes,
                            Store = s.StoreNameAr
                        }
                        ).ToList().Union(
                        from n in DB.ACC_CashNotes
                        join s in DB.IC_Stores
                        on n.StoreId equals s.StoreId

                        where n.IsVendor.HasValue && n.IsVendor.Value == (byte)DAL.IsVendor.Customer
                        where n.DealerId == cust.CustomerId

                        where fltrTyp_Date == 1 ? n.NoteDate.Date == date1.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        n.NoteDate >= date1 && n.NoteDate <= date2 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        n.NoteDate >= date1 : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        n.NoteDate <= date2 : true
                        select new
                        {
                            Process = n.IsPay == false ? ((int)Process.CashIn).ToString() : ((int)Process.CashOut).ToString(),
                            ProcessName = n.IsPay == false ? "سند قبض نقدي" : "سند دفع نقدي",
                            Id = n.NoteId,
                            Code = n.NoteSerial.ToString(),
                            Date = n.NoteDate,
                            Emp = "",
                            Total = decimal.ToDouble(0),
                            DiscountRatio = decimal.ToDouble(0),
                            DiscountValue = decimal.ToDouble(n.DiscountValue),
                            Net = decimal.ToDouble(n.Amount),
                            Notes = n.Notes,
                            Store = s.StoreNameAr
                        }
                        ).ToList().Union(
                        from n in DB.ACC_NotesReceivables
                        join s in DB.IC_Stores
                        on n.StoreId equals s.StoreId

                        where n.IsVendor.HasValue && n.IsVendor.Value == false
                        where n.DealerId == cust.CustomerId

                        where fltrTyp_Date == 1 ? n.RegDate.Date == date1.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        n.RegDate >= date1 && n.RegDate <= date2 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        n.RegDate >= date1 : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        n.RegDate <= date2 : true
                        select new
                        {
                            Process = ((int)Process.NotesReceivable).ToString(),
                            ProcessName = n.NoteType == (int)NoteType.check ? "استلام شيك" : "استلام كمبيالة",
                            Id = n.ReceiveId,
                            Code = n.NoteNumber,
                            Date = n.RegDate,
                            Emp = "",
                            Total = decimal.ToDouble(0),
                            DiscountRatio = decimal.ToDouble(0),
                            DiscountValue = decimal.ToDouble(0),
                            Net = decimal.ToDouble(n.Amount),
                            //Notes = n.Notes ,
                            Notes = n.ResponseType ==
                            (byte)ReceiveNoteResponseType.Still ? "الحالة: مستحق" + "\r\n" + n.Notes :
                            (n.ResponseType == (byte)ReceiveNoteResponseType.PayedToBank ? "الحالة: مسدد" + "\r\n" + n.Notes :
                            (n.ResponseType == (byte)ReceiveNoteResponseType.PayedToDrawer ? "الحالة: مسدد" + "\r\n" + n.Notes :
                            (n.ResponseType == (byte)ReceiveNoteResponseType.Rejected ? "الحالة: مردود" + "\r\n" + n.Notes :
                            "الحالة: مظهر" + "\r\n" + n.Notes
                            ))),

                            Store = s.StoreNameAr
                        }
                        ).ToList().Union(
                        from n in DB.ACC_NotesPayables
                        join s in DB.IC_Stores
                        on n.StoreId equals s.StoreId

                        where n.IsVendor.HasValue && n.IsVendor.Value == false
                        where n.DealerId == cust.CustomerId

                        where fltrTyp_Date == 1 ? n.RegDate.Date == date1.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        n.RegDate >= date1 && n.RegDate <= date2 : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        n.RegDate >= date1 : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        n.RegDate <= date2 : true
                        select new
                        {
                            Process = ((int)Process.NotesPayable).ToString(),
                            ProcessName = n.NoteType == (int)NoteType.check ? "إصدار شيك" : "إصدار كمبيالة",
                            Id = n.PayNoteId,
                            Code = n.NoteNumber,
                            Date = n.RegDate,
                            Emp = "",
                            Total = decimal.ToDouble(0),
                            DiscountRatio = decimal.ToDouble(0),
                            DiscountValue = decimal.ToDouble(0),
                            Net = decimal.ToDouble(n.Amount),
                            //Notes = n.Notes,
                            Notes = n.ResponseType ==
                            (byte)PayNoteResponseType.Still ? "الحالة: مستحق" + "\r\n" + n.Notes :
                            (n.ResponseType == (byte)PayNoteResponseType.Payed ? "الحالة: مسدد" + "\r\n" + n.Notes :
                            "الحالة: مردود" + "\r\n" + n.Notes),
                            Store = s.StoreNameAr
                        }
                        )).ToList().ToList();

            this.DataSource = data1;

            if (data1 != null && data1.Count > 0)
            {
                totalSell = data1.Where(x => x.Process == ((int)Process.SellInvoice).ToString()).Sum(x => x.Net);
                totalSellReturn = data1.Where(x => x.Process == ((int)Process.SellReturn).ToString()).Sum(x => x.Net);
                totalCashIn += data1.Where(x => x.Process == ((int)Process.CashIn).ToString()).Sum(x => x.Net) -
                    data1.Where(x => x.Process == ((int)Process.CashOut).ToString()).Sum(x => x.Net);
            }

            #region Main Data
            lbl_CustCode.Text = cust.CusCode.ToString();
            lbl_CustName.Text = cust.CusNameAr;
            lbl_Group.Text = cust.CategoryId.HasValue ? DB.SL_CustomerGroups.Where(z => z.CustomerGroupId == cust.CategoryId).Select(z => z.CGNameAr).First()
                : string.Empty;
            lbl_Mobile.Text = cust.Mobile;
            lbl_City.Text = cust.City;
            lbl_Address.Text = cust.Address;

            lbl_TotalSell.Text = totalSell.ToString();
            lbl_TotalSellReturn.Text = totalSellReturn.ToString();
            lbl_totalCashIn.Text = totalCashIn.ToString();
            lbl_totalDueNotes.Text = totalDueChecks.ToString();

            if (balance != null)
            {
                lblDebit.Text = balance.Debit > balance.Credit ?
                    decimal.ToDouble(balance.Debit - balance.Credit).ToString() : "";

                lblCredit.Text = balance.Credit > balance.Debit ?
                     decimal.ToDouble(balance.Credit - balance.Debit).ToString() : "";
            }
            #endregion

            #region Details invoices, checks ...
            lbl_ProcessName.DataBindings.Add("Text", data1, "ProcessName");
            lbl_Branch.DataBindings.Add("Text", data1, "Store");
            lbl_Code.DataBindings.Add("Text", data1, "Code");
            lbl_Date.DataBindings.Add("Text", data1, "Date");
            lbl_Emp.DataBindings.Add("Text", data1, "Emp");
            lbl_Total.DataBindings.Add("Text", data1, "Total");
            lbl_DiscRatio.DataBindings.Add("Text", data1, "DiscountRatio");
            lbl_DiscValue.DataBindings.Add("Text", data1, "DiscountValue");
            lbl_Net.DataBindings.Add("Text", data1, "Net");
            lbl_Notes.DataBindings.Add("Text", data1, "Notes");
            #endregion
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = ReportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
            lblFilter.Text = otherFilters;
        }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_CustomerTrans).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }

        //private void pivotGridControl1_CustomFieldSort(object sender, DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs e)
        //{
        //    if (e.Field.FieldName == "Service")
        //    {
        //        if (e.Value1 != e.Value2)
        //        {
        //            e.Result = Get_Row_Index(e.Value1.ToString()).CompareTo(Get_Row_Index(e.Value2.ToString()));
        //            e.Handled = true;
        //        }
        //    }
        //}

        //public int Get_Row_Index(string service)
        //{
        //    //return lst_Items_Names.IndexOf(service);

        //}

        private void bandedGridView1_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            //if (e.Column == col_BankAmount || e.Column == col_DrawerAmount || e.Column == col_OtherAmount)
            //{
            //    if (Convert.ToDecimal(e.Value) == 0)
            //        e.DisplayText = string.Empty;
            //}
        }

        private void xrSubreport2_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
            int processId = Convert.ToInt32(GetCurrentColumnValue("Process"));
            int sourceId = Convert.ToInt32(GetCurrentColumnValue("Id"));
            if (processId != (int)Process.NotesPayable &&
                processId != (int)Process.NotesReceivable &&
                processId != (int)Process.CashIn &&
                processId != (int)Process.CashOut)
                xrSubreport2.ReportSource = new Reports.rpt_SL_CustomerTrans_Sub(processId, sourceId);
            else
                xrSubreport2.ReportSource = null;
        }

    }
}
