﻿namespace Pharmacy.Forms
{
    partial class frm_SL_POS_Resturant_
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_POS_Resturant));
            DevExpress.XtraGrid.Views.Tile.TileViewItemElement tileViewItemElement1 = new DevExpress.XtraGrid.Views.Tile.TileViewItemElement();
            DevExpress.XtraGrid.Views.Tile.TileViewItemElement tileViewItemElement2 = new DevExpress.XtraGrid.Views.Tile.TileViewItemElement();
            this.tcol_ItemNameAr = new DevExpress.XtraGrid.Columns.TileViewColumn();
            this.tcol_ItemCode1 = new DevExpress.XtraGrid.Columns.TileViewColumn();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSalesReturn = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItem1 = new DevExpress.XtraBars.BarSubItem();
            this.barBtn_RCashNote = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_RNote = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnCancel = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnCommit = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barBtnFinish = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtInvoiceCode = new DevExpress.XtraEditors.TextEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.dtInvoiceDate = new DevExpress.XtraEditors.DateEdit();
            this.labelControl35 = new DevExpress.XtraEditors.LabelControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.lkpStore = new DevExpress.XtraEditors.LookUpEdit();
            this.lbl_credit_debit = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Drawers = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.txtNotes = new DevExpress.XtraEditors.MemoEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.btnAddCustomer = new DevExpress.XtraEditors.SimpleButton();
            this.lkp_Customers = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lblRemains = new DevExpress.XtraEditors.LabelControl();
            this.txtRemains = new DevExpress.XtraEditors.TextEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.txtPaid = new DevExpress.XtraEditors.TextEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.txtExpenses = new DevExpress.XtraEditors.TextEdit();
            this.txtDiscountValue = new DevExpress.XtraEditors.TextEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.txtDiscountRatio = new DevExpress.XtraEditors.TextEdit();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_frm_IC_Item = new System.Windows.Forms.ToolStripMenuItem();
            this.txtNet = new DevExpress.XtraEditors.TextEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.panelControl3 = new DevExpress.XtraEditors.PanelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.txt_TaxRatio = new DevExpress.XtraEditors.TextEdit();
            this.txt_TaxValue = new DevExpress.XtraEditors.TextEdit();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.contextMenuStrip2 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_Print = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_FinishInvoice = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_FinishAndPrint = new System.Windows.Forms.ToolStripMenuItem();
            this.grdPrInvoice = new DevExpress.XtraGrid.GridControl();
            this.gv_Details = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.gridColumn35 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.repSpinQty = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.gridColumn5 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_TotalSellPrice = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colPurchasePrice = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.txtItemCode = new DevExpress.XtraEditors.TextEdit();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.txtQty = new DevExpress.XtraEditors.TextEdit();
            this.tileControl1 = new DevExpress.XtraEditors.TileControl();
            this.listView_Items = new DevExpress.XtraGrid.GridControl();
            this.tileView1 = new DevExpress.XtraGrid.Views.Tile.TileView();
            this.tcol_CategoryNameAr = new DevExpress.XtraGrid.Columns.TileViewColumn();
            this.tcol_SmallUOMPrice = new DevExpress.XtraGrid.Columns.TileViewColumn();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRemains.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPaid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl3)).BeginInit();
            this.panelControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).BeginInit();
            this.contextMenuStrip2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_Details)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpinQty)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtQty.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listView_Items)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tileView1)).BeginInit();
            this.SuspendLayout();
            // 
            // tcol_ItemNameAr
            // 
            resources.ApplyResources(this.tcol_ItemNameAr, "tcol_ItemNameAr");
            this.tcol_ItemNameAr.FieldName = "ItemNameAr";
            this.tcol_ItemNameAr.Name = "tcol_ItemNameAr";
            this.tcol_ItemNameAr.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // tcol_ItemCode1
            // 
            resources.ApplyResources(this.tcol_ItemCode1, "tcol_ItemCode1");
            this.tcol_ItemCode1.FieldName = "ItemCode1";
            this.tcol_ItemCode1.Name = "tcol_ItemCode1";
            this.tcol_ItemCode1.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnCancel,
            this.barBtnClose,
            this.barBtnHelp,
            this.barBtnCommit,
            this.barSubItem1,
            this.barBtn_RCashNote,
            this.barBtn_RNote,
            this.barBtnSalesReturn,
            this.barBtnFinish});
            this.barManager1.MaxItemId = 38;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarAppearance.Normal.Font = ((System.Drawing.Font)(resources.GetObject("bar1.BarAppearance.Normal.Font")));
            this.bar1.BarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("bar1.BarAppearance.Normal.FontSizeDelta")));
            this.bar1.BarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bar1.BarAppearance.Normal.FontStyleDelta")));
            this.bar1.BarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bar1.BarAppearance.Normal.GradientMode")));
            this.bar1.BarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("bar1.BarAppearance.Normal.Image")));
            this.bar1.BarAppearance.Normal.Options.UseFont = true;
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(201, 161);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSalesReturn),
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem1),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnCancel),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnCommit),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnSalesReturn
            // 
            resources.ApplyResources(this.barBtnSalesReturn, "barBtnSalesReturn");
            this.barBtnSalesReturn.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSalesReturn.Glyph = global::Pharmacy.Properties.Resources.N_return3;
            this.barBtnSalesReturn.Id = 35;
            this.barBtnSalesReturn.Name = "barBtnSalesReturn";
            this.barBtnSalesReturn.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSalesReturn.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnSalesReturn_ItemClick);
            // 
            // barSubItem1
            // 
            resources.ApplyResources(this.barSubItem1, "barSubItem1");
            this.barSubItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barSubItem1.Id = 32;
            this.barSubItem1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_RCashNote),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_RNote)});
            this.barSubItem1.MenuAppearance.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barSubItem1.MenuAppearance.HeaderItemAppearance.FontSizeDelta")));
            this.barSubItem1.MenuAppearance.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barSubItem1.MenuAppearance.HeaderItemAppearance.FontStyleDelta")));
            this.barSubItem1.MenuAppearance.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barSubItem1.MenuAppearance.HeaderItemAppearance.GradientMode")));
            this.barSubItem1.MenuAppearance.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barSubItem1.MenuAppearance.HeaderItemAppearance.Image")));
            this.barSubItem1.Name = "barSubItem1";
            // 
            // barBtn_RCashNote
            // 
            resources.ApplyResources(this.barBtn_RCashNote, "barBtn_RCashNote");
            this.barBtn_RCashNote.Glyph = global::Pharmacy.Properties.Resources._16_CashIn;
            this.barBtn_RCashNote.Id = 33;
            this.barBtn_RCashNote.Name = "barBtn_RCashNote";
            this.barBtn_RCashNote.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_RCashNote.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_RCashNote_ItemClick);
            // 
            // barBtn_RNote
            // 
            resources.ApplyResources(this.barBtn_RNote, "barBtn_RNote");
            this.barBtn_RNote.Glyph = global::Pharmacy.Properties.Resources.N_CheckIn16;
            this.barBtn_RNote.Id = 34;
            this.barBtn_RNote.Name = "barBtn_RNote";
            this.barBtn_RNote.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_RNote.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_RNote_ItemClick);
            // 
            // barBtnCancel
            // 
            resources.ApplyResources(this.barBtnCancel, "barBtnCancel");
            this.barBtnCancel.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnCancel.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnCancel.Id = 0;
            this.barBtnCancel.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F9);
            this.barBtnCancel.Name = "barBtnCancel";
            this.barBtnCancel.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnCancel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnCancel_ItemClick);
            // 
            // barBtnCommit
            // 
            resources.ApplyResources(this.barBtnCommit, "barBtnCommit");
            this.barBtnCommit.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnCommit.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnCommit.Id = 25;
            this.barBtnCommit.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F12);
            this.barBtnCommit.Name = "barBtnCommit";
            this.barBtnCommit.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnCommit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 1;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.GradientMode")));
            this.barAndDockingController1.AppearancesBar.Dock.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.Image")));
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDe" +
        "lta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMod" +
        "e")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelt" +
        "a")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.Panel.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.Image")));
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.Image")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.Item.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.Image")));
            this.barAndDockingController1.AppearancesRibbon.Item.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.Image")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // barBtnFinish
            // 
            resources.ApplyResources(this.barBtnFinish, "barBtnFinish");
            this.barBtnFinish.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnFinish.Glyph = global::Pharmacy.Properties.Resources.cmt;
            this.barBtnFinish.Id = 37;
            this.barBtnFinish.Name = "barBtnFinish";
            this.barBtnFinish.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl2.Appearance.Font")));
            this.labelControl2.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl2.Appearance.FontSizeDelta")));
            this.labelControl2.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl2.Appearance.FontStyleDelta")));
            this.labelControl2.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl2.Appearance.GradientMode")));
            this.labelControl2.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl2.Appearance.Image")));
            this.labelControl2.Name = "labelControl2";
            // 
            // txtInvoiceCode
            // 
            resources.ApplyResources(this.txtInvoiceCode, "txtInvoiceCode");
            this.txtInvoiceCode.EnterMoveNextControl = true;
            this.txtInvoiceCode.Name = "txtInvoiceCode";
            this.txtInvoiceCode.Properties.AccessibleDescription = resources.GetString("txtInvoiceCode.Properties.AccessibleDescription");
            this.txtInvoiceCode.Properties.AccessibleName = resources.GetString("txtInvoiceCode.Properties.AccessibleName");
            this.txtInvoiceCode.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtInvoiceCode.Properties.Appearance.Font")));
            this.txtInvoiceCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtInvoiceCode.Properties.Appearance.FontSizeDelta")));
            this.txtInvoiceCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtInvoiceCode.Properties.Appearance.FontStyleDelta")));
            this.txtInvoiceCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtInvoiceCode.Properties.Appearance.GradientMode")));
            this.txtInvoiceCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtInvoiceCode.Properties.Appearance.Image")));
            this.txtInvoiceCode.Properties.Appearance.Options.UseFont = true;
            this.txtInvoiceCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtInvoiceCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtInvoiceCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtInvoiceCode.Properties.AutoHeight")));
            this.txtInvoiceCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtInvoiceCode.Properties.Mask.AutoComplete")));
            this.txtInvoiceCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.BeepOnError")));
            this.txtInvoiceCode.Properties.Mask.EditMask = resources.GetString("txtInvoiceCode.Properties.Mask.EditMask");
            this.txtInvoiceCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtInvoiceCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtInvoiceCode.Properties.Mask.MaskType")));
            this.txtInvoiceCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtInvoiceCode.Properties.Mask.PlaceHolder")));
            this.txtInvoiceCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.SaveLiteral")));
            this.txtInvoiceCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.ShowPlaceHolders")));
            this.txtInvoiceCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtInvoiceCode.Properties.NullValuePrompt = resources.GetString("txtInvoiceCode.Properties.NullValuePrompt");
            this.txtInvoiceCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtInvoiceCode.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl4.Appearance.Font")));
            this.labelControl4.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl4.Appearance.FontSizeDelta")));
            this.labelControl4.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl4.Appearance.FontStyleDelta")));
            this.labelControl4.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl4.Appearance.GradientMode")));
            this.labelControl4.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl4.Appearance.Image")));
            this.labelControl4.Name = "labelControl4";
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl3.Appearance.Font")));
            this.labelControl3.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl3.Appearance.FontSizeDelta")));
            this.labelControl3.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl3.Appearance.FontStyleDelta")));
            this.labelControl3.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl3.Appearance.GradientMode")));
            this.labelControl3.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl3.Appearance.Image")));
            this.labelControl3.Name = "labelControl3";
            // 
            // dtInvoiceDate
            // 
            resources.ApplyResources(this.dtInvoiceDate, "dtInvoiceDate");
            this.dtInvoiceDate.EnterMoveNextControl = true;
            this.dtInvoiceDate.MenuManager = this.barManager1;
            this.dtInvoiceDate.Name = "dtInvoiceDate";
            this.dtInvoiceDate.Properties.AccessibleDescription = resources.GetString("dtInvoiceDate.Properties.AccessibleDescription");
            this.dtInvoiceDate.Properties.AccessibleName = resources.GetString("dtInvoiceDate.Properties.AccessibleName");
            this.dtInvoiceDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.dtInvoiceDate.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("dtInvoiceDate.Properties.Appearance.Font")));
            this.dtInvoiceDate.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dtInvoiceDate.Properties.Appearance.FontSizeDelta")));
            this.dtInvoiceDate.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dtInvoiceDate.Properties.Appearance.FontStyleDelta")));
            this.dtInvoiceDate.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dtInvoiceDate.Properties.Appearance.GradientMode")));
            this.dtInvoiceDate.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dtInvoiceDate.Properties.Appearance.Image")));
            this.dtInvoiceDate.Properties.Appearance.Options.UseFont = true;
            this.dtInvoiceDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtInvoiceDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtInvoiceDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtInvoiceDate.Properties.AutoHeight")));
            this.dtInvoiceDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtInvoiceDate.Properties.Buttons"))))});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtInvoiceDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtInvoiceDate.Properties.Mask.AutoComplete")));
            this.dtInvoiceDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.BeepOnError")));
            this.dtInvoiceDate.Properties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.Mask.EditMask");
            this.dtInvoiceDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtInvoiceDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInvoiceDate.Properties.Mask.MaskType")));
            this.dtInvoiceDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtInvoiceDate.Properties.Mask.PlaceHolder")));
            this.dtInvoiceDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.SaveLiteral")));
            this.dtInvoiceDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.ShowPlaceHolders")));
            this.dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtInvoiceDate.Properties.NullValuePrompt = resources.GetString("dtInvoiceDate.Properties.NullValuePrompt");
            this.dtInvoiceDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtInvoiceDate.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl35
            // 
            resources.ApplyResources(this.labelControl35, "labelControl35");
            this.labelControl35.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl35.Appearance.Font")));
            this.labelControl35.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl35.Appearance.FontSizeDelta")));
            this.labelControl35.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl35.Appearance.FontStyleDelta")));
            this.labelControl35.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl35.Appearance.GradientMode")));
            this.labelControl35.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl35.Appearance.Image")));
            this.labelControl35.Name = "labelControl35";
            // 
            // panelControl1
            // 
            resources.ApplyResources(this.panelControl1, "panelControl1");
            this.panelControl1.Controls.Add(this.lkpStore);
            this.panelControl1.Controls.Add(this.lbl_credit_debit);
            this.panelControl1.Controls.Add(this.lkp_Drawers);
            this.panelControl1.Controls.Add(this.labelControl17);
            this.panelControl1.Controls.Add(this.txtNotes);
            this.panelControl1.Controls.Add(this.labelControl5);
            this.panelControl1.Controls.Add(this.dtInvoiceDate);
            this.panelControl1.Controls.Add(this.btnAddCustomer);
            this.panelControl1.Controls.Add(this.labelControl4);
            this.panelControl1.Controls.Add(this.labelControl3);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Controls.Add(this.txtInvoiceCode);
            this.panelControl1.Controls.Add(this.lkp_Customers);
            this.panelControl1.Controls.Add(this.labelControl35);
            this.panelControl1.Name = "panelControl1";
            // 
            // lkpStore
            // 
            resources.ApplyResources(this.lkpStore, "lkpStore");
            this.lkpStore.EnterMoveNextControl = true;
            this.lkpStore.MenuManager = this.barManager1;
            this.lkpStore.Name = "lkpStore";
            this.lkpStore.Properties.AccessibleDescription = resources.GetString("lkpStore.Properties.AccessibleDescription");
            this.lkpStore.Properties.AccessibleName = resources.GetString("lkpStore.Properties.AccessibleName");
            this.lkpStore.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpStore.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lkpStore.Properties.Appearance.Font")));
            this.lkpStore.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpStore.Properties.Appearance.FontSizeDelta")));
            this.lkpStore.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpStore.Properties.Appearance.FontStyleDelta")));
            this.lkpStore.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpStore.Properties.Appearance.GradientMode")));
            this.lkpStore.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpStore.Properties.Appearance.Image")));
            this.lkpStore.Properties.Appearance.Options.UseFont = true;
            this.lkpStore.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpStore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpStore.Properties.AutoHeight = ((bool)(resources.GetObject("lkpStore.Properties.AutoHeight")));
            this.lkpStore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpStore.Properties.Buttons"))))});
            this.lkpStore.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns"), resources.GetString("lkpStore.Properties.Columns1"), ((int)(resources.GetObject("lkpStore.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns3"))), resources.GetString("lkpStore.Properties.Columns4"), ((bool)(resources.GetObject("lkpStore.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns7"), resources.GetString("lkpStore.Properties.Columns8"), ((int)(resources.GetObject("lkpStore.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns10"))), resources.GetString("lkpStore.Properties.Columns11"), ((bool)(resources.GetObject("lkpStore.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns14"), resources.GetString("lkpStore.Properties.Columns15"), ((int)(resources.GetObject("lkpStore.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns17"))), resources.GetString("lkpStore.Properties.Columns18"), ((bool)(resources.GetObject("lkpStore.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns21"), resources.GetString("lkpStore.Properties.Columns22"), ((int)(resources.GetObject("lkpStore.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns24"))), resources.GetString("lkpStore.Properties.Columns25"), ((bool)(resources.GetObject("lkpStore.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns27")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns28"), resources.GetString("lkpStore.Properties.Columns29"), ((int)(resources.GetObject("lkpStore.Properties.Columns30"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns31"))), resources.GetString("lkpStore.Properties.Columns32"), ((bool)(resources.GetObject("lkpStore.Properties.Columns33"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns34")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns35"), resources.GetString("lkpStore.Properties.Columns36"), ((int)(resources.GetObject("lkpStore.Properties.Columns37"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns38"))), resources.GetString("lkpStore.Properties.Columns39"), ((bool)(resources.GetObject("lkpStore.Properties.Columns40"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns41")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns42"), resources.GetString("lkpStore.Properties.Columns43"), ((int)(resources.GetObject("lkpStore.Properties.Columns44"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns45"))), resources.GetString("lkpStore.Properties.Columns46"), ((bool)(resources.GetObject("lkpStore.Properties.Columns47"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns48")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns49"), resources.GetString("lkpStore.Properties.Columns50"), ((int)(resources.GetObject("lkpStore.Properties.Columns51"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns52"))), resources.GetString("lkpStore.Properties.Columns53"), ((bool)(resources.GetObject("lkpStore.Properties.Columns54"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns55"))))});
            this.lkpStore.Properties.NullText = resources.GetString("lkpStore.Properties.NullText");
            this.lkpStore.Properties.NullValuePrompt = resources.GetString("lkpStore.Properties.NullValuePrompt");
            this.lkpStore.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpStore.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpStore.EditValueChanged += new System.EventHandler(this.lkpStore_EditValueChanged);
            this.lkpStore.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.lkpStore_EditValueChanging);
            // 
            // lbl_credit_debit
            // 
            resources.ApplyResources(this.lbl_credit_debit, "lbl_credit_debit");
            this.lbl_credit_debit.Appearance.FontSizeDelta = ((int)(resources.GetObject("lbl_credit_debit.Appearance.FontSizeDelta")));
            this.lbl_credit_debit.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lbl_credit_debit.Appearance.FontStyleDelta")));
            this.lbl_credit_debit.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lbl_credit_debit.Appearance.GradientMode")));
            this.lbl_credit_debit.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lbl_credit_debit.Appearance.Image")));
            this.lbl_credit_debit.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lbl_credit_debit.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lbl_credit_debit.Name = "lbl_credit_debit";
            // 
            // lkp_Drawers
            // 
            resources.ApplyResources(this.lkp_Drawers, "lkp_Drawers");
            this.lkp_Drawers.EnterMoveNextControl = true;
            this.lkp_Drawers.MenuManager = this.barManager1;
            this.lkp_Drawers.Name = "lkp_Drawers";
            this.lkp_Drawers.Properties.AccessibleDescription = resources.GetString("lkp_Drawers.Properties.AccessibleDescription");
            this.lkp_Drawers.Properties.AccessibleName = resources.GetString("lkp_Drawers.Properties.AccessibleName");
            this.lkp_Drawers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Drawers.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lkp_Drawers.Properties.Appearance.Font")));
            this.lkp_Drawers.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Drawers.Properties.Appearance.FontSizeDelta")));
            this.lkp_Drawers.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Drawers.Properties.Appearance.FontStyleDelta")));
            this.lkp_Drawers.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Drawers.Properties.Appearance.GradientMode")));
            this.lkp_Drawers.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Drawers.Properties.Appearance.Image")));
            this.lkp_Drawers.Properties.Appearance.Options.UseFont = true;
            this.lkp_Drawers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Drawers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Drawers.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Drawers.Properties.AutoHeight")));
            this.lkp_Drawers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Drawers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Drawers.Properties.Buttons"))))});
            this.lkp_Drawers.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns"), resources.GetString("lkp_Drawers.Properties.Columns1"), ((int)(resources.GetObject("lkp_Drawers.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Drawers.Properties.Columns3"))), resources.GetString("lkp_Drawers.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Drawers.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Drawers.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns7"), resources.GetString("lkp_Drawers.Properties.Columns8"))});
            this.lkp_Drawers.Properties.DisplayMember = "AccountName";
            this.lkp_Drawers.Properties.NullText = resources.GetString("lkp_Drawers.Properties.NullText");
            this.lkp_Drawers.Properties.NullValuePrompt = resources.GetString("lkp_Drawers.Properties.NullValuePrompt");
            this.lkp_Drawers.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Drawers.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Drawers.Properties.ValueMember = "AccountId";
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl17.Appearance.Font")));
            this.labelControl17.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl17.Appearance.FontSizeDelta")));
            this.labelControl17.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl17.Appearance.FontStyleDelta")));
            this.labelControl17.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl17.Appearance.GradientMode")));
            this.labelControl17.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl17.Appearance.Image")));
            this.labelControl17.Name = "labelControl17";
            // 
            // txtNotes
            // 
            resources.ApplyResources(this.txtNotes, "txtNotes");
            this.txtNotes.EnterMoveNextControl = true;
            this.txtNotes.MenuManager = this.barManager1;
            this.txtNotes.Name = "txtNotes";
            this.txtNotes.Properties.AccessibleDescription = resources.GetString("txtNotes.Properties.AccessibleDescription");
            this.txtNotes.Properties.AccessibleName = resources.GetString("txtNotes.Properties.AccessibleName");
            this.txtNotes.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtNotes.Properties.Appearance.Font")));
            this.txtNotes.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtNotes.Properties.Appearance.FontSizeDelta")));
            this.txtNotes.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtNotes.Properties.Appearance.FontStyleDelta")));
            this.txtNotes.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtNotes.Properties.Appearance.GradientMode")));
            this.txtNotes.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtNotes.Properties.Appearance.Image")));
            this.txtNotes.Properties.Appearance.Options.UseFont = true;
            this.txtNotes.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNotes.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNotes.Properties.NullValuePrompt = resources.GetString("txtNotes.Properties.NullValuePrompt");
            this.txtNotes.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtNotes.Properties.NullValuePromptShowForEmptyValue")));
            this.txtNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.None;
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl5.Appearance.Font")));
            this.labelControl5.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl5.Appearance.FontSizeDelta")));
            this.labelControl5.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl5.Appearance.FontStyleDelta")));
            this.labelControl5.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl5.Appearance.GradientMode")));
            this.labelControl5.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl5.Appearance.Image")));
            this.labelControl5.Name = "labelControl5";
            // 
            // btnAddCustomer
            // 
            resources.ApplyResources(this.btnAddCustomer, "btnAddCustomer");
            this.btnAddCustomer.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("btnAddCustomer.Appearance.Font")));
            this.btnAddCustomer.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnAddCustomer.Appearance.FontSizeDelta")));
            this.btnAddCustomer.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnAddCustomer.Appearance.FontStyleDelta")));
            this.btnAddCustomer.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnAddCustomer.Appearance.GradientMode")));
            this.btnAddCustomer.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnAddCustomer.Appearance.Image")));
            this.btnAddCustomer.Appearance.Options.UseFont = true;
            this.btnAddCustomer.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnAddCustomer.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddCustomer.Name = "btnAddCustomer";
            this.btnAddCustomer.TabStop = false;
            this.btnAddCustomer.Click += new System.EventHandler(this.btnAddCustomer_Click);
            // 
            // lkp_Customers
            // 
            resources.ApplyResources(this.lkp_Customers, "lkp_Customers");
            this.lkp_Customers.EnterMoveNextControl = true;
            this.lkp_Customers.MenuManager = this.barManager1;
            this.lkp_Customers.Name = "lkp_Customers";
            this.lkp_Customers.Properties.AccessibleDescription = resources.GetString("lkp_Customers.Properties.AccessibleDescription");
            this.lkp_Customers.Properties.AccessibleName = resources.GetString("lkp_Customers.Properties.AccessibleName");
            this.lkp_Customers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Customers.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lkp_Customers.Properties.Appearance.Font")));
            this.lkp_Customers.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Customers.Properties.Appearance.FontSizeDelta")));
            this.lkp_Customers.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Customers.Properties.Appearance.FontStyleDelta")));
            this.lkp_Customers.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Customers.Properties.Appearance.GradientMode")));
            this.lkp_Customers.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Customers.Properties.Appearance.Image")));
            this.lkp_Customers.Properties.Appearance.Options.UseFont = true;
            this.lkp_Customers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Customers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Customers.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Customers.Properties.AutoHeight")));
            this.lkp_Customers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Customers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Customers.Properties.Buttons"))))});
            this.lkp_Customers.Properties.ImmediatePopup = true;
            this.lkp_Customers.Properties.NullText = resources.GetString("lkp_Customers.Properties.NullText");
            this.lkp_Customers.Properties.NullValuePrompt = resources.GetString("lkp_Customers.Properties.NullValuePrompt");
            this.lkp_Customers.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Customers.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Customers.Properties.View = this.gridView1;
            this.lkp_Customers.EditValueChanged += new System.EventHandler(this.lkp_Customers_EditValueChanged);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn4});
            this.gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.EnableAppearanceOddRow = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // gridColumn19
            // 
            resources.ApplyResources(this.gridColumn19, "gridColumn19");
            this.gridColumn19.FieldName = "CustomerId";
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            resources.ApplyResources(this.gridColumn20, "gridColumn20");
            this.gridColumn20.FieldName = "CusCode";
            this.gridColumn20.Name = "gridColumn20";
            // 
            // gridColumn21
            // 
            resources.ApplyResources(this.gridColumn21, "gridColumn21");
            this.gridColumn21.FieldName = "CusNameAr";
            this.gridColumn21.Name = "gridColumn21";
            // 
            // gridColumn22
            // 
            resources.ApplyResources(this.gridColumn22, "gridColumn22");
            this.gridColumn22.FieldName = "CusNameEn";
            this.gridColumn22.Name = "gridColumn22";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "DiscountRatio";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // lblRemains
            // 
            resources.ApplyResources(this.lblRemains, "lblRemains");
            this.lblRemains.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblRemains.Appearance.Font")));
            this.lblRemains.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblRemains.Appearance.FontSizeDelta")));
            this.lblRemains.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblRemains.Appearance.FontStyleDelta")));
            this.lblRemains.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblRemains.Appearance.GradientMode")));
            this.lblRemains.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblRemains.Appearance.Image")));
            this.lblRemains.Name = "lblRemains";
            // 
            // txtRemains
            // 
            resources.ApplyResources(this.txtRemains, "txtRemains");
            this.txtRemains.Name = "txtRemains";
            this.txtRemains.Properties.AccessibleDescription = resources.GetString("txtRemains.Properties.AccessibleDescription");
            this.txtRemains.Properties.AccessibleName = resources.GetString("txtRemains.Properties.AccessibleName");
            this.txtRemains.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtRemains.Properties.Appearance.Font")));
            this.txtRemains.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtRemains.Properties.Appearance.FontSizeDelta")));
            this.txtRemains.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtRemains.Properties.Appearance.FontStyleDelta")));
            this.txtRemains.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtRemains.Properties.Appearance.GradientMode")));
            this.txtRemains.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtRemains.Properties.Appearance.Image")));
            this.txtRemains.Properties.Appearance.Options.UseFont = true;
            this.txtRemains.Properties.Appearance.Options.UseTextOptions = true;
            this.txtRemains.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtRemains.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("txtRemains.Properties.AppearanceDisabled.BackColor")));
            this.txtRemains.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("txtRemains.Properties.AppearanceDisabled.FontSizeDelta")));
            this.txtRemains.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtRemains.Properties.AppearanceDisabled.FontStyleDelta")));
            this.txtRemains.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtRemains.Properties.AppearanceDisabled.ForeColor")));
            this.txtRemains.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtRemains.Properties.AppearanceDisabled.GradientMode")));
            this.txtRemains.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("txtRemains.Properties.AppearanceDisabled.Image")));
            this.txtRemains.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.txtRemains.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.txtRemains.Properties.AutoHeight = ((bool)(resources.GetObject("txtRemains.Properties.AutoHeight")));
            this.txtRemains.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtRemains.Properties.Mask.AutoComplete")));
            this.txtRemains.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtRemains.Properties.Mask.BeepOnError")));
            this.txtRemains.Properties.Mask.EditMask = resources.GetString("txtRemains.Properties.Mask.EditMask");
            this.txtRemains.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtRemains.Properties.Mask.IgnoreMaskBlank")));
            this.txtRemains.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtRemains.Properties.Mask.MaskType")));
            this.txtRemains.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtRemains.Properties.Mask.PlaceHolder")));
            this.txtRemains.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtRemains.Properties.Mask.SaveLiteral")));
            this.txtRemains.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtRemains.Properties.Mask.ShowPlaceHolders")));
            this.txtRemains.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtRemains.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtRemains.Properties.NullValuePrompt = resources.GetString("txtRemains.Properties.NullValuePrompt");
            this.txtRemains.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtRemains.Properties.NullValuePromptShowForEmptyValue")));
            this.txtRemains.TabStop = false;
            this.txtRemains.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtRemains.EditValueChanged += new System.EventHandler(this.txtRemains_EditValueChanged);
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl14.Appearance.Font")));
            this.labelControl14.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl14.Appearance.FontSizeDelta")));
            this.labelControl14.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl14.Appearance.FontStyleDelta")));
            this.labelControl14.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl14.Appearance.GradientMode")));
            this.labelControl14.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl14.Appearance.Image")));
            this.labelControl14.Name = "labelControl14";
            // 
            // txtPaid
            // 
            resources.ApplyResources(this.txtPaid, "txtPaid");
            this.txtPaid.EnterMoveNextControl = true;
            this.txtPaid.Name = "txtPaid";
            this.txtPaid.Properties.AccessibleDescription = resources.GetString("txtPaid.Properties.AccessibleDescription");
            this.txtPaid.Properties.AccessibleName = resources.GetString("txtPaid.Properties.AccessibleName");
            this.txtPaid.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtPaid.Properties.Appearance.Font")));
            this.txtPaid.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtPaid.Properties.Appearance.FontSizeDelta")));
            this.txtPaid.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtPaid.Properties.Appearance.FontStyleDelta")));
            this.txtPaid.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtPaid.Properties.Appearance.GradientMode")));
            this.txtPaid.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtPaid.Properties.Appearance.Image")));
            this.txtPaid.Properties.Appearance.Options.UseFont = true;
            this.txtPaid.Properties.Appearance.Options.UseTextOptions = true;
            this.txtPaid.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtPaid.Properties.AutoHeight = ((bool)(resources.GetObject("txtPaid.Properties.AutoHeight")));
            this.txtPaid.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtPaid.Properties.Mask.AutoComplete")));
            this.txtPaid.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtPaid.Properties.Mask.BeepOnError")));
            this.txtPaid.Properties.Mask.EditMask = resources.GetString("txtPaid.Properties.Mask.EditMask");
            this.txtPaid.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtPaid.Properties.Mask.IgnoreMaskBlank")));
            this.txtPaid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtPaid.Properties.Mask.MaskType")));
            this.txtPaid.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtPaid.Properties.Mask.PlaceHolder")));
            this.txtPaid.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtPaid.Properties.Mask.SaveLiteral")));
            this.txtPaid.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtPaid.Properties.Mask.ShowPlaceHolders")));
            this.txtPaid.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtPaid.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtPaid.Properties.NullValuePrompt = resources.GetString("txtPaid.Properties.NullValuePrompt");
            this.txtPaid.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtPaid.Properties.NullValuePromptShowForEmptyValue")));
            this.txtPaid.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtPaid.EditValueChanged += new System.EventHandler(this.txt_paid_EditValueChanged);
            this.txtPaid.Enter += new System.EventHandler(this.txtDiscountRatio_Enter);
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl9.Appearance.Font")));
            this.labelControl9.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl9.Appearance.FontSizeDelta")));
            this.labelControl9.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl9.Appearance.FontStyleDelta")));
            this.labelControl9.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl9.Appearance.GradientMode")));
            this.labelControl9.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl9.Appearance.Image")));
            this.labelControl9.Name = "labelControl9";
            // 
            // txtExpenses
            // 
            resources.ApplyResources(this.txtExpenses, "txtExpenses");
            this.txtExpenses.EnterMoveNextControl = true;
            this.txtExpenses.Name = "txtExpenses";
            this.txtExpenses.Properties.AccessibleDescription = resources.GetString("txtExpenses.Properties.AccessibleDescription");
            this.txtExpenses.Properties.AccessibleName = resources.GetString("txtExpenses.Properties.AccessibleName");
            this.txtExpenses.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtExpenses.Properties.Appearance.Font")));
            this.txtExpenses.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtExpenses.Properties.Appearance.FontSizeDelta")));
            this.txtExpenses.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtExpenses.Properties.Appearance.FontStyleDelta")));
            this.txtExpenses.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtExpenses.Properties.Appearance.GradientMode")));
            this.txtExpenses.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtExpenses.Properties.Appearance.Image")));
            this.txtExpenses.Properties.Appearance.Options.UseFont = true;
            this.txtExpenses.Properties.Appearance.Options.UseTextOptions = true;
            this.txtExpenses.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtExpenses.Properties.AutoHeight = ((bool)(resources.GetObject("txtExpenses.Properties.AutoHeight")));
            this.txtExpenses.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtExpenses.Properties.Mask.AutoComplete")));
            this.txtExpenses.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.BeepOnError")));
            this.txtExpenses.Properties.Mask.EditMask = resources.GetString("txtExpenses.Properties.Mask.EditMask");
            this.txtExpenses.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.IgnoreMaskBlank")));
            this.txtExpenses.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtExpenses.Properties.Mask.MaskType")));
            this.txtExpenses.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtExpenses.Properties.Mask.PlaceHolder")));
            this.txtExpenses.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.SaveLiteral")));
            this.txtExpenses.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.ShowPlaceHolders")));
            this.txtExpenses.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtExpenses.Properties.NullValuePrompt = resources.GetString("txtExpenses.Properties.NullValuePrompt");
            this.txtExpenses.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtExpenses.Properties.NullValuePromptShowForEmptyValue")));
            this.txtExpenses.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtExpenses.EditValueChanged += new System.EventHandler(this.txtDiscountExpenses_EditValueChanged);
            this.txtExpenses.Enter += new System.EventHandler(this.txtDiscountRatio_Enter);
            // 
            // txtDiscountValue
            // 
            resources.ApplyResources(this.txtDiscountValue, "txtDiscountValue");
            this.txtDiscountValue.EnterMoveNextControl = true;
            this.txtDiscountValue.Name = "txtDiscountValue";
            this.txtDiscountValue.Properties.AccessibleDescription = resources.GetString("txtDiscountValue.Properties.AccessibleDescription");
            this.txtDiscountValue.Properties.AccessibleName = resources.GetString("txtDiscountValue.Properties.AccessibleName");
            this.txtDiscountValue.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtDiscountValue.Properties.Appearance.Font")));
            this.txtDiscountValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtDiscountValue.Properties.Appearance.FontSizeDelta")));
            this.txtDiscountValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtDiscountValue.Properties.Appearance.FontStyleDelta")));
            this.txtDiscountValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtDiscountValue.Properties.Appearance.GradientMode")));
            this.txtDiscountValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtDiscountValue.Properties.Appearance.Image")));
            this.txtDiscountValue.Properties.Appearance.Options.UseFont = true;
            this.txtDiscountValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountValue.Properties.AutoHeight = ((bool)(resources.GetObject("txtDiscountValue.Properties.AutoHeight")));
            this.txtDiscountValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtDiscountValue.Properties.Mask.AutoComplete")));
            this.txtDiscountValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.BeepOnError")));
            this.txtDiscountValue.Properties.Mask.EditMask = resources.GetString("txtDiscountValue.Properties.Mask.EditMask");
            this.txtDiscountValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.IgnoreMaskBlank")));
            this.txtDiscountValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDiscountValue.Properties.Mask.MaskType")));
            this.txtDiscountValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtDiscountValue.Properties.Mask.PlaceHolder")));
            this.txtDiscountValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.SaveLiteral")));
            this.txtDiscountValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.ShowPlaceHolders")));
            this.txtDiscountValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtDiscountValue.Properties.NullValuePrompt = resources.GetString("txtDiscountValue.Properties.NullValuePrompt");
            this.txtDiscountValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtDiscountValue.Properties.NullValuePromptShowForEmptyValue")));
            this.txtDiscountValue.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtDiscountValue.EditValueChanged += new System.EventHandler(this.txtDiscountExpenses_EditValueChanged);
            this.txtDiscountValue.Enter += new System.EventHandler(this.txtDiscountRatio_Enter);
            this.txtDiscountValue.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl6.Appearance.Font")));
            this.labelControl6.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl6.Appearance.FontSizeDelta")));
            this.labelControl6.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl6.Appearance.FontStyleDelta")));
            this.labelControl6.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl6.Appearance.GradientMode")));
            this.labelControl6.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl6.Appearance.Image")));
            this.labelControl6.Name = "labelControl6";
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl7.Appearance.Font")));
            this.labelControl7.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl7.Appearance.FontSizeDelta")));
            this.labelControl7.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl7.Appearance.FontStyleDelta")));
            this.labelControl7.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl7.Appearance.GradientMode")));
            this.labelControl7.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl7.Appearance.Image")));
            this.labelControl7.Name = "labelControl7";
            // 
            // txtDiscountRatio
            // 
            resources.ApplyResources(this.txtDiscountRatio, "txtDiscountRatio");
            this.txtDiscountRatio.EnterMoveNextControl = true;
            this.txtDiscountRatio.Name = "txtDiscountRatio";
            this.txtDiscountRatio.Properties.AccessibleDescription = resources.GetString("txtDiscountRatio.Properties.AccessibleDescription");
            this.txtDiscountRatio.Properties.AccessibleName = resources.GetString("txtDiscountRatio.Properties.AccessibleName");
            this.txtDiscountRatio.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtDiscountRatio.Properties.Appearance.Font")));
            this.txtDiscountRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtDiscountRatio.Properties.Appearance.FontSizeDelta")));
            this.txtDiscountRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtDiscountRatio.Properties.Appearance.FontStyleDelta")));
            this.txtDiscountRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtDiscountRatio.Properties.Appearance.GradientMode")));
            this.txtDiscountRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtDiscountRatio.Properties.Appearance.Image")));
            this.txtDiscountRatio.Properties.Appearance.Options.UseFont = true;
            this.txtDiscountRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtDiscountRatio.Properties.AutoHeight")));
            this.txtDiscountRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtDiscountRatio.Properties.Mask.AutoComplete")));
            this.txtDiscountRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.BeepOnError")));
            this.txtDiscountRatio.Properties.Mask.EditMask = resources.GetString("txtDiscountRatio.Properties.Mask.EditMask");
            this.txtDiscountRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtDiscountRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDiscountRatio.Properties.Mask.MaskType")));
            this.txtDiscountRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtDiscountRatio.Properties.Mask.PlaceHolder")));
            this.txtDiscountRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.SaveLiteral")));
            this.txtDiscountRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtDiscountRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtDiscountRatio.Properties.NullValuePrompt = resources.GetString("txtDiscountRatio.Properties.NullValuePrompt");
            this.txtDiscountRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtDiscountRatio.Properties.NullValuePromptShowForEmptyValue")));
            this.txtDiscountRatio.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtDiscountRatio.EditValueChanged += new System.EventHandler(this.txtDiscountExpenses_EditValueChanged);
            this.txtDiscountRatio.Enter += new System.EventHandler(this.txtDiscountRatio_Enter);
            this.txtDiscountRatio.Leave += new System.EventHandler(this.txtDiscountRatio_Leave);
            // 
            // contextMenuStrip1
            // 
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_frm_IC_Item});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            // 
            // mi_frm_IC_Item
            // 
            resources.ApplyResources(this.mi_frm_IC_Item, "mi_frm_IC_Item");
            this.mi_frm_IC_Item.Name = "mi_frm_IC_Item";
            this.mi_frm_IC_Item.Click += new System.EventHandler(this.mi_frm_IC_Item_Click);
            // 
            // txtNet
            // 
            resources.ApplyResources(this.txtNet, "txtNet");
            this.txtNet.EnterMoveNextControl = true;
            this.txtNet.Name = "txtNet";
            this.txtNet.Properties.AccessibleDescription = resources.GetString("txtNet.Properties.AccessibleDescription");
            this.txtNet.Properties.AccessibleName = resources.GetString("txtNet.Properties.AccessibleName");
            this.txtNet.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtNet.Properties.Appearance.Font")));
            this.txtNet.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtNet.Properties.Appearance.FontSizeDelta")));
            this.txtNet.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtNet.Properties.Appearance.FontStyleDelta")));
            this.txtNet.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtNet.Properties.Appearance.GradientMode")));
            this.txtNet.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtNet.Properties.Appearance.Image")));
            this.txtNet.Properties.Appearance.Options.UseFont = true;
            this.txtNet.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNet.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNet.Properties.AutoHeight = ((bool)(resources.GetObject("txtNet.Properties.AutoHeight")));
            this.txtNet.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtNet.Properties.Mask.AutoComplete")));
            this.txtNet.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtNet.Properties.Mask.BeepOnError")));
            this.txtNet.Properties.Mask.EditMask = resources.GetString("txtNet.Properties.Mask.EditMask");
            this.txtNet.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtNet.Properties.Mask.IgnoreMaskBlank")));
            this.txtNet.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtNet.Properties.Mask.MaskType")));
            this.txtNet.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtNet.Properties.Mask.PlaceHolder")));
            this.txtNet.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtNet.Properties.Mask.SaveLiteral")));
            this.txtNet.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtNet.Properties.Mask.ShowPlaceHolders")));
            this.txtNet.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtNet.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtNet.Properties.NullValuePrompt = resources.GetString("txtNet.Properties.NullValuePrompt");
            this.txtNet.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtNet.Properties.NullValuePromptShowForEmptyValue")));
            this.txtNet.Properties.ReadOnly = true;
            this.txtNet.TabStop = false;
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl13.Appearance.Font")));
            this.labelControl13.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl13.Appearance.FontSizeDelta")));
            this.labelControl13.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl13.Appearance.FontStyleDelta")));
            this.labelControl13.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl13.Appearance.GradientMode")));
            this.labelControl13.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl13.Appearance.Image")));
            this.labelControl13.Name = "labelControl13";
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl19.Appearance.Font")));
            this.labelControl19.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl19.Appearance.FontSizeDelta")));
            this.labelControl19.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl19.Appearance.FontStyleDelta")));
            this.labelControl19.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl19.Appearance.GradientMode")));
            this.labelControl19.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl19.Appearance.Image")));
            this.labelControl19.Name = "labelControl19";
            // 
            // panelControl3
            // 
            resources.ApplyResources(this.panelControl3, "panelControl3");
            this.panelControl3.Controls.Add(this.labelControl12);
            this.panelControl3.Controls.Add(this.txt_TaxRatio);
            this.panelControl3.Controls.Add(this.txt_TaxValue);
            this.panelControl3.Controls.Add(this.labelControl16);
            this.panelControl3.Controls.Add(this.labelControl20);
            this.panelControl3.Controls.Add(this.labelControl19);
            this.panelControl3.Controls.Add(this.txtRemains);
            this.panelControl3.Controls.Add(this.lblRemains);
            this.panelControl3.Controls.Add(this.txtNet);
            this.panelControl3.Controls.Add(this.labelControl14);
            this.panelControl3.Controls.Add(this.labelControl13);
            this.panelControl3.Controls.Add(this.txtPaid);
            this.panelControl3.Controls.Add(this.txtDiscountRatio);
            this.panelControl3.Controls.Add(this.txtDiscountValue);
            this.panelControl3.Controls.Add(this.labelControl7);
            this.panelControl3.Controls.Add(this.labelControl6);
            this.panelControl3.Controls.Add(this.labelControl9);
            this.panelControl3.Controls.Add(this.txtExpenses);
            this.panelControl3.Name = "panelControl3";
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl12.Appearance.Font")));
            this.labelControl12.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl12.Appearance.FontSizeDelta")));
            this.labelControl12.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl12.Appearance.FontStyleDelta")));
            this.labelControl12.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl12.Appearance.GradientMode")));
            this.labelControl12.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl12.Appearance.Image")));
            this.labelControl12.Name = "labelControl12";
            // 
            // txt_TaxRatio
            // 
            resources.ApplyResources(this.txt_TaxRatio, "txt_TaxRatio");
            this.txt_TaxRatio.EnterMoveNextControl = true;
            this.txt_TaxRatio.Name = "txt_TaxRatio";
            this.txt_TaxRatio.Properties.AccessibleDescription = resources.GetString("txt_TaxRatio.Properties.AccessibleDescription");
            this.txt_TaxRatio.Properties.AccessibleName = resources.GetString("txt_TaxRatio.Properties.AccessibleName");
            this.txt_TaxRatio.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txt_TaxRatio.Properties.Appearance.Font")));
            this.txt_TaxRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_TaxRatio.Properties.Appearance.FontSizeDelta")));
            this.txt_TaxRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_TaxRatio.Properties.Appearance.FontStyleDelta")));
            this.txt_TaxRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_TaxRatio.Properties.Appearance.GradientMode")));
            this.txt_TaxRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_TaxRatio.Properties.Appearance.Image")));
            this.txt_TaxRatio.Properties.Appearance.Options.UseFont = true;
            this.txt_TaxRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_TaxRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_TaxRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txt_TaxRatio.Properties.AutoHeight")));
            this.txt_TaxRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_TaxRatio.Properties.Mask.AutoComplete")));
            this.txt_TaxRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_TaxRatio.Properties.Mask.BeepOnError")));
            this.txt_TaxRatio.Properties.Mask.EditMask = resources.GetString("txt_TaxRatio.Properties.Mask.EditMask");
            this.txt_TaxRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_TaxRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txt_TaxRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_TaxRatio.Properties.Mask.MaskType")));
            this.txt_TaxRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_TaxRatio.Properties.Mask.PlaceHolder")));
            this.txt_TaxRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_TaxRatio.Properties.Mask.SaveLiteral")));
            this.txt_TaxRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_TaxRatio.Properties.Mask.ShowPlaceHolders")));
            this.txt_TaxRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_TaxRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_TaxRatio.Properties.NullValuePrompt = resources.GetString("txt_TaxRatio.Properties.NullValuePrompt");
            this.txt_TaxRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_TaxRatio.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_TaxRatio.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_TaxRatio.EditValueChanged += new System.EventHandler(this.txtDiscountExpenses_EditValueChanged);
            this.txt_TaxRatio.Enter += new System.EventHandler(this.txtDiscountRatio_Enter);
            this.txt_TaxRatio.Leave += new System.EventHandler(this.txt_TaxRatio_Leave);
            // 
            // txt_TaxValue
            // 
            resources.ApplyResources(this.txt_TaxValue, "txt_TaxValue");
            this.txt_TaxValue.EnterMoveNextControl = true;
            this.txt_TaxValue.Name = "txt_TaxValue";
            this.txt_TaxValue.Properties.AccessibleDescription = resources.GetString("txt_TaxValue.Properties.AccessibleDescription");
            this.txt_TaxValue.Properties.AccessibleName = resources.GetString("txt_TaxValue.Properties.AccessibleName");
            this.txt_TaxValue.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txt_TaxValue.Properties.Appearance.Font")));
            this.txt_TaxValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_TaxValue.Properties.Appearance.FontSizeDelta")));
            this.txt_TaxValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_TaxValue.Properties.Appearance.FontStyleDelta")));
            this.txt_TaxValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_TaxValue.Properties.Appearance.GradientMode")));
            this.txt_TaxValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_TaxValue.Properties.Appearance.Image")));
            this.txt_TaxValue.Properties.Appearance.Options.UseFont = true;
            this.txt_TaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_TaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_TaxValue.Properties.AutoHeight = ((bool)(resources.GetObject("txt_TaxValue.Properties.AutoHeight")));
            this.txt_TaxValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_TaxValue.Properties.Mask.AutoComplete")));
            this.txt_TaxValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.BeepOnError")));
            this.txt_TaxValue.Properties.Mask.EditMask = resources.GetString("txt_TaxValue.Properties.Mask.EditMask");
            this.txt_TaxValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.IgnoreMaskBlank")));
            this.txt_TaxValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_TaxValue.Properties.Mask.MaskType")));
            this.txt_TaxValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_TaxValue.Properties.Mask.PlaceHolder")));
            this.txt_TaxValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.SaveLiteral")));
            this.txt_TaxValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.ShowPlaceHolders")));
            this.txt_TaxValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_TaxValue.Properties.NullValuePrompt = resources.GetString("txt_TaxValue.Properties.NullValuePrompt");
            this.txt_TaxValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_TaxValue.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_TaxValue.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_TaxValue.EditValueChanged += new System.EventHandler(this.txtDiscountExpenses_EditValueChanged);
            this.txt_TaxValue.Enter += new System.EventHandler(this.txtDiscountRatio_Enter);
            this.txt_TaxValue.Leave += new System.EventHandler(this.txt_TaxValue_Leave);
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl16.Appearance.Font")));
            this.labelControl16.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl16.Appearance.FontSizeDelta")));
            this.labelControl16.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl16.Appearance.FontStyleDelta")));
            this.labelControl16.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl16.Appearance.GradientMode")));
            this.labelControl16.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl16.Appearance.Image")));
            this.labelControl16.Name = "labelControl16";
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl20.Appearance.Font")));
            this.labelControl20.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl20.Appearance.FontSizeDelta")));
            this.labelControl20.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl20.Appearance.FontStyleDelta")));
            this.labelControl20.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl20.Appearance.GradientMode")));
            this.labelControl20.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl20.Appearance.Image")));
            this.labelControl20.Name = "labelControl20";
            // 
            // contextMenuStrip2
            // 
            resources.ApplyResources(this.contextMenuStrip2, "contextMenuStrip2");
            this.contextMenuStrip2.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_Print,
            this.mi_FinishInvoice,
            this.mi_FinishAndPrint});
            this.contextMenuStrip2.Name = "contextMenuStrip2";
            // 
            // mi_Print
            // 
            resources.ApplyResources(this.mi_Print, "mi_Print");
            this.mi_Print.Name = "mi_Print";
            this.mi_Print.Click += new System.EventHandler(this.mi_Print_Click);
            // 
            // mi_FinishInvoice
            // 
            resources.ApplyResources(this.mi_FinishInvoice, "mi_FinishInvoice");
            this.mi_FinishInvoice.Name = "mi_FinishInvoice";
            this.mi_FinishInvoice.Click += new System.EventHandler(this.mi_FinishInvoice_Click);
            // 
            // mi_FinishAndPrint
            // 
            resources.ApplyResources(this.mi_FinishAndPrint, "mi_FinishAndPrint");
            this.mi_FinishAndPrint.Name = "mi_FinishAndPrint";
            this.mi_FinishAndPrint.Click += new System.EventHandler(this.mi_FinishAndPrint_Click);
            // 
            // grdPrInvoice
            // 
            resources.ApplyResources(this.grdPrInvoice, "grdPrInvoice");
            this.grdPrInvoice.ContextMenuStrip = this.contextMenuStrip1;
            this.grdPrInvoice.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleDescription");
            this.grdPrInvoice.EmbeddedNavigator.AccessibleName = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleName");
            this.grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdPrInvoice.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.Anchor")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImage")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdPrInvoice.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ImeMode")));
            this.grdPrInvoice.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.MaximumSize")));
            this.grdPrInvoice.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.TextLocation")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTip = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTip");
            this.grdPrInvoice.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ToolTipIconType")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTipTitle");
            this.grdPrInvoice.MainView = this.gv_Details;
            this.grdPrInvoice.MenuManager = this.barManager1;
            this.grdPrInvoice.Name = "grdPrInvoice";
            this.grdPrInvoice.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpinQty});
            this.grdPrInvoice.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_Details});
            // 
            // gv_Details
            // 
            this.gv_Details.Appearance.BandPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Details.Appearance.BandPanel.FontSizeDelta")));
            this.gv_Details.Appearance.BandPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Details.Appearance.BandPanel.FontStyleDelta")));
            this.gv_Details.Appearance.BandPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Details.Appearance.BandPanel.GradientMode")));
            this.gv_Details.Appearance.BandPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Details.Appearance.BandPanel.Image")));
            this.gv_Details.Appearance.BandPanel.Options.UseTextOptions = true;
            this.gv_Details.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Details.Appearance.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("gv_Details.Appearance.FooterPanel.Font")));
            this.gv_Details.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Details.Appearance.FooterPanel.FontSizeDelta")));
            this.gv_Details.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Details.Appearance.FooterPanel.FontStyleDelta")));
            this.gv_Details.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Details.Appearance.FooterPanel.GradientMode")));
            this.gv_Details.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Details.Appearance.FooterPanel.Image")));
            this.gv_Details.Appearance.FooterPanel.Options.UseFont = true;
            this.gv_Details.Appearance.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gv_Details.Appearance.HeaderPanel.Font")));
            this.gv_Details.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Details.Appearance.HeaderPanel.FontSizeDelta")));
            this.gv_Details.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Details.Appearance.HeaderPanel.FontStyleDelta")));
            this.gv_Details.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Details.Appearance.HeaderPanel.GradientMode")));
            this.gv_Details.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Details.Appearance.HeaderPanel.Image")));
            this.gv_Details.Appearance.HeaderPanel.Options.UseFont = true;
            this.gv_Details.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gv_Details.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gv_Details.Appearance.Row.Font = ((System.Drawing.Font)(resources.GetObject("gv_Details.Appearance.Row.Font")));
            this.gv_Details.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gv_Details.Appearance.Row.FontSizeDelta")));
            this.gv_Details.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Details.Appearance.Row.FontStyleDelta")));
            this.gv_Details.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Details.Appearance.Row.GradientMode")));
            this.gv_Details.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gv_Details.Appearance.Row.Image")));
            this.gv_Details.Appearance.Row.Options.UseFont = true;
            this.gv_Details.Appearance.Row.Options.UseTextOptions = true;
            this.gv_Details.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gv_Details.BandPanelRowHeight = 25;
            this.gv_Details.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand2});
            resources.ApplyResources(this.gv_Details, "gv_Details");
            this.gv_Details.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.gridColumn35,
            this.col_TotalSellPrice,
            this.gridColumn5,
            this.colPurchasePrice,
            this.colQty,
            this.gridColumn8,
            this.gridColumn3,
            this.gridColumn10,
            this.gridColumn31,
            this.gridColumn1,
            this.gridColumn2});
            this.gv_Details.GridControl = this.grdPrInvoice;
            this.gv_Details.Name = "gv_Details";
            this.gv_Details.OptionsCustomization.AllowGroup = false;
            this.gv_Details.OptionsCustomization.AllowSort = false;
            this.gv_Details.OptionsDetail.AllowZoomDetail = false;
            this.gv_Details.OptionsDetail.EnableMasterViewMode = false;
            this.gv_Details.OptionsDetail.ShowDetailTabs = false;
            this.gv_Details.OptionsDetail.SmartDetailExpand = false;
            this.gv_Details.OptionsFilter.AllowColumnMRUFilterList = false;
            this.gv_Details.OptionsFilter.AllowFilterEditor = false;
            this.gv_Details.OptionsFilter.AllowMRUFilterList = false;
            this.gv_Details.OptionsHint.ShowCellHints = false;
            this.gv_Details.OptionsHint.ShowColumnHeaderHints = false;
            this.gv_Details.OptionsHint.ShowFooterHints = false;
            this.gv_Details.OptionsLayout.StoreDataSettings = false;
            this.gv_Details.OptionsLayout.StoreVisualOptions = false;
            this.gv_Details.OptionsMenu.EnableColumnMenu = false;
            this.gv_Details.OptionsMenu.EnableFooterMenu = false;
            this.gv_Details.OptionsMenu.EnableGroupPanelMenu = false;
            this.gv_Details.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gv_Details.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gv_Details.OptionsNavigation.AutoMoveRowFocus = false;
            this.gv_Details.OptionsNavigation.UseOfficePageNavigation = false;
            this.gv_Details.OptionsNavigation.UseTabKey = false;
            this.gv_Details.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_Details.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gv_Details.OptionsView.EnableAppearanceOddRow = true;
            this.gv_Details.OptionsView.RowAutoHeight = true;
            this.gv_Details.OptionsView.ShowDetailButtons = false;
            this.gv_Details.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            this.gv_Details.OptionsView.ShowFooter = true;
            this.gv_Details.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gv_Details.OptionsView.ShowGroupPanel = false;
            this.gv_Details.OptionsView.ShowIndicator = false;
            this.gv_Details.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gridView2_CellValueChanged);
            this.gv_Details.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView2_CustomUnboundColumnData);
            this.gv_Details.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView2_CustomColumnDisplayText);
            this.gv_Details.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gridView2_KeyDown);
            // 
            // gridBand2
            // 
            resources.ApplyResources(this.gridBand2, "gridBand2");
            this.gridBand2.Columns.Add(this.gridColumn35);
            this.gridBand2.Columns.Add(this.gridColumn31);
            this.gridBand2.Columns.Add(this.col_TotalSellPrice);
            this.gridBand2.Columns.Add(this.colQty);
            this.gridBand2.Columns.Add(this.gridColumn5);
            this.gridBand2.Columns.Add(this.colPurchasePrice);
            this.gridBand2.Columns.Add(this.gridColumn8);
            this.gridBand2.Columns.Add(this.gridColumn3);
            this.gridBand2.Columns.Add(this.gridColumn10);
            this.gridBand2.Columns.Add(this.gridColumn1);
            this.gridBand2.Columns.Add(this.gridColumn2);
            this.gridBand2.VisibleIndex = 0;
            // 
            // gridColumn35
            // 
            this.gridColumn35.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn35.AppearanceCell.FontSizeDelta")));
            this.gridColumn35.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn35.AppearanceCell.FontStyleDelta")));
            this.gridColumn35.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn35.AppearanceCell.GradientMode")));
            this.gridColumn35.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn35.AppearanceCell.Image")));
            this.gridColumn35.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn35.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn35.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn35.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn35.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn35.AppearanceHeader.FontSizeDelta")));
            this.gridColumn35.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn35.AppearanceHeader.FontStyleDelta")));
            this.gridColumn35.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn35.AppearanceHeader.GradientMode")));
            this.gridColumn35.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn35.AppearanceHeader.Image")));
            this.gridColumn35.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn35.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn35.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn35.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn35, "gridColumn35");
            this.gridColumn35.FieldName = "TotalPurchasePrice";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.OptionsColumn.AllowEdit = false;
            this.gridColumn35.OptionsColumn.AllowFocus = false;
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "Index";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowEdit = false;
            this.gridColumn1.OptionsColumn.AllowFocus = false;
            this.gridColumn1.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // gridColumn31
            // 
            this.gridColumn31.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn31.AppearanceCell.FontSizeDelta")));
            this.gridColumn31.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn31.AppearanceCell.FontStyleDelta")));
            this.gridColumn31.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn31.AppearanceCell.GradientMode")));
            this.gridColumn31.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn31.AppearanceCell.Image")));
            this.gridColumn31.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn31.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn31.AppearanceHeader.FontSizeDelta")));
            this.gridColumn31.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn31.AppearanceHeader.FontStyleDelta")));
            this.gridColumn31.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn31.AppearanceHeader.GradientMode")));
            this.gridColumn31.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn31.AppearanceHeader.Image")));
            this.gridColumn31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn31, "gridColumn31");
            this.gridColumn31.FieldName = "ItemName";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.OptionsColumn.AllowEdit = false;
            this.gridColumn31.OptionsColumn.AllowFocus = false;
            // 
            // colQty
            // 
            this.colQty.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("colQty.AppearanceCell.FontSizeDelta")));
            this.colQty.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colQty.AppearanceCell.FontStyleDelta")));
            this.colQty.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colQty.AppearanceCell.GradientMode")));
            this.colQty.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("colQty.AppearanceCell.Image")));
            this.colQty.AppearanceCell.Options.UseTextOptions = true;
            this.colQty.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colQty.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colQty.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colQty.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("colQty.AppearanceHeader.FontSizeDelta")));
            this.colQty.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colQty.AppearanceHeader.FontStyleDelta")));
            this.colQty.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colQty.AppearanceHeader.GradientMode")));
            this.colQty.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("colQty.AppearanceHeader.Image")));
            this.colQty.AppearanceHeader.Options.UseTextOptions = true;
            this.colQty.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colQty.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colQty.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colQty, "colQty");
            this.colQty.ColumnEdit = this.repSpinQty;
            this.colQty.DisplayFormat.FormatString = "n2";
            this.colQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colQty.FieldName = "Qty";
            this.colQty.Name = "colQty";
            this.colQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colQty.Summary"))), resources.GetString("colQty.Summary1"), resources.GetString("colQty.Summary2"))});
            // 
            // repSpinQty
            // 
            resources.ApplyResources(this.repSpinQty, "repSpinQty");
            this.repSpinQty.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repSpinQty.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repSpinQty.Mask.AutoComplete")));
            this.repSpinQty.Mask.BeepOnError = ((bool)(resources.GetObject("repSpinQty.Mask.BeepOnError")));
            this.repSpinQty.Mask.EditMask = resources.GetString("repSpinQty.Mask.EditMask");
            this.repSpinQty.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repSpinQty.Mask.IgnoreMaskBlank")));
            this.repSpinQty.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repSpinQty.Mask.MaskType")));
            this.repSpinQty.Mask.PlaceHolder = ((char)(resources.GetObject("repSpinQty.Mask.PlaceHolder")));
            this.repSpinQty.Mask.SaveLiteral = ((bool)(resources.GetObject("repSpinQty.Mask.SaveLiteral")));
            this.repSpinQty.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repSpinQty.Mask.ShowPlaceHolders")));
            this.repSpinQty.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repSpinQty.Mask.UseMaskAsDisplayFormat")));
            this.repSpinQty.MaxValue = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.repSpinQty.Name = "repSpinQty";
            // 
            // gridColumn5
            // 
            this.gridColumn5.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn5.AppearanceCell.FontSizeDelta")));
            this.gridColumn5.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn5.AppearanceCell.FontStyleDelta")));
            this.gridColumn5.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn5.AppearanceCell.GradientMode")));
            this.gridColumn5.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn5.AppearanceCell.Image")));
            this.gridColumn5.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn5.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn5.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn5.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn5.AppearanceHeader.FontSizeDelta")));
            this.gridColumn5.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn5.AppearanceHeader.FontStyleDelta")));
            this.gridColumn5.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn5.AppearanceHeader.GradientMode")));
            this.gridColumn5.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn5.AppearanceHeader.Image")));
            this.gridColumn5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn5.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "SellPrice";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowEdit = false;
            // 
            // col_TotalSellPrice
            // 
            this.col_TotalSellPrice.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_TotalSellPrice.AppearanceCell.FontSizeDelta")));
            this.col_TotalSellPrice.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_TotalSellPrice.AppearanceCell.FontStyleDelta")));
            this.col_TotalSellPrice.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_TotalSellPrice.AppearanceCell.GradientMode")));
            this.col_TotalSellPrice.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_TotalSellPrice.AppearanceCell.Image")));
            this.col_TotalSellPrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_TotalSellPrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_TotalSellPrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_TotalSellPrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_TotalSellPrice.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_TotalSellPrice.AppearanceHeader.FontSizeDelta")));
            this.col_TotalSellPrice.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_TotalSellPrice.AppearanceHeader.FontStyleDelta")));
            this.col_TotalSellPrice.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_TotalSellPrice.AppearanceHeader.GradientMode")));
            this.col_TotalSellPrice.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_TotalSellPrice.AppearanceHeader.Image")));
            this.col_TotalSellPrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_TotalSellPrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_TotalSellPrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_TotalSellPrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_TotalSellPrice, "col_TotalSellPrice");
            this.col_TotalSellPrice.DisplayFormat.FormatString = "n2";
            this.col_TotalSellPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalSellPrice.FieldName = "TotalSellPrice";
            this.col_TotalSellPrice.Name = "col_TotalSellPrice";
            this.col_TotalSellPrice.OptionsColumn.AllowEdit = false;
            this.col_TotalSellPrice.OptionsColumn.ReadOnly = true;
            this.col_TotalSellPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSellPrice.Summary"))), resources.GetString("col_TotalSellPrice.Summary1"), resources.GetString("col_TotalSellPrice.Summary2"))});
            // 
            // colPurchasePrice
            // 
            this.colPurchasePrice.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("colPurchasePrice.AppearanceCell.FontSizeDelta")));
            this.colPurchasePrice.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colPurchasePrice.AppearanceCell.FontStyleDelta")));
            this.colPurchasePrice.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colPurchasePrice.AppearanceCell.GradientMode")));
            this.colPurchasePrice.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("colPurchasePrice.AppearanceCell.Image")));
            this.colPurchasePrice.AppearanceCell.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colPurchasePrice.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("colPurchasePrice.AppearanceHeader.FontSizeDelta")));
            this.colPurchasePrice.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colPurchasePrice.AppearanceHeader.FontStyleDelta")));
            this.colPurchasePrice.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colPurchasePrice.AppearanceHeader.GradientMode")));
            this.colPurchasePrice.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("colPurchasePrice.AppearanceHeader.Image")));
            this.colPurchasePrice.AppearanceHeader.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colPurchasePrice, "colPurchasePrice");
            this.colPurchasePrice.FieldName = "PurchasePrice";
            this.colPurchasePrice.Name = "colPurchasePrice";
            this.colPurchasePrice.OptionsColumn.AllowEdit = false;
            this.colPurchasePrice.OptionsColumn.AllowFocus = false;
            this.colPurchasePrice.OptionsColumn.ReadOnly = true;
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn8.AppearanceCell.FontSizeDelta")));
            this.gridColumn8.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn8.AppearanceCell.FontStyleDelta")));
            this.gridColumn8.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn8.AppearanceCell.GradientMode")));
            this.gridColumn8.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn8.AppearanceCell.Image")));
            this.gridColumn8.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn8.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn8.AppearanceHeader.FontSizeDelta")));
            this.gridColumn8.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn8.AppearanceHeader.FontStyleDelta")));
            this.gridColumn8.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn8.AppearanceHeader.GradientMode")));
            this.gridColumn8.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn8.AppearanceHeader.Image")));
            this.gridColumn8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.FieldName = "UOMName";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.OptionsColumn.AllowEdit = false;
            this.gridColumn8.OptionsColumn.AllowFocus = false;
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "UOMId";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.OptionsColumn.AllowEdit = false;
            this.gridColumn3.OptionsColumn.AllowFocus = false;
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn10.AppearanceCell.FontSizeDelta")));
            this.gridColumn10.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn10.AppearanceCell.FontStyleDelta")));
            this.gridColumn10.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn10.AppearanceCell.GradientMode")));
            this.gridColumn10.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn10.AppearanceCell.Image")));
            this.gridColumn10.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn10.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn10.AppearanceHeader.FontSizeDelta")));
            this.gridColumn10.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn10.AppearanceHeader.FontStyleDelta")));
            this.gridColumn10.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn10.AppearanceHeader.GradientMode")));
            this.gridColumn10.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn10.AppearanceHeader.Image")));
            this.gridColumn10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.FieldName = "ItemName";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.OptionsColumn.AllowEdit = false;
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.FieldName = "ItemId";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowEdit = false;
            this.gridColumn2.OptionsColumn.AllowFocus = false;
            // 
            // txtItemCode
            // 
            resources.ApplyResources(this.txtItemCode, "txtItemCode");
            this.txtItemCode.Name = "txtItemCode";
            this.txtItemCode.Properties.AccessibleDescription = resources.GetString("txtItemCode.Properties.AccessibleDescription");
            this.txtItemCode.Properties.AccessibleName = resources.GetString("txtItemCode.Properties.AccessibleName");
            this.txtItemCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtItemCode.Properties.Appearance.FontSizeDelta")));
            this.txtItemCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtItemCode.Properties.Appearance.FontStyleDelta")));
            this.txtItemCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtItemCode.Properties.Appearance.GradientMode")));
            this.txtItemCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtItemCode.Properties.Appearance.Image")));
            this.txtItemCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtItemCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtItemCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtItemCode.Properties.AutoHeight")));
            this.txtItemCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtItemCode.Properties.Mask.AutoComplete")));
            this.txtItemCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtItemCode.Properties.Mask.BeepOnError")));
            this.txtItemCode.Properties.Mask.EditMask = resources.GetString("txtItemCode.Properties.Mask.EditMask");
            this.txtItemCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtItemCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtItemCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtItemCode.Properties.Mask.MaskType")));
            this.txtItemCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtItemCode.Properties.Mask.PlaceHolder")));
            this.txtItemCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtItemCode.Properties.Mask.SaveLiteral")));
            this.txtItemCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtItemCode.Properties.Mask.ShowPlaceHolders")));
            this.txtItemCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtItemCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtItemCode.Properties.NullValuePrompt = resources.GetString("txtItemCode.Properties.NullValuePrompt");
            this.txtItemCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtItemCode.Properties.NullValuePromptShowForEmptyValue")));
            this.txtItemCode.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtItemCode_KeyDown);
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // txtQty
            // 
            resources.ApplyResources(this.txtQty, "txtQty");
            this.txtQty.Name = "txtQty";
            this.txtQty.Properties.AccessibleDescription = resources.GetString("txtQty.Properties.AccessibleDescription");
            this.txtQty.Properties.AccessibleName = resources.GetString("txtQty.Properties.AccessibleName");
            this.txtQty.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtQty.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtQty.Properties.Appearance.FontSizeDelta")));
            this.txtQty.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtQty.Properties.Appearance.FontStyleDelta")));
            this.txtQty.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtQty.Properties.Appearance.GradientMode")));
            this.txtQty.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtQty.Properties.Appearance.Image")));
            this.txtQty.Properties.Appearance.Options.UseTextOptions = true;
            this.txtQty.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtQty.Properties.AutoHeight = ((bool)(resources.GetObject("txtQty.Properties.AutoHeight")));
            this.txtQty.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtQty.Properties.Mask.AutoComplete")));
            this.txtQty.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtQty.Properties.Mask.BeepOnError")));
            this.txtQty.Properties.Mask.EditMask = resources.GetString("txtQty.Properties.Mask.EditMask");
            this.txtQty.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtQty.Properties.Mask.IgnoreMaskBlank")));
            this.txtQty.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtQty.Properties.Mask.MaskType")));
            this.txtQty.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtQty.Properties.Mask.PlaceHolder")));
            this.txtQty.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtQty.Properties.Mask.SaveLiteral")));
            this.txtQty.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtQty.Properties.Mask.ShowPlaceHolders")));
            this.txtQty.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtQty.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtQty.Properties.NullText = resources.GetString("txtQty.Properties.NullText");
            this.txtQty.Properties.NullValuePrompt = resources.GetString("txtQty.Properties.NullValuePrompt");
            this.txtQty.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtQty.Properties.NullValuePromptShowForEmptyValue")));
            this.txtQty.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            this.txtQty.KeyUp += new System.Windows.Forms.KeyEventHandler(this.txtQty_KeyUp);
            this.txtQty.Leave += new System.EventHandler(this.txtQty_Leave);
            // 
            // tileControl1
            // 
            resources.ApplyResources(this.tileControl1, "tileControl1");
            this.tileControl1.AllowDrag = false;
            this.tileControl1.AllowDragTilesBetweenGroups = false;
            this.tileControl1.AllowItemHover = true;
            this.tileControl1.DragSize = new System.Drawing.Size(0, 0);
            this.tileControl1.IndentBetweenGroups = 15;
            this.tileControl1.ItemSize = 80;
            this.tileControl1.MaxId = 4;
            this.tileControl1.Name = "tileControl1";
            this.tileControl1.Orientation = System.Windows.Forms.Orientation.Vertical;
            this.tileControl1.RowCount = 4;
            this.tileControl1.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileControl1_ItemClick);
            // 
            // listView_Items
            // 
            resources.ApplyResources(this.listView_Items, "listView_Items");
            this.listView_Items.EmbeddedNavigator.AccessibleDescription = resources.GetString("listView_Items.EmbeddedNavigator.AccessibleDescription");
            this.listView_Items.EmbeddedNavigator.AccessibleName = resources.GetString("listView_Items.EmbeddedNavigator.AccessibleName");
            this.listView_Items.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("listView_Items.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.listView_Items.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("listView_Items.EmbeddedNavigator.Anchor")));
            this.listView_Items.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("listView_Items.EmbeddedNavigator.BackgroundImage")));
            this.listView_Items.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("listView_Items.EmbeddedNavigator.BackgroundImageLayout")));
            this.listView_Items.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("listView_Items.EmbeddedNavigator.ImeMode")));
            this.listView_Items.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("listView_Items.EmbeddedNavigator.MaximumSize")));
            this.listView_Items.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("listView_Items.EmbeddedNavigator.TextLocation")));
            this.listView_Items.EmbeddedNavigator.ToolTip = resources.GetString("listView_Items.EmbeddedNavigator.ToolTip");
            this.listView_Items.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("listView_Items.EmbeddedNavigator.ToolTipIconType")));
            this.listView_Items.EmbeddedNavigator.ToolTipTitle = resources.GetString("listView_Items.EmbeddedNavigator.ToolTipTitle");
            this.listView_Items.MainView = this.tileView1;
            this.listView_Items.MenuManager = this.barManager1;
            this.listView_Items.Name = "listView_Items";
            this.listView_Items.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.tileView1});
            // 
            // tileView1
            // 
            this.tileView1.Appearance.GroupText.Font = ((System.Drawing.Font)(resources.GetObject("tileView1.Appearance.GroupText.Font")));
            this.tileView1.Appearance.GroupText.FontSizeDelta = ((int)(resources.GetObject("tileView1.Appearance.GroupText.FontSizeDelta")));
            this.tileView1.Appearance.GroupText.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tileView1.Appearance.GroupText.FontStyleDelta")));
            this.tileView1.Appearance.GroupText.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tileView1.Appearance.GroupText.GradientMode")));
            this.tileView1.Appearance.GroupText.Image = ((System.Drawing.Image)(resources.GetObject("tileView1.Appearance.GroupText.Image")));
            this.tileView1.Appearance.GroupText.Options.UseFont = true;
            this.tileView1.Appearance.ItemFocused.Font = ((System.Drawing.Font)(resources.GetObject("tileView1.Appearance.ItemFocused.Font")));
            this.tileView1.Appearance.ItemFocused.FontSizeDelta = ((int)(resources.GetObject("tileView1.Appearance.ItemFocused.FontSizeDelta")));
            this.tileView1.Appearance.ItemFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tileView1.Appearance.ItemFocused.FontStyleDelta")));
            this.tileView1.Appearance.ItemFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tileView1.Appearance.ItemFocused.GradientMode")));
            this.tileView1.Appearance.ItemFocused.Image = ((System.Drawing.Image)(resources.GetObject("tileView1.Appearance.ItemFocused.Image")));
            this.tileView1.Appearance.ItemFocused.Options.UseFont = true;
            this.tileView1.Appearance.ItemHovered.Font = ((System.Drawing.Font)(resources.GetObject("tileView1.Appearance.ItemHovered.Font")));
            this.tileView1.Appearance.ItemHovered.FontSizeDelta = ((int)(resources.GetObject("tileView1.Appearance.ItemHovered.FontSizeDelta")));
            this.tileView1.Appearance.ItemHovered.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tileView1.Appearance.ItemHovered.FontStyleDelta")));
            this.tileView1.Appearance.ItemHovered.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tileView1.Appearance.ItemHovered.GradientMode")));
            this.tileView1.Appearance.ItemHovered.Image = ((System.Drawing.Image)(resources.GetObject("tileView1.Appearance.ItemHovered.Image")));
            this.tileView1.Appearance.ItemHovered.Options.UseFont = true;
            this.tileView1.Appearance.ItemNormal.BackColor = ((System.Drawing.Color)(resources.GetObject("tileView1.Appearance.ItemNormal.BackColor")));
            this.tileView1.Appearance.ItemNormal.Font = ((System.Drawing.Font)(resources.GetObject("tileView1.Appearance.ItemNormal.Font")));
            this.tileView1.Appearance.ItemNormal.FontSizeDelta = ((int)(resources.GetObject("tileView1.Appearance.ItemNormal.FontSizeDelta")));
            this.tileView1.Appearance.ItemNormal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tileView1.Appearance.ItemNormal.FontStyleDelta")));
            this.tileView1.Appearance.ItemNormal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tileView1.Appearance.ItemNormal.GradientMode")));
            this.tileView1.Appearance.ItemNormal.Image = ((System.Drawing.Image)(resources.GetObject("tileView1.Appearance.ItemNormal.Image")));
            this.tileView1.Appearance.ItemNormal.Options.UseBackColor = true;
            this.tileView1.Appearance.ItemNormal.Options.UseFont = true;
            this.tileView1.Appearance.ItemNormal.Options.UseTextOptions = true;
            this.tileView1.Appearance.ItemNormal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tileView1.Appearance.ItemNormal.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.tileView1.Appearance.ItemNormal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.tileView1.Appearance.ItemSelected.Font = ((System.Drawing.Font)(resources.GetObject("tileView1.Appearance.ItemSelected.Font")));
            this.tileView1.Appearance.ItemSelected.FontSizeDelta = ((int)(resources.GetObject("tileView1.Appearance.ItemSelected.FontSizeDelta")));
            this.tileView1.Appearance.ItemSelected.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tileView1.Appearance.ItemSelected.FontStyleDelta")));
            this.tileView1.Appearance.ItemSelected.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tileView1.Appearance.ItemSelected.GradientMode")));
            this.tileView1.Appearance.ItemSelected.Image = ((System.Drawing.Image)(resources.GetObject("tileView1.Appearance.ItemSelected.Image")));
            this.tileView1.Appearance.ItemSelected.Options.UseFont = true;
            this.tileView1.Appearance.ViewCaption.FontSizeDelta = ((int)(resources.GetObject("tileView1.Appearance.ViewCaption.FontSizeDelta")));
            this.tileView1.Appearance.ViewCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tileView1.Appearance.ViewCaption.FontStyleDelta")));
            this.tileView1.Appearance.ViewCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tileView1.Appearance.ViewCaption.GradientMode")));
            this.tileView1.Appearance.ViewCaption.Image = ((System.Drawing.Image)(resources.GetObject("tileView1.Appearance.ViewCaption.Image")));
            this.tileView1.Appearance.ViewCaption.Options.UseTextOptions = true;
            this.tileView1.Appearance.ViewCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tileView1.Appearance.ViewCaption.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.tileView1.Appearance.ViewCaption.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.tileView1.AppearanceGroupText.FontSizeDelta = ((int)(resources.GetObject("tileView1.AppearanceGroupText.FontSizeDelta")));
            this.tileView1.AppearanceGroupText.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tileView1.AppearanceGroupText.FontStyleDelta")));
            this.tileView1.AppearanceGroupText.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tileView1.AppearanceGroupText.GradientMode")));
            this.tileView1.AppearanceGroupText.Image = ((System.Drawing.Image)(resources.GetObject("tileView1.AppearanceGroupText.Image")));
            this.tileView1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Style3D;
            this.tileView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.tcol_ItemNameAr,
            this.tcol_CategoryNameAr,
            this.tcol_SmallUOMPrice,
            this.tcol_ItemCode1});
            this.tileView1.ColumnSet.GroupColumn = this.tcol_CategoryNameAr;
            this.tileView1.GridControl = this.listView_Items;
            this.tileView1.Name = "tileView1";
            this.tileView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.tileView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.tileView1.OptionsBehavior.Editable = false;
            this.tileView1.OptionsTiles.IndentBetweenItems = 15;
            this.tileView1.OptionsTiles.ItemPadding = new System.Windows.Forms.Padding(3);
            this.tileView1.OptionsTiles.ItemSize = new System.Drawing.Size(150, 50);
            this.tileView1.OptionsTiles.Orientation = System.Windows.Forms.Orientation.Vertical;
            this.tileView1.OptionsTiles.Padding = new System.Windows.Forms.Padding(3);
            this.tileView1.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.AnimateAllContent;
            this.tileView1.SortInfo.AddRange(new DevExpress.XtraGrid.Columns.GridColumnSortInfo[] {
            new DevExpress.XtraGrid.Columns.GridColumnSortInfo(this.tcol_CategoryNameAr, DevExpress.Data.ColumnSortOrder.Ascending)});
            tileViewItemElement1.Column = this.tcol_ItemNameAr;
            tileViewItemElement1.StretchHorizontal = true;
            tileViewItemElement1.StretchVertical = true;
            resources.ApplyResources(tileViewItemElement1, "tileViewItemElement1");
            tileViewItemElement1.TextAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileViewItemElement2.Appearance.Normal.Font = ((System.Drawing.Font)(resources.GetObject("resource.Font")));
            tileViewItemElement2.Appearance.Normal.FontSizeDelta = ((int)(resources.GetObject("resource.FontSizeDelta")));
            tileViewItemElement2.Appearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("resource.FontStyleDelta")));
            tileViewItemElement2.Appearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("resource.GradientMode")));
            tileViewItemElement2.Appearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image")));
            tileViewItemElement2.Appearance.Normal.Options.UseFont = true;
            tileViewItemElement2.Column = this.tcol_ItemCode1;
            tileViewItemElement2.MaxWidth = 0;
            resources.ApplyResources(tileViewItemElement2, "tileViewItemElement2");
            tileViewItemElement2.TextLocation = new System.Drawing.Point(40, 40);
            this.tileView1.TileTemplate.Add(tileViewItemElement1);
            this.tileView1.TileTemplate.Add(tileViewItemElement2);
            resources.ApplyResources(this.tileView1, "tileView1");
            this.tileView1.ItemClick += new DevExpress.XtraGrid.Views.Tile.TileViewItemClickEventHandler(this.tileView1_ItemClick);
            // 
            // tcol_CategoryNameAr
            // 
            resources.ApplyResources(this.tcol_CategoryNameAr, "tcol_CategoryNameAr");
            this.tcol_CategoryNameAr.FieldName = "CategoryNameAr";
            this.tcol_CategoryNameAr.FieldNameSortGroup = "CategoryNameAr";
            this.tcol_CategoryNameAr.Name = "tcol_CategoryNameAr";
            this.tcol_CategoryNameAr.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // tcol_SmallUOMPrice
            // 
            resources.ApplyResources(this.tcol_SmallUOMPrice, "tcol_SmallUOMPrice");
            this.tcol_SmallUOMPrice.FieldName = "SmallUOMPrice";
            this.tcol_SmallUOMPrice.Name = "tcol_SmallUOMPrice";
            // 
            // frm_SL_POS_Resturant
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.listView_Items);
            this.Controls.Add(this.tileControl1);
            this.Controls.Add(this.panelControl3);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.grdPrInvoice);
            this.Controls.Add(this.txtQty);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.txtItemCode);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_POS_Resturant";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_POS_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_POS_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_SL_POS_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRemains.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPaid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl3)).EndInit();
            this.panelControl3.ResumeLayout(false);
            this.panelControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).EndInit();
            this.contextMenuStrip2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_Details)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpinQty)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtQty.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listView_Items)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tileView1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnCancel;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.TextEdit txtInvoiceCode;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraBars.BarButtonItem barBtnCommit;
        private DevExpress.XtraEditors.LabelControl labelControl35;
        private DevExpress.XtraEditors.DateEdit dtInvoiceDate;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.MemoEdit txtNotes;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.TextEdit txtDiscountValue;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.TextEdit txtDiscountRatio;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.TextEdit txtExpenses;
        private DevExpress.XtraEditors.GridLookUpEdit lkp_Customers;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraEditors.TextEdit txtNet;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.TextEdit txtPaid;
        private DevExpress.XtraEditors.LabelControl lblRemains;
        private DevExpress.XtraEditors.TextEdit txtRemains;
        private DevExpress.XtraEditors.LookUpEdit lkp_Drawers;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.LookUpEdit lkpStore;
        private DevExpress.XtraEditors.SimpleButton btnAddCustomer;
        private DevExpress.XtraEditors.LabelControl lbl_credit_debit;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_frm_IC_Item;
        private DevExpress.XtraEditors.PanelControl panelControl3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.TextEdit txt_TaxRatio;
        private DevExpress.XtraEditors.TextEdit txt_TaxValue;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraBars.BarSubItem barSubItem1;
        private DevExpress.XtraBars.BarButtonItem barBtn_RCashNote;
        private DevExpress.XtraBars.BarButtonItem barBtn_RNote;
        private DevExpress.XtraBars.BarButtonItem barBtnSalesReturn;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip2;
        private System.Windows.Forms.ToolStripMenuItem mi_FinishInvoice;
        private System.Windows.Forms.ToolStripMenuItem mi_FinishAndPrint;
        private DevExpress.XtraGrid.GridControl grdPrInvoice;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView gv_Details;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn35;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_TotalSellPrice;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colPurchasePrice;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn8;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn31;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn gridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpinQty;
        private System.Windows.Forms.ToolStripMenuItem mi_Print;
        private DevExpress.XtraEditors.TextEdit txtQty;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.TextEdit txtItemCode;
        private DevExpress.XtraBars.BarButtonItem barBtnFinish;
        private DevExpress.XtraEditors.TileControl tileControl1;
        private DevExpress.XtraGrid.GridControl listView_Items;
        private DevExpress.XtraGrid.Views.Tile.TileView tileView1;
        private DevExpress.XtraGrid.Columns.TileViewColumn tcol_ItemNameAr;
        private DevExpress.XtraGrid.Columns.TileViewColumn tcol_CategoryNameAr;
        private DevExpress.XtraGrid.Columns.TileViewColumn tcol_SmallUOMPrice;
        private DevExpress.XtraGrid.Columns.TileViewColumn tcol_ItemCode1;
    }
}