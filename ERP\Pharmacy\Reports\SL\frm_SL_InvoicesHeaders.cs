﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_InvoicesHeaders : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int store_id1, store_id2, CustomerId1, CustomerId2, custGroupId, salesEmpId;

        string custGroupAccNumber;

        byte FltrTyp_Store, fltrTyp_Date, FltrTyp_Customer, FltrTyp_InvBook;
        DateTime date1, date2;

        List<int> lst_invBooksId = new List<int>();

        private void btn_Landscape_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void btn_Portrait_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void grdCategory_Click(object sender, EventArgs e)
        {

        }

        public frm_SL_InvoicesHeaders(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Store, int store_id1, int store_id2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2, int custGroupId, string custGroupAccNumber,
                int salesEmpId, byte FltrTyp_InvBook, string InvBooks)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Store = fltrTyp_Store;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;

            this.store_id1 = store_id1;
            this.store_id2 = store_id2;

            this.date1 = date1;
            this.date2 = date2;

            this.CustomerId1 = customerId1;
            this.CustomerId2 = customerId2;
            this.salesEmpId = salesEmpId;
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();

            LoadData();

            if (Shared.user.SL_Invoice_PayMethod.HasValue)
                col_PaidInAdvance.OptionsColumn.ShowInCustomizationForm =
                col_PaidInAdvance.OptionsColumn.ShowInCustomizationForm =
                col_Remains.OptionsColumn.ShowInCustomizationForm =
                col_Remains.OptionsColumn.ShowInCustomizationForm =
                Shared.user.SL_Invoice_PayMethod.Value;

            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));

            rep_Currency.DataSource = Shared.lstCurrency;
            rep_Currency.ValueMember = "CrncId";
            rep_Currency.DisplayMember = "crncName";
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //grdCategory.MinimumSize = grdCategory.Size;
            //new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
            //        lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            //grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();





            var data =
                (from c in DB.SL_Customers
                 //join a in DB.ACC_Accounts
                 //on c.AccountId equals a.AccountId
                 //where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                 where FltrTyp_Customer == 1 ? c.CustomerId == CustomerId1 : true
                 where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 != 0) ?
                 c.CustomerId >= CustomerId1 && c.CustomerId <= CustomerId2 : true
                 where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 == 0) ?
                 c.CustomerId >= CustomerId1 : true
                 where (FltrTyp_Customer == 2 && CustomerId1 == 0 && CustomerId2 != 0) ?
                 c.CustomerId <= CustomerId2 : true

                 join d in DB.SL_Invoices on c.CustomerId equals d.CustomerId


                 //update
                 join dt in DB.SL_InvoiceDetails on d.SL_InvoiceId equals dt.SL_InvoiceId
                 where FltrTyp_Store == 1 ? (d.StoreId == store_id1 || dt.StoreId == store_id1) : true
                 where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
                (d.StoreId >= store_id1 || dt.StoreId >= store_id1) && (d.StoreId <= store_id2 || dt.StoreId <= store_id2) : true
                 where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
                 (d.StoreId >= store_id1 || dt.StoreId >= store_id1) : true
                 where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
                 (d.StoreId <= store_id2 || dt.StoreId <= store_id2) : true

                 //where FltrTyp_Store == 1 ? d.StoreId == store_id1 : true
                 //where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
                 //d.StoreId >= store_id1 && d.StoreId <= store_id2 : true
                 //where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
                 //d.StoreId >= store_id1 : true
                 //where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
                 //d.StoreId <= store_id2 : true





                 where fltrTyp_Date == 1 ? d.InvoiceDate.Date == date1.Date : true
                 where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                 d.InvoiceDate.Date >= date1.Date && d.InvoiceDate.Date <= date2.Date : true
                 where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                 d.InvoiceDate.Date >= date1.Date : true
                 where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                 d.InvoiceDate.Date <= date2.Date : true

                 where salesEmpId == 0 ? true : d.SalesEmpId == salesEmpId

                 where FltrTyp_InvBook == 0 ? true : (d.InvoiceBookId.HasValue && lst_invBooksId.Contains(d.InvoiceBookId.Value))

                 let TradeDiscount = (d == null || d.SL_InvoiceDetails.Count() == 0) ? 0 : d.SL_InvoiceDetails.Sum(x => x == null ? 0 : x.DiscountValue)

                 let TotalInvoice = d.Net + d.DiscountValue + d.CustomTaxValue + d.AddTaxValue + d.DeductTaxValue + d.RetentionValue + d.AdvancePaymentValue - d.Expenses - (d.HandingValue.HasValue ? d.HandingValue.Value : 0) - (d.TransportationValue.HasValue ? d.TransportationValue.Value : 0) - (d.ShiftAdd.HasValue ? d.ShiftAdd.Value : 0)

                 //update
                 //let CashPaid = from c in DB.ACC_CashNotes.Where(x => x.SourceId == d.SL_InvoiceId).DefaultIfEmpty()
                 //               where c.IsPay == false && c.ProcessId == (int)Process.SellInvoice
                 //               select new { c.SourceId, c.Amount }
                 //let NotePaid = from i3 in DB.ACC_NotesReceivables.Where(x => x.SourceId == d.SL_InvoiceId).DefaultIfEmpty()
                 //               where i3.ResponseType == 3 || i3.ResponseType == 4 || i3.ResponseType == 6
                 //               select new { i3.SourceId, i3.Amount }

                 let Net = d.Net
                 //let cash = CashPaid.Count() > 0 ? CashPaid.Sum(x => x.Amount) : 0
                 //let note = NotePaid.Count() > 0 ? NotePaid.Sum(x => x.Amount) : 0
                 //let IsPaid = d.PayMethod == true ? true : cash + note + d.Paid >= Net
                 ////=========update 1==========//
                 //let CashDetailsPaid = from c in DB.ACC_CashNoteDetails.Where(x => x.SourceId == d.SL_InvoiceId)
                 //                  select new { c.SourceId, c.Amount }
                 //let cashDetail = CashDetailsPaid.Count() > 0 ? CashDetailsPaid.Sum(x => x.Amount) : 0
                 ////===========Update 2=============//
                 //let noteRecivbleDetails = from c in DB.ACC_NoteRecivable_Details.Where(x => x.SourceId == d.SL_InvoiceId)
                 //                      select new { c.SourceId, c.Amount }
                 //let noteRecivble = noteRecivbleDetails.Count() > 0 ? noteRecivbleDetails.Sum(x => x.Amount) : 0
                 //=============================//
                 let CustCategory = DB.SL_Customers.SingleOrDefault(s => s.CustomerId == c.CustomerId).CategoryId
                 //let CustGroup = DB.SL_Group_Customers
                 let Users = DB.HR_Users
                 let SL_CustomerGroups = DB.SL_CustomerGroups
                 let stores = DB.IC_Stores
                 let SalesRep = DB.HR_Employees.Where(x => x.SalesRep && d.SalesEmpId == x.EmpId).Select(x => Shared.IsEnglish ? x.EmpFName : x.EmpName).FirstOrDefault()
                // let Total = Convert.ToDouble(DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).Sum(z => z.TotalSellPrice))
                 let TaxIdDiscount = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault()
                 let TotalTaxesAddedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                            join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                            join dd in DB.SL_InvoiceDetails on r.InvoiceDetailId equals dd.SL_InvoiceDetailId
                                            where dd.SL_InvoiceDetailId == dt.SL_InvoiceDetailId
                                            where rd.ParentTaxId != TaxIdDiscount.E_TaxableTypeId
                                            select r).ToList()
                 let TotalTaxesRemovedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                              join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                              join dd in DB.SL_InvoiceDetails on r.InvoiceDetailId equals dd.SL_InvoiceDetailId
                                              where dd.SL_InvoiceDetailId == dt.SL_InvoiceDetailId
                                              where rd.ParentTaxId == TaxIdDiscount.E_TaxableTypeId
                                              select r).ToList()
                 let TotaltaxesAddValue = TotalTaxesAddedList.Count != 0 ? Convert.ToDouble(TotalTaxesAddedList.Sum(z => z.value)) : 0
                 let TotaltaxesRemovedValue = TotalTaxesRemovedList.Count != 0 ? Convert.ToDouble(TotalTaxesRemovedList.Sum(z => z.value)) : 0
                 //  let Totaltaxes = DB.SL_InvoiceDetailSubTaxValues
                 //.Where(a => DB.SL_InvoiceDetails.Where(v => v.SL_InvoiceId == d.SL_InvoiceId).ToList().Select(v => v.SL_InvoiceDetailId).Contains(a.InvoiceDetailId)).ToList() 
                 select new
                 {
                     d.InvoiceCode,
                     d.InvoiceDate,
                     d.DiscountRatio,
                     d.DiscountValue,
                     d.Expenses,
                     PayMethod = d.PayMethod.HasValue ? (d.PayMethod.Value == true ? "كاش" : "اجل") : "نقدي/اجل",
                     d.Net,
                     PaidInAdvance = d.Paid,
                     //Paid = cash + note + d.Paid+ cashDetail+noteRecivble,
                     ////d.Remains,
                     //Remains = Net - (cash + note + d.Paid+ cashDetail+noteRecivble),
                     Customer = c.CusNameAr,
                     d.AddTaxValue,
                     d.DeductTaxValue,
                     d.TaxValue,
                     handing = (double?)(d.HandingValue),
                     Total =(double?) TotalInvoice,
                     TotalTradeDiscount = TradeDiscount,
                     TotalBeforeTradeDisc = TotalInvoice + TradeDiscount,
                     //IsPaid = IsPaid.ToString() == "True" ? (Shared.IsEnglish ? ResSLEn.True : ResSLAr.True) : (Shared.IsEnglish ? ResSLEn.False : ResSLAr.False),
                     Store = d.StoreId == 0 ? stores.SingleOrDefault(c => c.StoreId == dt.StoreId).StoreNameAr :
                     stores.SingleOrDefault(c => c.StoreId == d.StoreId).StoreNameAr,
                     CGNameAr = SL_CustomerGroups.SingleOrDefault(g => g.CustomerGroupId == CustCategory).CGNameAr,
                     d.ProcessId,
                     d.CrncId,
                     d.CrncRate,
                     TotalSellPrice_Local = Decimal.ToDouble(d.Net * d.CrncRate),
                     Total_Local = (double)(TotalInvoice * d.CrncRate),
                     d.DueDate,
                     //mohammad 10-11-2019
                     //CustGroup = CustGroup.Where(x => x.GroupId == c.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault(),
                     UserName = Users.Where(x => x.UserId == d.UserId).Select(x => x.UserName).FirstOrDefault(),
                     // update adel 
                     // إظهار اسم مندوب البيع ان وجد 
                     SalesEmp = SalesRep,
                     c.City,
                     // Totaltaxes = Totaltaxes.Count != 0 ? Convert.ToDouble(Totaltaxes.Sum(z => z.value)) : 0
                     Totaltaxes = TotaltaxesAddValue - TotaltaxesRemovedValue

                 }).Distinct();
            //var xxx = data.ToString();


            grdCategory.DataSource = data;

        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;
        }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_InvoicesHeaders).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }



    }
}