﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraEditors;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using System.Data;

namespace Reports
{
    public partial class rpt_SL_ItemTrade : DevExpress.XtraReports.UI.XtraReport
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int itemId1, store_id1;       

        public rpt_SL_ItemTrade(string reportName, string dateFilter, string otherFilters,
            int store_id1,
            int itemId1)
        {

            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)            
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            if (Shared.IsEnglish)
                ReportsRTL.MirrorGridControl(this.grdJStatement);

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;
                                  
            this.itemId1 = itemId1;            
            this.store_id1 = store_id1;            

            getReportHeader();

            LoadData();
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            DataTable dtStatement = new DataTable();
            dtStatement.Columns.Add("Name");
            dtStatement.Columns.Add("Debit");
            dtStatement.Columns.Add("Credit");

            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            
            var sl = (from d in DB.SL_InvoiceDetails
                        join s in DB.SL_Invoices
                        on d.SL_InvoiceId equals s.SL_InvoiceId
                        where s.StoreId == store_id1
                        where d.ItemId == itemId1                                                
                        select d.TotalSellPrice).ToList().DefaultIfEmpty(0).Sum();

            var sr = (from d in DB.SL_ReturnDetails
                      join s in DB.SL_Returns
                      on d.SL_ReturnId equals s.SL_ReturnId
                      where s.StoreId == store_id1
                      where d.ItemId == itemId1
                      select d.TotalSellPrice).ToList().DefaultIfEmpty(0).Sum();

            var pr = (from d in DB.PR_InvoiceDetails
                      join s in DB.PR_Invoices
                      on d.PR_InvoiceId equals s.PR_InvoiceId
                      where s.StoreId == store_id1
                      where d.ItemId == itemId1
                      select d.TotalPurchasePrice).ToList().DefaultIfEmpty(0).Sum();

            var pr_return = (from d in DB.PR_ReturnDetails
                             join s in DB.PR_Returns
                      on d.PR_ReturnId equals s.PR_ReturnId
                      where s.StoreId == store_id1
                      where d.ItemId == itemId1
                      select d.TotalPurchasePrice).ToList().DefaultIfEmpty(0).Sum();            
            
            //open balance
            var OpenInv = DB.IC_ItemStores.Where(
                        i =>
                            i.StoreId == store_id1 &&
                            i.ItemId == itemId1 &&
                            i.ProcessId == (byte)Process.OpenBalance                            
                            ).Select(i => i.PurchasePrice).ToList().DefaultIfEmpty(0).Sum();


            var closeinv = (from i in DB.IC_ItemStores
                           where i.StoreId == store_id1 &&
                            i.ItemId == itemId1
                           select new 
                           {
                               PurchasePrice= i.IsInTrns?i.PurchasePrice:(i.PurchasePrice*-1)
                           }).Select(x => x.PurchasePrice).ToList().DefaultIfEmpty(0).Sum();            
                        
            decimal Debit = 0, Credit = 0;
            dtStatement.Rows.Add("المشتريات", decimal.ToDouble(pr), "");
            Debit += pr;
            dtStatement.Rows.Add("مردود المشتريات", "", decimal.ToDouble(pr_return));
            Credit += pr_return;
            dtStatement.Rows.Add("المبيعات", "", decimal.ToDouble(sl));
            Credit += sl;
            dtStatement.Rows.Add("مردود المبيعات", decimal.ToDouble(sr) ,"");
            Debit += sr;
            dtStatement.Rows.Add("رصيد أول المدة", decimal.ToDouble(OpenInv) ,"");
            Debit += OpenInv;
            dtStatement.Rows.Add("رصيد نهاية المدة", "", decimal.ToDouble(closeinv));
            Credit += closeinv;

            if (Debit > Credit)
            {
                lblProfit.Text = "مجمل الخساره:" + decimal.ToDouble(Debit - Credit).ToString();
            }
            else if (Credit > Debit)
            {
                lblProfit.Text = "مجمل الربح:" + decimal.ToDouble(Credit - Debit).ToString();                
            }
            else
            {
                lblProfit.Text = "لايوجد ربح أو خسارة";
            }
            grdJStatement.DataSource = dtStatement;
        }


        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {

        }
        private void gridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "Credit" || e.Column.FieldName == "Debit")                
            {
                if (e.Value != DBNull.Value)
                    e.DisplayText = decimal.ToDouble(Convert.ToDecimal(e.Value)).ToString();
            }
        }        


        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_ItemTrade).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }
    }
}
