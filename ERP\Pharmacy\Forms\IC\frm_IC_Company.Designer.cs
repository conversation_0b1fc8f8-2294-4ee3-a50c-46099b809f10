﻿namespace Pharmacy.Forms
{
    partial class frm_IC_Company
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_Company));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_List = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtCompanyNameAr = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtCompanyNameEn = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtCompanyCode = new DevExpress.XtraEditors.TextEdit();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrev = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameAr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameEn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyCode.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnDelete,
            this.barBtn_Help,
            this.barBtn_List,
            this.barBtnClose,
            this.barBtnNew});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnNew, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnDelete, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnSave, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtn_List, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnClose, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", "")});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 27;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnDelete
            // 
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 1;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Delete_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtn_List
            // 
            resources.ApplyResources(this.barBtn_List, "barBtn_List");
            this.barBtn_List.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_List.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.barBtn_List.Id = 25;
            this.barBtn_List.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.barBtn_List.Name = "barBtn_List";
            this.barBtn_List.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_List.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_List_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 26;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // txtCompanyNameAr
            // 
            resources.ApplyResources(this.txtCompanyNameAr, "txtCompanyNameAr");
            this.txtCompanyNameAr.EnterMoveNextControl = true;
            this.txtCompanyNameAr.Name = "txtCompanyNameAr";
            this.txtCompanyNameAr.Properties.AccessibleDescription = resources.GetString("txtCompanyNameAr.Properties.AccessibleDescription");
            this.txtCompanyNameAr.Properties.AccessibleName = resources.GetString("txtCompanyNameAr.Properties.AccessibleName");
            this.txtCompanyNameAr.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.FontSizeDelta")));
            this.txtCompanyNameAr.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.FontStyleDelta")));
            this.txtCompanyNameAr.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.GradientMode")));
            this.txtCompanyNameAr.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.Image")));
            this.txtCompanyNameAr.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCompanyNameAr.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCompanyNameAr.Properties.AutoHeight = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.AutoHeight")));
            this.txtCompanyNameAr.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCompanyNameAr.Properties.Mask.AutoComplete")));
            this.txtCompanyNameAr.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.BeepOnError")));
            this.txtCompanyNameAr.Properties.Mask.EditMask = resources.GetString("txtCompanyNameAr.Properties.Mask.EditMask");
            this.txtCompanyNameAr.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.IgnoreMaskBlank")));
            this.txtCompanyNameAr.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCompanyNameAr.Properties.Mask.MaskType")));
            this.txtCompanyNameAr.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCompanyNameAr.Properties.Mask.PlaceHolder")));
            this.txtCompanyNameAr.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.SaveLiteral")));
            this.txtCompanyNameAr.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.ShowPlaceHolders")));
            this.txtCompanyNameAr.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCompanyNameAr.Properties.MaxLength = 50;
            this.txtCompanyNameAr.Properties.NullValuePrompt = resources.GetString("txtCompanyNameAr.Properties.NullValuePrompt");
            this.txtCompanyNameAr.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // txtCompanyNameEn
            // 
            resources.ApplyResources(this.txtCompanyNameEn, "txtCompanyNameEn");
            this.txtCompanyNameEn.EnterMoveNextControl = true;
            this.txtCompanyNameEn.Name = "txtCompanyNameEn";
            this.txtCompanyNameEn.Properties.AccessibleDescription = resources.GetString("txtCompanyNameEn.Properties.AccessibleDescription");
            this.txtCompanyNameEn.Properties.AccessibleName = resources.GetString("txtCompanyNameEn.Properties.AccessibleName");
            this.txtCompanyNameEn.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.FontSizeDelta")));
            this.txtCompanyNameEn.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.FontStyleDelta")));
            this.txtCompanyNameEn.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.GradientMode")));
            this.txtCompanyNameEn.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.Image")));
            this.txtCompanyNameEn.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCompanyNameEn.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCompanyNameEn.Properties.AutoHeight = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.AutoHeight")));
            this.txtCompanyNameEn.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCompanyNameEn.Properties.Mask.AutoComplete")));
            this.txtCompanyNameEn.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.BeepOnError")));
            this.txtCompanyNameEn.Properties.Mask.EditMask = resources.GetString("txtCompanyNameEn.Properties.Mask.EditMask");
            this.txtCompanyNameEn.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.IgnoreMaskBlank")));
            this.txtCompanyNameEn.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCompanyNameEn.Properties.Mask.MaskType")));
            this.txtCompanyNameEn.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCompanyNameEn.Properties.Mask.PlaceHolder")));
            this.txtCompanyNameEn.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.SaveLiteral")));
            this.txtCompanyNameEn.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.ShowPlaceHolders")));
            this.txtCompanyNameEn.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCompanyNameEn.Properties.MaxLength = 50;
            this.txtCompanyNameEn.Properties.NullValuePrompt = resources.GetString("txtCompanyNameEn.Properties.NullValuePrompt");
            this.txtCompanyNameEn.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCompanyNameEn.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtCompanyNameAr_KeyPress);
            this.txtCompanyNameEn.Leave += new System.EventHandler(this.txt_CompanyNameEn_Leave);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // txtCompanyCode
            // 
            resources.ApplyResources(this.txtCompanyCode, "txtCompanyCode");
            this.txtCompanyCode.EnterMoveNextControl = true;
            this.txtCompanyCode.Name = "txtCompanyCode";
            this.txtCompanyCode.Properties.AccessibleDescription = resources.GetString("txtCompanyCode.Properties.AccessibleDescription");
            this.txtCompanyCode.Properties.AccessibleName = resources.GetString("txtCompanyCode.Properties.AccessibleName");
            this.txtCompanyCode.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtCompanyCode.Properties.Appearance.BackColor")));
            this.txtCompanyCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCompanyCode.Properties.Appearance.FontSizeDelta")));
            this.txtCompanyCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCompanyCode.Properties.Appearance.FontStyleDelta")));
            this.txtCompanyCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCompanyCode.Properties.Appearance.GradientMode")));
            this.txtCompanyCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCompanyCode.Properties.Appearance.Image")));
            this.txtCompanyCode.Properties.Appearance.Options.UseBackColor = true;
            this.txtCompanyCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCompanyCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCompanyCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtCompanyCode.Properties.AutoHeight")));
            this.txtCompanyCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCompanyCode.Properties.Mask.AutoComplete")));
            this.txtCompanyCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCompanyCode.Properties.Mask.BeepOnError")));
            this.txtCompanyCode.Properties.Mask.EditMask = resources.GetString("txtCompanyCode.Properties.Mask.EditMask");
            this.txtCompanyCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCompanyCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtCompanyCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCompanyCode.Properties.Mask.MaskType")));
            this.txtCompanyCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCompanyCode.Properties.Mask.PlaceHolder")));
            this.txtCompanyCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCompanyCode.Properties.Mask.SaveLiteral")));
            this.txtCompanyCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCompanyCode.Properties.Mask.ShowPlaceHolders")));
            this.txtCompanyCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCompanyCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCompanyCode.Properties.NullValuePrompt = resources.GetString("txtCompanyCode.Properties.NullValuePrompt");
            this.txtCompanyCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCompanyCode.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // btnNext
            // 
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            this.btnNext.Name = "btnNext";
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnPrev
            // 
            resources.ApplyResources(this.btnPrev, "btnPrev");
            this.btnPrev.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrev.Image = global::Pharmacy.Properties.Resources.prev32;
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // frm_IC_Company
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrev);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.txtCompanyCode);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.txtCompanyNameEn);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.txtCompanyNameAr);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_IC_Company";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_IC_Company_FormClosing);
            this.Load += new System.EventHandler(this.frm_IC_Company_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_IC_Company_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameAr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameEn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyCode.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit txtCompanyNameAr;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtCompanyNameEn;
        private DevExpress.XtraBars.BarButtonItem barBtn_List;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtCompanyCode;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrev;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
    }
}