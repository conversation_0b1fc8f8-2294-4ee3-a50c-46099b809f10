﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lkp_PerPetualSalesAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txt_StoreCode.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lblPurchasesReturnAccount.TabIndex" type="System.Int32, mscorlib">
    <value>158</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PeriodicPrReturnAcc.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="txtAddress.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lblSales.Name" xml:space="preserve">
    <value>lblSales</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txt_StoreCode.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lblCloseInventoryAccount.Text" xml:space="preserve">
    <value>Close Inventory Account</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkp_PeriodicOpenInvAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>668, 28</value>
  </data>
  <data name="&gt;&gt;lblSalesAccount.Name" xml:space="preserve">
    <value>lblSalesAccount</value>
  </data>
  <data name="lblSales.TabIndex" type="System.Int32, mscorlib">
    <value>130</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lkp_PerPetualSalesReturnAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblCloseInventoryAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 174</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;Mobile.Name" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtStoreNameEn.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="IsStopped.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="IsStopped.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;tab_PerpetualAcc.Name" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkpStore.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicPrDiscAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;labelControl8.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>465, 20</value>
  </data>
  <data name="barBtnDelete.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameEn.Size" type="System.Drawing.Size, System.Drawing">
    <value>465, 20</value>
  </data>
  <data name="&gt;&gt;labelControl15.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PerPetualInventoryAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="btnNext.Text" xml:space="preserve">
    <value>=&gt;</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicPrReturnAcc.Name" xml:space="preserve">
    <value>lkp_PeriodicPrReturnAcc</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 69</value>
  </data>
  <data name="txtStoreNameEn.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblInventory.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lbl_PriceList.Text" xml:space="preserve">
    <value>Price List</value>
  </data>
  <data name="labelControl10.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpStore.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl12.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;txt_StoreCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="&gt;&gt;lkpPriceLevel.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblPurchasesReturnAccount.Text" xml:space="preserve">
    <value>Purchases Return Account</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>75, 91</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_StoreCode.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lblCloseInventoryAccount.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tab_PeriodicAcc.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="Mobile.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>Costing Method</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barBtnHelp.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtAddress.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 13</value>
  </data>
  <data name="lblSalesAccount.TabIndex" type="System.Int32, mscorlib">
    <value>151</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="&gt;&gt;lkpPriceLevel.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtAddress.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblSalesReturn.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns44" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns47" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns24" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns40" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns43" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl12.TabIndex" type="System.Int32, mscorlib">
    <value>154</value>
  </data>
  <data name="txtAddress.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="Mobile.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Name" xml:space="preserve">
    <value>barBtnHelp</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="lblCloseInventoryAccount.TabIndex" type="System.Int32, mscorlib">
    <value>160</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns46" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>75, 41</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblSalesAccount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;txtAddress.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTel.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblOpenInventoryAccount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="&gt;&gt;labelControl15.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 13</value>
  </data>
  <data name="txtTel.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="tab_PeriodicAcc.Text" xml:space="preserve">
    <value>Accouts</value>
  </data>
  <data name="&gt;&gt;txtStoreNameEn.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="Mobile.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Branch/Store F Name</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesReturnAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txtAddress.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualCostOfSoldGoods.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>516, 40</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>70, 32</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cb_CostMethod.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lbl_PriceList.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_StoreCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;tab_PerpetualAcc.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameAr.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="cb_CostMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>354, 227</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkpStore.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;txtStoreNameAr.Name" xml:space="preserve">
    <value>txtStoreNameAr</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesDiscAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="btnNext.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tab_PerpetualAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblSalesDiscount.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="&gt;&gt;txtAddress.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtStoreNameAr.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesDiscount.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="txtAddress.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;cb_CostMethod.Name" xml:space="preserve">
    <value>cb_CostMethod</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lblSalesDiscount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lblSalesAccount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="txt_StoreCode.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="IsStopped.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtTel.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 201</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="lblSales.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Name" xml:space="preserve">
    <value>barBtnDelete</value>
  </data>
  <data name="txt_Manager.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_PriceList.Name" xml:space="preserve">
    <value>lbl_PriceList</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="txtTel.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl7.Name" xml:space="preserve">
    <value>labelControl7</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpStore.Properties.Columns25" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns24" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns21" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 504</value>
  </data>
  <data name="&gt;&gt;chk_autoCreateAccs.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns22" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_autoCreateAccs.TabIndex" type="System.Int32, mscorlib">
    <value>161</value>
  </data>
  <data name="&gt;&gt;IsStopped.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkpStore.Properties.Columns29" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns28" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="lkpPriceLevel.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpStore.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btnNext.Name" xml:space="preserve">
    <value>btnNext</value>
  </data>
  <data name="barBtnList.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;labelControl8.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl12.Name" xml:space="preserve">
    <value>labelControl12</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtStoreNameAr.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cb_CostMethod.Properties.Items" xml:space="preserve">
    <value>FIFO</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lblPurchaseDiscount.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="txt_Manager.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl14.TabIndex" type="System.Int32, mscorlib">
    <value>155</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>668, 28</value>
  </data>
  <data name="lkpStore.Properties.Columns1" xml:space="preserve">
    <value>Store Name</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblSalesReturn.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="txt_StoreCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>Store Code</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 224</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lblPurchaseDiscount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>75, 16</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="Mobile.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 112</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPurchaseAcc.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;lblPurchaseDiscount.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lblSalesReturnAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 13</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="&gt;&gt;lblOpenInventoryAccount.Name" xml:space="preserve">
    <value>lblOpenInventoryAccount</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 532</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtStoreNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Manager.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>668, 0</value>
  </data>
  <data name="&gt;&gt;chk_autoCreateAccs.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpStore.Properties.Columns7" xml:space="preserve">
    <value>StoreCode</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txtTel.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtAddress.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="lblSales.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 13</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicOpenInvAcc.Name" xml:space="preserve">
    <value>lkp_PeriodicOpenInvAcc</value>
  </data>
  <data name="Mobile.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="&gt;&gt;lblInventory.Name" xml:space="preserve">
    <value>lblInventory</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="IsStopped.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>75, 116</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtAddress.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txtAddress.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Mobile.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cb_CostMethod.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lblInventory.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns15" xml:space="preserve">
    <value>اسم قائمة الأسعار</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns25" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl12.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="&gt;&gt;lblSales.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;labelControl14.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="cb_CostMethod.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="IsStopped.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lblInventory.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 94</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lblSalesReturn.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;IsStopped.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_IC_Store</value>
  </data>
  <data name="txt_StoreCode.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtStoreNameEn.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txtAddress.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesDiscAcc.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 121</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>75, 141</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpStore.Properties.Columns34" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 13</value>
  </data>
  <data name="&gt;&gt;labelControl7.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualInventoryAcc.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="&gt;&gt;txtAddress.Name" xml:space="preserve">
    <value>txtAddress</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTel.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblSalesAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 49</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Manager.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;lbl_PriceList.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 257</value>
  </data>
  <data name="&gt;&gt;IsStopped.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblSalesReturnAccount.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="btnPrev.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 32</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>516, 115</value>
  </data>
  <data name="lblSalesReturnAccount.TabIndex" type="System.Int32, mscorlib">
    <value>152</value>
  </data>
  <data name="xtraTabControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txt_StoreCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lblPurchasesReturnAccount.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblSalesReturnAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 74</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>171</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="tab_PerpetualAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>643, 249</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns21" xml:space="preserve">
    <value>PriceLevelId</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;chk_autoCreateAccs.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblOpenInventoryAccount.TabIndex" type="System.Int32, mscorlib">
    <value>153</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_Manager.Name" xml:space="preserve">
    <value>txt_Manager</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lblPurchaseDiscount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Manager.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;lblPurchasesReturnAccount.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Manager.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;Mobile.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesAcc.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="txtTel.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="Mobile.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;tab_PeriodicAcc.Name" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 221</value>
  </data>
  <data name="Mobile.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualCostOfSoldGoods.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkpStore.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_autoCreateAccs.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtTel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txtAddress.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chk_autoCreateAccs.Size" type="System.Drawing.Size, System.Drawing">
    <value>259, 19</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cb_CostMethod.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkpStore.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtAddress.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSalesReturn.TabIndex" type="System.Int32, mscorlib">
    <value>132</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="Mobile.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualPurchaseDiscountAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Mobile.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Manager.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 170</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblCloseInventoryAccount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesReturnAcc.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtTel.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="IsStopped.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lblSalesReturnAccount.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="Mobile.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpPriceLevel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="txt_StoreCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="IsStopped.Properties.Caption" xml:space="preserve">
    <value>موقوف</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="txt_Manager.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="txt_StoreCode.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>668, 532</value>
  </data>
  <data name="lkpStore.Properties.Columns36" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns23" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lblPurchasesAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 99</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>Sales Discount Account</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 13</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkpPriceLevel.Name" xml:space="preserve">
    <value>lkpPriceLevel</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="&gt;&gt;lblSalesReturnAccount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cb_CostMethod.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesAcc.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="IsStopped.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txt_StoreCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 20</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnPrev.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="IsStopped.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl14.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="lkpStore.Properties.Columns41" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;cb_CostMethod.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Manager.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="lkpStore.Properties.Columns" xml:space="preserve">
    <value>StoreNameAr</value>
  </data>
  <data name="txtStoreNameAr.Size" type="System.Drawing.Size, System.Drawing">
    <value>465, 20</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesDiscount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtTel.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblSalesAccount.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl8.Name" xml:space="preserve">
    <value>labelControl8</value>
  </data>
  <data name="lbl_PriceList.Location" type="System.Drawing.Point, System.Drawing">
    <value>253, 230</value>
  </data>
  <data name="lkpStore.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtTel.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpPriceLevel.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txtAddress.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtStoreNameEn.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 13</value>
  </data>
  <data name="lkpStore.Properties.Columns31" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lblOpenInventoryAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 13</value>
  </data>
  <data name="barBtnSave.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="txtAddress.Size" type="System.Drawing.Size, System.Drawing">
    <value>465, 20</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl15.Name" xml:space="preserve">
    <value>labelControl15</value>
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cb_CostMethod.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnNext.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualInventoryAcc.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelControl14.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTel.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;IsStopped.Name" xml:space="preserve">
    <value>IsStopped</value>
  </data>
  <data name="tab_PerpetualAcc.Text" xml:space="preserve">
    <value>Accounts</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="txt_Manager.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtAddress.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 144</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualPurchaseDiscountAcc.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PeriodicPrReturnAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtAddress.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtStoreNameAr.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="xtraTabControl1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesReturnAcc.Name" xml:space="preserve">
    <value>lkp_PeriodicSalesReturnAcc</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Manager.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="Mobile.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cb_CostMethod.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Near</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;Mobile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="txtStoreNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 62</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualPurchaseDiscountAcc.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesReturnAcc.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualInventoryAcc.Name" xml:space="preserve">
    <value>lkp_PerPetualInventoryAcc</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 46</value>
  </data>
  <data name="labelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns14" xml:space="preserve">
    <value>PLName</value>
  </data>
  <data name="txtStoreNameAr.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lblSalesReturn.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 13</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>Purchase Discount Account</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicCloseInvAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl12.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtStoreNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 87</value>
  </data>
  <data name="txtStoreNameEn.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PerPetualCostOfSoldGoods.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="txtAddress.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lblSalesDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 144</value>
  </data>
  <data name="lblPurchasesAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 13</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>Cost of Sold Goods</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.Caption" xml:space="preserve">
    <value>Create store accounts automatically</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblSalesDiscount.TabIndex" type="System.Int32, mscorlib">
    <value>142</value>
  </data>
  <data name="txtTel.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lkpStore.Properties.Columns37" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lblPurchasesReturnAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 124</value>
  </data>
  <data name="lkpStore.Properties.Columns30" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns33" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="lkpPriceLevel.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns27" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtAddress.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lblSalesDiscount.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesReturnAcc.Name" xml:space="preserve">
    <value>lkp_PerPetualSalesReturnAcc</value>
  </data>
  <data name="txtStoreNameAr.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblPurchaseDiscount.Text" xml:space="preserve">
    <value>Purchase Discount Account</value>
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtTel.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="labelControl7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 13</value>
  </data>
  <data name="lblSalesReturnAccount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicPrDiscAcc.Name" xml:space="preserve">
    <value>lkp_PeriodicPrDiscAcc</value>
  </data>
  <data name="txt_Manager.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="Mobile.TabIndex" type="System.Int32, mscorlib">
    <value>173</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesReturnAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl8.TabIndex" type="System.Int32, mscorlib">
    <value>144</value>
  </data>
  <data name="txtStoreNameEn.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barBtnHelp.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicOpenInvAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblInventory.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>Manager Name</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lblPurchasesAccount.TabIndex" type="System.Int32, mscorlib">
    <value>156</value>
  </data>
  <data name="cb_CostMethod.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesDiscAcc.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;labelControl12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Manager.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="cb_CostMethod.Properties.Items3" xml:space="preserve">
    <value>LIFO</value>
  </data>
  <data name="btnPrev.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 19</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesReturnAcc.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualPurchaseDiscountAcc.Name" xml:space="preserve">
    <value>lkp_PerPetualPurchaseDiscountAcc</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cb_CostMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lblSalesReturnAccount.Text" xml:space="preserve">
    <value>Sales Return Account</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="xtraTabControl1.HeaderAutoFill" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 71</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>516, 90</value>
  </data>
  <data name="IsStopped.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="cb_CostMethod.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;labelControl7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblPurchasesAccount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cb_CostMethod.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="cb_CostMethod.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cb_CostMethod.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="lkpStore.Properties.Columns48" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="cb_CostMethod.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;lblPurchaseDiscount.Name" xml:space="preserve">
    <value>lblPurchaseDiscount</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 196</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;lblSalesReturn.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Manager.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txt_StoreCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkpPriceLevel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="&gt;&gt;txt_Manager.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns27" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="IsStopped.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Manager.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnPrev.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cb_CostMethod.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpStore.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lblSales.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtTel.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpPriceLevel.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="lkpStore.Properties.Columns32" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 199</value>
  </data>
  <data name="lkpStore.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesDiscount.Name" xml:space="preserve">
    <value>lkp_PerPetualSalesDiscount</value>
  </data>
  <data name="lblSalesDiscount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Manager.Size" type="System.Drawing.Size, System.Drawing">
    <value>465, 20</value>
  </data>
  <data name="txtAddress.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicPurchaseAcc.Name" xml:space="preserve">
    <value>lkp_PeriodicPurchaseAcc</value>
  </data>
  <data name="&gt;&gt;btnPrev.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblPurchasesReturnAccount.Name" xml:space="preserve">
    <value>lblPurchasesReturnAccount</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lblInventory.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lblInventory.Size" type="System.Drawing.Size, System.Drawing">
    <value>90, 13</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="cb_CostMethod.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_StoreCode.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="IsStopped.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>520, 204</value>
  </data>
  <data name="lblOpenInventoryAccount.Text" xml:space="preserve">
    <value>Open Inventory Account</value>
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="lblSalesAccount.Text" xml:space="preserve">
    <value>Sales Account</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>647, 273</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicCloseInvAcc.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="&gt;&gt;tab_PeriodicAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtStoreNameAr.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="IsStopped.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 34</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>265, 115</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="btnPrev.ToolTip" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesAcc.Name" xml:space="preserve">
    <value>lkp_PerPetualSalesAcc</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicCloseInvAcc.Name" xml:space="preserve">
    <value>lkp_PeriodicCloseInvAcc</value>
  </data>
  <data name="lkpStore.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lblCloseInventoryAccount.Name" xml:space="preserve">
    <value>lblCloseInventoryAccount</value>
  </data>
  <data name="lblPurchaseDiscount.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 13</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="lkpStore.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpStore.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>67</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>516, 230</value>
  </data>
  <data name="lkpStore.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns35" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicPurchaseAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barBtnList.Caption" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtStoreNameEn.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblPurchasesAccount.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Manager.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lblSalesReturn.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 44</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;cb_CostMethod.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lblPurchaseDiscount.TabIndex" type="System.Int32, mscorlib">
    <value>141</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="txtStoreNameAr.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lblCloseInventoryAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 13</value>
  </data>
  <data name="lblPurchasesAccount.Text" xml:space="preserve">
    <value>Purchases Account</value>
  </data>
  <data name="txt_StoreCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtTel.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_Manager.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lblSalesAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 13</value>
  </data>
  <data name="lblInventory.TabIndex" type="System.Int32, mscorlib">
    <value>134</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="cb_CostMethod.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="lblSalesDiscount.Text" xml:space="preserve">
    <value>Sales Discount Account</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PeriodicPurchaseAcc.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtStoreNameEn.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesAcc.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl15.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lblSalesReturn.Text" xml:space="preserve">
    <value>Sales Return Account</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns15" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesDiscount.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesDiscAcc.Name" xml:space="preserve">
    <value>lkp_PeriodicSalesDiscAcc</value>
  </data>
  <data name="lblPurchasesAccount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblSalesDiscount.Size" type="System.Drawing.Size, System.Drawing">
    <value>111, 13</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="&gt;&gt;lblCloseInventoryAccount.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lblOpenInventoryAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 149</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkpPriceLevel.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 227</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="&gt;&gt;txtStoreNameEn.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>516, 65</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;tab_PeriodicAcc.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>Store</value>
  </data>
  <data name="&gt;&gt;lkpStore.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lbl_PriceList.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="cb_CostMethod.Properties.Items6" xml:space="preserve">
    <value>Average</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 96</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cb_CostMethod.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PeriodicCloseInvAcc.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="lkpStore.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;btnNext.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="btnPrev.Text" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;lblPurchasesAccount.Name" xml:space="preserve">
    <value>lblPurchasesAccount</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PeriodicPrReturnAcc.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>Branch/Store code</value>
  </data>
  <data name="txtAddress.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns42" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Mobile.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>153</value>
  </data>
  <data name="Mobile.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lblInventory.Text" xml:space="preserve">
    <value>Inventory Account</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Branch/Store</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;lblOpenInventoryAccount.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lblOpenInventoryAccount.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="lkpStore.Properties.Columns26" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblSales.Text" xml:space="preserve">
    <value>Sales Account</value>
  </data>
  <data name="&gt;&gt;btnPrev.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Mobile.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;btnPrev.Name" xml:space="preserve">
    <value>btnPrev</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="txtTel.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns22" xml:space="preserve">
    <value>PriceLevelId</value>
  </data>
  <data name="&gt;&gt;txtStoreNameAr.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="tab_PeriodicAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>643, 249</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 19</value>
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblCloseInventoryAccount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesAcc.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="lkpPriceLevel.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns14" xml:space="preserve">
    <value>AccNumber</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtStoreNameEn.Name" xml:space="preserve">
    <value>txtStoreNameEn</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="lkpStore.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;lblSalesReturnAccount.Name" xml:space="preserve">
    <value>lblSalesReturnAccount</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Name" xml:space="preserve">
    <value>barBtnSave</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_StoreCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>438, 37</value>
  </data>
  <data name="xtraTabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="lkpStore.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>516, 147</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualSalesReturnAcc.Parent" xml:space="preserve">
    <value>tab_PerpetualAcc</value>
  </data>
  <data name="&gt;&gt;tab_PerpetualAcc.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_StoreCode.Name" xml:space="preserve">
    <value>txt_StoreCode</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="IsStopped.Size" type="System.Drawing.Size, System.Drawing">
    <value>154, 19</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtStoreNameAr.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="cb_CostMethod.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkp_PeriodicPrDiscAcc.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="&gt;&gt;lblPurchasesAccount.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicPurchaseAcc.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="&gt;&gt;chk_autoCreateAccs.Name" xml:space="preserve">
    <value>chk_autoCreateAccs</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_StoreCode.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;txtTel.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicPrDiscAcc.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="labelControl7.TabIndex" type="System.Int32, mscorlib">
    <value>159</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="cb_CostMethod.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 13</value>
  </data>
  <data name="lbl_PriceList.TabIndex" type="System.Int32, mscorlib">
    <value>165</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblPurchasesReturnAccount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTel.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="&gt;&gt;barBtnList.Name" xml:space="preserve">
    <value>barBtnList</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;lbl_PriceList.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>516, 173</value>
  </data>
  <data name="lbl_PriceList.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 13</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="chk_autoCreateAccs.Location" type="System.Drawing.Point, System.Drawing">
    <value>179, 21</value>
  </data>
  <data name="&gt;&gt;lkp_PerPetualCostOfSoldGoods.Name" xml:space="preserve">
    <value>lkp_PerPetualCostOfSoldGoods</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtTel.Name" xml:space="preserve">
    <value>txtTel</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 20</value>
  </data>
  <data name="txtStoreNameEn.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicOpenInvAcc.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Location" type="System.Drawing.Point, System.Drawing">
    <value>75, 66</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="txt_StoreCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpPriceLevel.TabIndex" type="System.Int32, mscorlib">
    <value>166</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns23" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lblSales.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;lblSalesReturn.Name" xml:space="preserve">
    <value>lblSalesReturn</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;labelControl8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Mobile.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblOpenInventoryAccount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="IsStopped.TabIndex" type="System.Int32, mscorlib">
    <value>178</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lblSalesDiscount.Name" xml:space="preserve">
    <value>lblSalesDiscount</value>
  </data>
  <data name="txt_Manager.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 171</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicSalesAcc.Name" xml:space="preserve">
    <value>lkp_PeriodicSalesAcc</value>
  </data>
  <data name="lkpStore.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Manager.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lblPurchasesReturnAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>127, 13</value>
  </data>
  <data name="&gt;&gt;lblSalesAccount.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="lkpStore.Properties.Columns39" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns15" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="&gt;&gt;cb_CostMethod.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTel.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>423, 20</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpPriceLevel.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="&gt;&gt;lblPurchasesReturnAccount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Mobile.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 20</value>
  </data>
  <data name="lblPurchaseDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 119</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkpStore.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="txtTel.Location" type="System.Drawing.Point, System.Drawing">
    <value>354, 112</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="Mobile.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;barBtnList.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkpStore.Properties.Columns45" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Mobile.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns26" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Manager.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 146</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>Branch/Store Name</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns7" xml:space="preserve">
    <value>AccName</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;labelControl14.Name" xml:space="preserve">
    <value>labelControl14</value>
  </data>
  <data name="&gt;&gt;lkpStore.Name" xml:space="preserve">
    <value>lkpStore</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns1" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>111, 13</value>
  </data>
  <data name="cb_CostMethod.EditValue" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns8" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="&gt;&gt;lkp_PeriodicOpenInvAcc.Parent" xml:space="preserve">
    <value>tab_PeriodicAcc</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;labelControl15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnNext.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="labelControl15.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 504</value>
  </data>
  <data name="lkpPriceLevel.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns38" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns14" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns1" xml:space="preserve">
    <value>تفاصيل</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns7" xml:space="preserve">
    <value>IsRatio</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns8" xml:space="preserve">
    <value>النوع</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 13</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="lblSales.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 19</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Name" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>25</value>
  </metadata>
</root>