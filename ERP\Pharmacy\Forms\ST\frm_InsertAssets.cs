﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using DevExpress.XtraPrinting;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;

namespace Pharmacy.Forms
{
    public partial class frm_InsertAssets : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();

        public frm_InsertAssets()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
        }

        private void frm_ContractTemplate_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            //ERPDataContext db = new ERPDataContext();
            //var sheet = db.F_Assets_s.ToList().OrderBy(x =>  x.Sub_group ).ToList();
            //var lastGroup = "";
            //var count = 1;
            //var number = "0000";
            //foreach (var s in sheet)
            //{
            //    //break;
            //    count++;
            //    if (s.sub_type == lastGroup)
            //    {
            //        if (count <= 9)
            //            number = "0000" + count.ToString();
            //        else if (count > 9 && count <= 99)
            //            number = "000" + count.ToString();
            //    }
            //    else
            //    {
            //        count = 1; number = "00001";
            //    }

            //    lastGroup = s.Sub_group;

            //    FA_FixedAsset fa = new FA_FixedAsset();
            //    fa.DefaultAge = 0;
            //    fa.DeprType = 1;
            //    fa.FaAccountId = 0;// db.ACC_Accounts.Where(x => x.AcNameAr == s.Description).Select(x => x.AccountId).First();
            //    fa.FaCode = Convert.ToInt32(s.New_Code);
            //    fa.FaNameAr = s.Description;
            //    fa.FaNameEn = s.Description;
            //    fa.GroupId =db.FA_FixedAssetGroups.Where(y=>y.FaAccountId== db.ACC_Accounts.Where(x => x.AcNameAr == s.Sub_group|| x.AcNameEn == s.Sub_group).Select(x => x.AccountId).First()).Select(y=>y.FaGrpId).First();
            //    fa.NetCostValue = s.سعر_شراء_الأصل.HasValue ? (decimal)s.سعر_شراء_الأصل.Value : 0;
            //    fa.PurchaseDate = new DateTime(2020, 12, 1);
            //    fa.PurchasePrice = s.سعر_شراء_الأصل.HasValue ? (decimal)s.سعر_شراء_الأصل.Value : 0;
            //    fa.ScrapValue = 0;
            //    fa.FaAcNumber = "";// db.ACC_Accounts.Where(x => x.AcNameAr == s.Sub_group).Select(x => x.AcNumber).First() + number;

            //    //db.FA_FixedAssets.InsertOnSubmit(fa);
            //    //db.SubmitChanges();
            //    frm_FA_FixedAsset fxd = new frm_FA_FixedAsset(fa);
            //    fxd.Show();
            //    fxd.SaveData();
            //    fxd.Close();

            //}

        }


    }
}