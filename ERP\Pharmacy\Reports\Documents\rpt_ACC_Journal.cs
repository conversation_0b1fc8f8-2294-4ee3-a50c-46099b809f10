using DAL;
using System.Data;

namespace Reports
{
    public partial class rpt_ACC_Journal : DevExpress.XtraReports.UI.XtraReport
    {
        string code, date,
            process, notes,
            userName, totalDebit, totalCredit, Monthly_Code, Number;

        DataTable dt_inv_details;

        public rpt_ACC_Journal()
        {
            InitializeComponent();
        }

        public rpt_ACC_Journal(string _code, string _date, string _process, string _notes,
            DataTable dt, string userName, string _totalDebit, string _totalCredit, string Monthly_Code, string Number)
        {
            InitializeComponent();

            code = _code;
            date = _date;
            process = _process;
            notes = _notes;
            this.Monthly_Code = Monthly_Code;
            this.Number = Number;
            this.userName = userName;
            totalDebit = _totalDebit;
            totalCredit = _totalCredit;

            dt_inv_details = dt;
            this.DataSource = dt_inv_details;
            LoadData();
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = Shared.IsEnglish ? comp.CmpNameEn : comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_code.Text = code;
            lbl_date.Text = date;
            lbl_process.Text = process;
            lbl_notes.Text = notes;
            lbl_User.Text = userName;
            lbl_Number.Text = Number;
            lbl_Monthly_Code.Text = Monthly_Code;

            lbl_TotalDebit.Text = totalDebit;
            lbl_TotalCredit.Text = totalCredit;
            lblTotal_Words.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(totalDebit, 0, Shared.lstCurrency) :
                       HelperAcc.ConvertMoneyToArabicText(totalDebit, 0, Shared.lstCurrency);

            this.DataSource = dt_inv_details;


            cell_accName.DataBindings.Add("Text", this.DataSource, "AccountName");
            cell_accId.DataBindings.Add("Text", this.DataSource, "AccountId");
            cell_debit.DataBindings.Add("Text", this.DataSource, "Debit");
            cell_credit.DataBindings.Add("Text", this.DataSource, "Credit");
            cell_notes.DataBindings.Add("Text", this.DataSource, "Notes");
            cell_costcenter.DataBindings.Add("Text", this.DataSource, "CostCenter");

            getReportHeader();
        }
    }
}
