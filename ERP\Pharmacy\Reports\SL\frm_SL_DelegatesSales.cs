﻿using DAL;
using DAL.Res;
using DevExpress.Charts.Native;
using DevExpress.XtraCharts;
using DevExpress.XtraEditors;
using DevExpress.XtraReports.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Reports.SL
{
    public partial class frm_SL_DelegatesSales : Form
    {

        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, customerId1, customerId2, custGroupId,
            storeId1, storeId2, salesEmpId;
        byte FltrTyp_item, fltrTyp_Date, FltrTyp_Customer, FltrTyp_Category,
            FltrTyp_Store, FltrTyp_InvBook;


        string categoryNum;
        DateTime date1, date2;

        private void barButtonItem5_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grd_data, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        private void barButtonItem6_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", ch_Report, true, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        private void barButtonItem7_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", ch_Report, grd_data, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        string custGroupAccNumber;
        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();
        public int count;

        byte FltrTyp_Company;
        int companyId;

        public frm_SL_DelegatesSales(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            byte FltrTyp_Category, string categoryNum,
            int custGroupId, string custGroupAccNumber,
            byte FltrTyp_Store, int storeId1, int storeId2,
            int salesEmpId,
            byte FltrTyp_InvBook, string InvBooks, byte FltrTyp_Company, int companyId)
        {

            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_Category = FltrTyp_Category;
            this.FltrTyp_Store = FltrTyp_Store;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;

            this.date1 = date1;
            this.date2 = date2;
            if (date2 == Shared.minDate)
                this.date2 = Shared.maxDate;

            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            this.categoryNum = categoryNum;

            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;
            this.storeId1 = storeId1;
            this.storeId2 = storeId2;
            this.salesEmpId = salesEmpId;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();

            //LoadData();
            //col_PiecesCount.Visible = Shared.st_Store.PiecesCount;
            //ReportsUtils.ColumnChooser(grdCategory);
        }

        private void barBtn_PreviewData_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grd_data.MinimumSize = grd_data.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grd_data, true, true).ShowPreview();
                grd_data.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void barButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", ch_Report, true, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        private void barButtonItem4_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", ch_Report, grd_data, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        private void barButtonItem3_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grd_data, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }



        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }


        private void frm_SL_DelegatesSales_Load(object sender, EventArgs e)
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            ERPDataContext DB = new DAL.ERPDataContext();
            #region old
            //var data3 = (from d in DB.SL_InvoiceDetails
            //            join t in DB.IC_Items.DefaultIfEmpty()
            //            on d.ItemId equals t.ItemId

            //            join c in DB.IC_Categories
            //            on t.Category equals c.CategoryId

            //            join i in DB.SL_Invoices
            //            on d.SL_InvoiceId equals i.SL_InvoiceId


            //            where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1.Date : true
            //            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
            //            i.InvoiceDate.Date >= date1.Date && i.InvoiceDate.Date <= date2.Date : true
            //            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
            //            i.InvoiceDate.Date >= date1.Date : true
            //            where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
            //            i.InvoiceDate.Date <= date2.Date : true

            //            join m in DB.HR_Employees
            //            on i.SalesEmpId equals m.EmpId
            //            where m.SalesRep == true
            //            where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

            //            group new { c, t, i, d, m/*, u*/ } by new { /*i.SL_InvoiceId,*/i.SalesEmpId, d.ItemId } into igroup

            //            let InvoiceId = igroup.Select(g => g.i.SL_InvoiceId).SingleOrDefault()
            //            let ItemId = igroup.Select(g => g.d.ItemId).SingleOrDefault()

            //            select new
            //            {
            //                ItemNameAr = igroup.Select(g => g.t.ItemNameAr).FirstOrDefault(),
            //                Category = igroup.Select(g => g.c.CategoryNameAr).FirstOrDefault(),
            //                Delegate = igroup.Select(g => g.m.EmpName).FirstOrDefault(),
            //                UOM = DB.IC_UOMs.FirstOrDefault(u => u.UOMId == igroup.Select(g => g.t.SmallUOM).FirstOrDefault()).UOM,
            //                #region new
            //                Qty = igroup.Sum(g => g.d.UOMIndex == 0 ? g.d.Qty : g.d.UOMIndex == 1 ? (g.d.Qty * Convert.ToDecimal(g.t.MediumUOMFactor)) : (g.d.Qty * Convert.ToDecimal(g.t.LargeUOMFactor))),


            //                #endregion

            //                Detail =
            //                from g in igroup
            //                group g by g.i.SL_InvoiceId into grp
            //                let customer = DB.SL_Customers
            //                select new
            //                {
            //                    InvoiceCode = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.i.InvoiceCode).FirstOrDefault(),
            //                    Qty = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.d.Qty).Sum(),
            //                    TotalSellPrice = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.d.TotalSellPrice).Sum(),
            //                    PayMethod = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.i.PayMethod).FirstOrDefault(),
            //                    InvoiceDate = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.i.InvoiceDate).FirstOrDefault(),
            //                    CusNameAr = customer.Where(xy => xy.CustomerId == grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.i.CustomerId).FirstOrDefault()).Select(x => x.CusNameAr).FirstOrDefault()
            //                }
            //            }).Distinct();


            #endregion




            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var data1 = (from d in DB.SL_InvoiceDetails
                         join t in DB.IC_Items.DefaultIfEmpty()
                         on d.ItemId equals t.ItemId
                         join ic in DB.IC_Categories on t.Category equals ic.CategoryId
                         where defaultCategories.Count() > 0 ? defaultCategories.Contains(ic.CategoryId) : true
                         join i in DB.SL_Invoices
                         on d.SL_InvoiceId equals i.SL_InvoiceId


                         where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                         i.InvoiceDate.Date >= date1.Date && i.InvoiceDate.Date <= date2.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                         i.InvoiceDate.Date >= date1.Date : true
                         where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                         i.InvoiceDate.Date <= date2.Date : true
                         where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId
                         join m in DB.HR_Employees
                            on i.SalesEmpId equals m.EmpId

                         group new { t, i, d, m } by new { i.SalesEmpId, d.ItemId } into igroup


                         select new
                         {
                             ItemNameAr = igroup.Select(g => g.t.ItemNameAr).FirstOrDefault(),
                             Category = DB.IC_Categories.SingleOrDefault(c => c.CategoryId == igroup.Select(g => g.t.Category).FirstOrDefault()).CategoryNameAr,
                             UOM = DB.IC_UOMs.FirstOrDefault(u => u.UOMId == igroup.Select(g => g.t.SmallUOM).FirstOrDefault()).UOM,
                             Qty = igroup.Sum(g => g.d.UOMIndex == 0 ? g.d.Qty : g.d.UOMIndex == 1 ? (g.d.Qty * Convert.ToDecimal(g.t.MediumUOMFactor)) : (g.d.Qty * Convert.ToDecimal(g.t.LargeUOMFactor))),
                             Delegate = igroup.Select(g => g.m.EmpName).FirstOrDefault(),
                             ItemId = igroup.Select(g => g.t.ItemId).FirstOrDefault(),
                             DelegateId= igroup.Select(g => g.m.EmpId).FirstOrDefault(),

                         }).Distinct().ToList();


            


         

            var data3 = (from d1 in data1
                         group d1 by new { d1.Delegate, d1.ItemId } into grp
                         let detail = from id in DB.SL_InvoiceDetails
                                      join i in DB.SL_Invoices on id.SL_InvoiceId equals i.SL_InvoiceId
                                      where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1.Date : true
                                      where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                      i.InvoiceDate.Date >= date1.Date && i.InvoiceDate.Date <= date2.Date : true
                                      where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                      i.InvoiceDate.Date >= date1.Date : true
                                      where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                      i.InvoiceDate.Date <= date2.Date : true
                                      select new { id, i }

                         select new
                         {
                             grp.FirstOrDefault().ItemNameAr,
                             grp.FirstOrDefault().Category,
                             grp.FirstOrDefault().UOM,
                             Qty = grp.Where(x => x.Delegate == grp.Key.Delegate).Select(x => x.Qty).Sum(),
                             grp.FirstOrDefault().Delegate,
                             Detail = from d in detail
                                      where d.id.ItemId == grp.Key.ItemId
                                      where d.i.SalesEmpId == grp.FirstOrDefault().DelegateId
                                      group d by new { d.i.SL_InvoiceId,d.id.ItemId } into igroup
                                      let item = DB.IC_Items.Where(t => t.ItemId == grp.Key.ItemId)

                                      select new
                                      {
                                          grp.FirstOrDefault().Delegate,
                                          igroup.FirstOrDefault().i.InvoiceCode,
                                          igroup.FirstOrDefault().i.InvoiceDate,
                                          igroup.FirstOrDefault().i.PayMethod,
                                          Qty = igroup.Sum(g=>g.id.UOMIndex == 0 ? g.id.Qty : g.id.UOMIndex == 1 ? (g.id.Qty * Convert.ToDecimal(item.SingleOrDefault().MediumUOMFactor)) : (g.id.Qty * Convert.ToDecimal(item.SingleOrDefault().LargeUOMFactor))),
                                          CusNameAr = DB.SL_Customers.SingleOrDefault(x => x.CustomerId == igroup.FirstOrDefault().i.CustomerId).CusNameAr,
                                          TotalSellPrice = igroup.Sum( g=>g.id.TotalSellPrice)

                                          
                                      }

                         }).Distinct().ToList();





            gridView1.DataController.AllowIEnumerableDetails = true;
            grd_data.DataSource = data3;


            ch_Delegates.DataSource = data3;
            ch_Delegates.Series[0].ArgumentDataMember = "Delegate";
            ch_Delegates.Series[0].ValueDataMembers[0] = "Qty";



            #region chart
            //ch_Report.DataSource = data;
            var emp = DB.HR_Employees.Where(m => m.SalesRep == true && salesEmpId == 0 ? true : m.EmpId == salesEmpId);

            var counter = 0;
            foreach (var item in emp)
            {
                Series newSeries = new Series(item.EmpName.ToString(), ViewType.Bar);
                ch_Report.Series.Add(newSeries);
                //newSeries.ChangeView(ViewType.Bar);

                ch_Report.Series[counter].DataSource = data3.Where(x => x.Delegate == item.EmpName);

                ch_Report.Series[counter].ArgumentDataMember = "ItemNameAr";
                ch_Report.Series[counter].ValueDataMembers[0] = "Qty"/*+(count+1)*/;
                counter++;
            }

            #endregion
            var total = (from i in DB.SL_Invoices
                         where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId
                         //where DB.HR_Employees.SingleOrDefault(x => x.EmpId == i.SalesEmpId).SalesRep == true
                         select i.Net).ToList().DefaultIfEmpty(0).Sum();
            lbl_TotalText.Text = Math.Round(total, 3).ToString();


            var Cash1 = (from i in DB.SL_Invoices
                         where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId
                         where i.PayMethod == true
                         select i.Net).ToList().DefaultIfEmpty(0).Sum();

            var Cash2 = (from i in DB.SL_Invoices
                         where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId
                         where i.PayMethod == null
                         select i.Paid).ToList().DefaultIfEmpty(0).Sum();


            lbl_TotalCashtext.Text = Math.Round(Cash1 + Cash2, 3).ToString();


            var OnCredit1 = (from i in DB.SL_Invoices
                             where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId
                             where i.PayMethod == false
                             select i.Net).ToList().DefaultIfEmpty(0).Sum();

            var OnCredit2 = (from i in DB.SL_Invoices
                             where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId
                             where i.PayMethod == null
                             select i.Remains).ToList().DefaultIfEmpty(0).Sum();

            lbl_TotalOnCreditText.Text = Math.Round(OnCredit1 + OnCredit2, 3).ToString();

        }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_InvoicesHeaders).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }



    }
}
