﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="textEdit5.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_CompanyNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="col_Serial2.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="col_TotalQty.Width" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="textEdit1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl35.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl13.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;barBtnPrint.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl17.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl16.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lkp_SubTaxes.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn43.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl19.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="gridColumn42.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txt_AddTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;mi_PasteRows.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="col_Expire.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit7.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="frm_SL_Return.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;textEdit1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl25.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;btnImport.Name" xml:space="preserve">
    <value>btnImport</value>
  </data>
  <data name="lkp_SubTaxes.Columns1" xml:space="preserve">
    <value>DescriptionAr</value>
  </data>
  <data name="txtNet.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SubTaxes.Columns2" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkp_SubTaxes.Columns5" xml:space="preserve">
    <value>E_TaxableTypeId</value>
  </data>
  <data name="lkp_SubTaxes.Columns4" xml:space="preserve">
    <value>E_TaxableTypeId</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkp_SubTaxes.Columns7" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SubTaxes.Columns6" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="Value.Width" type="System.Int32, mscorlib">
    <value>178</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn28.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Length.Width" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gv_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit1.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Name" xml:space="preserve">
    <value>gridColumn13</value>
  </data>
  <data name="lkp_Drawers.Location" type="System.Drawing.Point, System.Drawing">
    <value>178, 3</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="colEtaxValue.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="txtNet.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxV.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn29.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_CusTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;groupControl1.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="grd_SubTaxes.TabIndex" type="System.Int32, mscorlib">
    <value>343</value>
  </data>
  <data name="txtExpenses.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="gridColumn2.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbPayMethod.Properties.Items3" xml:space="preserve">
    <value>Cash</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl20.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>Disc R</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Name" xml:space="preserve">
    <value>lkpCostCenter</value>
  </data>
  <data name="col_Rate.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_VariableWeight.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tableTaxValue.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn8.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_EtaxValue.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.Caption" xml:space="preserve">
    <value>Added to Store</value>
  </data>
  <data name="lkpStore.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio2.Name" xml:space="preserve">
    <value>col_DiscountRatio2</value>
  </data>
  <data name="gridColumn31.Caption" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn35.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl19.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="repExpireDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnAddCustomer.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn9.Caption" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="txt_Remains.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn29.Caption" xml:space="preserve">
    <value>LargeUOMFactor</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>61, 493</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn16.Caption" xml:space="preserve">
    <value>Factor</value>
  </data>
  <data name="labelControl13.TabIndex" type="System.Int32, mscorlib">
    <value>167</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Parent" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="TotalTaxes.Caption" xml:space="preserve">
    <value>TotalTaxes</value>
  </data>
  <data name="lkp_SubTaxes.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit7.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="Code.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="groupControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="txt_CusTaxV.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;rep_btnAddTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtInvoiceCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Name" xml:space="preserve">
    <value>gridColumn18</value>
  </data>
  <data name="col_SalesTax.Width" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="textEdit7.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 22</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_PayAcc1_Paid.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdPrInvoice.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="txt_paid.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="gridView5.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="colDescription.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grdPrInvoice.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="gridColumn1.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl26.Location" type="System.Drawing.Point, System.Drawing">
    <value>113, 7</value>
  </data>
  <data name="textEdit1.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txtExpenses.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="&gt;&gt;gridColumn42.Name" xml:space="preserve">
    <value>gridColumn42</value>
  </data>
  <data name="repDiscountRatio.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;pnlInvCode.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Name" xml:space="preserve">
    <value>gridColumn23</value>
  </data>
  <data name="&gt;&gt;DescriptionAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbPayMethod.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Name" xml:space="preserve">
    <value>gridColumn16</value>
  </data>
  <data name="txt_TaxValue.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="&gt;&gt;gridView3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;batBtnList.Name" xml:space="preserve">
    <value>batBtnList</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl23.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>265</value>
  </data>
  <data name="totalTaxesRatio.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl27.TabIndex" type="System.Int32, mscorlib">
    <value>287</value>
  </data>
  <data name="&gt;&gt;col_PricingWithSmall.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl29.TabIndex" type="System.Int32, mscorlib">
    <value>333</value>
  </data>
  <data name="textEdit6.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn35.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_TotalQty.Caption" xml:space="preserve">
    <value>Total Qty</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="repExpireDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="labelControl29.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;totalTaxesRatio.Name" xml:space="preserve">
    <value>totalTaxesRatio</value>
  </data>
  <data name="textEdit5.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="uc_Currency1.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 24</value>
  </data>
  <data name="txt_Remains.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn38.Caption" xml:space="preserve">
    <value>UomIndex</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Expire.Caption" xml:space="preserve">
    <value>Expire Date</value>
  </data>
  <data name="lkp_Drawers2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Name" xml:space="preserve">
    <value>txtDiscountRatio</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn1.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_CusTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;lkp_SubTaxes.Name" xml:space="preserve">
    <value>lkp_SubTaxes</value>
  </data>
  <data name="gridColumn14.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="lkp_Customers.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl14.Name" xml:space="preserve">
    <value>labelControl14</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_Is_Libra.Name" xml:space="preserve">
    <value>col_Is_Libra</value>
  </data>
  <data name="lkpStore.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Text" xml:space="preserve">
    <value>Static Invoice Dimenstions</value>
  </data>
  <data name="gridView2.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn40.Caption" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="barBtnCancel.Caption" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="lkpCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 24</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl16.TabIndex" type="System.Int32, mscorlib">
    <value>172</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsInTrns.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns1" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns8" xml:space="preserve">
    <value>Account Id</value>
  </data>
  <data name="rep_vendors.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txt_paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 47</value>
  </data>
  <data name="gridColumn35.Width" type="System.Int32, mscorlib">
    <value>57</value>
  </data>
  <data name="txt_TaxValue.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsInTrns.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;col_Is_Libra.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_paid.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit5.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn24.Caption" xml:space="preserve">
    <value>Sell Price</value>
  </data>
  <data name="&gt;&gt;col_SalesTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="uc_Currency1.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 19</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;pnlDate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;panelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_SubTaxes.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="gridColumn13.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="textEdit6.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 22</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Name" xml:space="preserve">
    <value>gridColumn28</value>
  </data>
  <data name="txt_EtaxValue.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>77, 493</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Down</value>
  </data>
  <data name="&gt;&gt;txt_EtaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_paid.Name" xml:space="preserve">
    <value>txt_paid</value>
  </data>
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="textEdit5.EditValue" xml:space="preserve">
    <value>Branch</value>
  </data>
  <data name="&gt;&gt;col_Width.Name" xml:space="preserve">
    <value>col_Width</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>77, 559</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbPayMethod.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;pnlCrncy.Name" xml:space="preserve">
    <value>pnlCrncy</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_vendors.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;chk_IsInTrns.Name" xml:space="preserve">
    <value>chk_IsInTrns</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn24.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtExpenses.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate_txt.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn26.Name" xml:space="preserve">
    <value>gridColumn26</value>
  </data>
  <data name="txt_Remains.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Name" xml:space="preserve">
    <value>txt_AddTaxR</value>
  </data>
  <data name="textEdit6.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="groupControl1.AppearanceCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn31.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TaxValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl5.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colbonusDiscount.Caption" xml:space="preserve">
    <value>Bonus Discount</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="col_TotalSellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn20.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="rep_btnAddTaxes.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkp_Customers.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_ItemDescription.Name" xml:space="preserve">
    <value>col_ItemDescription</value>
  </data>
  <data name="&gt;&gt;gridColumn27.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;colLocationNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit7.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn10.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl35.ZOrder" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="txt_PayAcc1_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 3</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Name" xml:space="preserve">
    <value>txt_AddTaxV</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridView2.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn2.Summary1" xml:space="preserve">
    <value>DiscountValue</value>
  </data>
  <data name="cmbPayMethod.Properties.Items4" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn33.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtDiscountRatio.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="txt_AddTaxR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridView5.Name" xml:space="preserve">
    <value>gridView5</value>
  </data>
  <data name="repDiscountRatio.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn1.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_btnAddTaxes.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>61, 559</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.Name" xml:space="preserve">
    <value>uc_Currency1</value>
  </data>
  <data name="txt_EtaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 464</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;gridColumn40.Name" xml:space="preserve">
    <value>gridColumn40</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxV.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridView4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;pnlDate.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="gridColumn10.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtDiscountRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1182, 31</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Name" xml:space="preserve">
    <value>gridColumn14</value>
  </data>
  <data name="textEdit6.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="SubTaxId.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView4.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_paid.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl11.TabIndex" type="System.Int32, mscorlib">
    <value>169</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="repSpin.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Name" xml:space="preserve">
    <value>barBtnSave</value>
  </data>
  <data name="textEdit5.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl20.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Name" xml:space="preserve">
    <value>gridColumn19</value>
  </data>
  <data name="flowLayoutPanel1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_Expire.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_Expire.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridColumn33.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl19.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl29.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 467</value>
  </data>
  <data name="btn_AddTaxes.Caption" xml:space="preserve">
    <value>AddTaxes</value>
  </data>
  <data name="&gt;&gt;gridColumn30.Name" xml:space="preserve">
    <value>gridColumn30</value>
  </data>
  <data name="SubTaxId.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl12.Name" xml:space="preserve">
    <value>labelControl12</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn28.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colEtaxValue.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;repExpireDate.Name" xml:space="preserve">
    <value>repExpireDate</value>
  </data>
  <data name="btnAddCustomer.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="&gt;&gt;labelControl16.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Name" xml:space="preserve">
    <value>txtCurrency</value>
  </data>
  <data name="&gt;&gt;SubTaxId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repSpin.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns" xml:space="preserve">
    <value>AccountName</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 13</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>807, 33</value>
  </data>
  <data name="gridColumn22.Width" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.ZOrder" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="&gt;&gt;gridColumn38.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl28.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="&gt;&gt;col_CategoryNameAr.Name" xml:space="preserve">
    <value>col_CategoryNameAr</value>
  </data>
  <data name="gridColumn28.Width" type="System.Int32, mscorlib">
    <value>102</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn11.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barbtnLoadSellInvoice.Name" xml:space="preserve">
    <value>barbtnLoadSellInvoice</value>
  </data>
  <data name="txtInvoiceCode.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Remains.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SubTaxes.Columns3" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="&gt;&gt;gv_SubTaxes.Name" xml:space="preserve">
    <value>gv_SubTaxes</value>
  </data>
  <data name="txt_AddTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_PayAcc2_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit5.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>113, 50</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>F Name</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repDiscountRatio.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="col_Rate.Caption" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_CompanyNameAr.Name" xml:space="preserve">
    <value>col_CompanyNameAr</value>
  </data>
  <data name="cmbPayMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>105, 18</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl35.Name" xml:space="preserve">
    <value>labelControl35</value>
  </data>
  <data name="labelControl14.TabIndex" type="System.Int32, mscorlib">
    <value>160</value>
  </data>
  <data name="lkpCostCenter.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repExpireDate_txt.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="gridColumn6.Width" type="System.Int32, mscorlib">
    <value>184</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_SubTaxes.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn2.Name" xml:space="preserve">
    <value>gridColumn2</value>
  </data>
  <data name="txtDiscountValue.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn24.Name" xml:space="preserve">
    <value>gridColumn24</value>
  </data>
  <data name="col_Batch.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_EtaxValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn12.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl26.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;txtExpenses.ZOrder" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="txtExpenses.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 604</value>
  </data>
  <data name="txt_Remains.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Rate.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl19.Name" xml:space="preserve">
    <value>labelControl19</value>
  </data>
  <data name="lkpStore.Properties.Columns35" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns34" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns37" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns36" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns31" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns30" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns33" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns32" xml:space="preserve">
    <value />
  </data>
  <data name="colbonusDiscount.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="frm_SL_Return.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Customers.Size" type="System.Drawing.Size, System.Drawing">
    <value>205, 20</value>
  </data>
  <data name="lkpStore.Properties.Columns39" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns38" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Name" xml:space="preserve">
    <value>gridColumn6</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Drawers.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>256</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Name" xml:space="preserve">
    <value>barBtnDelete</value>
  </data>
  <data name="gridColumn8.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;rep_vendors.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl2.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="txt_AddTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btn_AddTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;importFromExcelSheetToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txt_paid.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lkpCostCenter.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gv_SubTaxes.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="rep_expireDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="rep_btnAddTaxes.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="repExpireDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Name" xml:space="preserve">
    <value>txt_PayAcc2_Paid</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl14.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="grdPrInvoice.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="repSpin.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit5.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit6.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="&gt;&gt;totalTableTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbPayMethod.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="col_Rate.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_paid.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpStore.Properties.Columns47" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnNext.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkpStore.Properties.Columns41" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit1.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="lkpStore.Properties.Columns43" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns42" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>Disc V</value>
  </data>
  <data name="lkpStore.Properties.Columns48" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txt_Total.Name" xml:space="preserve">
    <value>txt_Total</value>
  </data>
  <data name="col_CurrentQty.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_EtaxValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridColumn19.Caption" xml:space="preserve">
    <value>CustomerId</value>
  </data>
  <data name="txt_AddTaxR.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn28.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn18.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit6.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit6.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_PayAcc1_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="repDiscountRatio.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="&gt;&gt;groupControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.Type" xml:space="preserve">
    <value>Pharmacy.Forms.uc_Currency, LinkIT ERP System, Version=2.24.9.18, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="txt_PayAcc1_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="lkpStore.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;pnlBranch.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="gridColumn30.Caption" xml:space="preserve">
    <value>CostCenter Name</value>
  </data>
  <data name="repLocation.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Name" xml:space="preserve">
    <value>txt_DeductTaxV</value>
  </data>
  <data name="gridColumn7.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_CusTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsInTrns.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_TaxValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 20</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1182, 0</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 21</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn28.Caption" xml:space="preserve">
    <value>MediumUOMFactor</value>
  </data>
  <data name="lkpStore.Properties.Columns14" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="lkpStore.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridColumn2.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpStore.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtCurrency.TabIndex" type="System.Int32, mscorlib">
    <value>277</value>
  </data>
  <data name="lkpStore.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn18.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Name" xml:space="preserve">
    <value>dtInvoiceDate</value>
  </data>
  <data name="&gt;&gt;col_Serial.Name" xml:space="preserve">
    <value>col_Serial</value>
  </data>
  <data name="lkpStore.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="txtCurrency.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colDescription.Caption" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.ZOrder" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="col_SalesTax.Caption" xml:space="preserve">
    <value>Sales Tax</value>
  </data>
  <data name="gridColumn5.Width" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="&gt;&gt;labelControl28.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl11.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn43.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn30.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl7.TabIndex" type="System.Int32, mscorlib">
    <value>161</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_CusTax.Caption" xml:space="preserve">
    <value>Custom Tax</value>
  </data>
  <data name="colbonusDiscount.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtNet.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="SubTaxId.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txtInvoiceCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 20</value>
  </data>
  <data name="txtNotes.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 90</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Customers.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="totalTaxesRatio.VisibleIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="txtDiscountRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>94, 490</value>
  </data>
  <data name="lkpCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 20</value>
  </data>
  <data name="repDiscountRatio.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Properties.Columns25" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns24" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns27" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns26" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns21" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns23" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns22" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="&gt;&gt;labelControl27.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkpStore.Properties.Columns29" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns28" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="gridColumn31.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 582</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl23.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="col_DiscountRatio2.Width" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="&gt;&gt;labelControl7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_Rate.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnPrevious.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_TotalSellPrice.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="&gt;&gt;txt_paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Customers.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;col_PricingWithSmall.Name" xml:space="preserve">
    <value>col_PricingWithSmall</value>
  </data>
  <data name="textEdit1.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_EtaxValue.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SubTaxes.Columns" xml:space="preserve">
    <value>Description Ar</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit6.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repLocation.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="pnlInvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 44</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Batch.Width" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="txtExpenses.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCostCenter.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_VariableWeight.Name" xml:space="preserve">
    <value>col_VariableWeight</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lbl_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>164</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="txt_DeductTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 20</value>
  </data>
  <data name="col_Serial2.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_Length.Name" xml:space="preserve">
    <value>col_Length</value>
  </data>
  <data name="&gt;&gt;barBtnPrint.Name" xml:space="preserve">
    <value>barBtnPrint</value>
  </data>
  <data name="txt_paid.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_CusTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 537</value>
  </data>
  <data name="txt_EtaxValue.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtExpenses.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;DescriptionAr.Name" xml:space="preserve">
    <value>DescriptionAr</value>
  </data>
  <data name="col_PricingWithSmall.Caption" xml:space="preserve">
    <value>PricingWithSmall</value>
  </data>
  <data name="textEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="txtNotes.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtExpenses.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_CurrentQty.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txt_DeductTaxR.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="Code.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>342, 29</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_DeductTaxR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="repTaxTypes.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;repItems.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbPayMethod.EditValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Customers.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;repExpireDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemDateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnPrevious.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="gridColumn2.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpCostCenter.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="repDiscountRatio.Mask.EditMask" xml:space="preserve">
    <value>p2</value>
  </data>
  <data name="col_CurrentQty.Width" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="rep_expireDate.Mask.EditMask" xml:space="preserve">
    <value>M-yyyy</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repSpin.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="btnAddCustomer.TabIndex" type="System.Int32, mscorlib">
    <value>177</value>
  </data>
  <data name="&gt;&gt;repExpireDate_txt.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDisc.Name" xml:space="preserve">
    <value>mi_InvoiceStaticDisc</value>
  </data>
  <data name="&gt;&gt;pnlDate.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="dtInvoiceDate.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn20.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Name" xml:space="preserve">
    <value>txtInvoiceCode</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lkp_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 621</value>
  </data>
  <data name="&gt;&gt;col_Pack.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="&gt;&gt;panelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridColumn7.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_TaxValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Name" xml:space="preserve">
    <value>lkp_Drawers</value>
  </data>
  <data name="col_Batch.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn36.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gv_SubTaxes.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="panelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="colbonusDiscount.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn21.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Expire.Width" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="gridColumn1.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="mi_frm_IC_Item.Text" xml:space="preserve">
    <value>Item Info</value>
  </data>
  <data name="tableTaxValue.Caption" xml:space="preserve">
    <value>Table Tax Value</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_ItemDescription.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Drawers.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;rep_expireDate.Name" xml:space="preserve">
    <value>rep_expireDate</value>
  </data>
  <data name="barBtnCancel.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABF0RVh0VGl0
        bGUAUmVzZXQ7VW5kbzsTgRb/AAAA70lEQVQ4T6XTMWoCURDG8W2EWGgRC7ERDF7AVrDwAN7BRgQheAev
        EbxDAilsFEQQtBQLu1WwsJVYis//LEx4+5jVQIrfsvLNfDwebuSc+5eoM5r4XvAJhxuGCGdS/B8FTKHL
        A/i5SV9esYIu95AazCKPCraQ5SwXHPANOVkRvwUzWEuPnNBCUlDFDtbgI3KqenIMlLGBBFd0oZnKoYEF
        tGTsD8hFriGBXGQffq5q0IJ9GMrlzKEl7whnhBZcrPCZOrQgtgaylNDGElrwYQ2GdDj0gzdrIWQtH9FE
        6lvIIgtnxPiC/M3zSHLzE/07F90Bmo0RPdcw7NUAAAAASUVORK5CYII=
</value>
  </data>
  <data name="col_CurrentQty.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;btn_AddTaxes.Name" xml:space="preserve">
    <value>btn_AddTaxes</value>
  </data>
  <data name="cmbPayMethod.Properties.Items7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl17.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl19.TabIndex" type="System.Int32, mscorlib">
    <value>172</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Name" xml:space="preserve">
    <value>contextMenuStrip1</value>
  </data>
  <data name="txtCurrency.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Remains.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn39.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit1.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;salePriceWithTaxTable.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscountValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 20</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 652</value>
  </data>
  <data name="col_CategoryNameAr.Caption" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="&gt;&gt;col_PiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Batch.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="txtDiscountValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountValue.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;repManufactureDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemDateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="repTaxTypes.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repLocation.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="textEdit7.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 33</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_TotalQty.Name" xml:space="preserve">
    <value>col_TotalQty</value>
  </data>
  <data name="&gt;&gt;col_Width.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="pnlInvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>747, 1</value>
  </data>
  <data name="gridColumn14.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="SubTaxId.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_Total.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="gridColumn1.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_EtaxValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl25.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="&gt;&gt;mi_PasteRows.Name" xml:space="preserve">
    <value>mi_PasteRows</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn14.Caption" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_SL_Return</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="txtNet.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;Code.Name" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="colTaxType.Caption" xml:space="preserve">
    <value>Tax Type</value>
  </data>
  <data name="col_CompanyNameAr.Caption" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="lkpStore.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;textEdit7.Parent" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="txt_EtaxValue.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_CurrentQty.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="txtNotes.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_DiscountRatio3.Name" xml:space="preserve">
    <value>col_DiscountRatio3</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn23.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtNet.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 625</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;Value.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="lkp_Customers.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl28.Name" xml:space="preserve">
    <value>labelControl28</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;salePriceWithTaxTable.Name" xml:space="preserve">
    <value>salePriceWithTaxTable</value>
  </data>
  <data name="lkpStore.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lbl_Paid.Text" xml:space="preserve">
    <value>Total Paid</value>
  </data>
  <data name="gridColumn6.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Name" xml:space="preserve">
    <value>lbl_Paid</value>
  </data>
  <data name="col_ItemDescription.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit6.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl1.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn7.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repItems.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;uc_Currency1.Parent" xml:space="preserve">
    <value>pnlCrncy</value>
  </data>
  <data name="gridColumn33.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn42.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>247, 245, 241</value>
  </data>
  <data name="&gt;&gt;colEtaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItemDescriptionEn.Name" xml:space="preserve">
    <value>col_ItemDescriptionEn</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="gridColumn8.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtNet.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 20</value>
  </data>
  <data name="txt_AddTaxV.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Name" xml:space="preserve">
    <value>cmbPayMethod</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="txtExpenses.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>Cus Tax</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="textEdit1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl25.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;E_TaxableTypeId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_SubTaxes.Location" type="System.Drawing.Point, System.Drawing">
    <value>254, 457</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 556</value>
  </data>
  <data name="rep_expireDate.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>Sales Return Invoices</value>
  </data>
  <data name="textEdit6.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Drawers.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;textEdit7.Name" xml:space="preserve">
    <value>textEdit7</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Name" xml:space="preserve">
    <value>gridColumn12</value>
  </data>
  <data name="&gt;&gt;labelControl14.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_SubTaxes.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="contextMenuStrip1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemDescription.Caption" xml:space="preserve">
    <value>Item Description</value>
  </data>
  <data name="col_ETaxRatio.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="textEdit5.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnAddCustomer.Location" type="System.Drawing.Point, System.Drawing">
    <value>875, 30</value>
  </data>
  <data name="&gt;&gt;textEdit7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl8.Name" xml:space="preserve">
    <value>labelControl8</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Serial.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>286</value>
  </data>
  <data name="&gt;&gt;labelControl15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="SubTaxId.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="totalTableTaxes.Caption" xml:space="preserve">
    <value>totalTableTaxes</value>
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="txtNet.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chk_IsInTrns.Location" type="System.Drawing.Point, System.Drawing">
    <value>537, 30</value>
  </data>
  <data name="rep_expireDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repTaxTypes.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>77, 537</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="panelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 162</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;labelControl12.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtExpenses.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridView2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn13.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_CusTaxV.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;colTaxType.Name" xml:space="preserve">
    <value>colTaxType</value>
  </data>
  <data name="gridColumn13.Width" type="System.Int32, mscorlib">
    <value>320</value>
  </data>
  <data name="repSpin.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;col_TotalSubAddTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repSpin.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl20.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_CurrentQty.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>Unit Of Measure</value>
  </data>
  <data name="gridColumn4.Width" type="System.Int32, mscorlib">
    <value>165</value>
  </data>
  <data name="labelControl9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl7.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.ZOrder" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="col_Serial.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Remains.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;gridView6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl16.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.ZOrder" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="gridColumn18.Caption" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="&gt;&gt;labelControl23.Name" xml:space="preserve">
    <value>labelControl23</value>
  </data>
  <data name="gridColumn29.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_EtaxValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_Rate.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn28.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn36.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_EtaxValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="textEdit5.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="salePriceWithTaxTable.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_ItemNameF.Caption" xml:space="preserve">
    <value>Item Name F</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="&gt;&gt;Code.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView4.Name" xml:space="preserve">
    <value>gridView4</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="&gt;&gt;labelControl13.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_AddTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn7.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rep_expireDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="tableTaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barBtnCancel.LargeGlyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABF0RVh0VGl0
        bGUAUmVzZXQ7VW5kbzsTgRb/AAABwklEQVRYR8XVsUuVURzGcSOQyAYjCUJwa3DQv0TbnGpqaGioIQIr
        UBF1cLWxoT/DpUla3BwiiKIlGhp0VEF9+z5xrzy/c39X33uV0/AZfofnnOfAfd/3jjRN81+lizWlizWl
        izWlizWlizWFYX51u585HKExy8iyF/I+CUO2AQs4hpevIMteyvskDMmGJziBl68iy7bifRKGIvwMp/Dy
        NZS5gXifhMGCL3AGL9+AZ4bifRKGTmgRXnwZPR/7+IFP2MIj3MZ5cZf3SRgI6PfNSoZxgCWMofUFNpEd
        dhVfMIVWF5B1ZAddxR5uIfRJGBTo0EemPOQtPONGcRfTeIrPKPe/QuiTMChg3qA85KJLuBv4CN/7FaFP
        wqBA4TX8EGl7iUmU35F73idhIJB5ifKb0PYSv+H7ZrxPwkCgn+cY5hK/4HtmvU/CQOA6PUD5P3Lf+yQM
        BK6LHsIP8PLvCH0SBgWGcBN6BScwC72GO/ByeYfQJ2FQYEBlST/fcAehT8KgwICystJPPMS/Pd4nYeiG
        BpAVdh3iPcZxvsf7JAwebKlbpr/kP9iFvoCPoeeiZ4/3Sc9CbeliTeliTeliTeliPc3IX9pfvQlNxdSA
        AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="&gt;&gt;lkpStore.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Name" xml:space="preserve">
    <value>gridColumn3</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_Total.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 439</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Name" xml:space="preserve">
    <value>gridColumn22</value>
  </data>
  <data name="&gt;&gt;totalTaxesRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnNext.Name" xml:space="preserve">
    <value>btnNext</value>
  </data>
  <data name="&gt;&gt;col_CategoryNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtNet.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit6.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="col_Expire.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkp_Drawers2.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_btnAddTaxes.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_DeductTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtNet.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridView3.Name" xml:space="preserve">
    <value>gridView3</value>
  </data>
  <data name="grd_SubTaxes.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView3.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit7.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="col_CurrentQty.Caption" xml:space="preserve">
    <value>Current Qty</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Name" xml:space="preserve">
    <value>txtDiscountValue</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="col_CurrentQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Name" xml:space="preserve">
    <value>gridColumn7</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="dtInvoiceDate.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn12.Width" type="System.Int32, mscorlib">
    <value>310</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnPrevious.Text" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Name" xml:space="preserve">
    <value>txt_DeductTaxR</value>
  </data>
  <data name="gridColumn10.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_PiecesCount.Name" xml:space="preserve">
    <value>col_PiecesCount</value>
  </data>
  <data name="gv_SubTaxes.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Name" xml:space="preserve">
    <value>gridColumn41</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="colDescription.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl18.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_EtaxValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 20</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="gridColumn8.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Serial.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_ItemDescription.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barBtnCancel.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl35.TabIndex" type="System.Int32, mscorlib">
    <value>85</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl19.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Name" xml:space="preserve">
    <value>gridColumn10</value>
  </data>
  <data name="textEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 22</value>
  </data>
  <data name="&gt;&gt;SubTaxId.Name" xml:space="preserve">
    <value>SubTaxId</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;mi_frm_IC_Item.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelControl27.Name" xml:space="preserve">
    <value>labelControl27</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>270</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colbonusDiscount.Summary1" xml:space="preserve">
    <value>bonusDiscount</value>
  </data>
  <data name="&gt;&gt;labelControl26.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_paid.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="groupControl1.AppearanceCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="gridColumn3.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_EtaxValue.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;pnlCrncy.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="gridColumn24.Width" type="System.Int32, mscorlib">
    <value>125</value>
  </data>
  <data name="repExpireDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="panelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1158, 226</value>
  </data>
  <data name="dtInvoiceDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn5.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colTaxType.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="addTaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtNotes.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 7</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="col_CurrentQty.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView5.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Name" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="gridColumn31.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Serial.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_EtaxValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 490</value>
  </data>
  <data name="txtInvoiceCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>161</value>
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>Sell P</value>
  </data>
  <data name="gridColumn2.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 2</value>
  </data>
  <data name="txt_EtaxValue.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn23.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn3.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="labelControl18.TabIndex" type="System.Int32, mscorlib">
    <value>170</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn11.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl17.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="DescriptionAr.Caption" xml:space="preserve">
    <value>DescriptionAr</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repItems.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="btnImport.Caption" xml:space="preserve">
    <value>Import Invoice</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="DescriptionAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_TotalSellPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="col_Batch.Caption" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="textEdit7.TabIndex" type="System.Int32, mscorlib">
    <value>258</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit6.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>42, 30</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridView2.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtNet.Name" xml:space="preserve">
    <value>txtNet</value>
  </data>
  <data name="labelControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;col_TotalSellPrice.Name" xml:space="preserve">
    <value>col_TotalSellPrice</value>
  </data>
  <data name="&gt;&gt;labelControl26.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="Value.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>178, 47</value>
  </data>
  <data name="chk_IsInTrns.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.ZOrder" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscountValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtNet.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="chk_IsInTrns.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.Name" xml:space="preserve">
    <value>txt_CusTaxV</value>
  </data>
  <data name="txtCurrency.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn20.Name" xml:space="preserve">
    <value>gridColumn20</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tableTaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn31.VisibleIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpCostCenter.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lbl_remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>341, 50</value>
  </data>
  <data name="txt_PayAcc2_Paid.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;panelControl1.Name" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="txtCurrency.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl23.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_EtaxValue.Name" xml:space="preserve">
    <value>txt_EtaxValue</value>
  </data>
  <data name="&gt;&gt;textEdit6.Parent" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;gridColumn40.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit5.Name" xml:space="preserve">
    <value>textEdit5</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlDate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="colLocationNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn23.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_DeductTaxR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;colEtaxValue.Name" xml:space="preserve">
    <value>colEtaxValue</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repExpireDate.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl20.TabIndex" type="System.Int32, mscorlib">
    <value>202</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn43.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Total.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;grdPrInvoice.Name" xml:space="preserve">
    <value>grdPrInvoice</value>
  </data>
  <data name="pnlCostCenter.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate_txt.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn27.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="totalTaxesRatio.Caption" xml:space="preserve">
    <value>totalTaxesRatio</value>
  </data>
  <data name="col_LibraQty.Caption" xml:space="preserve">
    <value>Libra Qty</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnAddCustomer.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl13.Name" xml:space="preserve">
    <value>labelControl13</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.FlowLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;col_ItemDescription.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Total.TabIndex" type="System.Int32, mscorlib">
    <value>171</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="labelControl3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="gridColumn11.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>Disc</value>
  </data>
  <data name="gridView3.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textEdit5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl25.Name" xml:space="preserve">
    <value>labelControl25</value>
  </data>
  <data name="txtCurrency.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>353, 1</value>
  </data>
  <data name="textEdit1.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn36.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="mi_InvoiceStaticDimensions.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="&gt;&gt;repUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_Batch.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Serial.Caption" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="txtCurrency.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 22</value>
  </data>
  <data name="textEdit6.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>342, 10</value>
  </data>
  <data name="gridColumn40.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;col_Pack.Name" xml:space="preserve">
    <value>col_Pack</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn20.Width" type="System.Int32, mscorlib">
    <value>172</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Deduct Tax</value>
  </data>
  <data name="txt_EtaxValue.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_DiscountRatio3.Width" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="gridColumn27.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Remains.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="&gt;&gt;labelControl15.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="textEdit1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="gridColumn1.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Total.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn18.Width" type="System.Int32, mscorlib">
    <value>184</value>
  </data>
  <data name="lkpStore.Properties.Columns" xml:space="preserve">
    <value>StoreNameAr</value>
  </data>
  <data name="gridColumn16.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lkpCostCenter.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpenses.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_TotalSellPrice.Caption" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="&gt;&gt;repDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="pnlCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="pnlCrncy.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;colPurchasePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns45" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txt_DeductTaxV.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="flowLayoutPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>316, 7</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView4.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn31.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>1119, 33</value>
  </data>
  <data name="textEdit7.EditValue" xml:space="preserve">
    <value>Invoice Code</value>
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lkpStore.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridColumn12.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Name" xml:space="preserve">
    <value>gridColumn21</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkpStore.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridView1.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl13.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="gridColumn7.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="pnlBranch.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="colPurchasePrice.Caption" xml:space="preserve">
    <value>Purchase P</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="col_Expire.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="salePriceWithTaxTable.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Name" xml:space="preserve">
    <value>lkp_Customers</value>
  </data>
  <data name="gridColumn7.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Drawers.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="barBtnDelete.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACl0RVh0VGl0
        bGUAUmVtb3ZlO0RlbGV0ZTtCYXJzO1JpYmJvbjtTdGFuZGFyZDtjVkgwAAACeElEQVQ4T6WTW0iTYRjH
        LXdw6nKtm0jaliWlkkrJKrtJb5KROYvMdEtrHqrNQ03zgI1ouF2kUnPqTSeJWVC2K01CyoEHyEGWkYEU
        HcjoYGu1Sq/+vc+3aV91Vw/8vu99/8/z/2/f9/KFAfgvgpdftZQhCN2XkBAqWoczhAzqcz1+AAmCh6WH
        zkwfK/s4uDv7CNvTMOmEcLRYZ6Te+OEiK9uLSecHhI8X623vr3RgfnIIU6fr0ZuZaWS6iIY9hfmmmfaz
        rOfBu0tOjOgL7EwX8gNEE0V63/dRNwK9bZgbvoGJk1W4nKau6tuTW/2yzYZ5bx8CN1sRGOjGxEG9j3kk
        /ACBa1u68YHJgK/udvi7rfhxrwf3K8rxzG7B3JgbX65aOX04X4tz8QnH6Uf5AfSc4s6k5MoR3T74upsx
        21WPQP8FfLt9EZ+6GjDbWYdBTQZssSozm40kDz+AikIkdsVa812tBh8cNXhrNTBKuHv/jq1olK+sZzPR
        DDqloPePgMjzqeraMYMeb6zleFWzf5FRXR5aUtIa2YyU8VcAZ25JSDV7dPmYaa3FdIkG06Uh2Pq19SiG
        8vbCtj558V/wA0TNaxLNd3Ky8cJShicFGZhiXFunxPWEeG5NPD9RiAFNFizKDQ3M89spRHcoEv1PTQcw
        mbsdj3LS0ROngFEQfcoojLL0xKkwqU3neFy0C05lkp95ZPyAiKYVKod78yZ4s9RwKVajUiSl540hTOKY
        JpdKCe/OLbiVshF1cmUH06P4AfRSYmqksQ6bTOmrCJqXhXSuZ4xY3sR6n6ulq5xsLyedH0BFg3S+MoYk
        tF+ohR4Z6QXShxX00uXfQdhPmOi/wI4pGN8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="rep_btnAddTaxes.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_paid.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkp_Drawers.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="panelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txt_TaxValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_CusTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl14.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="textEdit6.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;panelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn36.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="textEdit6.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_PiecesCount.Width" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="&gt;&gt;gridView5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>434, 69</value>
  </data>
  <data name="textEdit1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmbPayMethod.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>259</value>
  </data>
  <data name="textEdit7.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns44" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Customers.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="colLocationNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lkp_Drawers2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="col_CurrentQty.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl12.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="pnlCrncy.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 1</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 13</value>
  </data>
  <data name="col_Rate.Width" type="System.Int32, mscorlib">
    <value>180</value>
  </data>
  <data name="gridColumn11.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkpStore.Name" xml:space="preserve">
    <value>lkpStore</value>
  </data>
  <data name="chk_IsInTrns.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_Height.Name" xml:space="preserve">
    <value>col_Height</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="importFromExcelSheetToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="lkpStore.Properties.Columns7" xml:space="preserve">
    <value>StoreCode</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl15.TabIndex" type="System.Int32, mscorlib">
    <value>169</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="TotalTaxes.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="textEdit6.EditValue" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="&gt;&gt;repLocation.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl11.Name" xml:space="preserve">
    <value>labelControl11</value>
  </data>
  <data name="labelControl29.Text" xml:space="preserve">
    <value>Tax V</value>
  </data>
  <data name="rep_btnAddTaxes.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Plus</value>
  </data>
  <data name="pnlInvCode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="&gt;&gt;col_Rate.Name" xml:space="preserve">
    <value>col_Rate</value>
  </data>
  <data name="txt_CusTaxV.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="importFromExcelSheetToolStripMenuItem.Text" xml:space="preserve">
    <value>Import From Excel sheet</value>
  </data>
  <data name="&gt;&gt;gridColumn39.Name" xml:space="preserve">
    <value>gridColumn39</value>
  </data>
  <data name="txt_Total.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pnlDate.Name" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="barBtnHelp.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn10.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Total.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_Total.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtExpenses.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="lkp_SubTaxes.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_Remains.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn9.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="gridColumn31.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpCostCenter.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="pnlCrncy.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="txt_paid.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn1.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn2.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Serial2.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_SubTaxes.Columns10" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn2.Width" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn1.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Location" type="System.Drawing.Point, System.Drawing">
    <value>178, 25</value>
  </data>
  <data name="frm_SL_Return.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxR.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.Name" xml:space="preserve">
    <value>txt_TaxValue</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colEtaxValue.Caption" xml:space="preserve">
    <value>Tax Value</value>
  </data>
  <data name="&gt;&gt;col_ItemNameF.Name" xml:space="preserve">
    <value>col_ItemNameF</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Name" xml:space="preserve">
    <value>txt_Remains</value>
  </data>
  <data name="txt_DeductTaxR.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="txtDiscountValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtNet.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl15.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;col_TotalSubDiscountTax.Name" xml:space="preserve">
    <value>col_TotalSubDiscountTax</value>
  </data>
  <data name="grd_SubTaxes.Size" type="System.Drawing.Size, System.Drawing">
    <value>389, 183</value>
  </data>
  <data name="&gt;&gt;labelControl17.Name" xml:space="preserve">
    <value>labelControl17</value>
  </data>
  <data name="cmbPayMethod.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl18.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="Value.Caption" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl20.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="repDiscountRatio.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_Expire.Name" xml:space="preserve">
    <value>col_Expire</value>
  </data>
  <data name="&gt;&gt;labelControl23.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtNet.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_DeductTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>94, 534</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl11.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="lkpStore.Properties.Columns46" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;repTaxTypes.Name" xml:space="preserve">
    <value>repTaxTypes</value>
  </data>
  <data name="gridColumn8.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Name" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="lkp_SubTaxes.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_btnAddTaxes.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Drawers.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="gridColumn36.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;rep_vendors.Name" xml:space="preserve">
    <value>rep_vendors</value>
  </data>
  <data name="&gt;&gt;txtNotes.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl7.Name" xml:space="preserve">
    <value>labelControl7</value>
  </data>
  <data name="txtInvoiceCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn43.Caption" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Total.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="pnlBranch.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 44</value>
  </data>
  <data name="txtCurrency.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Batch.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Name" xml:space="preserve">
    <value>txtExpenses</value>
  </data>
  <data name="&gt;&gt;labelControl16.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn26.Caption" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Name" xml:space="preserve">
    <value>txt_PayAcc1_Paid</value>
  </data>
  <data name="txtCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 22</value>
  </data>
  <data name="lkp_SubTaxes.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl36.Name" xml:space="preserve">
    <value>labelControl36</value>
  </data>
  <data name="&gt;&gt;col_CurrentQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="panelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1158, 102</value>
  </data>
  <data name="gridColumn27.Caption" xml:space="preserve">
    <value>CostCenter Code</value>
  </data>
  <data name="&gt;&gt;chk_IsInTrns.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="textEdit5.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txtNotes.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txt_DeductTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn23.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_TotalSellPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 31</value>
  </data>
  <data name="gridColumn33.Caption" xml:space="preserve">
    <value>Producer Company</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="&gt;&gt;labelControl36.ZOrder" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="pnlCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>122, 44</value>
  </data>
  <data name="txt_Total.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Serial2.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 20</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="&gt;&gt;col_IsOffer.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItemNameF.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btn_AddTaxes.VisibleIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repManufactureDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridView3.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txt_paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="textEdit7.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rep_expireDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl12.TabIndex" type="System.Int32, mscorlib">
    <value>161</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit5.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="col_Serial.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Width.Caption" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="gridColumn30.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 608</value>
  </data>
  <data name="txtCurrency.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl5.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Total.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;addTaxValue.Name" xml:space="preserve">
    <value>addTaxValue</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Name" xml:space="preserve">
    <value>repositoryItemGridLookUpEdit1View</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Is_Libra.Caption" xml:space="preserve">
    <value>Is_Libra</value>
  </data>
  <data name="&gt;&gt;labelControl14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridView5.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 2</value>
  </data>
  <data name="gridColumn4.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtNet.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repExpireDate_txt.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn41.Width" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Name" xml:space="preserve">
    <value>btnAddCustomer</value>
  </data>
  <data name="col_PiecesCount.Caption" xml:space="preserve">
    <value>Pieces Count</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="gridView3.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn35.Name" xml:space="preserve">
    <value>gridColumn35</value>
  </data>
  <data name="&gt;&gt;col_Serial2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn33.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repManufactureDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtNotes.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Serial2.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 13</value>
  </data>
  <data name="txt_AddTaxV.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpStore.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="dtInvoiceDate.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_AddTaxR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lkpStore.Properties.Columns40" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Drawers2.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="textEdit6.TabIndex" type="System.Int32, mscorlib">
    <value>257</value>
  </data>
  <data name="txt_Remains.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="col_DiscountRatio2.Caption" xml:space="preserve">
    <value>Disc R2</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 559</value>
  </data>
  <data name="gv_SubTaxes.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="gv_SubTaxes.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpStore.Properties.Columns15" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="&gt;&gt;txtNet.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="labelControl8.TabIndex" type="System.Int32, mscorlib">
    <value>169</value>
  </data>
  <data name="mi_PasteRows.Text" xml:space="preserve">
    <value>Paste Rows</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;colLocationNameAr.Name" xml:space="preserve">
    <value>colLocationNameAr</value>
  </data>
  <data name="txtDiscountRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="textEdit5.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="col_TotalSellPrice.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="txt_paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="&gt;&gt;labelControl35.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl27.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lbl_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="&gt;&gt;col_CurrentQty.Name" xml:space="preserve">
    <value>col_CurrentQty</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDimensions.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_Serial2.Name" xml:space="preserve">
    <value>col_Serial2</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Expire.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;textEdit6.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl36.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtNotes.Name" xml:space="preserve">
    <value>txtNotes</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnNext.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscountValue.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Parent" xml:space="preserve">
    <value>pnlCrncy</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="colLocationNameAr.Caption" xml:space="preserve">
    <value>Location Name</value>
  </data>
  <data name="txt_TaxValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Serial2.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit5.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNet.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpenses.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="btnNext.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="gridColumn29.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Length.Caption" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>162</value>
  </data>
  <data name="&gt;&gt;importFromExcelSheetToolStripMenuItem.Name" xml:space="preserve">
    <value>importFromExcelSheetToolStripMenuItem</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="col_Serial.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpCostCenter.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gv_SubTaxes.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Name" xml:space="preserve">
    <value>gridColumn8</value>
  </data>
  <data name="&gt;&gt;labelControl36.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalSubCustomTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Total.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Name" xml:space="preserve">
    <value>btnPrevious</value>
  </data>
  <data name="pnlDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>623, 1</value>
  </data>
  <data name="txt_AddTaxR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="gridColumn43.Width" type="System.Int32, mscorlib">
    <value>86</value>
  </data>
  <data name="&gt;&gt;col_LibraQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="batBtnList.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txtCurrency.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountValue.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Height.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn35.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_TaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 512</value>
  </data>
  <data name="&gt;&gt;repSpin.Name" xml:space="preserve">
    <value>repSpin</value>
  </data>
  <data name="&gt;&gt;labelControl12.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 537</value>
  </data>
  <data name="lkp_Drawers.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountValue.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_ETaxRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_btnAddTaxes.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl13.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtNet.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="lkpCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsInTrns.TabIndex" type="System.Int32, mscorlib">
    <value>271</value>
  </data>
  <data name="txtNotes.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns1" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="&gt;&gt;gridColumn43.Name" xml:space="preserve">
    <value>gridColumn43</value>
  </data>
  <data name="&gt;&gt;col_kg_Weight_libra.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CusTax.Name" xml:space="preserve">
    <value>col_CusTax</value>
  </data>
  <data name="uc_Currency1.TabIndex" type="System.Int32, mscorlib">
    <value>278</value>
  </data>
  <data name="gridColumn10.VisibleIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="gridColumn23.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtExpenses.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 20</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Name" xml:space="preserve">
    <value>gridColumn17</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="&gt;&gt;labelControl18.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repUOM.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Rate.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelControl18.Name" xml:space="preserve">
    <value>labelControl18</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDisc.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_LibraQty.Name" xml:space="preserve">
    <value>col_LibraQty</value>
  </data>
  <data name="repDiscountRatio.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn17.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_DeductTaxR.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn36.Caption" xml:space="preserve">
    <value>TotalMainPrice</value>
  </data>
  <data name="&gt;&gt;col_Batch.Name" xml:space="preserve">
    <value>col_Batch</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_kg_Weight_libra.Name" xml:space="preserve">
    <value>col_kg_Weight_libra</value>
  </data>
  <data name="&gt;&gt;barbtnLoadSellInvoice.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit6.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnPrevious.ZOrder" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textEdit1.Parent" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Name" xml:space="preserve">
    <value>gridColumn33</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="labelControl5.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit5.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Remains.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;repExpireDate_txt.Name" xml:space="preserve">
    <value>repExpireDate_txt</value>
  </data>
  <data name="repSpin.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="mi_frm_IC_Item.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Name" xml:space="preserve">
    <value>gridColumn4</value>
  </data>
  <data name="txt_TaxValue.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit7.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="col_ItemDescriptionEn.Caption" xml:space="preserve">
    <value>Item Description En</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Customers.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="txt_paid.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl20.Name" xml:space="preserve">
    <value>labelControl20</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Batch.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.ZOrder" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="gv_SubTaxes.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn7.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_TotalSubDiscountTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn17.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;tableTaxValue.Name" xml:space="preserve">
    <value>tableTaxValue</value>
  </data>
  <data name="txt_AddTaxR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="frm_SL_Return.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="panelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkpCostCenter.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_CusTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 23</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>172</value>
  </data>
  <data name="lbl_remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="&gt;&gt;col_ItemDescriptionEn.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_CusTaxV.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_CurrentQty.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_ETaxRatio.Name" xml:space="preserve">
    <value>col_ETaxRatio</value>
  </data>
  <data name="lkp_Drawers2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn35.Caption" xml:space="preserve">
    <value>MainPrice</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grdPrInvoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>1154, 222</value>
  </data>
  <data name="&gt;&gt;col_SalesTax.Name" xml:space="preserve">
    <value>col_SalesTax</value>
  </data>
  <data name="txtNet.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.EditValue" xml:space="preserve">
    <value>Cost Center</value>
  </data>
  <data name="txtInvoiceCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="&gt;&gt;labelControl28.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txtCurrency.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn5.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl28.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txtDiscountRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Total.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 20</value>
  </data>
  <data name="&gt;&gt;textEdit7.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rep_expireDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn24.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="&gt;&gt;txtCurrency.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="E_TaxableTypeId.Caption" xml:space="preserve">
    <value>E_TaxableTypeId</value>
  </data>
  <data name="&gt;&gt;labelControl29.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;gridColumn27.Name" xml:space="preserve">
    <value>gridColumn27</value>
  </data>
  <data name="&gt;&gt;pnlCrncy.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="col_Expire.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl5.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView2.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn11.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="SubTaxId.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;gridColumn38.Name" xml:space="preserve">
    <value>gridColumn38</value>
  </data>
  <data name="repManufactureDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridColumn31.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 559</value>
  </data>
  <data name="col_Expire.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 114</value>
  </data>
  <data name="textEdit7.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Remains.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns8" xml:space="preserve">
    <value>كود الحساب</value>
  </data>
  <data name="gridView5.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn6.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn41.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="col_Expire.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="pnlDate.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txt_EtaxValue.TabIndex" type="System.Int32, mscorlib">
    <value>332</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1182, 31</value>
  </data>
  <data name="gridColumn5.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;gridColumn36.Name" xml:space="preserve">
    <value>gridColumn36</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="dtInvoiceDate.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.TabIndex" type="System.Int32, mscorlib">
    <value>168</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>Pay Account 1</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="col_Width.Width" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Name" xml:space="preserve">
    <value>gridColumn11</value>
  </data>
  <data name="&gt;&gt;col_TaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;addTaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns1" xml:space="preserve">
    <value>Branch Name</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 20</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>61, 537</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmbPayMethod.Properties.Items6" xml:space="preserve">
    <value>Cash / On Credit</value>
  </data>
  <data name="repManufactureDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;TotalTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl15.Name" xml:space="preserve">
    <value>labelControl15</value>
  </data>
  <data name="txt_DeductTaxV.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repSpin.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 515</value>
  </data>
  <data name="btnPrevious.ToolTip" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TaxValue.Name" xml:space="preserve">
    <value>col_TaxValue</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns" xml:space="preserve">
    <value>AccountName</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Drawers.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn23.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repExpireDate_txt.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_btnAddTaxes.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn11.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit6.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;panelControl2.Name" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="cmbPayMethod.Properties.Items" xml:space="preserve">
    <value>On Credit</value>
  </data>
  <data name="&gt;&gt;btnNext.ZOrder" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>Other charge</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Name" xml:space="preserve">
    <value>lkp_Drawers2</value>
  </data>
  <data name="lkp_Drawers2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView3.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="chk_IsInTrns.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>Pay Account 2</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Batch.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl17.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtnPrint.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;textEdit5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="DescriptionAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>663, 552</value>
  </data>
  <data name="btnAddCustomer.ToolTip" xml:space="preserve">
    <value>Add Customer</value>
  </data>
  <data name="repUOM.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;colLocationId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="repSpin.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;repTaxTypes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupControl1.Name" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txt_DeductTaxV.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_CusTaxV.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rep_btnAddTaxes.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl23.TabIndex" type="System.Int32, mscorlib">
    <value>288</value>
  </data>
  <data name="salePriceWithTaxTable.Caption" xml:space="preserve">
    <value>Sale Price With Tax Table</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn40.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.ZOrder" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="&gt;&gt;textEdit1.Name" xml:space="preserve">
    <value>textEdit1</value>
  </data>
  <data name="textEdit5.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtExpenses.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="SubTaxId.Caption" xml:space="preserve">
    <value>Sub Taxes</value>
  </data>
  <data name="gridColumn10.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="rep_btnAddTaxes.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupControl1.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.Width" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>203</value>
  </data>
  <data name="lkpStore.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkpCostCenter.Parent" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="flowLayoutPanel1.FlowDirection" type="System.Windows.Forms.FlowDirection, System.Windows.Forms">
    <value>RightToLeft</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn23.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="gridColumn11.Width" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="textEdit1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;Value.Name" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="lkpCostCenter.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="mi_InvoiceStaticDisc.Text" xml:space="preserve">
    <value>Static Invoice Discounts</value>
  </data>
  <data name="&gt;&gt;labelControl29.Name" xml:space="preserve">
    <value>labelControl29</value>
  </data>
  <data name="gridColumn14.Width" type="System.Int32, mscorlib">
    <value>187</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 582</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 515</value>
  </data>
  <data name="lkpStore.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn36.Width" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="txt_PayAcc2_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 621</value>
  </data>
  <data name="pnlCrncy.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 44</value>
  </data>
  <data name="txtInvoiceCode.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit6.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;totalTableTaxes.Name" xml:space="preserve">
    <value>totalTableTaxes</value>
  </data>
  <data name="lkpStore.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Height.Caption" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Bold, Underline</value>
  </data>
  <data name="repExpireDate_txt.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Remains.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Name" xml:space="preserve">
    <value>lbl_remains</value>
  </data>
  <data name="&gt;&gt;labelControl19.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repManufactureDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repExpireDate.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn7.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;repDiscountRatio.Name" xml:space="preserve">
    <value>repDiscountRatio</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_Rate.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gv_CostCenter.Name" xml:space="preserve">
    <value>gv_CostCenter</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl26.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="mi_PasteRows.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="lkpStore.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="txt_CusTaxV.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;colbonusDiscount.Name" xml:space="preserve">
    <value>colbonusDiscount</value>
  </data>
  <data name="txtExpenses.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repSpin.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_ItemDescription.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="pnlBranch.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repExpireDate_txt.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDimensions.Name" xml:space="preserve">
    <value>mi_InvoiceStaticDimensions</value>
  </data>
  <data name="cmbPayMethod.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="col_IsOffer.Caption" xml:space="preserve">
    <value>IsOffer</value>
  </data>
  <data name="&gt;&gt;labelControl27.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;repUOM.Name" xml:space="preserve">
    <value>repUOM</value>
  </data>
  <data name="txt_DeductTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate_txt.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Drawers.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Total.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn41.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtDiscountRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>Branch Code</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridColumn15.Caption" xml:space="preserve">
    <value>itemId</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn11.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lbl_remains.Text" xml:space="preserve">
    <value>Remains</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn3.Caption" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridView2.Name" xml:space="preserve">
    <value>gridView2</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="dtInvoiceDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 20</value>
  </data>
  <data name="&gt;&gt;repLocation.Name" xml:space="preserve">
    <value>repLocation</value>
  </data>
  <data name="textEdit5.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lbl_Paid.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;textEdit5.Parent" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="lkpStore.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="txt_Total.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 628</value>
  </data>
  <data name="&gt;&gt;barBtnCancel.Name" xml:space="preserve">
    <value>barBtnCancel</value>
  </data>
  <data name="repManufactureDate.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>122, 44</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="gridColumn13.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colEtaxValue.Summary1" xml:space="preserve">
    <value>EtaxValue</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="repManufactureDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colEtaxValue.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="txt_CusTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 20</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.Name" xml:space="preserve">
    <value>grd_SubTaxes</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;textEdit6.Name" xml:space="preserve">
    <value>textEdit6</value>
  </data>
  <data name="&gt;&gt;col_TotalSubCustomTax.Name" xml:space="preserve">
    <value>col_TotalSubCustomTax</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>Add Tax</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="col_Height.Width" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="&gt;&gt;gridView6.Name" xml:space="preserve">
    <value>gridView6</value>
  </data>
  <data name="repExpireDate_txt.Mask.EditMask" xml:space="preserve">
    <value>\d?\d?/20\d\d</value>
  </data>
  <data name="repDiscountRatio.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Serial.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView2.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="colTaxType.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="gridColumn40.Width" type="System.Int32, mscorlib">
    <value>80</value>
  </data>
  <data name="&gt;&gt;chk_IsInTrns.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Name" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="gridColumn33.Width" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="&gt;&gt;gridColumn29.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Remains.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="txt_DeductTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="SubTaxId.Width" type="System.Int32, mscorlib">
    <value>275</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtNotes.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="gridColumn35.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;repManufactureDate.Name" xml:space="preserve">
    <value>repManufactureDate</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 534</value>
  </data>
  <data name="txtNotes.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;pnlCrncy.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btnPrevious.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 30</value>
  </data>
  <data name="&gt;&gt;E_TaxableTypeId.Name" xml:space="preserve">
    <value>E_TaxableTypeId</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelControl29.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="col_Batch.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl35.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="col_ETaxRatio.Caption" xml:space="preserve">
    <value>Tax Ratio</value>
  </data>
  <data name="dtInvoiceDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>160</value>
  </data>
  <data name="repDiscountRatio.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn39.Caption" xml:space="preserve">
    <value>VendorId</value>
  </data>
  <data name="txt_EtaxValue.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SubTaxes.AppearanceDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="panelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 55</value>
  </data>
  <data name="gridColumn10.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpCostCenter.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="gridColumn4.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="txt_Total.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 493</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="dtInvoiceDate.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;panelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupControl1.AppearanceCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Serial2.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn42.Width" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn41.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="colEtaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lkpStore.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="col_Serial.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_DeductTaxR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn26.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="&gt;&gt;textEdit6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btn_AddTaxes.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl8.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="&gt;&gt;labelControl16.Name" xml:space="preserve">
    <value>labelControl16</value>
  </data>
  <data name="gridView1.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lkpStore.Parent" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtNotes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repSpin.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;batBtnList.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;colDescription.Name" xml:space="preserve">
    <value>colDescription</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="gridColumn42.Caption" xml:space="preserve">
    <value>F Name</value>
  </data>
  <data name="flowLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>837, 94</value>
  </data>
  <data name="lkpCostCenter.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="&gt;&gt;labelControl18.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn29.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_ETaxRatio.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="dtInvoiceDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Sales Return Invoice</value>
  </data>
  <data name="gridColumn23.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>160</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.EditMask" xml:space="preserve">
    <value>g</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn35.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SubTaxes.Columns9" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl19.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;colLocationId.Name" xml:space="preserve">
    <value>colLocationId</value>
  </data>
  <data name="gridColumn16.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gv_SubTaxes.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Customers.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCurrency.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView4.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;gridColumn29.Name" xml:space="preserve">
    <value>gridColumn29</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="rep_vendors.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="barBtnDelete.LargeGlyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACl0RVh0VGl0
        bGUAUmVtb3ZlO0RlbGV0ZTtCYXJzO1JpYmJvbjtTdGFuZGFyZDtjVkgwAAAH1klEQVRYR8WWe1RVVR7H
        rzWTJSimiDQoXBB8XEF5KAqooMhDAVEBHQcZy1R8BKmAgoCggshDFDELRkB80SJsQT7CUBDE14gilK6k
        iIsP0CBFc6TVxPnO73cu5ybc66r+mX5rfdibffb+fb97n733uTIAfypaG/+fqP5ojz7EK91w/Y8E9+89
        XiPHywxIA/9K9O2G67/XCPd5lXiNeKMbzsFtPcZrMyAN7lu5cGGoMnlHpzI5qbMiIOA9anu9+1mfG4sX
        y5jaxYEqAlXwM+IvxBtlfvNDGhMTOxsTEjpL585dR239up+pTfQ2IIm/fm6Bf+SdvWn47706EeWeFJzx
        8wujZzwb0UTXg69kIq3Ml9SkFu93ytd3fdOuJPysvIafm2rwHdVPzfGNpmc63X1EE9oMvFbu57e+mcR/
        ulmNJ6WF6Dh+BD/VVaApIwWlvj1n8ktLveyX+/VUVYvrfObtE9aYnoTnV79A+6EP0JadimdVxWjcuR0n
        vLwjqA+vJL9ODQOvTJLLdRvi4zufXzuDjpLDeFyUK/KoKAfPa8rAiY97+fBKvDgTtXjxbK+wb1MT8J/L
        pWjLy8CDfUl4uDcRrRnb0PH5UdyKiu4ca2ioR315FTUMvOplMXLgxWUrnrQX5eOHgqxfOfoR2o98SIlP
        gQWKZ81mE7oEbzRG95jHrPCG5G14duEk2vanozWThbeidVc8WtJicTc5GtVLlz9zNjYZzFqE5goQffdZ
        2bxdGRTY1XYgE2353RzYg+/zmAz8eL4EDTu24hM3z3DqP5ApdPOIuJ20BT9WleBhVipadm3B/fQ43Cfh
        e8mbcGd7JM76enftVox/l/rzPtL6Cngp2Vm/3WMsV1YsWtj1IDsND/+1Ew+zmTQ8yCI+SsHTs0X4OiEO
        h6a7Rh1ycY26lbAZTys+ResHSbi/czPupsTg7g4STtyI5m3hOO3l2ZVsPnoN5e5P8JFmLQ0DHNL71E0Z
        MWp12fx5XS17t4uJuWyhZW3ZQ2Qk4DG905vx0SIdZYViGy8zz/ZOwgY0bw2HMm4dTrrNFBLk5nyMBxBq
        cQ4NAzkWCi64A3fsv93EfPXnPt5dd9PjcY/ZGYd7aTTDVNUsfyg+gEcnDlNbHM10A5Q0W2X8ejSRcGN0
        KEpcnIW4v5mEUC7eeLxXelxmGgay5SNlTyuOcVUyMSB+mHzNCU+PruakKNwhmmmGzYmRUNIsldsioNwS
        hiYWjV2L72JCSTgE325chSInRyFqiFEo5eB9ohbfa2RKhSo0DOwbZibr+KJA1nG6gP9Vm4geahRyzGGS
        cHtNEL6JCEbjhlVojFqDRkkwYiUa1r2L26FLcGtZAD62Hi9EDBr6Po3tId5ekifbPXQYVVWhYWDPW8ay
        RycOEvmyR8fzuUk8GYR+mL5hWKGtjVC/0BNfMgs8UB+goi7AHfX+7qjzd8MRxRjhPb3BfOEYENKl06et
        MEvG7BxsSP+qQsPASRc3Wfun+2Xtx/bzv7wCPJiTDMmwsd98cclifBU0Fzfmu3UzE7XzXFE7nxDLmaj6
        ewDSrOziacxQQjpyfR4czpQxqQPZlyo0DHw2xVX2fcE+rkrivHxvZlrbb7q0fCmUyVGoW+SF63Nn4Lov
        Mx3X5kzHdR8XKp1R4+OMbzatxvklQUi3nBBDY/nS4RUUTbTkpMk+mTCFqqrQMFDs4MKFJM4DB+0eNyG6
        +p1/oikhHLULPFHjTULe04ipqPGaiqte03B1NpUSs6bg6/DlqAxchFSFTSzlYBPqL+nHto5UqELDAEUP
        8XRL25iqoEA0xq1FrZ8b/k3JRTydcIXxYBy7ccAVdwdcdp+My26TcTNkCcoXBCBp1LjNlEufUO8HQoze
        BnqIpylsYug2RAMt6TVack5+RUxOIiRwaeZk5I0wFZiLMybhkqu9ihkTqZyIi1TWBy9C2bx5SDC3jKOc
        Q4geJrQZ4Hc+KGW0dcxZfz9ayhW01C6/Jic48QWXicgzkwsrdPRimQMWI4Rq5wnUbkfY4oKzLaqn2RG2
        uPGOH0q9vbHVbKxkgieoYYAb+B31T7SwCjvtMwe33n8bV2ZNpYSqxNXOlJw4T4lzTU2ENboDN1J/I2a1
        7sDIPPMRQtUUa5xXM16kymkcrv3DB8dd3RErH72B+vOtKH7KexvgS0f/qJ1zR11wIC7TO66eaoPzIpSQ
        qHKyRo7cWBLn88Q/ThgDNpFrZiqcc7BCJXFuMmOJc5NUXPbzQL6V4xPqy8dT/CZoNXDQ2qnt4mxnElO5
        r3JkKBklzTEZLrAQ9WNx6ecZw3WDVbp6UbmmcqHcXoGKiYT9GKjqY3BmoiVyFfbt3I/QaoATDYg0Hhlc
        aGMvVDqw87GoIMqJ/cbDeotLm4nhOrcNXamjtylXbiKcsRuFs0S5LZXE4VFWwjpD09XUh69n8Ui+aICD
        E/Em1I8wMl9bYGUjlNkpcGaCAllGRlrFa5cFyGqX+lO1hwlDNpE9fLhwevxIlI6zwEGLsUKogZx/RfHy
        SxeTSruXAX4gXr1hb5mt/FBu1U48CtbR4+85i6uPUbKevuxFuK37mbgSy3X0QjOHKx5nDlO0hxiY8I+R
        HlczoWGAQ0rCLnm38iCGl01yLg5+Sbw4/k2CvzwM19W3ISGGNgMcUhI+KvxKGK7/lrgUv3v8ywxIwZ1f
        5I/Gb45XG/jzgOx/2ZzKgxEwf90AAAAASUVORK5CYII=
</value>
  </data>
  <data name="txt_Total.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Remains.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_Remains.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="batBtnList.Caption" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;col_IsOffer.Name" xml:space="preserve">
    <value>col_IsOffer</value>
  </data>
  <data name="txtNet.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl27.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Customers.Location" type="System.Drawing.Point, System.Drawing">
    <value>906, 30</value>
  </data>
  <data name="textEdit5.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_CurrentQty.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn10.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="txt_CusTaxV.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="gridColumn23.Caption" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="&gt;&gt;rep_expireDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemDateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Drawers2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;labelControl11.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Name" xml:space="preserve">
    <value>gridColumn9</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>247, 245, 241</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>Pay Method</value>
  </data>
  <data name="repExpireDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="col_Pack.Caption" xml:space="preserve">
    <value>Pack</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Name" xml:space="preserve">
    <value>barBtnHelp</value>
  </data>
  <data name="barBtnSave.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txt_AddTaxR.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn11.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Parent" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn7.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnNext.Text" xml:space="preserve">
    <value>=&gt;</value>
  </data>
  <data name="&gt;&gt;rep_btnAddTaxes.Name" xml:space="preserve">
    <value>rep_btnAddTaxes</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn21.Width" type="System.Int32, mscorlib">
    <value>383</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1182, 652</value>
  </data>
  <data name="lkp_SubTaxes.Columns8" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_EtaxValue.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_EtaxValue.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Total.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl5.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Underline</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl29.Size" type="System.Drawing.Size, System.Drawing">
    <value>27, 13</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>S. Tax</value>
  </data>
  <data name="txtCurrency.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_TaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Parent" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalSubAddTax.Name" xml:space="preserve">
    <value>col_TotalSubAddTax</value>
  </data>
  <data name="Value.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;TotalTaxes.Name" xml:space="preserve">
    <value>TotalTaxes</value>
  </data>
  <data name="col_Batch.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 25</value>
  </data>
  <data name="cmbPayMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>696, 30</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn17.Caption" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Name" xml:space="preserve">
    <value>gridColumn5</value>
  </data>
  <data name="gridColumn23.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn3.Width" type="System.Int32, mscorlib">
    <value>184</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn5.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountValue.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="labelControl36.TabIndex" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="flowLayoutPanel1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl29.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="&gt;&gt;col_Serial.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn24.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repExpireDate_txt.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_Total.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtExpenses.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>113, 28</value>
  </data>
  <data name="txtCurrency.EditValue" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Name" xml:space="preserve">
    <value>gridColumn1</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="TotalTaxes.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Name" xml:space="preserve">
    <value>gridColumn15</value>
  </data>
  <data name="lkp_Drawers2.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lbl_remains.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="gridColumn2.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="&gt;&gt;repItems.Name" xml:space="preserve">
    <value>repItems</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_VariableWeight.Caption" xml:space="preserve">
    <value>VariableWeight</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn30.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Customers.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn20.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;colDescription.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_AddTaxV.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colLocationId.Caption" xml:space="preserve">
    <value>gridColumn29</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="&gt;&gt;mi_frm_IC_Item.Name" xml:space="preserve">
    <value>mi_frm_IC_Item</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl10.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 13</value>
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="&gt;&gt;col_Length.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn31.Name" xml:space="preserve">
    <value>gridColumn31</value>
  </data>
  <data name="txt_AddTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="col_DiscountRatio3.Caption" xml:space="preserve">
    <value>Disc R3</value>
  </data>
  <data name="gridColumn29.Width" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="txtNet.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;gridColumn35.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupControl1.AppearanceCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;gridColumn42.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl26.Name" xml:space="preserve">
    <value>labelControl26</value>
  </data>
  <data name="txt_CusTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 579</value>
  </data>
  <data name="addTaxValue.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="pnlBranch.Location" type="System.Drawing.Point, System.Drawing">
    <value>477, 1</value>
  </data>
  <data name="barBtnPrint.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="labelControl11.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn10.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lbl_remains.TabIndex" type="System.Int32, mscorlib">
    <value>166</value>
  </data>
  <data name="col_Serial2.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Name" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 493</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_paid.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlInvCode.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="txtInvoiceCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_Total.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="cmbPayMethod.Properties.Items1" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Drawers.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gv_CostCenter.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridView4.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="panelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl15.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;colPurchasePrice.Name" xml:space="preserve">
    <value>colPurchasePrice</value>
  </data>
  <data name="cmbPayMethod.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="gridColumn5.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn31.Width" type="System.Int32, mscorlib">
    <value>126</value>
  </data>
  <data name="rep_expireDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;colbonusDiscount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtCurrency.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_CusTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_kg_Weight_libra.Caption" xml:space="preserve">
    <value>Weight (kg)</value>
  </data>
  <data name="&gt;&gt;colTaxType.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit6.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl25.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Code.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="txt_Total.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 442</value>
  </data>
  <data name="colPurchasePrice.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="&gt;&gt;btnImport.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_AddTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>94, 556</value>
  </data>
  <data name="chk_IsInTrns.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 19</value>
  </data>
  <data name="gridColumn21.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Name" xml:space="preserve">
    <value>gridColumn25</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="addTaxValue.Caption" xml:space="preserve">
    <value>Add Tax Value</value>
  </data>
  <data name="colbonusDiscount.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barbtnLoadSellInvoice.Caption" xml:space="preserve">
    <value>Load Sell Invoice</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="txtCurrency.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>518, 17</value>
  </metadata>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>33</value>
  </metadata>
</root>