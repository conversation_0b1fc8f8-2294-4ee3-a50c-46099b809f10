﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_E_Invoice : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int store_id1, store_id2, CustomerId1, CustomerId2, custGroupId, salesEmpId;

        string custGroupAccNumber;

        byte FltrTyp_Store, fltrTyp_Date, FltrTyp_Customer, FltrTyp_InvBook;
        DateTime date1, date2;

        List<int> lst_invBooksId = new List<int>();

        private void btn_Landscape_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void btn_Portrait_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void grdCategory_Click(object sender, EventArgs e)
        {

        }

        private void gridView1_CellMerge(object sender, CellMergeEventArgs e)
        {
            GridView view = sender as GridView;
            if (view == null) return;
            if (e.Column.FieldName == "Customer")
            {
                string text1 = view.GetRowCellDisplayText(e.RowHandle1, "Customer");
                string text2 = view.GetRowCellDisplayText(e.RowHandle2, "Customer");
                e.Merge = (text1 == text2);
                e.Handled = true;
            }
        }

        public frm_SL_E_Invoice(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Store, int store_id1, int store_id2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2, int custGroupId, string custGroupAccNumber,
                int salesEmpId, byte FltrTyp_InvBook, string InvBooks)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Store = fltrTyp_Store;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;

            this.store_id1 = store_id1;
            this.store_id2 = store_id2;

            this.date1 = date1;
            this.date2 = date2;

            this.CustomerId1 = customerId1;
            this.CustomerId2 = customerId2;
            this.salesEmpId = salesEmpId;
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();

            LoadData();
            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));

            rep_Currency.DataSource = Shared.lstCurrency;
            rep_Currency.ValueMember = "CrncId";
            rep_Currency.DisplayMember = "crncName";
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //grdCategory.MinimumSize = grdCategory.Size;
            //new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
            //        lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            //grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var data =
                (from c in DB.SL_Customers
                 where FltrTyp_Customer == 1 ? c.CustomerId == CustomerId1 : true
                 where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 != 0) ?
                 c.CustomerId >= CustomerId1 && c.CustomerId <= CustomerId2 : true
                 where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 == 0) ?
                 c.CustomerId >= CustomerId1 : true
                 where (FltrTyp_Customer == 2 && CustomerId1 == 0 && CustomerId2 != 0) ?
                 c.CustomerId <= CustomerId2 : true
                 join d in DB.SL_Invoices on c.CustomerId equals d.CustomerId
                 join dt in DB.SL_InvoiceDetails on d.SL_InvoiceId equals dt.SL_InvoiceId
                 join i in DB.IC_Items on dt.ItemId equals i.ItemId
                 join u in DB.IC_UOMs on dt.UOMId equals u.UOMId
                 where FltrTyp_Store == 1 ? (d.StoreId == store_id1 || dt.StoreId == store_id1) : true
                 where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
                 (d.StoreId >= store_id1 || dt.StoreId >= store_id1) && (d.StoreId <= store_id2 || dt.StoreId <= store_id2) : true
                 where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
                 (d.StoreId >= store_id1 || dt.StoreId >= store_id1) : true
                 where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
                 (d.StoreId <= store_id2 || dt.StoreId <= store_id2) : true
                 where fltrTyp_Date == 1 ? d.InvoiceDate.Date == date1.Date : true
                 where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                 d.InvoiceDate.Date >= date1.Date && d.InvoiceDate.Date <= date2.Date : true
                 where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                 d.InvoiceDate.Date >= date1.Date : true
                 where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                 d.InvoiceDate.Date <= date2.Date : true

                 where d.Estatus == "Valid"

                 let TaxIdDiscount = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault()
                 let TotalTaxesAddedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                            join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                            join dd in DB.SL_InvoiceDetails on r.InvoiceDetailId equals dd.SL_InvoiceDetailId
                                            where dd.SL_InvoiceDetailId == dt.SL_InvoiceDetailId
                                            where rd.ParentTaxId != TaxIdDiscount.E_TaxableTypeId
                                            select r).ToList()
                 let TotalTaxesRemovedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                              join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                              join dd in DB.SL_InvoiceDetails on r.InvoiceDetailId equals dd.SL_InvoiceDetailId
                                              where dd.SL_InvoiceDetailId == dt.SL_InvoiceDetailId
                                              where rd.ParentTaxId == TaxIdDiscount.E_TaxableTypeId
                                              select r).ToList()
                 let TotaltaxesAddValue = TotalTaxesAddedList.Count != 0 ? Convert.ToDouble(TotalTaxesAddedList.Sum(z => z.value)) : 0
             
                 let TotaltaxesRemovedValue = TotalTaxesRemovedList.Count != 0 ? Convert.ToDouble(TotalTaxesRemovedList.Sum(z => z.value)) : 0
                // let DiscountRatiott =Convert.ToDouble(dt.DiscountRatio*100) 
                 select new
                 {
                     d.InvoiceDate ,
                     d.InvoiceCode,
                     d.syncDate,
                     Customer=c.CusNameAr,
                     i.ItemNameAr,
                     u.UOM,
                     Qty= Convert.ToDouble(dt.Qty),
                     SellPrice= Convert.ToDouble(dt.SellPrice),
                     priceForQty=Convert.ToDouble(dt.SellPrice*dt.Qty),
                    // DiscountRatiott,
                     DiscountRatio=Convert.ToDouble(dt.DiscountRatio),
                     DiscountValue= Convert.ToDouble(dt.DiscountValue),
                     valueAfterDiscount= Convert.ToDouble(dt.SellPrice * dt.Qty- dt.DiscountValue),
                     Totaltaxes = TotaltaxesAddValue - TotaltaxesRemovedValue,
                     Total= dt.TotalSellPrice,
                     d.SL_InvoiceId,
                 }).Distinct();


        
            grdCategory.DataSource = data;
          //  gridView1.Columns["Customer"].OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;

        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;
        }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SL_E_Invoice).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }



    }
}