﻿namespace Reports
{
    partial class rpt_ACC_Journal
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_costcenter = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_notes = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_credit = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_debit = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_accId = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_accName = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_process = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_code = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.lbl_TotalCredit = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotal_Words = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TotalDebit = new DevExpress.XtraReports.UI.XRLabel();
            this.xrCrossBandLine2 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine3 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine4 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine5 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine6 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandBox1 = new DevExpress.XtraReports.UI.XRCrossBandBox();
            this.lbl_Monthly_Code = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            this.Detail.HeightF = 29.16667F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable2.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 0F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.SizeF = new System.Drawing.SizeF(763.5F, 29.16667F);
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            this.xrTable2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_costcenter,
            this.cell_notes,
            this.cell_credit,
            this.cell_debit,
            this.cell_accId,
            this.cell_accName});
            this.xrTableRow2.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            this.xrTableRow2.Weight = 0.54901959587545957D;
            // 
            // cell_costcenter
            // 
            this.cell_costcenter.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            this.cell_costcenter.Name = "cell_costcenter";
            this.cell_costcenter.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_costcenter.StylePriority.UseBorders = false;
            this.cell_costcenter.StylePriority.UsePadding = false;
            this.cell_costcenter.StylePriority.UseTextAlignment = false;
            this.cell_costcenter.Text = "مركز تكلفة";
            this.cell_costcenter.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_costcenter.Weight = 0.30116941635644334D;
            // 
            // cell_notes
            // 
            this.cell_notes.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            this.cell_notes.Name = "cell_notes";
            this.cell_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_notes.StylePriority.UseBorders = false;
            this.cell_notes.StylePriority.UsePadding = false;
            this.cell_notes.StylePriority.UseTextAlignment = false;
            this.cell_notes.Text = "البيان";
            this.cell_notes.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_notes.Weight = 0.67092035016940765D;
            // 
            // cell_credit
            // 
            this.cell_credit.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            this.cell_credit.Name = "cell_credit";
            this.cell_credit.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_credit.StylePriority.UseBorders = false;
            this.cell_credit.StylePriority.UsePadding = false;
            this.cell_credit.StylePriority.UseTextAlignment = false;
            this.cell_credit.Text = "دائن";
            this.cell_credit.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_credit.Weight = 0.22960394211397822D;
            // 
            // cell_debit
            // 
            this.cell_debit.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            this.cell_debit.Name = "cell_debit";
            this.cell_debit.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_debit.StylePriority.UseBorders = false;
            this.cell_debit.StylePriority.UsePadding = false;
            this.cell_debit.StylePriority.UseTextAlignment = false;
            this.cell_debit.Text = "مدين";
            this.cell_debit.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_debit.Weight = 0.22471399889647489D;
            // 
            // cell_accId
            // 
            this.cell_accId.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            this.cell_accId.Name = "cell_accId";
            this.cell_accId.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_accId.StylePriority.UseBorders = false;
            this.cell_accId.StylePriority.UsePadding = false;
            this.cell_accId.StylePriority.UseTextAlignment = false;
            this.cell_accId.Text = "رقم الحساب";
            this.cell_accId.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_accId.Weight = 0.25238564360232746D;
            // 
            // cell_accName
            // 
            this.cell_accName.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            this.cell_accName.Name = "cell_accName";
            this.cell_accName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_accName.StylePriority.UseBorders = false;
            this.cell_accName.StylePriority.UsePadding = false;
            this.cell_accName.StylePriority.UseTextAlignment = false;
            this.cell_accName.Text = "اسم الحساب";
            this.cell_accName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_accName.Weight = 0.50679825014957169D;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLine1,
            this.lbl_User,
            this.xrLabel6,
            this.lbl_notes,
            this.xrLabel4,
            this.lbl_date,
            this.lbl_process,
            this.xrLabel7,
            this.xrLabel8,
            this.lbl_code,
            this.xrLabel2,
            this.lblReportName,
            this.picLogo,
            this.lblCompName});
            this.TopMargin.Font = new System.Drawing.Font("Times New Roman", 9.75F, System.Drawing.FontStyle.Bold);
            this.TopMargin.HeightF = 251F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.StylePriority.UseFont = false;
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrLine1
            // 
            this.xrLine1.BackColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderWidth = 0F;
            this.xrLine1.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 159.0001F);
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.SizeF = new System.Drawing.SizeF(762.5001F, 3.541748F);
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_User.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_User.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 131F);
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.SizeF = new System.Drawing.SizeF(105.2085F, 24.49998F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            this.lbl_User.Text = "..";
            this.lbl_User.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel6.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(118.5F, 131F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(67.7085F, 24.49998F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "المستخدم";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_notes
            // 
            this.lbl_notes.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_notes.LocationFloat = new DevExpress.Utils.PointFloat(350F, 194.5418F);
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.SizeF = new System.Drawing.SizeF(349.9583F, 50.9583F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            this.lbl_notes.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(699.9583F, 194.5418F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(75.04175F, 24.49998F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "ملاحظات";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_date
            // 
            this.lbl_date.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_date.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 106F);
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.SizeF = new System.Drawing.SizeF(105.2085F, 24.5F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            this.lbl_date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_process
            // 
            this.lbl_process.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_process.LocationFloat = new DevExpress.Utils.PointFloat(528.0832F, 170.0418F);
            this.lbl_process.Name = "lbl_process";
            this.lbl_process.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_process.SizeF = new System.Drawing.SizeF(171.8751F, 24.49998F);
            this.lbl_process.StylePriority.UseFont = false;
            this.lbl_process.StylePriority.UseTextAlignment = false;
            this.lbl_process.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(118.5F, 106F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(67.7085F, 24.49999F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "التاريخ";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(699.9583F, 170.0418F);
            this.xrLabel8.Multiline = true;
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(75.04175F, 24.49998F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "مصدر القيد";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_code
            // 
            this.lbl_code.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_code.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 81F);
            this.lbl_code.Name = "lbl_code";
            this.lbl_code.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_code.SizeF = new System.Drawing.SizeF(105.2085F, 24.5F);
            this.lbl_code.StylePriority.UseFont = false;
            this.lbl_code.StylePriority.UseTextAlignment = false;
            this.lbl_code.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(118.5F, 81F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(67.7085F, 24.49999F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "رقم القيد";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblReportName
            // 
            this.lblReportName.Font = new System.Drawing.Font("Times New Roman", 18F, System.Drawing.FontStyle.Bold);
            this.lblReportName.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 44.5F);
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.SizeF = new System.Drawing.SizeF(173.7085F, 30F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            this.lblReportName.Text = "قيـد يوميـة";
            this.lblReportName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // picLogo
            // 
            this.picLogo.LocationFloat = new DevExpress.Utils.PointFloat(651.8334F, 81F);
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeF = new System.Drawing.SizeF(123.1667F, 74.49999F);
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lblCompName
            // 
            this.lblCompName.Font = new System.Drawing.Font("Times New Roman", 18F, System.Drawing.FontStyle.Bold);
            this.lblCompName.LocationFloat = new DevExpress.Utils.PointFloat(350F, 44.5F);
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.SizeF = new System.Drawing.SizeF(425.0001F, 30F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            this.lblCompName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_Number,
            this.lbl_Monthly_Code});
            this.BottomMargin.HeightF = 45F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrPageInfo1
            // 
            this.xrPageInfo1.Format = "Page {0} of {1} ";
            this.xrPageInfo1.LocationFloat = new DevExpress.Utils.PointFloat(13.79166F, 96F);
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.SizeF = new System.Drawing.SizeF(109.375F, 23F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            this.xrPageInfo1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            this.PageHeader.HeightF = 29.29167F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.BackColor = System.Drawing.Color.Silver;
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 0F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(763.5F, 29.29167F);
            this.xrTable1.StylePriority.UseBackColor = false;
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell2,
            this.xrTableCell6,
            this.xrTableCell5,
            this.xrTableCell3,
            this.xrTableCell10,
            this.xrTableCell8});
            this.xrTableRow1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            this.xrTableRow1.Weight = 1D;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.BackColor = System.Drawing.Color.LightGray;
            this.xrTableCell2.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.StylePriority.UseBackColor = false;
            this.xrTableCell2.StylePriority.UseBorders = false;
            this.xrTableCell2.StylePriority.UseTextAlignment = false;
            this.xrTableCell2.Text = "مركز تكلفة";
            this.xrTableCell2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomCenter;
            this.xrTableCell2.Weight = 0.30116941635644334D;
            // 
            // xrTableCell6
            // 
            this.xrTableCell6.BackColor = System.Drawing.Color.LightGray;
            this.xrTableCell6.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.StylePriority.UseBorders = false;
            this.xrTableCell6.StylePriority.UseTextAlignment = false;
            this.xrTableCell6.Text = "البيان";
            this.xrTableCell6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomCenter;
            this.xrTableCell6.Weight = 0.67092026280992811D;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.BackColor = System.Drawing.Color.LightGray;
            this.xrTableCell5.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell5.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseBackColor = false;
            this.xrTableCell5.StylePriority.UseBorders = false;
            this.xrTableCell5.StylePriority.UseFont = false;
            this.xrTableCell5.StylePriority.UseTextAlignment = false;
            this.xrTableCell5.Text = "دائن";
            this.xrTableCell5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomCenter;
            this.xrTableCell5.Weight = 0.22960402947345754D;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.BackColor = System.Drawing.Color.LightGray;
            this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            this.xrTableCell3.StylePriority.UseTextAlignment = false;
            this.xrTableCell3.Text = "مدين";
            this.xrTableCell3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomCenter;
            this.xrTableCell3.Weight = 0.22471399889647514D;
            // 
            // xrTableCell10
            // 
            this.xrTableCell10.BackColor = System.Drawing.Color.LightGray;
            this.xrTableCell10.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell10.Name = "xrTableCell10";
            this.xrTableCell10.StylePriority.UseBackColor = false;
            this.xrTableCell10.StylePriority.UseBorders = false;
            this.xrTableCell10.StylePriority.UseTextAlignment = false;
            this.xrTableCell10.Text = "رقم الحساب";
            this.xrTableCell10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomCenter;
            this.xrTableCell10.Weight = 0.2523856436023274D;
            // 
            // xrTableCell8
            // 
            this.xrTableCell8.BackColor = System.Drawing.Color.LightGray;
            this.xrTableCell8.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseBackColor = false;
            this.xrTableCell8.StylePriority.UseBorders = false;
            this.xrTableCell8.StylePriority.UseTextAlignment = false;
            this.xrTableCell8.Text = "اسم الحساب";
            this.xrTableCell8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomCenter;
            this.xrTableCell8.Weight = 0.50679825014957169D;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_TotalCredit,
            this.lblTotal_Words,
            this.xrLabel5,
            this.lbl_TotalDebit,
            this.xrPageInfo1});
            this.ReportFooter.HeightF = 123F;
            this.ReportFooter.Name = "ReportFooter";
            this.ReportFooter.PrintAtBottom = true;
            // 
            // lbl_TotalCredit
            // 
            this.lbl_TotalCredit.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.lbl_TotalCredit.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.lbl_TotalCredit.LocationFloat = new DevExpress.Utils.PointFloat(432.2917F, 0F);
            this.lbl_TotalCredit.Name = "lbl_TotalCredit";
            this.lbl_TotalCredit.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_TotalCredit.SizeF = new System.Drawing.SizeF(77.50006F, 24.49996F);
            this.lbl_TotalCredit.StylePriority.UseBorders = false;
            this.lbl_TotalCredit.StylePriority.UseFont = false;
            this.lbl_TotalCredit.StylePriority.UseTextAlignment = false;
            this.lbl_TotalCredit.Text = " ";
            this.lbl_TotalCredit.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblTotal_Words
            // 
            this.lblTotal_Words.BackColor = System.Drawing.Color.Silver;
            this.lblTotal_Words.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lblTotal_Words.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 36.79161F);
            this.lblTotal_Words.Name = "lblTotal_Words";
            this.lblTotal_Words.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotal_Words.SizeF = new System.Drawing.SizeF(419.7917F, 24.49999F);
            this.lblTotal_Words.StylePriority.UseBackColor = false;
            this.lblTotal_Words.StylePriority.UseFont = false;
            this.lblTotal_Words.StylePriority.UseTextAlignment = false;
            this.lblTotal_Words.Text = "..";
            this.lblTotal_Words.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomRight;
            this.lblTotal_Words.Visible = false;
            // 
            // xrLabel5
            // 
            this.xrLabel5.BackColor = System.Drawing.Color.Silver;
            this.xrLabel5.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(432.2917F, 36.79161F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(78.51208F, 24.49999F);
            this.xrLabel5.StylePriority.UseBackColor = false;
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "فقط وقدره";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.BottomCenter;
            this.xrLabel5.Visible = false;
            // 
            // lbl_TotalDebit
            // 
            this.lbl_TotalDebit.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.lbl_TotalDebit.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.lbl_TotalDebit.LocationFloat = new DevExpress.Utils.PointFloat(352.0834F, 0F);
            this.lbl_TotalDebit.Name = "lbl_TotalDebit";
            this.lbl_TotalDebit.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_TotalDebit.SizeF = new System.Drawing.SizeF(80.20837F, 24.49996F);
            this.lbl_TotalDebit.StylePriority.UseBorders = false;
            this.lbl_TotalDebit.StylePriority.UseFont = false;
            this.lbl_TotalDebit.StylePriority.UseTextAlignment = false;
            this.lbl_TotalDebit.Text = " ";
            this.lbl_TotalDebit.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrCrossBandLine2
            // 
            this.xrCrossBandLine2.EndBand = this.ReportFooter;
            this.xrCrossBandLine2.EndPointFloat = new DevExpress.Utils.PointFloat(352.2016F, 24.22396F);
            this.xrCrossBandLine2.LocationFloat = new DevExpress.Utils.PointFloat(352.2016F, 0.6720384F);
            this.xrCrossBandLine2.Name = "xrCrossBandLine2";
            this.xrCrossBandLine2.StartBand = this.PageHeader;
            this.xrCrossBandLine2.StartPointFloat = new DevExpress.Utils.PointFloat(352.2016F, 0.6720384F);
            this.xrCrossBandLine2.WidthF = 1F;
            // 
            // xrCrossBandLine3
            // 
            this.xrCrossBandLine3.EndBand = this.ReportFooter;
            this.xrCrossBandLine3.EndPointFloat = new DevExpress.Utils.PointFloat(117F, 0.6145795F);
            this.xrCrossBandLine3.LocationFloat = new DevExpress.Utils.PointFloat(117F, 0F);
            this.xrCrossBandLine3.Name = "xrCrossBandLine3";
            this.xrCrossBandLine3.StartBand = this.PageHeader;
            this.xrCrossBandLine3.StartPointFloat = new DevExpress.Utils.PointFloat(117F, 0F);
            this.xrCrossBandLine3.WidthF = 1F;
            // 
            // xrCrossBandLine4
            // 
            this.xrCrossBandLine4.EndBand = this.ReportFooter;
            this.xrCrossBandLine4.EndPointFloat = new DevExpress.Utils.PointFloat(432.1748F, 23.83334F);
            this.xrCrossBandLine4.LocationFloat = new DevExpress.Utils.PointFloat(432.1748F, 1.344093F);
            this.xrCrossBandLine4.Name = "xrCrossBandLine4";
            this.xrCrossBandLine4.StartBand = this.PageHeader;
            this.xrCrossBandLine4.StartPointFloat = new DevExpress.Utils.PointFloat(432.1748F, 1.344093F);
            this.xrCrossBandLine4.WidthF = 1F;
            // 
            // xrCrossBandLine5
            // 
            this.xrCrossBandLine5.EndBand = this.ReportFooter;
            this.xrCrossBandLine5.EndPointFloat = new DevExpress.Utils.PointFloat(509.8038F, 24.59375F);
            this.xrCrossBandLine5.LocationFloat = new DevExpress.Utils.PointFloat(509.8038F, 0.3360271F);
            this.xrCrossBandLine5.Name = "xrCrossBandLine5";
            this.xrCrossBandLine5.StartBand = this.PageHeader;
            this.xrCrossBandLine5.StartPointFloat = new DevExpress.Utils.PointFloat(509.8038F, 0.3360271F);
            this.xrCrossBandLine5.WidthF = 1.000031F;
            // 
            // xrCrossBandLine6
            // 
            this.xrCrossBandLine6.EndBand = this.ReportFooter;
            this.xrCrossBandLine6.EndPointFloat = new DevExpress.Utils.PointFloat(597.8415F, 0F);
            this.xrCrossBandLine6.LocationFloat = new DevExpress.Utils.PointFloat(597.8415F, 1.672045F);
            this.xrCrossBandLine6.Name = "xrCrossBandLine6";
            this.xrCrossBandLine6.StartBand = this.PageHeader;
            this.xrCrossBandLine6.StartPointFloat = new DevExpress.Utils.PointFloat(597.8415F, 1.672045F);
            this.xrCrossBandLine6.WidthF = 1F;
            // 
            // xrCrossBandBox1
            // 
            this.xrCrossBandBox1.BorderWidth = 1F;
            this.xrCrossBandBox1.EndBand = this.ReportFooter;
            this.xrCrossBandBox1.EndPointFloat = new DevExpress.Utils.PointFloat(12.74999F, 0.4843712F);
            this.xrCrossBandBox1.LocationFloat = new DevExpress.Utils.PointFloat(12.74999F, 0.5208333F);
            this.xrCrossBandBox1.Name = "xrCrossBandBox1";
            this.xrCrossBandBox1.StartBand = this.PageHeader;
            this.xrCrossBandBox1.StartPointFloat = new DevExpress.Utils.PointFloat(12.74999F, 0.5208333F);
            this.xrCrossBandBox1.WidthF = 763.2499F;
            // 
            // lbl_Monthly_Code
            // 
            this.lbl_Monthly_Code.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Monthly_Code.LocationFloat = new DevExpress.Utils.PointFloat(265.0416F, 10.25001F);
            this.lbl_Monthly_Code.Name = "lbl_Monthly_Code";
            this.lbl_Monthly_Code.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Monthly_Code.SizeF = new System.Drawing.SizeF(171.8751F, 24.49998F);
            this.lbl_Monthly_Code.StylePriority.UseFont = false;
            this.lbl_Monthly_Code.StylePriority.UseTextAlignment = false;
            this.lbl_Monthly_Code.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Number
            // 
            this.lbl_Number.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Number.LocationFloat = new DevExpress.Utils.PointFloat(471.2917F, 9.999974F);
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.SizeF = new System.Drawing.SizeF(171.8751F, 24.49998F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            this.lbl_Number.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // rpt_ACC_Journal
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter});
            this.CrossBandControls.AddRange(new DevExpress.XtraReports.UI.XRCrossBandControl[] {
            this.xrCrossBandBox1,
            this.xrCrossBandLine6,
            this.xrCrossBandLine5,
            this.xrCrossBandLine4,
            this.xrCrossBandLine3,
            this.xrCrossBandLine2});
            this.Margins = new System.Drawing.Printing.Margins(19, 31, 251, 45);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.ShowPrintMarginsWarning = false;
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel lbl_process;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_code;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_costcenter;
        private DevExpress.XtraReports.UI.XRTableCell cell_notes;
        private DevExpress.XtraReports.UI.XRTableCell cell_credit;
        private DevExpress.XtraReports.UI.XRTableCell cell_debit;
        private DevExpress.XtraReports.UI.XRTableCell cell_accName;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRTableCell cell_accId;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell10;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel lbl_TotalCredit;
        private DevExpress.XtraReports.UI.XRLabel lblTotal_Words;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lbl_TotalDebit;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine2;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine3;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine4;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine5;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine6;
        private DevExpress.XtraReports.UI.XRCrossBandBox xrCrossBandBox1;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_Monthly_Code;
    }
}
