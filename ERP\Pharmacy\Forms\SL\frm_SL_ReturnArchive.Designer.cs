﻿namespace Pharmacy.Forms
{
    partial class frm_SL_ReturnArchive
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_ReturnArchive));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItem1 = new DevExpress.XtraBars.BarSubItem();
            this.barbtnLoadSellInvoice = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.batBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.txtInvoiceCode = new DevExpress.XtraEditors.TextEdit();
            this.btnPrevious = new DevExpress.XtraEditors.SimpleButton();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.dtInvoiceDate = new DevExpress.XtraEditors.DateEdit();
            this.labelControl35 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.pnlBook = new System.Windows.Forms.Panel();
            this.textEdit8 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_InvoiceBook = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlInvCode = new System.Windows.Forms.Panel();
            this.textEdit7 = new DevExpress.XtraEditors.TextEdit();
            this.pnlDate = new System.Windows.Forms.Panel();
            this.textEdit6 = new DevExpress.XtraEditors.TextEdit();
            this.pnlBranch = new System.Windows.Forms.Panel();
            this.textEdit5 = new DevExpress.XtraEditors.TextEdit();
            this.lkpStore = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlCrncy = new System.Windows.Forms.Panel();
            this.txtCurrency = new DevExpress.XtraEditors.TextEdit();
            this.uc_Currency1 = new Pharmacy.Forms.uc_Currency();
            this.pnlSalesEmp = new System.Windows.Forms.Panel();
            this.textEdit3 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_SalesEmp = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlCostCenter = new System.Windows.Forms.Panel();
            this.lkpCostCenter = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.pnlSrcPrc = new System.Windows.Forms.Panel();
            this.txtSourceCode = new DevExpress.XtraEditors.TextEdit();
            this.btnSourceId = new DevExpress.XtraEditors.ButtonEdit();
            this.cmdProcess = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtNotes = new DevExpress.XtraEditors.MemoEdit();
            this.chk_IsInTrns = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Drawers = new DevExpress.XtraEditors.LookUpEdit();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.grdPrInvoice = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_frm_IC_Item = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_InvoiceStaticDisc = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_InvoiceStaticDimensions = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_PasteRows = new System.Windows.Forms.ToolStripMenuItem();
            this.importFromExcelSheetToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_vendors = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repDiscountRatio = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repSpin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repUOM = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repItems = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CompanyNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CategoryNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Expire = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_expireDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.col_Batch = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Height = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Width = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Length = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PiecesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemDescriptionEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SalesTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountRatio2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountRatio3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Serial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repManufactureDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.repExpireDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.repLocation = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLocationNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLocationId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repExpireDate_txt = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.lkp_Customers = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Total = new DevExpress.XtraEditors.TextEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Remains = new DevExpress.XtraEditors.TextEdit();
            this.txtNet = new DevExpress.XtraEditors.TextEdit();
            this.lbl_remains = new DevExpress.XtraEditors.LabelControl();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.lbl_Paid = new DevExpress.XtraEditors.LabelControl();
            this.txt_paid = new DevExpress.XtraEditors.TextEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.btnAddCustomer = new DevExpress.XtraEditors.SimpleButton();
            this.txtExpenses = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.lkp_Drawers2 = new DevExpress.XtraEditors.LookUpEdit();
            this.txt_PayAcc1_Paid = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.txt_PayAcc2_Paid = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.btn_AddMatrixItems = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl40 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.cmbPayMethod = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.txtDiscountRatio = new DevExpress.XtraEditors.SpinEdit();
            this.txtDiscountValue = new DevExpress.XtraEditors.SpinEdit();
            this.txt_TaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DeductTaxR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DeductTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AddTaxR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AddTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.page_AccInfo = new DevExpress.XtraTab.XtraTabPage();
            this.txt_Balance_After = new DevExpress.XtraEditors.LabelControl();
            this.txt_Balance_Before = new DevExpress.XtraEditors.LabelControl();
            this.txt_MaxCredit = new DevExpress.XtraEditors.LabelControl();
            this.lbl_IsCredit_After = new DevExpress.XtraEditors.LabelControl();
            this.lbl_IsCredit_Before = new DevExpress.XtraEditors.LabelControl();
            this.lblBlncAftr = new DevExpress.XtraEditors.LabelControl();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.tabExtraData = new DevExpress.XtraTab.XtraTabPage();
            this.txtScaleSerial = new DevExpress.XtraEditors.TextEdit();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.txtDestination = new DevExpress.XtraEditors.TextEdit();
            this.lblDestination = new DevExpress.XtraEditors.LabelControl();
            this.txtVehicleNumber = new DevExpress.XtraEditors.TextEdit();
            this.lblVehicleNumber = new DevExpress.XtraEditors.LabelControl();
            this.txtDriverName = new DevExpress.XtraEditors.TextEdit();
            this.lblDriverName = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            this.flowLayoutPanel1.SuspendLayout();
            this.pnlBook.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_InvoiceBook.Properties)).BeginInit();
            this.pnlInvCode.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).BeginInit();
            this.pnlDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).BeginInit();
            this.pnlBranch.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).BeginInit();
            this.pnlCrncy.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).BeginInit();
            this.pnlSalesEmp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).BeginInit();
            this.pnlCostCenter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCostCenter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            this.pnlSrcPrc.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtSourceCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnSourceId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmdProcess.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsInTrns.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_vendors)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDiscountRatio)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLocation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate_txt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Total.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Remains.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc1_Paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc2_Paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPayMethod.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.page_AccInfo.SuspendLayout();
            this.tabExtraData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtScaleSerial.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDestination.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVehicleNumber.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDriverName.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnClose,
            this.barBtnHelp,
            this.batBtnList,
            this.barBtnNew,
            this.barBtnDelete,
            this.barBtnPrint,
            this.barbtnLoadSellInvoice,
            this.barSubItem1});
            this.barManager1.MaxItemId = 33;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItem1),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnDelete),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.batBtnList),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barSubItem1
            // 
            this.barSubItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barSubItem1, "barSubItem1");
            this.barSubItem1.Enabled = false;
            this.barSubItem1.Glyph = global::Pharmacy.Properties.Resources._16_convert;
            this.barSubItem1.Id = 32;
            this.barSubItem1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnLoadSellInvoice)});
            this.barSubItem1.Name = "barSubItem1";
            this.barSubItem1.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barbtnLoadSellInvoice
            // 
            resources.ApplyResources(this.barbtnLoadSellInvoice, "barbtnLoadSellInvoice");
            this.barbtnLoadSellInvoice.Glyph = global::Pharmacy.Properties.Resources.N_return1;
            this.barbtnLoadSellInvoice.Id = 31;
            this.barbtnLoadSellInvoice.Name = "barbtnLoadSellInvoice";
            this.barbtnLoadSellInvoice.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barbtnLoadSellInvoice.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barbtnLoadSellInvoice_ItemClick);
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barBtnPrint.Id = 30;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnNew
            // 
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Enabled = false;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 26;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnDelete
            // 
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Enabled = false;
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 28;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnDelete_ItemClick);
            // 
            // barBtnSave
            // 
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Enabled = false;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // batBtnList
            // 
            this.batBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.batBtnList, "batBtnList");
            this.batBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.batBtnList.Id = 25;
            this.batBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.batBtnList.Name = "batBtnList";
            this.batBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.batBtnList.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.batBtnList_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 1;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.Item.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // txtInvoiceCode
            // 
            resources.ApplyResources(this.txtInvoiceCode, "txtInvoiceCode");
            this.txtInvoiceCode.EnterMoveNextControl = true;
            this.txtInvoiceCode.Name = "txtInvoiceCode";
            this.txtInvoiceCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtInvoiceCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtInvoiceCode.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // btnPrevious
            // 
            this.btnPrevious.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrevious.Image = global::Pharmacy.Properties.Resources.prev32;
            resources.ApplyResources(this.btnPrevious, "btnPrevious");
            this.btnPrevious.Name = "btnPrevious";
            this.btnPrevious.TabStop = false;
            this.btnPrevious.Click += new System.EventHandler(this.btnPrevious_Click);
            // 
            // btnNext
            // 
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.Name = "btnNext";
            this.btnNext.TabStop = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // dtInvoiceDate
            // 
            resources.ApplyResources(this.dtInvoiceDate, "dtInvoiceDate");
            this.dtInvoiceDate.EnterMoveNextControl = true;
            this.dtInvoiceDate.MenuManager = this.barManager1;
            this.dtInvoiceDate.Name = "dtInvoiceDate";
            this.dtInvoiceDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.dtInvoiceDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtInvoiceDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtInvoiceDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtInvoiceDate.Properties.Buttons"))))});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtInvoiceDate.Properties.DisplayFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.EditFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.Mask.EditMask");
            this.dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtInvoiceDate.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl35
            // 
            resources.ApplyResources(this.labelControl35, "labelControl35");
            this.labelControl35.Name = "labelControl35";
            // 
            // labelControl36
            // 
            resources.ApplyResources(this.labelControl36, "labelControl36");
            this.labelControl36.Name = "labelControl36";
            // 
            // panelControl1
            // 
            resources.ApplyResources(this.panelControl1, "panelControl1");
            this.panelControl1.Controls.Add(this.flowLayoutPanel1);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Controls.Add(this.txtNotes);
            this.panelControl1.Name = "panelControl1";
            // 
            // flowLayoutPanel1
            // 
            resources.ApplyResources(this.flowLayoutPanel1, "flowLayoutPanel1");
            this.flowLayoutPanel1.Controls.Add(this.pnlBook);
            this.flowLayoutPanel1.Controls.Add(this.pnlInvCode);
            this.flowLayoutPanel1.Controls.Add(this.pnlDate);
            this.flowLayoutPanel1.Controls.Add(this.pnlBranch);
            this.flowLayoutPanel1.Controls.Add(this.pnlCrncy);
            this.flowLayoutPanel1.Controls.Add(this.pnlSalesEmp);
            this.flowLayoutPanel1.Controls.Add(this.pnlCostCenter);
            this.flowLayoutPanel1.Controls.Add(this.pnlSrcPrc);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            // 
            // pnlBook
            // 
            this.pnlBook.Controls.Add(this.textEdit8);
            this.pnlBook.Controls.Add(this.lkp_InvoiceBook);
            resources.ApplyResources(this.pnlBook, "pnlBook");
            this.pnlBook.Name = "pnlBook";
            // 
            // textEdit8
            // 
            resources.ApplyResources(this.textEdit8, "textEdit8");
            this.textEdit8.EnterMoveNextControl = true;
            this.textEdit8.MenuManager = this.barManager1;
            this.textEdit8.Name = "textEdit8";
            this.textEdit8.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit8.Properties.Appearance.BackColor")));
            this.textEdit8.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit8.Properties.Appearance.ForeColor")));
            this.textEdit8.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit8.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit8.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit8.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit8.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit8.Properties.MaxLength = 190;
            this.textEdit8.TabStop = false;
            // 
            // lkp_InvoiceBook
            // 
            resources.ApplyResources(this.lkp_InvoiceBook, "lkp_InvoiceBook");
            this.lkp_InvoiceBook.EnterMoveNextControl = true;
            this.lkp_InvoiceBook.Name = "lkp_InvoiceBook";
            this.lkp_InvoiceBook.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_InvoiceBook.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_InvoiceBook.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_InvoiceBook.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_InvoiceBook.Properties.Buttons"))))});
            this.lkp_InvoiceBook.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_InvoiceBook.Properties.Columns"), resources.GetString("lkp_InvoiceBook.Properties.Columns1"), ((int)(resources.GetObject("lkp_InvoiceBook.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_InvoiceBook.Properties.Columns3"))), resources.GetString("lkp_InvoiceBook.Properties.Columns4"), ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_InvoiceBook.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_InvoiceBook.Properties.Columns7"), resources.GetString("lkp_InvoiceBook.Properties.Columns8"), ((int)(resources.GetObject("lkp_InvoiceBook.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_InvoiceBook.Properties.Columns10"))), resources.GetString("lkp_InvoiceBook.Properties.Columns11"), ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_InvoiceBook.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_InvoiceBook.Properties.Columns14"), resources.GetString("lkp_InvoiceBook.Properties.Columns15"), ((int)(resources.GetObject("lkp_InvoiceBook.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_InvoiceBook.Properties.Columns17"))), resources.GetString("lkp_InvoiceBook.Properties.Columns18"), ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_InvoiceBook.Properties.Columns20"))))});
            this.lkp_InvoiceBook.Properties.NullText = resources.GetString("lkp_InvoiceBook.Properties.NullText");
            this.lkp_InvoiceBook.EditValueChanged += new System.EventHandler(this.lkpStore_EditValueChanged);
            this.lkp_InvoiceBook.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlInvCode
            // 
            this.pnlInvCode.Controls.Add(this.textEdit7);
            this.pnlInvCode.Controls.Add(this.txtInvoiceCode);
            resources.ApplyResources(this.pnlInvCode, "pnlInvCode");
            this.pnlInvCode.Name = "pnlInvCode";
            // 
            // textEdit7
            // 
            resources.ApplyResources(this.textEdit7, "textEdit7");
            this.textEdit7.EnterMoveNextControl = true;
            this.textEdit7.MenuManager = this.barManager1;
            this.textEdit7.Name = "textEdit7";
            this.textEdit7.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit7.Properties.Appearance.BackColor")));
            this.textEdit7.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit7.Properties.Appearance.ForeColor")));
            this.textEdit7.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit7.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit7.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit7.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit7.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit7.Properties.MaxLength = 190;
            this.textEdit7.TabStop = false;
            // 
            // pnlDate
            // 
            this.pnlDate.Controls.Add(this.textEdit6);
            this.pnlDate.Controls.Add(this.dtInvoiceDate);
            resources.ApplyResources(this.pnlDate, "pnlDate");
            this.pnlDate.Name = "pnlDate";
            // 
            // textEdit6
            // 
            resources.ApplyResources(this.textEdit6, "textEdit6");
            this.textEdit6.EnterMoveNextControl = true;
            this.textEdit6.MenuManager = this.barManager1;
            this.textEdit6.Name = "textEdit6";
            this.textEdit6.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit6.Properties.Appearance.BackColor")));
            this.textEdit6.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit6.Properties.Appearance.ForeColor")));
            this.textEdit6.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit6.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit6.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit6.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit6.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit6.Properties.MaxLength = 190;
            this.textEdit6.TabStop = false;
            // 
            // pnlBranch
            // 
            this.pnlBranch.Controls.Add(this.textEdit5);
            this.pnlBranch.Controls.Add(this.lkpStore);
            resources.ApplyResources(this.pnlBranch, "pnlBranch");
            this.pnlBranch.Name = "pnlBranch";
            // 
            // textEdit5
            // 
            resources.ApplyResources(this.textEdit5, "textEdit5");
            this.textEdit5.EnterMoveNextControl = true;
            this.textEdit5.MenuManager = this.barManager1;
            this.textEdit5.Name = "textEdit5";
            this.textEdit5.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit5.Properties.Appearance.BackColor")));
            this.textEdit5.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit5.Properties.Appearance.ForeColor")));
            this.textEdit5.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit5.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit5.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit5.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit5.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit5.Properties.MaxLength = 190;
            this.textEdit5.TabStop = false;
            // 
            // lkpStore
            // 
            resources.ApplyResources(this.lkpStore, "lkpStore");
            this.lkpStore.EnterMoveNextControl = true;
            this.lkpStore.MenuManager = this.barManager1;
            this.lkpStore.Name = "lkpStore";
            this.lkpStore.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpStore.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpStore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpStore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpStore.Properties.Buttons"))))});
            this.lkpStore.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns"), resources.GetString("lkpStore.Properties.Columns1"), ((int)(resources.GetObject("lkpStore.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns3"))), resources.GetString("lkpStore.Properties.Columns4"), ((bool)(resources.GetObject("lkpStore.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns7"), resources.GetString("lkpStore.Properties.Columns8"), ((int)(resources.GetObject("lkpStore.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns10"))), resources.GetString("lkpStore.Properties.Columns11"), ((bool)(resources.GetObject("lkpStore.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns14"), resources.GetString("lkpStore.Properties.Columns15"), ((int)(resources.GetObject("lkpStore.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns17"))), resources.GetString("lkpStore.Properties.Columns18"), ((bool)(resources.GetObject("lkpStore.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns21"), resources.GetString("lkpStore.Properties.Columns22"), ((int)(resources.GetObject("lkpStore.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns24"))), resources.GetString("lkpStore.Properties.Columns25"), ((bool)(resources.GetObject("lkpStore.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns27")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns28"), resources.GetString("lkpStore.Properties.Columns29"), ((int)(resources.GetObject("lkpStore.Properties.Columns30"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns31"))), resources.GetString("lkpStore.Properties.Columns32"), ((bool)(resources.GetObject("lkpStore.Properties.Columns33"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns34")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns35"), resources.GetString("lkpStore.Properties.Columns36"), ((int)(resources.GetObject("lkpStore.Properties.Columns37"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns38"))), resources.GetString("lkpStore.Properties.Columns39"), ((bool)(resources.GetObject("lkpStore.Properties.Columns40"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns41")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns42"), resources.GetString("lkpStore.Properties.Columns43"), ((int)(resources.GetObject("lkpStore.Properties.Columns44"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns45"))), resources.GetString("lkpStore.Properties.Columns46"), ((bool)(resources.GetObject("lkpStore.Properties.Columns47"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns48"))))});
            this.lkpStore.Properties.NullText = resources.GetString("lkpStore.Properties.NullText");
            this.lkpStore.EditValueChanged += new System.EventHandler(this.lkpStore_EditValueChanged);
            this.lkpStore.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlCrncy
            // 
            this.pnlCrncy.Controls.Add(this.txtCurrency);
            this.pnlCrncy.Controls.Add(this.uc_Currency1);
            resources.ApplyResources(this.pnlCrncy, "pnlCrncy");
            this.pnlCrncy.Name = "pnlCrncy";
            // 
            // txtCurrency
            // 
            resources.ApplyResources(this.txtCurrency, "txtCurrency");
            this.txtCurrency.EnterMoveNextControl = true;
            this.txtCurrency.MenuManager = this.barManager1;
            this.txtCurrency.Name = "txtCurrency";
            this.txtCurrency.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtCurrency.Properties.Appearance.BackColor")));
            this.txtCurrency.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtCurrency.Properties.Appearance.ForeColor")));
            this.txtCurrency.Properties.Appearance.Options.UseBackColor = true;
            this.txtCurrency.Properties.Appearance.Options.UseForeColor = true;
            this.txtCurrency.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCurrency.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCurrency.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtCurrency.Properties.MaxLength = 190;
            this.txtCurrency.TabStop = false;
            // 
            // uc_Currency1
            // 
            resources.ApplyResources(this.uc_Currency1, "uc_Currency1");
            this.uc_Currency1.Name = "uc_Currency1";
            this.uc_Currency1.TabStop = false;
            // 
            // pnlSalesEmp
            // 
            this.pnlSalesEmp.Controls.Add(this.textEdit3);
            this.pnlSalesEmp.Controls.Add(this.lkp_SalesEmp);
            resources.ApplyResources(this.pnlSalesEmp, "pnlSalesEmp");
            this.pnlSalesEmp.Name = "pnlSalesEmp";
            // 
            // textEdit3
            // 
            resources.ApplyResources(this.textEdit3, "textEdit3");
            this.textEdit3.EnterMoveNextControl = true;
            this.textEdit3.MenuManager = this.barManager1;
            this.textEdit3.Name = "textEdit3";
            this.textEdit3.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit3.Properties.Appearance.BackColor")));
            this.textEdit3.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit3.Properties.Appearance.ForeColor")));
            this.textEdit3.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit3.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit3.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit3.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit3.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit3.Properties.MaxLength = 190;
            this.textEdit3.TabStop = false;
            // 
            // lkp_SalesEmp
            // 
            resources.ApplyResources(this.lkp_SalesEmp, "lkp_SalesEmp");
            this.lkp_SalesEmp.EnterMoveNextControl = true;
            this.lkp_SalesEmp.MenuManager = this.barManager1;
            this.lkp_SalesEmp.Name = "lkp_SalesEmp";
            this.lkp_SalesEmp.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_SalesEmp.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_SalesEmp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_SalesEmp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_SalesEmp.Properties.Buttons"))))});
            this.lkp_SalesEmp.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns"), resources.GetString("lkp_SalesEmp.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns2"), resources.GetString("lkp_SalesEmp.Properties.Columns3"), ((int)(resources.GetObject("lkp_SalesEmp.Properties.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SalesEmp.Properties.Columns5"))), resources.GetString("lkp_SalesEmp.Properties.Columns6"), ((bool)(resources.GetObject("lkp_SalesEmp.Properties.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SalesEmp.Properties.Columns8")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns9"), resources.GetString("lkp_SalesEmp.Properties.Columns10"))});
            this.lkp_SalesEmp.Properties.DisplayMember = "EmpName";
            this.lkp_SalesEmp.Properties.NullText = resources.GetString("lkp_SalesEmp.Properties.NullText");
            this.lkp_SalesEmp.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkp_SalesEmp.Properties.ValueMember = "EmpId";
            // 
            // pnlCostCenter
            // 
            this.pnlCostCenter.Controls.Add(this.lkpCostCenter);
            this.pnlCostCenter.Controls.Add(this.textEdit1);
            resources.ApplyResources(this.pnlCostCenter, "pnlCostCenter");
            this.pnlCostCenter.Name = "pnlCostCenter";
            // 
            // lkpCostCenter
            // 
            resources.ApplyResources(this.lkpCostCenter, "lkpCostCenter");
            this.lkpCostCenter.EnterMoveNextControl = true;
            this.lkpCostCenter.MenuManager = this.barManager1;
            this.lkpCostCenter.Name = "lkpCostCenter";
            this.lkpCostCenter.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpCostCenter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCostCenter.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpCostCenter.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpCostCenter.Properties.Buttons"))))});
            this.lkpCostCenter.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCostCenter.Properties.Columns"), resources.GetString("lkpCostCenter.Properties.Columns1"), ((int)(resources.GetObject("lkpCostCenter.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCostCenter.Properties.Columns3"))), resources.GetString("lkpCostCenter.Properties.Columns4"), ((bool)(resources.GetObject("lkpCostCenter.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCostCenter.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCostCenter.Properties.Columns7"), resources.GetString("lkpCostCenter.Properties.Columns8"), ((int)(resources.GetObject("lkpCostCenter.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCostCenter.Properties.Columns10"))), resources.GetString("lkpCostCenter.Properties.Columns11"), ((bool)(resources.GetObject("lkpCostCenter.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCostCenter.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCostCenter.Properties.Columns14"), resources.GetString("lkpCostCenter.Properties.Columns15"), ((int)(resources.GetObject("lkpCostCenter.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCostCenter.Properties.Columns17"))), resources.GetString("lkpCostCenter.Properties.Columns18"), ((bool)(resources.GetObject("lkpCostCenter.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCostCenter.Properties.Columns20"))))});
            this.lkpCostCenter.Properties.NullText = resources.GetString("lkpCostCenter.Properties.NullText");
            // 
            // textEdit1
            // 
            resources.ApplyResources(this.textEdit1, "textEdit1");
            this.textEdit1.EnterMoveNextControl = true;
            this.textEdit1.MenuManager = this.barManager1;
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit1.Properties.Appearance.BackColor")));
            this.textEdit1.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit1.Properties.Appearance.ForeColor")));
            this.textEdit1.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit1.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit1.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit1.Properties.MaxLength = 190;
            this.textEdit1.TabStop = false;
            // 
            // pnlSrcPrc
            // 
            this.pnlSrcPrc.Controls.Add(this.txtSourceCode);
            this.pnlSrcPrc.Controls.Add(this.btnSourceId);
            this.pnlSrcPrc.Controls.Add(this.cmdProcess);
            resources.ApplyResources(this.pnlSrcPrc, "pnlSrcPrc");
            this.pnlSrcPrc.Name = "pnlSrcPrc";
            // 
            // txtSourceCode
            // 
            resources.ApplyResources(this.txtSourceCode, "txtSourceCode");
            this.txtSourceCode.MenuManager = this.barManager1;
            this.txtSourceCode.Name = "txtSourceCode";
            this.txtSourceCode.Properties.AppearanceReadOnly.BackColor = ((System.Drawing.Color)(resources.GetObject("txtSourceCode.Properties.AppearanceReadOnly.BackColor")));
            this.txtSourceCode.Properties.AppearanceReadOnly.Options.UseBackColor = true;
            this.txtSourceCode.Properties.ReadOnly = true;
            // 
            // btnSourceId
            // 
            resources.ApplyResources(this.btnSourceId, "btnSourceId");
            this.btnSourceId.MenuManager = this.barManager1;
            this.btnSourceId.Name = "btnSourceId";
            this.btnSourceId.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.btnSourceId.Click += new System.EventHandler(this.btnSourceId_Click);
            // 
            // cmdProcess
            // 
            resources.ApplyResources(this.cmdProcess, "cmdProcess");
            this.cmdProcess.MenuManager = this.barManager1;
            this.cmdProcess.Name = "cmdProcess";
            this.cmdProcess.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("cmdProcess.Properties.Appearance.BackColor")));
            this.cmdProcess.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("cmdProcess.Properties.Appearance.ForeColor")));
            this.cmdProcess.Properties.Appearance.Options.UseBackColor = true;
            this.cmdProcess.Properties.Appearance.Options.UseForeColor = true;
            this.cmdProcess.Properties.Appearance.Options.UseTextOptions = true;
            this.cmdProcess.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmdProcess.Properties.AppearanceReadOnly.BackColor = ((System.Drawing.Color)(resources.GetObject("cmdProcess.Properties.AppearanceReadOnly.BackColor")));
            this.cmdProcess.Properties.AppearanceReadOnly.Options.UseBackColor = true;
            this.cmdProcess.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.cmdProcess.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmdProcess.Properties.Items"), ((object)(resources.GetObject("cmdProcess.Properties.Items1"))), ((int)(resources.GetObject("cmdProcess.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmdProcess.Properties.Items3"), ((object)(resources.GetObject("cmdProcess.Properties.Items4"))), ((int)(resources.GetObject("cmdProcess.Properties.Items5"))))});
            this.cmdProcess.Properties.MaxLength = 190;
            this.cmdProcess.Properties.ReadOnly = true;
            this.cmdProcess.TabStop = false;
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // txtNotes
            // 
            resources.ApplyResources(this.txtNotes, "txtNotes");
            this.txtNotes.MenuManager = this.barManager1;
            this.txtNotes.Name = "txtNotes";
            this.txtNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.txtNotes.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // chk_IsInTrns
            // 
            resources.ApplyResources(this.chk_IsInTrns, "chk_IsInTrns");
            this.chk_IsInTrns.Name = "chk_IsInTrns";
            this.chk_IsInTrns.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsInTrns.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsInTrns.Properties.AppearanceReadOnly.BackColor = ((System.Drawing.Color)(resources.GetObject("chk_IsInTrns.Properties.AppearanceReadOnly.BackColor")));
            this.chk_IsInTrns.Properties.AppearanceReadOnly.ForeColor = ((System.Drawing.Color)(resources.GetObject("chk_IsInTrns.Properties.AppearanceReadOnly.ForeColor")));
            this.chk_IsInTrns.Properties.AppearanceReadOnly.Options.UseBackColor = true;
            this.chk_IsInTrns.Properties.AppearanceReadOnly.Options.UseForeColor = true;
            this.chk_IsInTrns.Properties.Caption = resources.GetString("chk_IsInTrns.Properties.Caption");
            this.chk_IsInTrns.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsInTrns.Properties.GlyphAlignment")));
            this.chk_IsInTrns.Properties.ReadOnly = true;
            this.chk_IsInTrns.TabStop = false;
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Name = "labelControl17";
            // 
            // lkp_Drawers
            // 
            this.lkp_Drawers.EnterMoveNextControl = true;
            resources.ApplyResources(this.lkp_Drawers, "lkp_Drawers");
            this.lkp_Drawers.MenuManager = this.barManager1;
            this.lkp_Drawers.Name = "lkp_Drawers";
            this.lkp_Drawers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Drawers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Drawers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Drawers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Drawers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Drawers.Properties.Buttons"))))});
            this.lkp_Drawers.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns"), resources.GetString("lkp_Drawers.Properties.Columns1"), ((int)(resources.GetObject("lkp_Drawers.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Drawers.Properties.Columns3"))), resources.GetString("lkp_Drawers.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Drawers.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Drawers.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns7"), resources.GetString("lkp_Drawers.Properties.Columns8"))});
            this.lkp_Drawers.Properties.DisplayMember = "AccountName";
            this.lkp_Drawers.Properties.NullText = resources.GetString("lkp_Drawers.Properties.NullText");
            this.lkp_Drawers.Properties.ValueMember = "AccountId";
            this.lkp_Drawers.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // panelControl2
            // 
            resources.ApplyResources(this.panelControl2, "panelControl2");
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl2.Controls.Add(this.grdPrInvoice);
            this.panelControl2.Name = "panelControl2";
            // 
            // grdPrInvoice
            // 
            this.grdPrInvoice.ContextMenuStrip = this.contextMenuStrip1;
            this.grdPrInvoice.Cursor = System.Windows.Forms.Cursors.Default;
            resources.ApplyResources(this.grdPrInvoice, "grdPrInvoice");
            this.grdPrInvoice.MainView = this.gridView2;
            this.grdPrInvoice.Name = "grdPrInvoice";
            this.grdPrInvoice.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpin,
            this.repItems,
            this.repUOM,
            this.repExpireDate,
            this.repLocation,
            this.repExpireDate_txt,
            this.rep_vendors,
            this.repDiscountRatio,
            this.rep_expireDate,
            this.repManufactureDate});
            this.grdPrInvoice.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            this.grdPrInvoice.ProcessGridKey += new System.Windows.Forms.KeyEventHandler(this.grid_ProcessGridKey);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_frm_IC_Item,
            this.mi_InvoiceStaticDisc,
            this.mi_InvoiceStaticDimensions,
            this.mi_PasteRows,
            this.importFromExcelSheetToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            this.contextMenuStrip1.Opened += new System.EventHandler(this.contextMenuStrip1_Opened);
            // 
            // mi_frm_IC_Item
            // 
            this.mi_frm_IC_Item.Name = "mi_frm_IC_Item";
            resources.ApplyResources(this.mi_frm_IC_Item, "mi_frm_IC_Item");
            this.mi_frm_IC_Item.Click += new System.EventHandler(this.mi_frm_IC_Item_Click);
            // 
            // mi_InvoiceStaticDisc
            // 
            this.mi_InvoiceStaticDisc.Name = "mi_InvoiceStaticDisc";
            resources.ApplyResources(this.mi_InvoiceStaticDisc, "mi_InvoiceStaticDisc");
            this.mi_InvoiceStaticDisc.Click += new System.EventHandler(this.mi_InvoiceStaticDisc_Click);
            // 
            // mi_InvoiceStaticDimensions
            // 
            this.mi_InvoiceStaticDimensions.Name = "mi_InvoiceStaticDimensions";
            resources.ApplyResources(this.mi_InvoiceStaticDimensions, "mi_InvoiceStaticDimensions");
            this.mi_InvoiceStaticDimensions.Click += new System.EventHandler(this.mi_InvoiceStaticDimensions_Click);
            // 
            // mi_PasteRows
            // 
            this.mi_PasteRows.Name = "mi_PasteRows";
            resources.ApplyResources(this.mi_PasteRows, "mi_PasteRows");
            this.mi_PasteRows.Click += new System.EventHandler(this.mi_PasteRows_Click);
            // 
            // importFromExcelSheetToolStripMenuItem
            // 
            this.importFromExcelSheetToolStripMenuItem.Name = "importFromExcelSheetToolStripMenuItem";
            resources.ApplyResources(this.importFromExcelSheetToolStripMenuItem, "importFromExcelSheetToolStripMenuItem");
            this.importFromExcelSheetToolStripMenuItem.Click += new System.EventHandler(this.importFromExcelSheetToolStripMenuItem_Click);
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView2.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView2.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView2.ColumnPanelRowHeight = 50;
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn36,
            this.gridColumn35,
            this.gridColumn33,
            this.gridColumn29,
            this.gridColumn28,
            this.col_TotalSellPrice,
            this.gridColumn23,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn5,
            this.colPurchasePrice,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn31,
            this.gridColumn38,
            this.col_Expire,
            this.col_Batch,
            this.col_Height,
            this.col_Width,
            this.col_Length,
            this.col_TotalQty,
            this.col_PiecesCount,
            this.col_ItemDescription,
            this.col_ItemDescriptionEn,
            this.col_SalesTax,
            this.col_DiscountRatio2,
            this.col_DiscountRatio3,
            this.col_Serial,
            this.gridColumn25});
            this.gridView2.CustomizationFormBounds = new System.Drawing.Rectangle(982, 128, 216, 388);
            this.gridView2.GridControl = this.grdPrInvoice;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView2.OptionsView.EnableAppearanceOddRow = true;
            this.gridView2.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView2.OptionsView.RowAutoHeight = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.RowStyle += new DevExpress.XtraGrid.Views.Grid.RowStyleEventHandler(this.gridView2_RowStyle);
            this.gridView2.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.gridView2_ShowingEditor);
            this.gridView2.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridView1_FocusedRowChanged);
            this.gridView2.FocusedColumnChanged += new DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventHandler(this.gridView1_FocusedColumnChanged);
            this.gridView2.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gridView1_CellValueChanged);
            this.gridView2.InvalidRowException += new DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventHandler(this.gridView1_InvalidRowException);
            this.gridView2.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gridView1_ValidateRow);
            this.gridView2.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView2_CustomUnboundColumnData);
            this.gridView2.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView1_CustomColumnDisplayText);
            this.gridView2.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gridView1_KeyDown);
            // 
            // gridColumn36
            // 
            this.gridColumn36.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn36.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn36.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn36.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn36.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn36.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn36.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn36.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn36, "gridColumn36");
            this.gridColumn36.FieldName = "TotalSellPrice";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn36.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn36.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn35
            // 
            this.gridColumn35.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn35.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn35.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn35.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn35.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn35.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn35.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn35.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn35, "gridColumn35");
            this.gridColumn35.FieldName = "MainPrice";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn35.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn35.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn33
            // 
            this.gridColumn33.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn33.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn33.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn33.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn33.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn33.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn33.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn33.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn33, "gridColumn33");
            this.gridColumn33.FieldName = "CompanyNameAr";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn33.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn29
            // 
            this.gridColumn29.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn29.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn29, "gridColumn29");
            this.gridColumn29.FieldName = "LargeUOMFactor";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn29.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn29.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn28
            // 
            this.gridColumn28.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn28.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn28, "gridColumn28");
            this.gridColumn28.FieldName = "MediumUOMFactor";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn28.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn28.OptionsFilter.AllowFilter = false;
            // 
            // col_TotalSellPrice
            // 
            resources.ApplyResources(this.col_TotalSellPrice, "col_TotalSellPrice");
            this.col_TotalSellPrice.FieldName = "TotalSellPrice";
            this.col_TotalSellPrice.Name = "col_TotalSellPrice";
            this.col_TotalSellPrice.OptionsColumn.AllowEdit = false;
            this.col_TotalSellPrice.OptionsColumn.AllowFocus = false;
            this.col_TotalSellPrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalSellPrice.OptionsFilter.AllowFilter = false;
            this.col_TotalSellPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSellPrice.Summary"))))});
            // 
            // gridColumn23
            // 
            this.gridColumn23.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn23.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn23.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn23.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn23.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn23.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn23.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn23.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn23, "gridColumn23");
            this.gridColumn23.ColumnEdit = this.rep_vendors;
            this.gridColumn23.FieldName = "VendorId";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn23.OptionsFilter.AllowFilter = false;
            // 
            // rep_vendors
            // 
            resources.ApplyResources(this.rep_vendors, "rep_vendors");
            this.rep_vendors.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_vendors.Buttons"))))});
            this.rep_vendors.Name = "rep_vendors";
            this.rep_vendors.View = this.gridView5;
            // 
            // gridView5
            // 
            this.gridView5.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView5.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView5.Appearance.Row.Options.UseTextOptions = true;
            this.gridView5.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView5.OptionsView.EnableAppearanceOddRow = true;
            this.gridView5.OptionsView.ShowAutoFilterRow = true;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.OptionsView.ShowIndicator = false;
            // 
            // gridColumn39
            // 
            resources.ApplyResources(this.gridColumn39, "gridColumn39");
            this.gridColumn39.FieldName = "VendorId";
            this.gridColumn39.Name = "gridColumn39";
            // 
            // gridColumn40
            // 
            resources.ApplyResources(this.gridColumn40, "gridColumn40");
            this.gridColumn40.FieldName = "VenCode";
            this.gridColumn40.Name = "gridColumn40";
            // 
            // gridColumn41
            // 
            resources.ApplyResources(this.gridColumn41, "gridColumn41");
            this.gridColumn41.FieldName = "VenNameAr";
            this.gridColumn41.Name = "gridColumn41";
            // 
            // gridColumn42
            // 
            resources.ApplyResources(this.gridColumn42, "gridColumn42");
            this.gridColumn42.FieldName = "VenNameEn";
            this.gridColumn42.Name = "gridColumn42";
            // 
            // gridColumn43
            // 
            resources.ApplyResources(this.gridColumn43, "gridColumn43");
            this.gridColumn43.FieldName = "Credit";
            this.gridColumn43.Name = "gridColumn43";
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.ColumnEdit = this.repDiscountRatio;
            this.gridColumn1.FieldName = "DiscountRatio";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn1.OptionsFilter.AllowFilter = false;
            // 
            // repDiscountRatio
            // 
            resources.ApplyResources(this.repDiscountRatio, "repDiscountRatio");
            this.repDiscountRatio.Mask.EditMask = resources.GetString("repDiscountRatio.Mask.EditMask");
            this.repDiscountRatio.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repDiscountRatio.Mask.MaskType")));
            this.repDiscountRatio.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repDiscountRatio.Mask.UseMaskAsDisplayFormat")));
            this.repDiscountRatio.Name = "repDiscountRatio";
            // 
            // gridColumn2
            // 
            this.gridColumn2.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.ColumnEdit = this.repSpin;
            this.gridColumn2.FieldName = "DiscountValue";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn2.OptionsFilter.AllowFilter = false;
            // 
            // repSpin
            // 
            resources.ApplyResources(this.repSpin, "repSpin");
            this.repSpin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repSpin.Name = "repSpin";
            // 
            // gridColumn5
            // 
            this.gridColumn5.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn5.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn5.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn5.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.ColumnEdit = this.repSpin;
            this.gridColumn5.FieldName = "SellPrice";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn5.OptionsFilter.AllowFilter = false;
            // 
            // colPurchasePrice
            // 
            this.colPurchasePrice.AppearanceCell.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colPurchasePrice.AppearanceHeader.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colPurchasePrice, "colPurchasePrice");
            this.colPurchasePrice.ColumnEdit = this.repSpin;
            this.colPurchasePrice.FieldName = "PurchasePrice";
            this.colPurchasePrice.Name = "colPurchasePrice";
            this.colPurchasePrice.OptionsColumn.AllowEdit = false;
            this.colPurchasePrice.OptionsColumn.AllowFocus = false;
            this.colPurchasePrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colPurchasePrice.OptionsColumn.ReadOnly = true;
            this.colPurchasePrice.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.ColumnEdit = this.repSpin;
            this.gridColumn7.FieldName = "Qty";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn7.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.ColumnEdit = this.repUOM;
            this.gridColumn8.FieldName = "UOM";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn8.OptionsFilter.AllowFilter = false;
            // 
            // repUOM
            // 
            this.repUOM.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repUOM.Buttons"))))});
            this.repUOM.Name = "repUOM";
            resources.ApplyResources(this.repUOM, "repUOM");
            this.repUOM.View = this.gridView4;
            this.repUOM.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.repUOM_CustomDisplayText);
            // 
            // gridView4
            // 
            this.gridView4.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView4.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView4.Appearance.Row.Options.UseTextOptions = true;
            this.gridView4.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn16,
            this.gridColumn17});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowDetailButtons = false;
            this.gridView4.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            this.gridView4.OptionsView.ShowIndicator = false;
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "Index";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn16
            // 
            resources.ApplyResources(this.gridColumn16, "gridColumn16");
            this.gridColumn16.FieldName = "Factor";
            this.gridColumn16.Name = "gridColumn16";
            // 
            // gridColumn17
            // 
            resources.ApplyResources(this.gridColumn17, "gridColumn17");
            this.gridColumn17.FieldName = "Uom";
            this.gridColumn17.Name = "gridColumn17";
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.ColumnEdit = this.repItems;
            this.gridColumn10.FieldName = "ItemId";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn10.OptionsFilter.AllowFilter = false;
            // 
            // repItems
            // 
            this.repItems.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repItems.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.repItems.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repItems.Buttons"))))});
            this.repItems.DisplayMember = "ItemNameAr";
            this.repItems.ImmediatePopup = true;
            this.repItems.Name = "repItems";
            resources.ApplyResources(this.repItems, "repItems");
            this.repItems.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.repItems.ValueMember = "ItemId";
            this.repItems.View = this.repositoryItemGridLookUpEdit1View;
            this.repItems.Popup += new System.EventHandler(this.repItems_Popup);
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn24,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn4,
            this.col_CompanyNameAr,
            this.col_CategoryNameAr});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoSelectAllInEditor = false;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.UseIndicatorForSelection = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.BestFitMaxRowCount = 10;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // gridColumn24
            // 
            resources.ApplyResources(this.gridColumn24, "gridColumn24");
            this.gridColumn24.DisplayFormat.FormatString = "n2";
            this.gridColumn24.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn24.FieldName = "SellPrice";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // gridColumn12
            // 
            resources.ApplyResources(this.gridColumn12, "gridColumn12");
            this.gridColumn12.FieldName = "ItemNameEn";
            this.gridColumn12.Name = "gridColumn12";
            // 
            // gridColumn13
            // 
            resources.ApplyResources(this.gridColumn13, "gridColumn13");
            this.gridColumn13.FieldName = "ItemNameAr";
            this.gridColumn13.Name = "gridColumn13";
            // 
            // gridColumn14
            // 
            resources.ApplyResources(this.gridColumn14, "gridColumn14");
            this.gridColumn14.FieldName = "ItemCode1";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.FieldName = "ItemId";
            this.gridColumn15.Name = "gridColumn15";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "ItemCode2";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_CompanyNameAr
            // 
            resources.ApplyResources(this.col_CompanyNameAr, "col_CompanyNameAr");
            this.col_CompanyNameAr.FieldName = "CompanyNameAr";
            this.col_CompanyNameAr.Name = "col_CompanyNameAr";
            // 
            // col_CategoryNameAr
            // 
            resources.ApplyResources(this.col_CategoryNameAr, "col_CategoryNameAr");
            this.col_CategoryNameAr.FieldName = "CategoryNameAr";
            this.col_CategoryNameAr.Name = "col_CategoryNameAr";
            // 
            // gridColumn11
            // 
            this.gridColumn11.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.FieldName = "ItemCode2";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn11.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn31
            // 
            this.gridColumn31.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn31, "gridColumn31");
            this.gridColumn31.FieldName = "ItemCode1";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn31.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn38
            // 
            resources.ApplyResources(this.gridColumn38, "gridColumn38");
            this.gridColumn38.FieldName = "UomIndex";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn38.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn38.OptionsFilter.AllowFilter = false;
            // 
            // col_Expire
            // 
            this.col_Expire.AppearanceCell.Options.UseTextOptions = true;
            this.col_Expire.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Expire.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expire.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Expire.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Expire.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Expire.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expire.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Expire, "col_Expire");
            this.col_Expire.ColumnEdit = this.rep_expireDate;
            this.col_Expire.FieldName = "Expire";
            this.col_Expire.Name = "col_Expire";
            this.col_Expire.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Expire.OptionsFilter.AllowFilter = false;
            // 
            // rep_expireDate
            // 
            resources.ApplyResources(this.rep_expireDate, "rep_expireDate");
            this.rep_expireDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_expireDate.Buttons"))))});
            this.rep_expireDate.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.rep_expireDate.DisplayFormat.FormatString = "M-yyyy";
            this.rep_expireDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.rep_expireDate.EditFormat.FormatString = "M-yyyy";
            this.rep_expireDate.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.rep_expireDate.Mask.EditMask = resources.GetString("rep_expireDate.Mask.EditMask");
            this.rep_expireDate.Name = "rep_expireDate";
            this.rep_expireDate.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.rep_expireDate_CustomDisplayText);
            // 
            // col_Batch
            // 
            this.col_Batch.AppearanceCell.Options.UseTextOptions = true;
            this.col_Batch.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Batch.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Batch.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Batch.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Batch.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Batch.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Batch.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Batch, "col_Batch");
            this.col_Batch.FieldName = "Batch";
            this.col_Batch.Name = "col_Batch";
            this.col_Batch.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Batch.OptionsFilter.AllowFilter = false;
            // 
            // col_Height
            // 
            resources.ApplyResources(this.col_Height, "col_Height");
            this.col_Height.ColumnEdit = this.repSpin;
            this.col_Height.FieldName = "Height";
            this.col_Height.Name = "col_Height";
            this.col_Height.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Height.OptionsFilter.AllowFilter = false;
            // 
            // col_Width
            // 
            resources.ApplyResources(this.col_Width, "col_Width");
            this.col_Width.ColumnEdit = this.repSpin;
            this.col_Width.FieldName = "Width";
            this.col_Width.Name = "col_Width";
            this.col_Width.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Width.OptionsFilter.AllowFilter = false;
            // 
            // col_Length
            // 
            resources.ApplyResources(this.col_Length, "col_Length");
            this.col_Length.ColumnEdit = this.repSpin;
            this.col_Length.FieldName = "Length";
            this.col_Length.Name = "col_Length";
            this.col_Length.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Length.OptionsFilter.AllowFilter = false;
            // 
            // col_TotalQty
            // 
            resources.ApplyResources(this.col_TotalQty, "col_TotalQty");
            this.col_TotalQty.FieldName = "TotalQty";
            this.col_TotalQty.Name = "col_TotalQty";
            this.col_TotalQty.OptionsColumn.AllowEdit = false;
            this.col_TotalQty.OptionsColumn.AllowFocus = false;
            this.col_TotalQty.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalQty.OptionsFilter.AllowFilter = false;
            this.col_TotalQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_PiecesCount
            // 
            this.col_PiecesCount.AppearanceCell.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_PiecesCount.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PiecesCount.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_PiecesCount.AppearanceHeader.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_PiecesCount.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PiecesCount.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_PiecesCount, "col_PiecesCount");
            this.col_PiecesCount.ColumnEdit = this.repSpin;
            this.col_PiecesCount.FieldName = "PiecesCount";
            this.col_PiecesCount.Name = "col_PiecesCount";
            this.col_PiecesCount.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_PiecesCount.OptionsFilter.AllowFilter = false;
            // 
            // col_ItemDescription
            // 
            this.col_ItemDescription.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemDescription.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescription.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescription.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemDescription.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemDescription.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescription.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescription.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemDescription, "col_ItemDescription");
            this.col_ItemDescription.FieldName = "ItemDescription";
            this.col_ItemDescription.Name = "col_ItemDescription";
            this.col_ItemDescription.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemDescription.OptionsFilter.AllowFilter = false;
            // 
            // col_ItemDescriptionEn
            // 
            this.col_ItemDescriptionEn.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemDescriptionEn.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemDescriptionEn, "col_ItemDescriptionEn");
            this.col_ItemDescriptionEn.FieldName = "ItemDescriptionEn";
            this.col_ItemDescriptionEn.Name = "col_ItemDescriptionEn";
            this.col_ItemDescriptionEn.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemDescriptionEn.OptionsFilter.AllowFilter = false;
            // 
            // col_SalesTax
            // 
            resources.ApplyResources(this.col_SalesTax, "col_SalesTax");
            this.col_SalesTax.ColumnEdit = this.repSpin;
            this.col_SalesTax.FieldName = "SalesTax";
            this.col_SalesTax.Name = "col_SalesTax";
            this.col_SalesTax.OptionsColumn.AllowEdit = false;
            this.col_SalesTax.OptionsColumn.AllowFocus = false;
            this.col_SalesTax.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_SalesTax.OptionsFilter.AllowFilter = false;
            // 
            // col_DiscountRatio2
            // 
            resources.ApplyResources(this.col_DiscountRatio2, "col_DiscountRatio2");
            this.col_DiscountRatio2.ColumnEdit = this.repDiscountRatio;
            this.col_DiscountRatio2.FieldName = "DiscountRatio2";
            this.col_DiscountRatio2.Name = "col_DiscountRatio2";
            this.col_DiscountRatio2.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_DiscountRatio2.OptionsFilter.AllowFilter = false;
            // 
            // col_DiscountRatio3
            // 
            resources.ApplyResources(this.col_DiscountRatio3, "col_DiscountRatio3");
            this.col_DiscountRatio3.ColumnEdit = this.repDiscountRatio;
            this.col_DiscountRatio3.FieldName = "DiscountRatio3";
            this.col_DiscountRatio3.Name = "col_DiscountRatio3";
            this.col_DiscountRatio3.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_DiscountRatio3.OptionsFilter.AllowFilter = false;
            // 
            // col_Serial
            // 
            this.col_Serial.AppearanceCell.Options.UseTextOptions = true;
            this.col_Serial.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Serial.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Serial.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Serial, "col_Serial");
            this.col_Serial.FieldName = "Serial";
            this.col_Serial.Name = "col_Serial";
            this.col_Serial.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn25
            // 
            this.gridColumn25.ColumnEdit = this.repManufactureDate;
            this.gridColumn25.FieldName = "ManufactureDate";
            this.gridColumn25.Name = "gridColumn25";
            // 
            // repManufactureDate
            // 
            this.repManufactureDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repManufactureDate.Buttons"))))});
            this.repManufactureDate.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Buttons"))))});
            this.repManufactureDate.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repManufactureDate.Mask.UseMaskAsDisplayFormat")));
            this.repManufactureDate.Name = "repManufactureDate";
            // 
            // repExpireDate
            // 
            resources.ApplyResources(this.repExpireDate, "repExpireDate");
            this.repExpireDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repExpireDate.Buttons"))))});
            this.repExpireDate.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repExpireDate.Name = "repExpireDate";
            // 
            // repLocation
            // 
            resources.ApplyResources(this.repLocation, "repLocation");
            this.repLocation.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repLocation.Buttons"))))});
            this.repLocation.Name = "repLocation";
            this.repLocation.View = this.gridView3;
            // 
            // gridView3
            // 
            this.gridView3.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView3.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView3.Appearance.Row.Options.UseTextOptions = true;
            this.gridView3.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colDescription,
            this.colLocationNameAr,
            this.colLocationId});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // colDescription
            // 
            resources.ApplyResources(this.colDescription, "colDescription");
            this.colDescription.FieldName = "Description";
            this.colDescription.Name = "colDescription";
            // 
            // colLocationNameAr
            // 
            resources.ApplyResources(this.colLocationNameAr, "colLocationNameAr");
            this.colLocationNameAr.FieldName = "LocationNameAr";
            this.colLocationNameAr.Name = "colLocationNameAr";
            // 
            // colLocationId
            // 
            resources.ApplyResources(this.colLocationId, "colLocationId");
            this.colLocationId.FieldName = "LocationId";
            this.colLocationId.Name = "colLocationId";
            // 
            // repExpireDate_txt
            // 
            resources.ApplyResources(this.repExpireDate_txt, "repExpireDate_txt");
            this.repExpireDate_txt.Mask.EditMask = resources.GetString("repExpireDate_txt.Mask.EditMask");
            this.repExpireDate_txt.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repExpireDate_txt.Mask.MaskType")));
            this.repExpireDate_txt.Name = "repExpireDate_txt";
            // 
            // lkp_Customers
            // 
            resources.ApplyResources(this.lkp_Customers, "lkp_Customers");
            this.lkp_Customers.EnterMoveNextControl = true;
            this.lkp_Customers.MenuManager = this.barManager1;
            this.lkp_Customers.Name = "lkp_Customers";
            this.lkp_Customers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Customers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Customers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Customers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Customers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Customers.Properties.Buttons"))))});
            this.lkp_Customers.Properties.ImmediatePopup = true;
            this.lkp_Customers.Properties.NullText = resources.GetString("lkp_Customers.Properties.NullText");
            this.lkp_Customers.Properties.View = this.gridView1;
            this.lkp_Customers.EditValueChanged += new System.EventHandler(this.lkp_Customers_EditValueChanged);
            this.lkp_Customers.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn3,
            this.gridColumn6,
            this.gridColumn18});
            this.gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.EnableAppearanceOddRow = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // gridColumn19
            // 
            resources.ApplyResources(this.gridColumn19, "gridColumn19");
            this.gridColumn19.FieldName = "CustomerId";
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            resources.ApplyResources(this.gridColumn20, "gridColumn20");
            this.gridColumn20.FieldName = "CusCode";
            this.gridColumn20.Name = "gridColumn20";
            // 
            // gridColumn21
            // 
            resources.ApplyResources(this.gridColumn21, "gridColumn21");
            this.gridColumn21.FieldName = "CusNameAr";
            this.gridColumn21.Name = "gridColumn21";
            // 
            // gridColumn22
            // 
            resources.ApplyResources(this.gridColumn22, "gridColumn22");
            this.gridColumn22.FieldName = "CusNameEn";
            this.gridColumn22.Name = "gridColumn22";
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "City";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn6
            // 
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "Mobile";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // gridColumn18
            // 
            resources.ApplyResources(this.gridColumn18, "gridColumn18");
            this.gridColumn18.FieldName = "GroupId";
            this.gridColumn18.Name = "gridColumn18";
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Name = "labelControl19";
            // 
            // txt_Total
            // 
            resources.ApplyResources(this.txt_Total, "txt_Total");
            this.txt_Total.EnterMoveNextControl = true;
            this.txt_Total.Name = "txt_Total";
            this.txt_Total.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Total.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Total.Properties.ReadOnly = true;
            this.txt_Total.TabStop = false;
            // 
            // labelControl18
            // 
            resources.ApplyResources(this.labelControl18, "labelControl18");
            this.labelControl18.Name = "labelControl18";
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // txt_Remains
            // 
            this.txt_Remains.EnterMoveNextControl = true;
            resources.ApplyResources(this.txt_Remains, "txt_Remains");
            this.txt_Remains.Name = "txt_Remains";
            this.txt_Remains.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_Remains.Properties.Appearance.BackColor")));
            this.txt_Remains.Properties.Appearance.Options.UseBackColor = true;
            this.txt_Remains.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Remains.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Remains.Properties.Mask.EditMask = resources.GetString("txt_Remains.Properties.Mask.EditMask");
            this.txt_Remains.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Remains.Properties.Mask.MaskType")));
            this.txt_Remains.EditValueChanged += new System.EventHandler(this.txt_paid_EditValueChanged);
            this.txt_Remains.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_Remains.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txtNet
            // 
            resources.ApplyResources(this.txtNet, "txtNet");
            this.txtNet.EnterMoveNextControl = true;
            this.txtNet.Name = "txtNet";
            this.txtNet.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNet.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNet.Properties.ReadOnly = true;
            this.txtNet.TabStop = false;
            // 
            // lbl_remains
            // 
            resources.ApplyResources(this.lbl_remains, "lbl_remains");
            this.lbl_remains.Name = "lbl_remains";
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Name = "labelControl13";
            // 
            // lbl_Paid
            // 
            resources.ApplyResources(this.lbl_Paid, "lbl_Paid");
            this.lbl_Paid.Name = "lbl_Paid";
            // 
            // txt_paid
            // 
            this.txt_paid.EnterMoveNextControl = true;
            resources.ApplyResources(this.txt_paid, "txt_paid");
            this.txt_paid.Name = "txt_paid";
            this.txt_paid.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_paid.Properties.Appearance.BackColor")));
            this.txt_paid.Properties.Appearance.Options.UseBackColor = true;
            this.txt_paid.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_paid.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_paid.Properties.Mask.EditMask = resources.GetString("txt_paid.Properties.Mask.EditMask");
            this.txt_paid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_paid.Properties.Mask.MaskType")));
            this.txt_paid.EditValueChanged += new System.EventHandler(this.txt_paid_EditValueChanged);
            this.txt_paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_paid.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Name = "labelControl7";
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // btnAddCustomer
            // 
            resources.ApplyResources(this.btnAddCustomer, "btnAddCustomer");
            this.btnAddCustomer.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnAddCustomer.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddCustomer.Name = "btnAddCustomer";
            this.btnAddCustomer.TabStop = false;
            this.btnAddCustomer.Click += new System.EventHandler(this.btnAddCustomer_Click);
            // 
            // txtExpenses
            // 
            resources.ApplyResources(this.txtExpenses, "txtExpenses");
            this.txtExpenses.EnterMoveNextControl = true;
            this.txtExpenses.Name = "txtExpenses";
            this.txtExpenses.Properties.Appearance.Options.UseTextOptions = true;
            this.txtExpenses.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtExpenses.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtExpenses.Properties.Mask.EditMask = resources.GetString("txtExpenses.Properties.Mask.EditMask");
            this.txtExpenses.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtExpenses.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Name = "labelControl20";
            // 
            // groupControl1
            // 
            resources.ApplyResources(this.groupControl1, "groupControl1");
            this.groupControl1.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl1.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControl1.CaptionLocation = DevExpress.Utils.Locations.Right;
            this.groupControl1.Controls.Add(this.lkp_Drawers2);
            this.groupControl1.Controls.Add(this.labelControl17);
            this.groupControl1.Controls.Add(this.lkp_Drawers);
            this.groupControl1.Controls.Add(this.txt_PayAcc1_Paid);
            this.groupControl1.Controls.Add(this.labelControl28);
            this.groupControl1.Controls.Add(this.labelControl26);
            this.groupControl1.Controls.Add(this.txt_PayAcc2_Paid);
            this.groupControl1.Controls.Add(this.labelControl25);
            this.groupControl1.Controls.Add(this.txt_paid);
            this.groupControl1.Controls.Add(this.lbl_Paid);
            this.groupControl1.Controls.Add(this.txt_Remains);
            this.groupControl1.Controls.Add(this.lbl_remains);
            this.groupControl1.Name = "groupControl1";
            // 
            // lkp_Drawers2
            // 
            this.lkp_Drawers2.EnterMoveNextControl = true;
            resources.ApplyResources(this.lkp_Drawers2, "lkp_Drawers2");
            this.lkp_Drawers2.MenuManager = this.barManager1;
            this.lkp_Drawers2.Name = "lkp_Drawers2";
            this.lkp_Drawers2.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_Drawers2.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Drawers2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Drawers2.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Drawers2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Drawers2.Properties.Buttons"))))});
            this.lkp_Drawers2.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers2.Properties.Columns"), resources.GetString("lkp_Drawers2.Properties.Columns1"), ((int)(resources.GetObject("lkp_Drawers2.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Drawers2.Properties.Columns3"))), resources.GetString("lkp_Drawers2.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Drawers2.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Drawers2.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers2.Properties.Columns7"), resources.GetString("lkp_Drawers2.Properties.Columns8"))});
            this.lkp_Drawers2.Properties.DisplayMember = "AccountName";
            this.lkp_Drawers2.Properties.NullText = resources.GetString("lkp_Drawers2.Properties.NullText");
            this.lkp_Drawers2.Properties.ValueMember = "AccountId";
            this.lkp_Drawers2.EditValueChanged += new System.EventHandler(this.lkp_Drawers2_EditValueChanged);
            this.lkp_Drawers2.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txt_PayAcc1_Paid
            // 
            resources.ApplyResources(this.txt_PayAcc1_Paid, "txt_PayAcc1_Paid");
            this.txt_PayAcc1_Paid.EnterMoveNextControl = true;
            this.txt_PayAcc1_Paid.MenuManager = this.barManager1;
            this.txt_PayAcc1_Paid.Name = "txt_PayAcc1_Paid";
            this.txt_PayAcc1_Paid.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_PayAcc1_Paid.EditValueChanged += new System.EventHandler(this.txt_PayAcc1_Paid_EditValueChanged);
            this.txt_PayAcc1_Paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl28
            // 
            resources.ApplyResources(this.labelControl28, "labelControl28");
            this.labelControl28.Name = "labelControl28";
            // 
            // labelControl26
            // 
            resources.ApplyResources(this.labelControl26, "labelControl26");
            this.labelControl26.Name = "labelControl26";
            // 
            // txt_PayAcc2_Paid
            // 
            resources.ApplyResources(this.txt_PayAcc2_Paid, "txt_PayAcc2_Paid");
            this.txt_PayAcc2_Paid.EnterMoveNextControl = true;
            this.txt_PayAcc2_Paid.Name = "txt_PayAcc2_Paid";
            this.txt_PayAcc2_Paid.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_PayAcc2_Paid.EditValueChanged += new System.EventHandler(this.txt_PayAcc1_Paid_EditValueChanged);
            this.txt_PayAcc2_Paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Name = "labelControl25";
            // 
            // btn_AddMatrixItems
            // 
            resources.ApplyResources(this.btn_AddMatrixItems, "btn_AddMatrixItems");
            this.btn_AddMatrixItems.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btn_AddMatrixItems.Image = global::Pharmacy.Properties.Resources.add32;
            this.btn_AddMatrixItems.Name = "btn_AddMatrixItems";
            this.btn_AddMatrixItems.TabStop = false;
            this.btn_AddMatrixItems.Click += new System.EventHandler(this.btn_AddMatrixItems_Click);
            // 
            // labelControl40
            // 
            resources.ApplyResources(this.labelControl40, "labelControl40");
            this.labelControl40.Name = "labelControl40";
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl5.Appearance.Font")));
            this.labelControl5.Name = "labelControl5";
            // 
            // cmbPayMethod
            // 
            resources.ApplyResources(this.cmbPayMethod, "cmbPayMethod");
            this.cmbPayMethod.EnterMoveNextControl = true;
            this.cmbPayMethod.MenuManager = this.barManager1;
            this.cmbPayMethod.Name = "cmbPayMethod";
            this.cmbPayMethod.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbPayMethod.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.Appearance.BackColor")));
            this.cmbPayMethod.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("cmbPayMethod.Properties.Appearance.Font")));
            this.cmbPayMethod.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.Appearance.ForeColor")));
            this.cmbPayMethod.Properties.Appearance.Options.UseBackColor = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseFont = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseForeColor = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbPayMethod.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.cmbPayMethod.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.BackColor")));
            this.cmbPayMethod.Properties.AppearanceDisabled.Font = ((System.Drawing.Font)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.Font")));
            this.cmbPayMethod.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.ForeColor")));
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseFont = true;
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.cmbPayMethod.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.cmbPayMethod.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbPayMethod.Properties.Buttons"))))});
            this.cmbPayMethod.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items1"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items3"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items4"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items6"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items7"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items8"))))});
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Name = "labelControl12";
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Name = "labelControl14";
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Name = "labelControl16";
            // 
            // txtDiscountRatio
            // 
            resources.ApplyResources(this.txtDiscountRatio, "txtDiscountRatio");
            this.txtDiscountRatio.EnterMoveNextControl = true;
            this.txtDiscountRatio.Name = "txtDiscountRatio";
            this.txtDiscountRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtDiscountRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDiscountRatio.Properties.Mask.MaskType")));
            this.txtDiscountRatio.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txtDiscountRatio.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtDiscountRatio.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txtDiscountValue
            // 
            resources.ApplyResources(this.txtDiscountValue, "txtDiscountValue");
            this.txtDiscountValue.EnterMoveNextControl = true;
            this.txtDiscountValue.Name = "txtDiscountValue";
            this.txtDiscountValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtDiscountValue.Properties.Mask.EditMask = resources.GetString("txtDiscountValue.Properties.Mask.EditMask");
            this.txtDiscountValue.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtDiscountValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            this.txtDiscountValue.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txt_TaxValue
            // 
            resources.ApplyResources(this.txt_TaxValue, "txt_TaxValue");
            this.txt_TaxValue.EnterMoveNextControl = true;
            this.txt_TaxValue.Name = "txt_TaxValue";
            this.txt_TaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_TaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_TaxValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_TaxValue.Properties.Mask.EditMask = resources.GetString("txt_TaxValue.Properties.Mask.EditMask");
            this.txt_TaxValue.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_TaxValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txt_DeductTaxR
            // 
            resources.ApplyResources(this.txt_DeductTaxR, "txt_DeductTaxR");
            this.txt_DeductTaxR.EnterMoveNextControl = true;
            this.txt_DeductTaxR.Name = "txt_DeductTaxR";
            this.txt_DeductTaxR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DeductTaxR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DeductTaxR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_DeductTaxR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DeductTaxR.Properties.Mask.MaskType")));
            this.txt_DeductTaxR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txt_DeductTaxR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_DeductTaxR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txt_DeductTaxV
            // 
            resources.ApplyResources(this.txt_DeductTaxV, "txt_DeductTaxV");
            this.txt_DeductTaxV.EnterMoveNextControl = true;
            this.txt_DeductTaxV.Name = "txt_DeductTaxV";
            this.txt_DeductTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DeductTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DeductTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_DeductTaxV.Properties.Mask.EditMask = resources.GetString("txt_DeductTaxV.Properties.Mask.EditMask");
            this.txt_DeductTaxV.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_DeductTaxV.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            this.txt_DeductTaxV.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txt_AddTaxR
            // 
            resources.ApplyResources(this.txt_AddTaxR, "txt_AddTaxR");
            this.txt_AddTaxR.EnterMoveNextControl = true;
            this.txt_AddTaxR.Name = "txt_AddTaxR";
            this.txt_AddTaxR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AddTaxR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AddTaxR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AddTaxR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_AddTaxR.Properties.Mask.MaskType")));
            this.txt_AddTaxR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txt_AddTaxR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_AddTaxR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txt_AddTaxV
            // 
            resources.ApplyResources(this.txt_AddTaxV, "txt_AddTaxV");
            this.txt_AddTaxV.EnterMoveNextControl = true;
            this.txt_AddTaxV.Name = "txt_AddTaxV";
            this.txt_AddTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AddTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AddTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AddTaxV.Properties.Mask.EditMask = resources.GetString("txt_AddTaxV.Properties.Mask.EditMask");
            this.txt_AddTaxV.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_AddTaxV.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            this.txt_AddTaxV.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.AppearancePage.Header.Options.UseTextOptions = true;
            this.xtraTabControl1.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.page_AccInfo;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tabExtraData,
            this.page_AccInfo});
            // 
            // page_AccInfo
            // 
            this.page_AccInfo.Controls.Add(this.txt_Balance_After);
            this.page_AccInfo.Controls.Add(this.txt_Balance_Before);
            this.page_AccInfo.Controls.Add(this.txt_MaxCredit);
            this.page_AccInfo.Controls.Add(this.lbl_IsCredit_After);
            this.page_AccInfo.Controls.Add(this.lbl_IsCredit_Before);
            this.page_AccInfo.Controls.Add(this.lblBlncAftr);
            this.page_AccInfo.Controls.Add(this.labelControl21);
            this.page_AccInfo.Controls.Add(this.labelControl24);
            this.page_AccInfo.Name = "page_AccInfo";
            resources.ApplyResources(this.page_AccInfo, "page_AccInfo");
            // 
            // txt_Balance_After
            // 
            resources.ApplyResources(this.txt_Balance_After, "txt_Balance_After");
            this.txt_Balance_After.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Balance_After.Name = "txt_Balance_After";
            // 
            // txt_Balance_Before
            // 
            resources.ApplyResources(this.txt_Balance_Before, "txt_Balance_Before");
            this.txt_Balance_Before.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Balance_Before.Name = "txt_Balance_Before";
            // 
            // txt_MaxCredit
            // 
            resources.ApplyResources(this.txt_MaxCredit, "txt_MaxCredit");
            this.txt_MaxCredit.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_MaxCredit.Name = "txt_MaxCredit";
            // 
            // lbl_IsCredit_After
            // 
            resources.ApplyResources(this.lbl_IsCredit_After, "lbl_IsCredit_After");
            this.lbl_IsCredit_After.Name = "lbl_IsCredit_After";
            // 
            // lbl_IsCredit_Before
            // 
            resources.ApplyResources(this.lbl_IsCredit_Before, "lbl_IsCredit_Before");
            this.lbl_IsCredit_Before.Name = "lbl_IsCredit_Before";
            // 
            // lblBlncAftr
            // 
            resources.ApplyResources(this.lblBlncAftr, "lblBlncAftr");
            this.lblBlncAftr.Name = "lblBlncAftr";
            // 
            // labelControl21
            // 
            resources.ApplyResources(this.labelControl21, "labelControl21");
            this.labelControl21.Name = "labelControl21";
            // 
            // labelControl24
            // 
            resources.ApplyResources(this.labelControl24, "labelControl24");
            this.labelControl24.Name = "labelControl24";
            // 
            // tabExtraData
            // 
            this.tabExtraData.Controls.Add(this.txtScaleSerial);
            this.tabExtraData.Controls.Add(this.labelControl22);
            this.tabExtraData.Controls.Add(this.txtDestination);
            this.tabExtraData.Controls.Add(this.lblDestination);
            this.tabExtraData.Controls.Add(this.txtVehicleNumber);
            this.tabExtraData.Controls.Add(this.lblVehicleNumber);
            this.tabExtraData.Controls.Add(this.txtDriverName);
            this.tabExtraData.Controls.Add(this.lblDriverName);
            this.tabExtraData.Name = "tabExtraData";
            resources.ApplyResources(this.tabExtraData, "tabExtraData");
            // 
            // txtScaleSerial
            // 
            resources.ApplyResources(this.txtScaleSerial, "txtScaleSerial");
            this.txtScaleSerial.EnterMoveNextControl = true;
            this.txtScaleSerial.MenuManager = this.barManager1;
            this.txtScaleSerial.Name = "txtScaleSerial";
            this.txtScaleSerial.Properties.MaxLength = 190;
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Name = "labelControl22";
            // 
            // txtDestination
            // 
            resources.ApplyResources(this.txtDestination, "txtDestination");
            this.txtDestination.EnterMoveNextControl = true;
            this.txtDestination.MenuManager = this.barManager1;
            this.txtDestination.Name = "txtDestination";
            this.txtDestination.Properties.MaxLength = 190;
            // 
            // lblDestination
            // 
            resources.ApplyResources(this.lblDestination, "lblDestination");
            this.lblDestination.Name = "lblDestination";
            // 
            // txtVehicleNumber
            // 
            resources.ApplyResources(this.txtVehicleNumber, "txtVehicleNumber");
            this.txtVehicleNumber.EnterMoveNextControl = true;
            this.txtVehicleNumber.MenuManager = this.barManager1;
            this.txtVehicleNumber.Name = "txtVehicleNumber";
            this.txtVehicleNumber.Properties.MaxLength = 190;
            // 
            // lblVehicleNumber
            // 
            resources.ApplyResources(this.lblVehicleNumber, "lblVehicleNumber");
            this.lblVehicleNumber.Name = "lblVehicleNumber";
            // 
            // txtDriverName
            // 
            resources.ApplyResources(this.txtDriverName, "txtDriverName");
            this.txtDriverName.EnterMoveNextControl = true;
            this.txtDriverName.MenuManager = this.barManager1;
            this.txtDriverName.Name = "txtDriverName";
            this.txtDriverName.Properties.MaxLength = 190;
            // 
            // lblDriverName
            // 
            resources.ApplyResources(this.lblDriverName, "lblDriverName");
            this.lblDriverName.Name = "lblDriverName";
            // 
            // frm_SL_ReturnArchive
            // 
            this.Appearance.Options.UseTextOptions = true;
            this.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.xtraTabControl1);
            this.Controls.Add(this.cmbPayMethod);
            this.Controls.Add(this.chk_IsInTrns);
            this.Controls.Add(this.btnAddCustomer);
            this.Controls.Add(this.btn_AddMatrixItems);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.labelControl40);
            this.Controls.Add(this.txtNet);
            this.Controls.Add(this.labelControl19);
            this.Controls.Add(this.labelControl20);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.labelControl16);
            this.Controls.Add(this.lkp_Customers);
            this.Controls.Add(this.txt_Total);
            this.Controls.Add(this.labelControl18);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.labelControl13);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl14);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.labelControl36);
            this.Controls.Add(this.labelControl35);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrevious);
            this.Controls.Add(this.txtExpenses);
            this.Controls.Add(this.txtDiscountRatio);
            this.Controls.Add(this.txtDiscountValue);
            this.Controls.Add(this.txt_TaxValue);
            this.Controls.Add(this.txt_DeductTaxR);
            this.Controls.Add(this.txt_DeductTaxV);
            this.Controls.Add(this.txt_AddTaxR);
            this.Controls.Add(this.txt_AddTaxV);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_ReturnArchive";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_Return_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_Return_Load);
            this.Shown += new System.EventHandler(this.frm_SL_Return_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_SL_Return_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            this.flowLayoutPanel1.ResumeLayout(false);
            this.pnlBook.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_InvoiceBook.Properties)).EndInit();
            this.pnlInvCode.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).EndInit();
            this.pnlDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).EndInit();
            this.pnlBranch.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).EndInit();
            this.pnlCrncy.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).EndInit();
            this.pnlSalesEmp.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).EndInit();
            this.pnlCostCenter.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lkpCostCenter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            this.pnlSrcPrc.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtSourceCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnSourceId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmdProcess.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsInTrns.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_vendors)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDiscountRatio)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLocation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate_txt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Total.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Remains.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc1_Paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc2_Paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPayMethod.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.page_AccInfo.ResumeLayout(false);
            this.page_AccInfo.PerformLayout();
            this.tabExtraData.ResumeLayout(false);
            this.tabExtraData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtScaleSerial.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDestination.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVehicleNumber.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDriverName.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.TextEdit txtInvoiceCode;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrevious;
        private DevExpress.XtraBars.BarButtonItem batBtnList;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraEditors.LabelControl labelControl35;
        private DevExpress.XtraEditors.DateEdit dtInvoiceDate;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraEditors.MemoEdit txtNotes;
        
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraGrid.GridControl grdPrInvoice;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpin;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn colPurchasePrice;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repLocation;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn colDescription;
        private DevExpress.XtraGrid.Columns.GridColumn colLocationNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn colLocationId;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit repExpireDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repUOM;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repItems;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repExpireDate_txt;
        private DevExpress.XtraEditors.LookUpEdit lkp_Drawers;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSellPrice;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraEditors.GridLookUpEdit lkp_Customers;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_vendors;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.TextEdit txt_Total;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.TextEdit txt_Remains;
        private DevExpress.XtraEditors.TextEdit txtNet;
        private DevExpress.XtraEditors.LabelControl lbl_remains;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl lbl_Paid;
        private DevExpress.XtraEditors.TextEdit txt_paid;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repDiscountRatio;
        private DevExpress.XtraEditors.LookUpEdit lkpStore;
        private DevExpress.XtraEditors.SimpleButton btnAddCustomer;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_frm_IC_Item;
        private DevExpress.XtraEditors.SpinEdit txtExpenses;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraGrid.Columns.GridColumn col_Expire;
        private DevExpress.XtraGrid.Columns.GridColumn col_Batch;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit rep_expireDate;
        private DevExpress.XtraBars.BarButtonItem barbtnLoadSellInvoice;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LookUpEdit lkp_Drawers2;
        private DevExpress.XtraEditors.SpinEdit txt_PayAcc1_Paid;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.SpinEdit txt_PayAcc2_Paid;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.SimpleButton btn_AddMatrixItems;
        private DevExpress.XtraEditors.LabelControl labelControl40;
        private DevExpress.XtraGrid.Columns.GridColumn col_Length;
        private DevExpress.XtraGrid.Columns.GridColumn col_Width;
        private DevExpress.XtraGrid.Columns.GridColumn col_Height;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbPayMethod;
        private DevExpress.XtraEditors.TextEdit textEdit8;
        private DevExpress.XtraEditors.TextEdit textEdit7;
        private DevExpress.XtraEditors.TextEdit textEdit6;
        private DevExpress.XtraEditors.TextEdit textEdit5;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalQty;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn col_PiecesCount;
        private DevExpress.XtraEditors.CheckEdit chk_IsInTrns;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemDescription;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemDescriptionEn;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio2;
        private DevExpress.XtraGrid.Columns.GridColumn col_SalesTax;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio3;
        private DevExpress.XtraBars.BarSubItem barSubItem1;
        private DevExpress.XtraEditors.LookUpEdit lkp_InvoiceBook;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraEditors.TextEdit textEdit3;
        private DevExpress.XtraEditors.LookUpEdit lkp_SalesEmp;
        private DevExpress.XtraEditors.SpinEdit txtDiscountRatio;
        private DevExpress.XtraEditors.SpinEdit txtDiscountValue;
        private DevExpress.XtraEditors.SpinEdit txt_TaxValue;
        private DevExpress.XtraEditors.SpinEdit txt_DeductTaxR;
        private DevExpress.XtraEditors.SpinEdit txt_DeductTaxV;
        private DevExpress.XtraEditors.SpinEdit txt_AddTaxR;
        private DevExpress.XtraEditors.SpinEdit txt_AddTaxV;
        private System.Windows.Forms.ToolStripMenuItem mi_InvoiceStaticDisc;
        private DevExpress.XtraEditors.TextEdit txtCurrency;
        private uc_Currency uc_Currency1;
        private System.Windows.Forms.Panel pnlDate;
        private System.Windows.Forms.Panel pnlInvCode;
        private System.Windows.Forms.Panel pnlBook;
        private System.Windows.Forms.Panel pnlBranch;
        private System.Windows.Forms.Panel pnlSalesEmp;
        private System.Windows.Forms.Panel pnlCrncy;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.Panel pnlCostCenter;
        private DevExpress.XtraEditors.LookUpEdit lkpCostCenter;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private System.Windows.Forms.ToolStripMenuItem mi_InvoiceStaticDimensions;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage tabExtraData;
        private DevExpress.XtraEditors.TextEdit txtDestination;
        private DevExpress.XtraEditors.LabelControl lblDestination;
        private DevExpress.XtraEditors.TextEdit txtVehicleNumber;
        private DevExpress.XtraEditors.LabelControl lblVehicleNumber;
        private DevExpress.XtraEditors.TextEdit txtDriverName;
        private DevExpress.XtraEditors.LabelControl lblDriverName;
        private DevExpress.XtraTab.XtraTabPage page_AccInfo;
        private DevExpress.XtraEditors.LabelControl txt_Balance_After;
        private DevExpress.XtraEditors.LabelControl txt_Balance_Before;
        private DevExpress.XtraEditors.LabelControl txt_MaxCredit;
        private DevExpress.XtraEditors.LabelControl lbl_IsCredit_After;
        private DevExpress.XtraEditors.LabelControl lbl_IsCredit_Before;
        private DevExpress.XtraEditors.LabelControl lblBlncAftr;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private System.Windows.Forms.Panel pnlSrcPrc;
        private DevExpress.XtraEditors.TextEdit txtSourceCode;
        private DevExpress.XtraEditors.ButtonEdit btnSourceId;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmdProcess;
        private System.Windows.Forms.ToolStripMenuItem mi_PasteRows;
        private DevExpress.XtraGrid.Columns.GridColumn col_CompanyNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryNameAr;
        private DevExpress.XtraEditors.TextEdit txtScaleSerial;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial;
        private System.Windows.Forms.ToolStripMenuItem importFromExcelSheetToolStripMenuItem;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit repManufactureDate;
    }
}