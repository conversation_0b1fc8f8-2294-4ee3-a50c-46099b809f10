using System;
using System.Data;
using System.IO;
using ClosedXML.Excel;
using System.Linq;

namespace Pharmacy
{
    /// <summary>
    /// Helper class for Excel operations using ClosedXML
    /// Replaces ExcelDataReader functionality
    /// </summary>
    public static class ExcelHelper
    {
        /// <summary>
        /// Reads Excel file and returns DataTable
        /// Replaces the old exceldata() method that used OleDb
        /// </summary>
        /// <param name="filePath">Path to Excel file</param>
        /// <param name="worksheetName">Name of worksheet to read (optional, defaults to first worksheet)</param>
        /// <param name="hasHeaders">Whether first row contains headers</param>
        /// <returns>DataTable containing Excel data</returns>
        public static DataTable ReadExcelFile(string filePath, string worksheetName = null, bool hasHeaders = true)
        {
            try
            {
                using (var workbook = new XLWorkbook(filePath))
                {
                    IXLWorksheet worksheet;
                    
                    // Get the specified worksheet or the first one
                    if (!string.IsNullOrEmpty(worksheetName))
                    {
                        worksheet = workbook.Worksheet(worksheetName);
                    }
                    else
                    {
                        worksheet = workbook.Worksheet(1);
                    }

                    if (worksheet == null)
                        return null;

                    // Get the used range
                    var range = worksheet.RangeUsed();
                    if (range == null)
                        return new DataTable();

                    var dataTable = new DataTable();
                    
                    // Determine the starting row based on whether headers exist
                    int startRow = hasHeaders ? 2 : 1;
                    int headerRow = hasHeaders ? 1 : 0;

                    // Create columns
                    var firstRow = range.Row(headerRow > 0 ? headerRow : 1);
                    for (int col = 1; col <= range.ColumnCount(); col++)
                    {
                        string columnName;
                        if (hasHeaders && headerRow > 0)
                        {
                            columnName = firstRow.Cell(col).GetString();
                            if (string.IsNullOrEmpty(columnName))
                                columnName = $"Column{col}";
                        }
                        else
                        {
                            columnName = $"Column{col}";
                        }
                        
                        // Ensure unique column names
                        string uniqueColumnName = columnName;
                        int counter = 1;
                        while (dataTable.Columns.Contains(uniqueColumnName))
                        {
                            uniqueColumnName = $"{columnName}_{counter}";
                            counter++;
                        }
                        
                        dataTable.Columns.Add(uniqueColumnName, typeof(string));
                    }

                    // Add data rows
                    for (int row = startRow; row <= range.RowCount(); row++)
                    {
                        var dataRow = dataTable.NewRow();
                        bool hasData = false;
                        
                        for (int col = 1; col <= range.ColumnCount(); col++)
                        {
                            var cellValue = range.Cell(row, col).GetString();
                            dataRow[col - 1] = cellValue;
                            if (!string.IsNullOrEmpty(cellValue))
                                hasData = true;
                        }
                        
                        // Only add row if it contains data
                        if (hasData)
                            dataTable.Rows.Add(dataRow);
                    }

                    return dataTable;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error reading Excel file: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Reads Excel file and returns DataSet with all worksheets
        /// Replaces ExcelDataReader.AsDataSet() functionality
        /// </summary>
        /// <param name="filePath">Path to Excel file</param>
        /// <param name="hasHeaders">Whether first row contains headers</param>
        /// <returns>DataSet containing all worksheets</returns>
        public static DataSet ReadExcelFileAsDataSet(string filePath, bool hasHeaders = true)
        {
            try
            {
                var dataSet = new DataSet();
                
                using (var workbook = new XLWorkbook(filePath))
                {
                    foreach (var worksheet in workbook.Worksheets)
                    {
                        var dataTable = ReadWorksheetToDataTable(worksheet, hasHeaders);
                        dataTable.TableName = worksheet.Name;
                        dataSet.Tables.Add(dataTable);
                    }
                }

                return dataSet;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error reading Excel file as DataSet: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Helper method to convert worksheet to DataTable
        /// </summary>
        private static DataTable ReadWorksheetToDataTable(IXLWorksheet worksheet, bool hasHeaders)
        {
            var range = worksheet.RangeUsed();
            if (range == null)
                return new DataTable();

            var dataTable = new DataTable();
            
            // Determine the starting row based on whether headers exist
            int startRow = hasHeaders ? 2 : 1;
            int headerRow = hasHeaders ? 1 : 0;

            // Create columns
            var firstRow = range.Row(headerRow > 0 ? headerRow : 1);
            for (int col = 1; col <= range.ColumnCount(); col++)
            {
                string columnName;
                if (hasHeaders && headerRow > 0)
                {
                    columnName = firstRow.Cell(col).GetString();
                    if (string.IsNullOrEmpty(columnName))
                        columnName = $"Column{col}";
                }
                else
                {
                    columnName = $"Column{col}";
                }
                
                // Ensure unique column names
                string uniqueColumnName = columnName;
                int counter = 1;
                while (dataTable.Columns.Contains(uniqueColumnName))
                {
                    uniqueColumnName = $"{columnName}_{counter}";
                    counter++;
                }
                
                dataTable.Columns.Add(uniqueColumnName, typeof(string));
            }

            // Add data rows
            for (int row = startRow; row <= range.RowCount(); row++)
            {
                var dataRow = dataTable.NewRow();
                bool hasData = false;
                
                for (int col = 1; col <= range.ColumnCount(); col++)
                {
                    var cellValue = range.Cell(row, col).GetString();
                    dataRow[col - 1] = cellValue;
                    if (!string.IsNullOrEmpty(cellValue))
                        hasData = true;
                }
                
                // Only add row if it contains data
                if (hasData)
                    dataTable.Rows.Add(dataRow);
            }

            return dataTable;
        }

        /// <summary>
        /// Gets list of worksheet names from Excel file
        /// </summary>
        /// <param name="filePath">Path to Excel file</param>
        /// <returns>Array of worksheet names</returns>
        public static string[] GetWorksheetNames(string filePath)
        {
            try
            {
                using (var workbook = new XLWorkbook(filePath))
                {
                    var names = new string[workbook.Worksheets.Count];
                    for (int i = 0; i < workbook.Worksheets.Count; i++)
                    {
                        names[i] = workbook.Worksheets.ElementAt(i).Name;
                    }
                    return names;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting worksheet names: {ex.Message}", ex);
            }
        }
    }
}
