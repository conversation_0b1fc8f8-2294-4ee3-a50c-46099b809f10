﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="xrTableCell2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell2.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>رقم الفاتورة</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="xrTableCell2.Weight" type="System.Double, mscorlib">
    <value>0.60512154025705589</value>
  </data>
  <data name="lblCustomer.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lblCustomer.Text" xml:space="preserve">
    <value>lblCustomer</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lbl_notes.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>230.291412, 200.54184</value>
  </data>
  <data name="lbl_notes.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>214.203613, 45.9582825</value>
  </data>
  <data name="lbl_notes.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_User.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.207962, 119.499969</value>
  </data>
  <data name="lbl_User.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>122.583817, 24.4999847</value>
  </data>
  <data name="lbl_User.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLine2.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrLine2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 26.2083054</value>
  </data>
  <data name="xrLine2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>508.500031, 5</value>
  </data>
  <data name="xrTableCell4.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell4.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell4.Text" xml:space="preserve">
    <value>المندوب</value>
  </data>
  <data name="xrTableCell4.Weight" type="System.Double, mscorlib">
    <value>0.82211508621122764</value>
  </data>
  <data name="lbl_Number.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Number.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>212.5, 55</value>
  </data>
  <data name="lbl_Number.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>88.16629, 29.9999962</value>
  </data>
  <data name="lbl_Number.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>11.6249723</value>
  </data>
  <data name="xrLabel13.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel13.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>444.495026, 119.500069</value>
  </data>
  <data name="xrLabel13.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>76.5050354, 24.4999924</value>
  </data>
  <data name="xrLabel13.Text" xml:space="preserve">
    <value>تاريخ التسليم</value>
  </data>
  <data name="xrLabel13.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_SalesEmp.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.20797, 151.541779</value>
  </data>
  <data name="lbl_SalesEmp.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>122.583824, 24.4999847</value>
  </data>
  <data name="lbl_SalesEmp.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>444.495026, 95.0000153</value>
  </data>
  <data name="xrLabel7.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>76.50485, 24.4999847</value>
  </data>
  <data name="xrLabel7.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="xrLabel7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell1.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell1.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="xrTableCell1.Weight" type="System.Double, mscorlib">
    <value>0.54903909996986711</value>
  </data>
  <data name="xrLabel10.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel10.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>133.7918, 151.541809</value>
  </data>
  <data name="xrLabel10.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>91.61984, 24.4999542</value>
  </data>
  <data name="xrLabel10.Text" xml:space="preserve">
    <value>مندوب البيع</value>
  </data>
  <data name="xrLabel10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Emp.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_Emp.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Emp.Text" xml:space="preserve">
    <value>lbl_Emp</value>
  </data>
  <data name="xrLabel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 16pt, style=Bold</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>300.66626, 55.0000038</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>220.3338, 30.0000038</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>إذن تسليم لفاتورة بيع رقم</value>
  </data>
  <data name="xrLine1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.20797, 144.000061</value>
  </data>
  <data name="xrLine1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>509.792023, 2</value>
  </data>
  <data name="lbl_Shipping.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.20797, 200.54184</value>
  </data>
  <data name="lbl_Shipping.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>214.203613, 45.9582825</value>
  </data>
  <data name="lbl_Shipping.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel18.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel18.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>133.7918, 176.041733</value>
  </data>
  <data name="xrLabel18.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>91.61975, 24.49997</value>
  </data>
  <data name="xrLabel18.Text" xml:space="preserve">
    <value>شحن إلى</value>
  </data>
  <data name="xrLabel18.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>133.7918, 119.500069</value>
  </data>
  <data name="xrLabel6.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>91.61981, 24.4999847</value>
  </data>
  <data name="xrLabel6.Text" xml:space="preserve">
    <value>المستخدم</value>
  </data>
  <data name="xrLabel6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>444.495026, 176.0418</value>
  </data>
  <data name="xrLabel4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>76.5050354, 24.4999847</value>
  </data>
  <data name="xrLabel4.Text" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="xrLabel4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_store.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.20797, 95.0000153</value>
  </data>
  <data name="lbl_store.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>122.583824, 24.4999847</value>
  </data>
  <data name="lbl_store.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel8.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>133.7918, 95.0000458</value>
  </data>
  <data name="xrLabel8.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>91.61981, 24.4999847</value>
  </data>
  <data name="xrLabel8.Text" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="xrLabel8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Customer.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>230.291428, 151.541779</value>
  </data>
  <data name="lbl_Customer.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>214.2036, 24.4999847</value>
  </data>
  <data name="lbl_Customer.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>444.495026, 151.541779</value>
  </data>
  <data name="xrLabel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>76.5050354, 24.4999847</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="xrLabel1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="picLogo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.20797, 25</value>
  </data>
  <data name="lbl_date.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>333.369446, 94.99995</value>
  </data>
  <data name="lbl_date.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>111.12558, 24.4999847</value>
  </data>
  <data name="lbl_date.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>212.5, 25</value>
  </data>
  <data name="lblCompName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>306.619629, 30</value>
  </data>
  <data name="lblCompName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_DeliverDate.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>333.369446, 119.500069</value>
  </data>
  <data name="lbl_DeliverDate.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>111.125458, 24.4999924</value>
  </data>
  <data name="lbl_DeliverDate.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>246.500122</value>
  </data>
  <data name="lbl_StoreName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="lbl_StoreName.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="lbl_StoreName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>333.369446, 0</value>
  </data>
  <data name="lbl_StoreName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_InvoiceDate.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_InvoiceDate.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_InvoiceDate.Text" xml:space="preserve">
    <value>lbl_Date</value>
  </data>
  <data name="xrSubreport2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 38.499958</value>
  </data>
  <data name="xrSubreport2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>519.1196, 12.6666565</value>
  </data>
  <data name="xrTableCell3.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell3.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="xrTableCell3.Weight" type="System.Double, mscorlib">
    <value>1.0237242735618497</value>
  </data>
  <data name="lbl_BranchId.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="lbl_BranchId.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="lbl_BranchId.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>192.104919, 0.624974549</value>
  </data>
  <data name="lbl_BranchId.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_BranchId.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrTable1.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTable1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>684.7914, 31.208292</value>
  </data>
  <data name="xrTableRow2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="lbl_Code.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_Code.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Code.Text" xml:space="preserve">
    <value>lbl_Code</value>
  </data>
  <data name="xrTableRow2.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>73.20868, 19.9583263</value>
  </data>
  <data name="xrTable1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTable1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>52.62502</value>
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>26, 36, 247, 12</value>
  </data>
  <data name="$this.PageHeight" type="System.Int32, mscorlib">
    <value>827</value>
  </data>
  <data name="$this.PageWidth" type="System.Int32, mscorlib">
    <value>583</value>
  </data>
  <data name="$this.PaperKind" type="System.Drawing.Printing.PaperKind, System.Drawing">
    <value>A5</value>
  </data>
</root>