﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using ExcelDataReader;
using System.IO;

namespace Pharmacy.Forms
{
    public partial class frm_SL_ReturnList: DevExpress.XtraEditors.XtraForm
    {
        int customerId;
        DateTime dateFrom, dateTo;
        public bool IsOpenForSelect = false;
        public static int SelectedInvId = 0;
        public static string SelectedInvCode;
        bool Cars = false;
        public frm_SL_ReturnList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        public frm_SL_ReturnList(bool cars,bool iscars)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
            Cars = cars;
        }

        public frm_SL_ReturnList(int customerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.customerId = customerId;
        }

        public frm_SL_ReturnList(bool _IsOpenForSelection)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            IsOpenForSelect = _IsOpenForSelection;            
        }

        private void frm_SL_ReturnList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            if (Shared.SL_R_FromDate != null)
                dt1.DateTime = dateFrom = Shared.SL_R_FromDate.Value;
            else
            {
                dateFrom = Shared.minDate;
                dt1.EditValue = null;
            }

            if (Shared.SL_R_ToDate != null)
                dt2.DateTime = dateTo = Shared.SL_R_ToDate.Value;
            else
            {
                dateTo = Shared.maxDate;
                dt2.EditValue = null;
            }

            var view = grdCategory.FocusedView as GridView;
            if (dt1.EditValue == null && dt2.EditValue == null || customerId > 0 || view.RowCount < 1)
                GetInvoices();

            #region SalesEmp
            DataTable dt_SalesEmps = new DataTable();
            //DAL.MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            //rep_salesEmp.DataSource = dt_SalesEmps;
            rep_salesEmp.DisplayMember = "EmpName";
            rep_salesEmp.ValueMember = "EmpId";
            #endregion

            #region invoice bbok
            ERPDataContext DB = new ERPDataContext();
            //rep_InvoiceBook.DataSource = DB.ST_InvoiceBooks.Where(x => x.ProcessId == (int)DAL.Process.SellReturn)
            //    .Select(x => new { x.InvoiceBookId, x.InvoiceBookName }).ToList();
            rep_InvoiceBook.DisplayMember = "InvoiceBookName";
            rep_InvoiceBook.ValueMember = "InvoiceBookId";
            #endregion

            #region Currencies
            repCrncy.DataSource = Shared.lstCurrency;
            repCrncy.ValueMember = "CrncId";
            repCrncy.DisplayMember = "crncName";
            #endregion

            if (Shared.InvoicePostToStore)
                col_Is_InTrans.Visible = false;

            ErpUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grdCategory);
            ErpUtils.Tab_Enter_Process(grdCategory);
            LoadPrivilege();
        }       


        private void frm_SL_ReturnList_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (dt1.EditValue != null)
                Shared.SL_R_FromDate = dateFrom;
            else
                Shared.SL_R_FromDate = null;

            if (dt2.EditValue != null)
                Shared.SL_R_ToDate = dateTo;
            else
                Shared.SL_R_ToDate = null;

            ErpUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }

        private void frm_SL_ReturnList_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                dt1.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                grdCategory.Focus();
            }
        }


        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Shared.CarsAvailable == false)
            {
                if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
                    Application.OpenForms["frm_SL_Return"].Close();

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
                    Application.OpenForms["frm_SL_Return"].BringToFront();
                else
                    new frm_SL_Return().Show();
            }
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GetInvoices();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_Invoice();
        }


        private void grdCategory_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_Invoice();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void NBI_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            if (((NavBarItem)sender).Name == "NBI_Customers")
            {
                frmMain.OpenSL_Customer();
            }

            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int strId = Convert.ToInt32(view.GetFocusedRowCellValue(colStore));
            string strName = view.GetFocusedRowCellDisplayText(col_StoreId).ToString();
            string strFltr = (Shared.IsEnglish == true ? ResSLEn.txtStore : ResSLAr.txtStore)//"المخزن: " 
                + strName;
                        
          
        }

        private void dt1_EditValueChanged(object sender, EventArgs e)
        {
            if (dateFrom != DateTime.MinValue && dateTo != DateTime.MinValue)
            {
                if (dt1.DateTime != DateTime.MinValue)
                    dateFrom = dt1.DateTime;
                else
                    dateFrom = Shared.minDate;

                if (dt2.DateTime != DateTime.MinValue)
                    dateTo = dt2.DateTime;
                else
                {
                    dateTo = Shared.maxDate;
                }

            }
        }

        private void btnClearSearch_Click(object sender, EventArgs e)
        {
            dateFrom = Shared.minDate;
            dateTo = Shared.maxDate;
            dt1.EditValue = null;
            dt2.EditValue = null;
        }


        private void GetInvoices()
        {
            int focusedIndex = (grdCategory.FocusedView as GridView).FocusedRowHandle;

            DAL.ERPDataContext pharm = new DAL.ERPDataContext();
            var Invoices = (from c in pharm.SL_Returns
                            //join d in pharm.SL_ReturnDetails on c.SL_ReturnId equals d.SL_ReturnId
                            join v in pharm.SL_Customers on c.CustomerId equals v.CustomerId
                            join s in pharm.IC_Stores on c.StoreId equals s.StoreId
                            where customerId == 0 ? true : c.CustomerId == customerId
                            where c.ReturnDate.Date >= dateFrom && c.ReturnDate.Date <= dateTo
                            where Shared.user.UserChangeStore ? true : pharm.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                     x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(c.StoreId)// c.StoreId == Shared.user.DefaultStore
                            where Shared.user.AccessOtherUserTrns ? true : c.UserId == Shared.UserId                            
                            where Shared.E_invoiceAvailable ? c.InvoiceBookId != null :true
                            let CustomerCategory = pharm.SL_CustomerGroups
                            let invoiceDetailIds = pharm.SL_ReturnDetails.Where(a => a.SL_ReturnId == c.SL_ReturnId).Select(a => a.SL_ReturnDetailId).ToList()
                            let TaxIdDiscount = pharm.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault()
                            let TotalTaxesAddedList = (from r in pharm.SlReturnInvoiceDetailSubTaxValues
                                                       join rd in pharm.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                       where invoiceDetailIds.Contains(r.ReturnInvoiceDetailId)
                                                       where rd.ParentTaxId != TaxIdDiscount.E_TaxableTypeId
                                                       select r).ToList()
                            let TotalTaxesRemovedList = (from r in pharm.SlReturnInvoiceDetailSubTaxValues
                                                         join rd in pharm.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                         where invoiceDetailIds.Contains(r.ReturnInvoiceDetailId)
                                                         where rd.ParentTaxId == TaxIdDiscount.E_TaxableTypeId
                                                         select r).ToList()
                            let TotaltaxesAddValue = TotalTaxesAddedList.Count != 0 ? Convert.ToDouble(TotalTaxesAddedList.Sum(z => z.value)) : 0
                            let TotaltaxesRemovedValue = TotalTaxesRemovedList.Count != 0 ? Convert.ToDouble(TotalTaxesRemovedList.Sum(z => z.value)) : 0
                            // let Totaltaxes = pharm.SlReturnInvoiceDetailSubTaxValues
                            // .Where(a => pharm.SL_ReturnDetails.Where(v => v.SL_ReturnId == c.SL_ReturnId).ToList().Select(v => v.SL_ReturnDetailId).Contains(a.ReturnInvoiceDetailId)).ToList()
                            //let CustGroup = pharm.SL_Group_Customers
                            orderby c.ReturnDate
                            select new
                            {
                                DiscountRatio = decimal.ToDouble(c.DiscountRatio),
                                DiscountValue = decimal.ToDouble(c.DiscountValue),
                                Expenses = decimal.ToDouble(c.Expenses),
                                Net = decimal.ToDouble(c.Net),
                                Paid = decimal.ToDouble(c.Paid),
                                Remains = decimal.ToDouble(c.Remains),
                                c.ReturnCode,
                                c.ReturnDate,
                                c.JornalId,
                                c.Notes,
                                c.PayMethod,
                                c.SL_ReturnId,
                                StoreId = s.StoreNameAr,
                                Store = s.StoreId,
                                c.UserId,
                                CustomerId = v.CusNameAr,
                                CustId = v.CustomerId,
                                c.Is_InTrans,
                                c.SalesEmpId,
                                c.AddTaxValue,
                                c.DeductTaxValue,
                                c.TaxValue,
                                c.InvoiceBookId,
                                c.CrncId,
                                c.CrncRate,
                                c.DriverName,
                                c.VehicleNumber,
                                c.Destination,
                                CustCat = CustomerCategory.Where(x => x.CustomerGroupId == v.CategoryId).Select(x => Shared.IsEnglish ? x.CGNameEn : x.CGNameAr).FirstOrDefault(),
                                Totaltaxes = TotaltaxesAddValue - TotaltaxesRemovedValue
                                //  Totaltaxes = Totaltaxes.Count != 0 ? Convert.ToDouble(Totaltaxes.Sum(z => z.value)) : 0
                                //mohammad 10/11/2019
                                //CustomerGroup = CustGroup.Where(x => x.GroupId == v.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault()
                            }).ToList();

            grdCategory.DataSource = Invoices;
            (grdCategory.FocusedView as GridView).FocusedRowHandle = focusedIndex;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    NBI_Customers.Enabled = false;
                    mi_OpenDealer.Enabled = false;
                }

                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Return).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void Open_Selected_Invoice()
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_SL_ReturnId));
            if (Cars == false)
            {
                if (IsOpenForSelect == true)
                {
                    SelectedInvId = inv_id;
                    SelectedInvCode = view.GetRowCellValue(focused_row_index, col_ReturnCode).ToString();
                    this.Close();
                    return;
                }
                else
                {
                    if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
                        Application.OpenForms["frm_SL_Return"].Close();

                    if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
                        Application.OpenForms["frm_SL_Return"].BringToFront();
                    else
                        new frm_SL_Return(inv_id).Show();
                }
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مردود مبيعات جديدة");
        }

        private void grdCategory_Click(object sender, EventArgs e)
        {

        }

        private void btn_ImportSl_Return_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();
            DataTable dtUOM = new DataTable();
            List<DAL.IC_UOM> uom_list;
            MyHelper.GetUomDataTable(dtUOM);
            uom_list = DB.IC_UOMs.ToList();

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();


                    int count = 1;
                    string invCode = "";
                    int invId = 0;
                    decimal totalInvoice = 0;
                    bool newInvoice = true;
                    int index = 1;
                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;

                            if (result.Tables[0].Rows.IndexOf(d) == 0) { continue; }

                            if (!newInvoice && invCode != "" && invCode != Convert.ToString(d[0]))
                            {
                                frm_SL_Return frmsl = new frm_SL_Return();
                                frmsl.Show();
                                frmsl.invoiceId = invId;
                                frmsl.LoadInvoice();
                                frmsl.Save_Invoice();
                                frmsl.Close();
                                newInvoice = true;
                                totalInvoice = 0;
                            }

                            if (newInvoice || invCode != Convert.ToString(d[0]))
                            {
                                SL_Return inv = new SL_Return();
                                // invoice Code
                                inv.ReturnCode = invCode = Convert.ToString(d[0]);
                                //Check Invoice Code
                                var invoiceIds = DB.SL_Returns.Where(x => x.ReturnCode == inv.ReturnCode).Select(x => x.SL_ReturnId).ToList();
                                if (invoiceIds.Count > 0)
                                {
                                    XtraMessageBox.Show(
                                         string.Format(string.Format(" كود الفاتورة {0}  موجود", Convert.ToString(d[0])))
                                         , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                                // Customer
                                int csId = DB.SL_Customers.Where(x => x.CusNameAr == Convert.ToString(d[1])).Select(x => x.CustomerId).FirstOrDefault();
                                if (csId > 0)
                                {
                                    inv.CustomerId = csId;
                                }
                                else
                                {

                                    XtraMessageBox.Show(
                                        string.Format(string.Format("العميل {0} غير موجود", Convert.ToString(d[1])))
                                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                                // Invoice Date
                                if (string.IsNullOrEmpty(Convert.ToString(d[2])))
                                    inv.ReturnDate = MyHelper.Get_Server_DateTime();
                                else inv.ReturnDate = Convert.ToDateTime(Convert.ToString(d[2]));
                                // Store
                                int storeId = DB.IC_Stores.Where(x => x.StoreNameAr == Convert.ToString(d[3])).Select(x => x.StoreId).FirstOrDefault();
                                if (storeId > 0)
                                {
                                    inv.StoreId = storeId;
                                }
                                else
                                {

                                    XtraMessageBox.Show(
                                        string.Format(string.Format("المخزن/الفرع {0} غير موجود", Convert.ToString(d[3])))
                                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                                // CostCenter
                                if (!string.IsNullOrEmpty(Convert.ToString(d[4])))
                                {
                                    int ccId = DB.ACC_CostCenters.Where(x => x.CostCenterName == Convert.ToString(d[4])).Select(x => x.CostCenterId).FirstOrDefault();
                                    if (ccId > 0)
                                    {
                                        inv.CostCenterId = ccId;
                                    }
                                }
                                // Currency
                                if (!string.IsNullOrEmpty(Convert.ToString(d[5])))
                                {
                                    inv.CrncId = DB.ST_Currencies.Where(c => c.crncName == Convert.ToString(d[5])).Select(c => c.CrncId).FirstOrDefault();

                                }
                                else { inv.CrncId = 0; }
                                // Currency rate
                                if (!string.IsNullOrEmpty(Convert.ToString(d[6])))
                                {
                                    inv.CrncRate = Convert.ToDecimal(d[6]);
                                }
                                else { inv.CrncId = 1; }

                                // Paid
                                if (!string.IsNullOrEmpty(Convert.ToString(d[7])))
                                {
                                    inv.Paid = Convert.ToDecimal(d[7]);
                                }
                                else
                                {
                                    inv.Paid = 0;
                                }
                                // Discount per invoice
                                if (!string.IsNullOrEmpty(Convert.ToString(d[8])))
                                {
                                    inv.DiscountValue = Convert.ToDecimal(d[8]);
                                }
                                else
                                {
                                    inv.DiscountValue = 0;
                                }
                                // Discount per invoice
                                if (!string.IsNullOrEmpty(Convert.ToString(d[8])))
                                {
                                    inv.DiscountValue = Convert.ToDecimal(d[8]);
                                }
                                else
                                {
                                    inv.DiscountValue = 0;
                                }
                                // Net
                                //if (!string.IsNullOrEmpty(Convert.ToString(d[9])))
                                //{
                                //    inv.Net = Convert.ToDecimal(d[9]);
                                //}
                                //else
                                //{
                                inv.Net = (decimal)(from DataRow r in result.Tables[0].Rows
                                                    where r[0] == d[0]
                                                    select Convert.ToDouble(r[9])).Sum();

                                //}

                                inv.JornalId = 0;
                                newInvoice = false;

                                DB.SL_Returns.InsertOnSubmit(inv);
                                DB.SubmitChanges();
                                invId = inv.SL_ReturnId;
                            }

                            {
                                if (string.IsNullOrEmpty(Convert.ToString(d[10]))) continue;

                                IC_Item Item = DB.IC_Items.Where(x => x.ItemNameAr == Convert.ToString(d[10])).FirstOrDefault();
                                if (Item != null)
                                {
                                    SL_ReturnDetail detail = new SL_ReturnDetail();
                                    detail.SL_ReturnId = invId;
                                    detail.ItemId = Item.ItemId;
                                    detail.UOMIndex = Item.DfltSellUomIndx;
                                    if (string.IsNullOrEmpty(Convert.ToString(d[11])))
                                    {
                                        MyHelper.GetUOMs(Item, dtUOM, uom_list);
                                        detail.UOMId = Convert.ToInt32(dtUOM.Rows[Item.DfltSellUomIndx]["UomId"]);
                                    }
                                    else
                                    {
                                        detail.UOMId = DB.IC_UOMs.Where(i => i.UOM == Convert.ToString(d[11])).Select(x => x.UOMId).FirstOrDefault();
                                    }
                                    if (string.IsNullOrEmpty(Convert.ToString(d[12])) || Convert.ToString(d[12]) == "0") continue;
                                    detail.Qty = Convert.ToDecimal(d[12]);

                                    detail.Length = string.IsNullOrEmpty(Convert.ToString(d[13])) ? Item.Length : decimal.Parse(Convert.ToString(d[13]));
                                    detail.Width = string.IsNullOrEmpty(Convert.ToString(d[14])) ? Item.Width : decimal.Parse(Convert.ToString(d[14]));
                                    detail.Height = string.IsNullOrEmpty(Convert.ToString(d[15])) ? Item.Height : decimal.Parse(Convert.ToString(d[15]));

                                    detail.SellPrice = string.IsNullOrEmpty(Convert.ToString(d[16])) ? 0 : decimal.Parse(Convert.ToString(d[16]));

                                    #region Discount on Item
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[17])))
                                    {
                                        detail.DiscountRatio = string.IsNullOrEmpty(Convert.ToString(d[17])) ? 0 : decimal.Parse(Convert.ToString(d[17]));
                                        detail.DiscountValue = (detail.SellPrice * detail.Qty * detail.DiscountRatio) / 100;

                                    }

                                    if (d[18] != DBNull.Value && d[18] != null &&
                                        (!string.IsNullOrEmpty(Convert.ToString(d[18])) || Convert.ToString(d[18]) != "0"))
                                    {
                                        detail.DiscountValue = decimal.Parse(Convert.ToString(d[18]));
                                        detail.DiscountRatio = ((detail.DiscountValue * 100) / (detail.SellPrice * detail.Qty)) / 100;

                                    }
                                    if (Convert.ToString(d[18]) == "0" && Convert.ToString(d[17]) == "0")
                                    {
                                        detail.DiscountRatio = 0;
                                        detail.DiscountValue = 0;
                                    }
                                    #endregion
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[19])))
                                    {
                                        detail.bonusDiscount = decimal.Parse(Convert.ToString(d[19]));
                                    }
                                    //public static void CalcTotalPrice(int RowHandle, GridView view, decimal TotalQty, decimal SellPrice,
                                    //decimal SalesTaxRatio, decimal CustomTaxRatio, bool calcTaxBeforeDisc, decimal DiscV, decimal EtaxRatio)
                                    detail.TotalSellPrice = string.IsNullOrEmpty(Convert.ToString(d[9])) ? 0 : decimal.Parse(Convert.ToString(d[9]));

                                    totalInvoice += detail.TotalSellPrice;// (detail.SellPrice * detail.Qty) - detail.DiscountValue;
                                    //var invoice = DB.SL_Invoices.FirstOrDefault(a => a.SL_InvoiceId == invId);
                                    //invoice.Net = totalInvoice;
                                    DB.SL_ReturnDetails.InsertOnSubmit(detail);
                                    DB.SubmitChanges();

                                    #region Tax on Item
                                    int ccount = 20;
                                    while (result.Tables[0].Columns.Count > ccount)
                                    {
                                        if (string.IsNullOrEmpty(Convert.ToString(d[ccount])))
                                        {
                                            ccount++;
                                            continue;
                                        }
                                        else
                                        {
                                            string subt = Convert.ToString(d[ccount]);
                                            var subtaxId = DB.E_TaxableTypes.Where(s => s.Code == subt).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                                            if (subtaxId > 0)
                                            {
                                                SlReturnInvoiceDetailSubTaxValue subtax = new SlReturnInvoiceDetailSubTaxValue();
                                                subtax.ReturnInvoiceDetailId = detail.SL_ReturnDetailId;
                                                subtax.esubTypeId = subtaxId;
                                                ccount++;

                                                subtax.TaxRatio = string.IsNullOrEmpty(Convert.ToString(d[ccount])) ? 0 : decimal.Parse(Convert.ToString(d[ccount]));
                                                ccount++;
                                                subtax.value = string.IsNullOrEmpty(Convert.ToString(d[ccount])) ? 0 : decimal.Parse(Convert.ToString(d[ccount]));
                                                DB.SlReturnInvoiceDetailSubTaxValues.InsertOnSubmit(subtax);
                                                DB.SubmitChanges();
                                                ccount++;
                                            }
                                            else break;

                                        }
                                    }

                                    #endregion

                                    if (index == result.Tables[0].Rows.Count - 1)
                                    {
                                        frm_SL_Return frmsl = new frm_SL_Return();
                                        frmsl.Show();
                                        frmsl.invoiceId = invId;
                                        frmsl.LoadInvoice();
                                        frmsl.Save_Invoice();
                                        frmsl.Close();
                                        newInvoice = true;
                                        totalInvoice = 0;
                                    }
                                    index++;
                                }
                                else
                                {
                                    XtraMessageBox.Show(
                                  string.Format(string.Format("الصنف {0} غير موجود", Convert.ToString(d[10])))
                                  , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الفواتير")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الفواتير بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }

        private void mi_OpenDealer_Click(object sender, EventArgs e)
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int DealerId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_CustId));

            if (ErpUtils.IsFormOpen(typeof(frm_SL_Customer)))
                Application.OpenForms["frm_SL_Customer"].Close();

            new frm_SL_Customer(DealerId).Show();
        }
    }
}