﻿namespace Reports
{
    partial class frm_SubTaxNet
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SubTaxNet));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdSubTaxNet = new DevExpress.XtraGrid.GridControl();
            this.gv_SubTaxNet = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.SubTaxAddInvouce = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SubTaxReturnInvouce = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_subTaxParent = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SubTaxSlInvouce = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_subTaxName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_subTaxID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Net = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_MtrxParent = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_cat = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_comp = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_mtrx = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Vendor = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemMemoEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdSubTaxNet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SubTaxNet)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_MtrxParent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_cat)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_comp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_mtrx)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPreview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnPreview
            // 
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Preview_ItemClick);
            // 
            // barBtnRefresh
            // 
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdSubTaxNet
            // 
            resources.ApplyResources(this.grdSubTaxNet, "grdSubTaxNet");
            this.grdSubTaxNet.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdSubTaxNet.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdSubTaxNet.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdSubTaxNet.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdSubTaxNet.EmbeddedNavigator.Anchor")));
            this.grdSubTaxNet.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdSubTaxNet.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdSubTaxNet.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdSubTaxNet.EmbeddedNavigator.ImeMode")));
            this.grdSubTaxNet.EmbeddedNavigator.Margin = ((System.Windows.Forms.Padding)(resources.GetObject("grdSubTaxNet.EmbeddedNavigator.Margin")));
            this.grdSubTaxNet.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdSubTaxNet.EmbeddedNavigator.TextLocation")));
            this.grdSubTaxNet.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdSubTaxNet.EmbeddedNavigator.ToolTipIconType")));
            this.grdSubTaxNet.MainView = this.gv_SubTaxNet;
            this.grdSubTaxNet.Name = "grdSubTaxNet";
            this.grdSubTaxNet.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_MtrxParent,
            this.rep_cat,
            this.rep_comp,
            this.rep_mtrx,
            this.rep_Vendor,
            this.repositoryItemMemoEdit1});
            this.grdSubTaxNet.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_SubTaxNet});
            // 
            // gv_SubTaxNet
            // 
            this.gv_SubTaxNet.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gv_SubTaxNet.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_SubTaxNet.Appearance.FooterPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gv_SubTaxNet.Appearance.FooterPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gv_SubTaxNet.Appearance.GroupFooter.Options.UseTextOptions = true;
            this.gv_SubTaxNet.Appearance.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_SubTaxNet.Appearance.GroupFooter.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gv_SubTaxNet.Appearance.GroupFooter.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gv_SubTaxNet.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gv_SubTaxNet.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gv_SubTaxNet.Appearance.GroupRow.Options.UseTextOptions = true;
            this.gv_SubTaxNet.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gv_SubTaxNet.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gv_SubTaxNet.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_SubTaxNet.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gv_SubTaxNet.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gv_SubTaxNet.Appearance.Row.Options.UseTextOptions = true;
            this.gv_SubTaxNet.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gv_SubTaxNet.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gv_SubTaxNet.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.FooterPanel.BackColor")));
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.FooterPanel.BorderColor")));
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("gv_SubTaxNet.AppearancePrint.FooterPanel.Font")));
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.FooterPanel.ForeColor")));
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.Options.UseBackColor = true;
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.Options.UseFont = true;
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.Options.UseTextOptions = true;
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gv_SubTaxNet.AppearancePrint.FooterPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gv_SubTaxNet.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.GroupFooter.BorderColor")));
            this.gv_SubTaxNet.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.GroupFooter.ForeColor")));
            this.gv_SubTaxNet.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gv_SubTaxNet.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gv_SubTaxNet.AppearancePrint.GroupFooter.Options.UseTextOptions = true;
            this.gv_SubTaxNet.AppearancePrint.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_SubTaxNet.AppearancePrint.GroupFooter.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gv_SubTaxNet.AppearancePrint.GroupFooter.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gv_SubTaxNet.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.GroupRow.BorderColor")));
            this.gv_SubTaxNet.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.GroupRow.ForeColor")));
            this.gv_SubTaxNet.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gv_SubTaxNet.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.HeaderPanel.BackColor")));
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.HeaderPanel.BorderColor")));
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gv_SubTaxNet.AppearancePrint.HeaderPanel.Font")));
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.HeaderPanel.ForeColor")));
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.Options.UseBackColor = true;
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gv_SubTaxNet.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gv_SubTaxNet.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.Lines.BackColor")));
            this.gv_SubTaxNet.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.Lines.ForeColor")));
            this.gv_SubTaxNet.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gv_SubTaxNet.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gv_SubTaxNet.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.Row.BorderColor")));
            this.gv_SubTaxNet.AppearancePrint.Row.Font = ((System.Drawing.Font)(resources.GetObject("gv_SubTaxNet.AppearancePrint.Row.Font")));
            this.gv_SubTaxNet.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_SubTaxNet.AppearancePrint.Row.ForeColor")));
            this.gv_SubTaxNet.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gv_SubTaxNet.AppearancePrint.Row.Options.UseFont = true;
            this.gv_SubTaxNet.AppearancePrint.Row.Options.UseForeColor = true;
            this.gv_SubTaxNet.AppearancePrint.Row.Options.UseTextOptions = true;
            this.gv_SubTaxNet.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gv_SubTaxNet.ColumnPanelRowHeight = 55;
            this.gv_SubTaxNet.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.SubTaxAddInvouce,
            this.col_SubTaxReturnInvouce,
            this.col_subTaxParent,
            this.col_SubTaxSlInvouce,
            this.col_subTaxName,
            this.col_subTaxID,
            this.Net});
            this.gv_SubTaxNet.CustomizationFormBounds = new System.Drawing.Rectangle(730, 324, 208, 308);
            this.gv_SubTaxNet.GridControl = this.grdSubTaxNet;
            this.gv_SubTaxNet.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gv_SubTaxNet.GroupSummary"))), resources.GetString("gv_SubTaxNet.GroupSummary1"), this.col_SubTaxSlInvouce, resources.GetString("gv_SubTaxNet.GroupSummary2")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gv_SubTaxNet.GroupSummary3"))), resources.GetString("gv_SubTaxNet.GroupSummary4"), this.Net, resources.GetString("gv_SubTaxNet.GroupSummary5")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gv_SubTaxNet.GroupSummary6"))), resources.GetString("gv_SubTaxNet.GroupSummary7"), this.SubTaxAddInvouce, resources.GetString("gv_SubTaxNet.GroupSummary8"))});
            this.gv_SubTaxNet.HorzScrollStep = 2;
            this.gv_SubTaxNet.Name = "gv_SubTaxNet";
            this.gv_SubTaxNet.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gv_SubTaxNet.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gv_SubTaxNet.OptionsBehavior.Editable = false;
            this.gv_SubTaxNet.OptionsPrint.ExpandAllGroups = false;
            this.gv_SubTaxNet.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_SubTaxNet.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gv_SubTaxNet.OptionsView.EnableAppearanceEvenRow = true;
            this.gv_SubTaxNet.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.gv_SubTaxNet.OptionsView.RowAutoHeight = true;
            this.gv_SubTaxNet.OptionsView.ShowAutoFilterRow = true;
            this.gv_SubTaxNet.OptionsView.ShowFooter = true;
            this.gv_SubTaxNet.OptionsView.ShowIndicator = false;
            this.gv_SubTaxNet.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            // 
            // SubTaxAddInvouce
            // 
            this.SubTaxAddInvouce.AppearanceCell.Options.UseTextOptions = true;
            this.SubTaxAddInvouce.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.SubTaxAddInvouce, "SubTaxAddInvouce");
            this.SubTaxAddInvouce.FieldName = "SubTaxAddInvouce";
            this.SubTaxAddInvouce.Name = "SubTaxAddInvouce";
            this.SubTaxAddInvouce.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("SubTaxAddInvouce.Summary"))), resources.GetString("SubTaxAddInvouce.Summary1"), resources.GetString("SubTaxAddInvouce.Summary2"))});
            // 
            // col_SubTaxReturnInvouce
            // 
            this.col_SubTaxReturnInvouce.AppearanceCell.Options.UseTextOptions = true;
            this.col_SubTaxReturnInvouce.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SubTaxReturnInvouce.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SubTaxReturnInvouce.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SubTaxReturnInvouce.AppearanceHeader.Options.UseTextOptions = true;
            this.col_SubTaxReturnInvouce.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SubTaxReturnInvouce.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SubTaxReturnInvouce.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_SubTaxReturnInvouce, "col_SubTaxReturnInvouce");
            this.col_SubTaxReturnInvouce.FieldName = "SubTaxReturnInvouce";
            this.col_SubTaxReturnInvouce.Name = "col_SubTaxReturnInvouce";
            this.col_SubTaxReturnInvouce.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SubTaxReturnInvouce.Summary"))), resources.GetString("col_SubTaxReturnInvouce.Summary1"), resources.GetString("col_SubTaxReturnInvouce.Summary2"))});
            // 
            // col_subTaxParent
            // 
            this.col_subTaxParent.AppearanceCell.Options.UseTextOptions = true;
            this.col_subTaxParent.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_subTaxParent.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_subTaxParent.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_subTaxParent.AppearanceHeader.Options.UseTextOptions = true;
            this.col_subTaxParent.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_subTaxParent.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_subTaxParent.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_subTaxParent, "col_subTaxParent");
            this.col_subTaxParent.FieldName = "subTaxParent";
            this.col_subTaxParent.Name = "col_subTaxParent";
            this.col_subTaxParent.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_SubTaxSlInvouce
            // 
            this.col_SubTaxSlInvouce.AppearanceCell.Options.UseTextOptions = true;
            this.col_SubTaxSlInvouce.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SubTaxSlInvouce.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SubTaxSlInvouce.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SubTaxSlInvouce.AppearanceHeader.Options.UseTextOptions = true;
            this.col_SubTaxSlInvouce.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SubTaxSlInvouce.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SubTaxSlInvouce.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_SubTaxSlInvouce, "col_SubTaxSlInvouce");
            this.col_SubTaxSlInvouce.FieldName = "SubTaxSlInvouce";
            this.col_SubTaxSlInvouce.Name = "col_SubTaxSlInvouce";
            this.col_SubTaxSlInvouce.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.col_SubTaxSlInvouce.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SubTaxSlInvouce.Summary"))), resources.GetString("col_SubTaxSlInvouce.Summary1"), resources.GetString("col_SubTaxSlInvouce.Summary2"))});
            // 
            // col_subTaxName
            // 
            this.col_subTaxName.AppearanceCell.Options.UseTextOptions = true;
            this.col_subTaxName.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_subTaxName, "col_subTaxName");
            this.col_subTaxName.FieldName = "subTaxName";
            this.col_subTaxName.Name = "col_subTaxName";
            this.col_subTaxName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            // 
            // col_subTaxID
            // 
            resources.ApplyResources(this.col_subTaxID, "col_subTaxID");
            this.col_subTaxID.FieldName = "subTaxID";
            this.col_subTaxID.Name = "col_subTaxID";
            // 
            // Net
            // 
            this.Net.AppearanceCell.Options.UseTextOptions = true;
            this.Net.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.Net.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.Net.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.Net.AppearanceHeader.Options.UseTextOptions = true;
            this.Net.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.Net.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.Net.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.Net, "Net");
            this.Net.FieldName = "Net";
            this.Net.Name = "Net";
            this.Net.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("Net.Summary"))), resources.GetString("Net.Summary1"), resources.GetString("Net.Summary2"))});
            // 
            // rep_MtrxParent
            // 
            this.rep_MtrxParent.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_MtrxParent.Buttons"))))});
            this.rep_MtrxParent.Name = "rep_MtrxParent";
            resources.ApplyResources(this.rep_MtrxParent, "rep_MtrxParent");
            this.rep_MtrxParent.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.FieldName = "ItemNameAr";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // gridColumn2
            // 
            this.gridColumn2.FieldName = "ItemId";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // rep_cat
            // 
            this.rep_cat.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_cat.Buttons"))))});
            this.rep_cat.Name = "rep_cat";
            resources.ApplyResources(this.rep_cat, "rep_cat");
            this.rep_cat.View = this.gridView2;
            // 
            // gridView2
            // 
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3,
            this.gridColumn4});
            this.gridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn3
            // 
            this.gridColumn3.FieldName = "CategoryNameAr";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn4
            // 
            this.gridColumn4.FieldName = "CategoryId";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // rep_comp
            // 
            this.rep_comp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_comp.Buttons"))))});
            this.rep_comp.Name = "rep_comp";
            resources.ApplyResources(this.rep_comp, "rep_comp");
            this.rep_comp.View = this.gridView3;
            // 
            // gridView3
            // 
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn6});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn5
            // 
            this.gridColumn5.FieldName = "CompanyNameAr";
            this.gridColumn5.Name = "gridColumn5";
            // 
            // gridColumn6
            // 
            this.gridColumn6.FieldName = "CompanyId";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // rep_mtrx
            // 
            this.rep_mtrx.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_mtrx.Buttons"))))});
            this.rep_mtrx.Name = "rep_mtrx";
            resources.ApplyResources(this.rep_mtrx, "rep_mtrx");
            this.rep_mtrx.ShowDropDown = DevExpress.XtraEditors.Controls.ShowDropDown.Never;
            this.rep_mtrx.View = this.gridView4;
            // 
            // gridView4
            // 
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.FieldName = "MDName";
            this.gridColumn7.Name = "gridColumn7";
            // 
            // gridColumn8
            // 
            this.gridColumn8.FieldName = "MatrixDetailId";
            this.gridColumn8.Name = "gridColumn8";
            // 
            // rep_Vendor
            // 
            resources.ApplyResources(this.rep_Vendor, "rep_Vendor");
            this.rep_Vendor.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Vendor.Buttons"))))});
            this.rep_Vendor.Name = "rep_Vendor";
            this.rep_Vendor.View = this.gridView5;
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "VenNameAr";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn10
            // 
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.FieldName = "VendorId";
            this.gridColumn10.Name = "gridColumn10";
            // 
            // repositoryItemMemoEdit1
            // 
            this.repositoryItemMemoEdit1.Name = "repositoryItemMemoEdit1";
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblReportName.Properties.AutoHeight = ((bool)(resources.GetObject("lblReportName.Properties.AutoHeight")));
            this.lblReportName.Properties.Mask.EditMask = resources.GetString("lblReportName.Properties.Mask.EditMask");
            this.lblReportName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblReportName.Properties.Mask.IgnoreMaskBlank")));
            this.lblReportName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblReportName.Properties.Mask.SaveLiteral")));
            this.lblReportName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblReportName.Properties.Mask.ShowPlaceHolders")));
            this.lblReportName.Properties.NullValuePrompt = resources.GetString("lblReportName.Properties.NullValuePrompt");
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblDateFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblDateFilter.Properties.AutoHeight")));
            this.lblDateFilter.Properties.Mask.EditMask = resources.GetString("lblDateFilter.Properties.Mask.EditMask");
            this.lblDateFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblDateFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.SaveLiteral")));
            this.lblDateFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblDateFilter.Properties.NullValuePrompt = resources.GetString("lblDateFilter.Properties.NullValuePrompt");
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblFilter.Properties.AutoHeight")));
            this.lblFilter.Properties.Mask.EditMask = resources.GetString("lblFilter.Properties.Mask.EditMask");
            this.lblFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblFilter.Properties.Mask.SaveLiteral")));
            this.lblFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblFilter.Properties.NullValuePrompt = resources.GetString("lblFilter.Properties.NullValuePrompt");
            // 
            // frm_SubTaxNet
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grdSubTaxNet);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SubTaxNet";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_ImExp_Fines_FormClosing);
            this.Load += new System.EventHandler(this.frm_ImExp_Fines_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdSubTaxNet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SubTaxNet)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_MtrxParent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_cat)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_comp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_mtrx)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdSubTaxNet;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_SubTaxNet;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_MtrxParent;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_comp;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_cat;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_mtrx;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Vendor;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn SubTaxAddInvouce;
        private DevExpress.XtraGrid.Columns.GridColumn col_SubTaxReturnInvouce;
        private DevExpress.XtraGrid.Columns.GridColumn col_subTaxParent;
        private DevExpress.XtraGrid.Columns.GridColumn col_SubTaxSlInvouce;
        private DevExpress.XtraGrid.Columns.GridColumn col_subTaxName;
        private DevExpress.XtraGrid.Columns.GridColumn col_subTaxID;
        private DevExpress.XtraGrid.Columns.GridColumn Net;
    }
}