﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Data.OleDb;
using System.Linq;
using System.Globalization;
using System.Threading;
using DevExpress.XtraPrinting;
using DAL;
using DAL.Res;
using Pharmacy.Forms;

namespace Pharmacy
{
    public partial class frm_Excel_Import : DevExpress.XtraEditors.XtraForm
    {
        DataTable dt = new DataTable();

        public frm_Excel_Import()
        {
            InitializeComponent();
        }

        private void menu_item_open_xls_Click(object sender, EventArgs e)
        {
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    dt = ErpUtils.exceldata(ofd.FileName);
                    if (dt != null)
                    {
                        gridView1.Columns.Clear();
                        gridControl1.DataSource = dt;
                    }
                    else
                        return;
                }
                catch
                { }
            }
            else
                return;
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                gridView1.DeleteSelectedRows();
            }
        }


        private void حفظToolStripMenuItem_Click(object sender, EventArgs e)
        {
            PrintingSystem printSystem = new PrintingSystem();
            PrintableComponentLink printLink = new PrintableComponentLink();

            ((System.ComponentModel.ISupportInitialize)(printSystem)).BeginInit();

            printSystem.Links.AddRange(new object[] {
            printLink});
            printLink.Component = this.gridControl1;

            printLink.PaperKind = System.Drawing.Printing.PaperKind.A4;
            printLink.Landscape = true;
            printLink.Margins = new System.Drawing.Printing.Margins(20, 20, 120, 20);
            printLink.PrintingSystem = printSystem;
            printLink.PrintingSystemBase = printSystem;

            ((System.ComponentModel.ISupportInitialize)(printSystem)).EndInit();

            printLink.CreateDocument();
            printLink.ShowPreview();

        }

        private void حذفالمكررToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var data = (from DataRow dr in dt.Rows
                        select new
                        {
                            name = dr[0].ToString()
                        }).Distinct().ToList();

            dt.Rows.Clear();
            foreach (var d in data)
            {
                dt.Rows.Add(d.name);
            }

            gridControl1.RefreshDataSource();
        }

        private void insertcustomersToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            List<ACC_JournalDetail> lst = new List<ACC_JournalDetail>();

            #region Acc_Open_Balance_Journal
            ACC_Journal jornal = new ACC_Journal();
            jornal.InsertUser = Shared.UserId;
            jornal.InsertDate = MyHelper.Get_Server_DateTime();
            jornal.JCode = 1;
            jornal.JNotes = Shared.IsEnglish ? ResAccEn.txtOpenBalance : ResAccEn.txtOpenBalance;
            jornal.ProcessId = (int)Process.OpenBalance;
            jornal.SourceId = 0;
            jornal.IsPosted = true;
            jornal.StoreId = 1;
            jornal.CrncId = 0;
            jornal.CrncRate = 1;
            DB.ACC_Journals.InsertOnSubmit(jornal);
            DB.SubmitChanges();
            #endregion

            int count = 1;
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (dr["CustomerName"] == null || dr["CustomerName"] == DBNull.Value || dr["CustomerName"].ToString() == string.Empty)
                    continue;

                string CustomerName = dr["CustomerName"].ToString();
                decimal openbalance = 0;
                decimal.TryParse(dr["Balance"] + "", out openbalance);


                int GroupAccId = Convert.ToInt32(dr["GroupAccId"]);
                int GroupId = Convert.ToInt32(dr["GroupId"]);

                if (DB.SL_Customers.Where(x => x.CusNameAr == CustomerName).Count() > 0)
                    continue;

                #region Customer
                var customer = new SL_Customer();

                customer.CusCode = count;
                customer.CusNameAr = CustomerName;
                customer.CusNameEn = "";
                customer.MaxCredit = 0;
                customer.DiscountRatio = 0;
                customer.HasSeparateAccount = true;
                customer.IsTaxable = false;
                customer.CategoryId = GroupId;

                DB.SL_Customers.InsertOnSubmit(customer);
                DB.SubmitChanges();

                int AccId = HelperAcc.Get_CustomerAccount_Id(customer.CustomerId, GroupAccId, Shared.st_Store.CustomersAcc);

                #region
                if (openbalance != 0)
                {
                    /*من حساب العميل */
                    ACC_JournalDetail jornal_detail_Capital = new ACC_JournalDetail();
                    jornal_detail_Capital.AccountId = AccId;
                    jornal_detail_Capital.Credit = openbalance < 0 ? Math.Abs(openbalance) : 0;
                    jornal_detail_Capital.Debit = openbalance < 0 ? 0 : Math.Abs(openbalance);
                    jornal_detail_Capital.JournalId = jornal.JournalId;
                    jornal_detail_Capital.Notes = Shared.IsEnglish ? ResEn.custOpen : ResAr.custOpen; //"رصيد افتتاحي عميل"
                    jornal_detail_Capital.CrncId = 0;
                    jornal_detail_Capital.CrncRate = 1;
                    lst.Add(jornal_detail_Capital);
                }
                #endregion

                #endregion

                count++;
            }

            DB = new ERPDataContext();
            DB.ACC_JournalDetails.InsertAllOnSubmit(lst);
            DB.SubmitChanges();

            MessageBox.Show(count.ToString());
        }

        private void insertitemsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            #region ic_intrans
            IC_InTrn intrn = new IC_InTrn();
            intrn.InTrnsCode = "1";
            intrn.StoreId = 1;
            intrn.VendorId = null;
            intrn.InTrnsDate = MyHelper.Get_Server_DateTime();
            intrn.Notes = "الأرصدة الافتتاحية";
            intrn.UserId = 1;
            intrn.TotalPurchasePrice = 0;
            intrn.ProcessId = null;
            intrn.SourceId = null;
            intrn.IsVendor = null;
            intrn.ItemId = null;
            intrn.PiecesCount = null;
            intrn.TotalQty = 0;
            intrn.DetailIdBrcodPrntd = false;
            intrn.CrncId = 1;
            intrn.CrncRate = 1;
            intrn.IsOpenBalance = true;
            intrn.JornalId = null;
            intrn.AccountId = null;
            intrn.CostCenterId = null;

            DB.IC_InTrns.InsertOnSubmit(intrn);
            DB.SubmitChanges();

            #endregion

            int count = 1;
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (dr["ItemName"] == null || dr["ItemName"] == DBNull.Value || dr["ItemName"].ToString() == string.Empty)
                    continue;

                string ItemName = dr["ItemName"].ToString();
                decimal openbalance = 0;
                decimal.TryParse(dr["Balance"] + "", out openbalance);
                int catId = 1;
                int.TryParse(dr["CatId"] + "", out catId);


                if (DB.IC_Items.Where(x => x.ItemNameAr == ItemName).Count() > 0)
                    continue;

                #region IC_Item
                IC_Item item = new IC_Item();
                item.ItemCode1 = count;
                item.ItemNameAr = ItemName;
                item.ItemNameEn = "";
                item.Category = catId;
                item.Company = 1;
                item.PurchasePrice = 0;
                item.SmallUOM = 1;
                item.SmallUOMPrice = 0;
                item.ReorderLevel = 0;
                item.MaxQty = 0;
                item.MinQty = 0;
                item.ChangePriceMethod = 1;
                item.ChangeSellPrice = true;
                item.IsExpire = false;
                item.IsDeleted = false;
                item.ItemType = 0;
                item.PurchaseDiscRatio = 0;
                item.PurchaseTaxValue = 0;
                item.SalesDiscRatio = 0;
                item.SalesTaxRatio = 0;
                item.SalesTaxValue = 0;
                item.PurchaseTaxRatio = 0;
                item.UsedInMarketing = false;
                item.Height = 1;
                item.Width = 1;
                item.Length = 1;
                item.DfltSellUomIndx = 0;
                item.DfltPrchsUomIndx = 0;

                DB.IC_Items.InsertOnSubmit(item);
                #endregion

                DB.SubmitChanges();

                #region
                if (openbalance > 0)
                {
                    IC_InTrnsDetail in_detail = new IC_InTrnsDetail();
                    in_detail.InTrnsId = 1;
                    in_detail.ItemId = item.ItemId;
                    in_detail.UOMIndex = 0;
                    in_detail.UOMId = 1;
                    in_detail.Qty = openbalance;
                    in_detail.PurchasePrice = 0;
                    in_detail.SellPrice = 0;
                    in_detail.TotalPurchasePrice = 0;
                    in_detail.Expire = null;
                    in_detail.Batch = null;
                    in_detail.Height = 1;
                    in_detail.Length = 1;
                    in_detail.Width = 1;
                    in_detail.PiecesCount = 0;
                    in_detail.QC = null;
                    DB.IC_InTrnsDetails.InsertOnSubmit(in_detail);
                }
                #endregion

                count++;
            }

            DB.SubmitChanges();

            MessageBox.Show(count.ToString());
        }

        private void تعديلاساميالبنوكToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            int count = 1;
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (dr["BankName"] == null || dr["BankName"] == DBNull.Value || dr["BankName"].ToString() == string.Empty)
                    continue;

                string BankName = dr["BankName"].ToString();

                var bank = DB.ACC_Banks.Where(x => x.BankId == count).FirstOrDefault();

                bank.BankName = BankName;
                bank.BankAccountNumber = "";
                bank.BranchName = "";

                var acc = DB.ACC_Accounts.Where(x => x.AccountId == bank.AccountId).FirstOrDefault();
                acc.AcNameAr = BankName;

                DB.SubmitChanges();

                count++;
            }

            DB.SubmitChanges();

            MessageBox.Show(count.ToString());
        }

        private void insertCustomersWithoutJournalsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            int count = DB.SL_Customers.Select(x => x.CustomerId).Max() + 1;
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (dr["CustomerName"] == null || dr["CustomerName"] == DBNull.Value || dr["CustomerName"].ToString() == string.Empty)
                    continue;

                string CustomerName = dr["CustomerName"].ToString();

                if (DB.SL_Customers.Where(x => x.CusNameAr == CustomerName).Count() > 0)
                    continue;

                #region Customer
                var customer = new SL_Customer();

                customer.CusCode = count;
                customer.CusNameAr = CustomerName;
                customer.CusNameEn = "";
                customer.MaxCredit = 0;
                customer.DiscountRatio = 0;
                customer.HasSeparateAccount = true;
                customer.IsTaxable = false;

                DB.SL_Customers.InsertOnSubmit(customer);
                DB.SubmitChanges();

                int AccId = HelperAcc.Get_CustomerAccount_Id(customer.CustomerId, 0, Shared.st_Store.CustomersAcc);

                #endregion

                count++;
            }

            DB.SubmitChanges();

            MessageBox.Show(count.ToString());
        }

        private void insertVendorsWithoutJournalsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            int count = DB.PR_Vendors.Select(x => x.VendorId).Max() + 1;
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (dr["VendorName"] == null || dr["VendorName"] == DBNull.Value || dr["VendorName"].ToString() == string.Empty)
                    continue;

                string VendorName = dr["VendorName"].ToString();

                if (DB.PR_Vendors.Where(x => x.VenNameAr == VendorName).Count() > 0)
                    continue;

                #region Vendor
                var Vendor = new PR_Vendor();

                Vendor.VenCode = count;
                Vendor.VenNameAr = VendorName;
                Vendor.VenNameEn = "";
                Vendor.MaxCredit = 0;
                Vendor.HasSeparateAccount = true;
                Vendor.IsTaxable = false;

                DB.PR_Vendors.InsertOnSubmit(Vendor);
                DB.SubmitChanges();

                int AccId = HelperAcc.Get_VendorAccount_Id(Vendor.VendorId, 0, Shared.st_Store.VendorsAcc);

                #endregion

                count++;
            }

            DB.SubmitChanges();

            MessageBox.Show(count.ToString());
        }

        private void insertAccountsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            int count = 0;
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (dr["AcNameAr"] == null || dr["AcNameAr"] == DBNull.Value || dr["AcNameAr"].ToString() == string.Empty)
                    continue;

                string AcNameAr = dr["AcNameAr"].ToString();

                if (DB.ACC_Accounts.Where(x => x.AcNameAr == AcNameAr).Count() > 0)
                    continue;

                #region AcName
                var account = new ACC_Account();

                account.AccSecurityLevel = 1;
                account.AcNameAr = AcNameAr;
                account.AcNameEn = "";
                account.AcNumber = dr["AcNumber"].ToString();
                account.ParentActId = Convert.ToInt32(dr["ParentId"].ToString());
                account.AcType = false;
                account.Notes = "";
                account.AllowEdit = true;
                account.AllowChild = true;
                account.CostCenter = false;
                account.Level = 3;
                account.Budget = 0;

                DB.ACC_Accounts.InsertOnSubmit(account);
                DB.SubmitChanges();

                #endregion

                count++;
            }

            DB.SubmitChanges();

            MessageBox.Show(count.ToString());
        }

        private void mi_InsertItems2_Click(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            int count = 1;
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (dr["ItemName"] == null || dr["ItemName"] == DBNull.Value || dr["ItemName"].ToString() == string.Empty)
                    continue;

                string ItemName = dr["ItemName"].ToString();
                int catId = 1;
                int.TryParse(dr["CatId"] + "", out catId);


                if (DB.IC_Items.Where(x => x.ItemNameAr == ItemName).Count() > 0)
                    continue;

                #region IC_Item
                IC_Item item = new IC_Item();
                item.ItemCode1 = count;
                item.ItemCode2 = dr["ItemCode2"] + "";
                item.ItemNameAr = ItemName;
                item.ItemNameEn = "";
                item.Category = catId;
                item.Company = 1;
                item.PurchasePrice = 0;
                item.SmallUOM = 1;
                item.SmallUOMPrice = Convert.ToDecimal(dr["SmallUOMPrice"]);

                item.MediumUOM = Convert.ToByte(dr["MediumUOM"]);
                item.MediumUOMPrice = Convert.ToDecimal(dr["MediumUOMPrice"]);
                item.MediumUOMFactor = dr["MediumUOMFactor"] + "";
                item.MediumUOMFactorDecimal = Convert.ToDecimal(dr["MediumUOMFactor"]);

                item.ReorderLevel = 0;
                item.MaxQty = 0;
                item.MinQty = 0;
                item.ChangePriceMethod = 1;
                item.ChangeSellPrice = true;
                item.IsExpire = false;
                item.IsDeleted = false;
                item.ItemType = 0;
                item.PurchaseDiscRatio = 0;
                item.PurchaseTaxValue = 0;
                item.SalesDiscRatio = 0;
                item.SalesTaxRatio = 0;
                item.SalesTaxValue = 0;
                item.PurchaseTaxRatio = 0;
                item.UsedInMarketing = false;
                item.Height = 1;
                item.Width = 1;
                item.Length = 1;
                item.DfltSellUomIndx = 1;
                item.DfltPrchsUomIndx = 1;

                DB.IC_Items.InsertOnSubmit(item);
                #endregion

                DB.SubmitChanges();

                count++;
            }

            DB.SubmitChanges();

            MessageBox.Show(count.ToString());
        }

        private void mi_InsertCustomers2_Click(object sender, EventArgs e)
        {

        }

        private void itemsPriceListToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            // create price list
            IC_PriceLevel priceSales = new IC_PriceLevel();
            priceSales.IsRatio = false;
            priceSales.IsRatioIncrease = false;
            priceSales.PLName = "تجزئة";
            priceSales.Ratio = 0;
            DB.IC_PriceLevels.InsertOnSubmit(priceSales);
            DB.SubmitChanges();


            int count = 1;
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (dr["ItemName"] == null || dr["ItemName"] == DBNull.Value || dr["ItemName"].ToString() == string.Empty)
                    continue;

                string ItemName = dr["ItemName"].ToString();

                if (DB.IC_Items.Where(x => x.ItemNameAr == ItemName).Count() > 0)
                    continue;

                #region IC_Item
                IC_Item item = new IC_Item();
                item.ItemCode1 = count;
                item.ItemCode2 = string.Empty;
                item.ItemNameAr = ItemName;
                item.ItemNameEn = "";
                item.Category = 1;
                item.Company = 1;
                item.PurchasePrice = Convert.ToDecimal(dr["Purchase"]);

                string uom = Convert.ToString(dr["UOM"]);
                var u = DB.IC_UOMs.Where(x => x.UOM == uom).FirstOrDefault();
                if(u == null)
                {
                    IC_UOM newUOM = new IC_UOM();
                    newUOM.UOM = uom;
                    DB.IC_UOMs.InsertOnSubmit(newUOM);
                    DB.SubmitChanges();
                    item.SmallUOM = Convert.ToByte(newUOM.UOMId);
                }
                else
                {
                    item.SmallUOM = Convert.ToByte(u.UOMId);
                }

                
                item.SmallUOMPrice = Convert.ToDecimal(dr["Gomla"]);

                item.ReorderLevel = Convert.ToInt32(dr["Reorder"]);
                item.MaxQty = 0;
                item.MinQty = 0;
                item.ChangePriceMethod = 1;
                item.ChangeSellPrice = true;
                item.IsExpire = false;
                item.IsDeleted = false;
                item.ItemType = 0;
                item.PurchaseDiscRatio = 0;
                item.PurchaseTaxValue = 0;
                item.SalesDiscRatio = 0;
                item.SalesTaxRatio = 0;
                item.SalesTaxValue = 0;
                item.PurchaseTaxRatio = 0;
                item.UsedInMarketing = false;
                item.Height = 1;
                item.Width = 1;
                item.Length = 1;
                item.DfltSellUomIndx = 1;
                item.DfltPrchsUomIndx = 1;

                DB.IC_Items.InsertOnSubmit(item);
                #endregion

                DB.SubmitChanges();
                IC_PriceLevelDetail pDetail = new IC_PriceLevelDetail();
                pDetail.PriceLevelId = priceSales.PriceLevelId;
                pDetail.ItemId = item.ItemId;
                pDetail.smallUOMPrice = Convert.ToDecimal(dr["Sales"]);
                DB.IC_PriceLevelDetails.InsertOnSubmit(pDetail);
                DB.SubmitChanges();

                count++;
            }

            DB.SubmitChanges();

            MessageBox.Show(count.ToString());
        }



    }
}