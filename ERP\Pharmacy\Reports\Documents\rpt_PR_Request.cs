using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_PR_Request : DevExpress.XtraReports.UI.XtraReport
    {
        string number, date, store, notes, userName;

        DataTable dt_inv_details;

        public rpt_PR_Request()
        {
            InitializeComponent();
        }
        public rpt_PR_Request(string _number, string _date, string _store, string _notes, DataTable dt, string userName)
        {
            InitializeComponent();
            number = _number;
            date = _date;
            store = _store;

            notes = _notes;

            this.userName = userName;

            dt_inv_details = dt;
            this.DataSource = dt_inv_details;
            //LoadData();            
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;
            
            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_date.Text = date;
            lbl_notes.Text = notes;
            lbl_Number.Text = number;

            lbl_store.Text = store;
            lbl_User.Text = userName;

            this.DataSource = dt_inv_details;

            cell_code.DataBindings.Add("Text", this.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", this.DataSource, "ItemCode2");
            cell_Qty.DataBindings.Add("Text", this.DataSource, "Qty");
            cell_ItemName.DataBindings.Add("Text", this.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", this.DataSource, "UOM");
            cell_Height.DataBindings.Add("Text", this.DataSource, "Height");
            cell_Width.DataBindings.Add("Text", this.DataSource, "Width");
            cell_Length.DataBindings.Add("Text", this.DataSource, "Length");
            cell_TotalQty.DataBindings.Add("Text", this.DataSource, "TotalQty");

            cell_ItemDescription.DataBindings.Add("Text", this.DataSource, "ItemDescription");
            Cell_ItemDescriptionEn.DataBindings.Add("Text", this.DataSource, "ItemDescriptionEn");

            getReportHeader();
        }

    }
}
