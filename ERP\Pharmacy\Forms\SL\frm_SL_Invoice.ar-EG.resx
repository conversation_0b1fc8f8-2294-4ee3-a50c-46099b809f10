﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 660</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 629</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 629</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1174, 660</value>
  </data>
  <data name="labelControl57.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 510</value>
  </data>
  <data name="labelControl57.Size" type="System.Drawing.Size, System.Drawing">
    <value>78, 13</value>
  </data>
  <data name="labelControl57.Text" xml:space="preserve">
    <value>اجمالي خ البونص</value>
  </data>
  <data name="txt_bounsDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 507</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="grd_SubTaxes.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="grd_SubTaxes.Location" type="System.Drawing.Point, System.Drawing">
    <value>272, 499</value>
  </data>
  <data name="Value.Caption" xml:space="preserve">
    <value>القيمة</value>
  </data>
  <data name="SubTaxId.Caption" xml:space="preserve">
    <value>نوع الضريبة</value>
  </data>
  <data name="lkp_SubTaxes.Columns" xml:space="preserve">
    <value>DescriptionAr</value>
  </data>
  <data name="lkp_SubTaxes.Columns1" xml:space="preserve">
    <value>الوصف</value>
  </data>
  <data name="col_Rate.Caption" xml:space="preserve">
    <value>النسبة</value>
  </data>
  <data name="labelControl56.Location" type="System.Drawing.Point, System.Drawing">
    <value>643, 454</value>
  </data>
  <data name="labelControl56.Size" type="System.Drawing.Size, System.Drawing">
    <value>95, 13</value>
  </data>
  <data name="labelControl56.Text" xml:space="preserve">
    <value>الإجمالي بعد الضريبة</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="labelControl56.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl40.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 582</value>
  </data>
  <data name="labelControl40.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 13</value>
  </data>
  <data name="labelControl40.Text" xml:space="preserve">
    <value>الإجمالي بعد الخصم</value>
  </data>
  <data name="labelControl55.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="labelControl55.Location" type="System.Drawing.Point, System.Drawing">
    <value>122, 37</value>
  </data>
  <data name="labelControl55.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 13</value>
  </data>
  <data name="labelControl55.Text" xml:space="preserve">
    <value>دفتر الفواتير</value>
  </data>
  <data name="chk_Approved.Location" type="System.Drawing.Point, System.Drawing">
    <value>221, 35</value>
  </data>
  <data name="chk_Offer.Location" type="System.Drawing.Point, System.Drawing">
    <value>290, 35</value>
  </data>
  <data name="chk_Offer.Properties.Caption" xml:space="preserve">
    <value>عرض</value>
  </data>
  <data name="chk_Offer.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 19</value>
  </data>
  <data name="chk_IsOutTrns.Location" type="System.Drawing.Point, System.Drawing">
    <value>355, 35</value>
  </data>
  <data name="chk_IsOutTrns.Properties.Caption" xml:space="preserve">
    <value>تم الصرف من المخزن</value>
  </data>
  <data name="chk_IsOutTrns.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 19</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>621, 37</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>نوع السداد</value>
  </data>
  <data name="txt_AttnMr.Location" type="System.Drawing.Point, System.Drawing">
    <value>686, 34</value>
  </data>
  <data name="txt_AttnMr.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txt_AttnMr.Size" type="System.Drawing.Size, System.Drawing">
    <value>209, 20</value>
  </data>
  <data name="labelControl41.Location" type="System.Drawing.Point, System.Drawing">
    <value>898, 37</value>
  </data>
  <data name="labelControl41.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 13</value>
  </data>
  <data name="labelControl41.Text" xml:space="preserve">
    <value>عناية</value>
  </data>
  <data name="btnCeil.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnCeil.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 46</value>
  </data>
  <data name="btnCeil.Size" type="System.Drawing.Size, System.Drawing">
    <value>110, 22</value>
  </data>
  <data name="btnCeil.Text" xml:space="preserve">
    <value>جبر الكسر</value>
  </data>
  <data name="btnCeil.ToolTip" xml:space="preserve">
    <value>جبر كسر الإجمالى</value>
  </data>
  <data name="txt_ShiftAdd.Size" type="System.Drawing.Size, System.Drawing">
    <value>110, 20</value>
  </data>
  <data name="labelControl53.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl53.Text" xml:space="preserve">
    <value>الوردية</value>
  </data>
  <data name="txt_transportation.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 14</value>
  </data>
  <data name="txt_transportation.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 20</value>
  </data>
  <data name="labelControl49.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="labelControl49.Text" xml:space="preserve">
    <value>إيراد نقل</value>
  </data>
  <data name="txt_Handing.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 49</value>
  </data>
  <data name="txt_Handing.Size" type="System.Drawing.Size, System.Drawing">
    <value>145, 20</value>
  </data>
  <data name="labelControl50.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 51</value>
  </data>
  <data name="labelControl50.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="labelControl50.Text" xml:space="preserve">
    <value>إيراد عمالة</value>
  </data>
  <data name="grp_ExtraRevenues.Location" type="System.Drawing.Point, System.Drawing">
    <value>730, 415</value>
  </data>
  <data name="grp_ExtraRevenues.Size" type="System.Drawing.Size, System.Drawing">
    <value>413, 78</value>
  </data>
  <data name="cmbPayMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>509, 36</value>
  </data>
  <data name="cmbPayMethod.Properties.Items" xml:space="preserve">
    <value>آجل</value>
  </data>
  <data name="cmbPayMethod.Properties.Items3" xml:space="preserve">
    <value>كاش</value>
  </data>
  <data name="cmbPayMethod.Properties.Items6" xml:space="preserve">
    <value>اجل/كاش</value>
  </data>
  <data name="btnAddCustomer.Location" type="System.Drawing.Point, System.Drawing">
    <value>936, 32</value>
  </data>
  <data name="btnAddCustomer.ToolTip" xml:space="preserve">
    <value>اضافة عميل</value>
  </data>
  <data name="lkp_Customers.Location" type="System.Drawing.Point, System.Drawing">
    <value>960, 34</value>
  </data>
  <data name="lkp_Customers.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>1137, 37</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="txt_Subtotal.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 583</value>
  </data>
  <data name="labelControl29.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 484</value>
  </data>
  <data name="labelControl29.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="labelControl29.Text" xml:space="preserve">
    <value>اجمالي ض</value>
  </data>
  <data name="txt_EtaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 481</value>
  </data>
  <data name="bookId.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 34</value>
  </data>
  <data name="bookId.Properties.Items" xml:space="preserve">
    <value>الكل</value>
  </data>
  <data name="bookId.Properties.Items3" xml:space="preserve">
    <value>ضريبي</value>
  </data>
  <data name="bookId.Properties.Items6" xml:space="preserve">
    <value>غير ضريبي</value>
  </data>
  <data name="bookId.Size" type="System.Drawing.Size, System.Drawing">
    <value>90, 20</value>
  </data>
  <data name="labelControl37.Location" type="System.Drawing.Point, System.Drawing">
    <value>849, 429</value>
  </data>
  <data name="labelControl37.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 13</value>
  </data>
  <data name="labelControl37.Text" xml:space="preserve">
    <value>الاحتفاظ</value>
  </data>
  <data name="labelControl38.Location" type="System.Drawing.Point, System.Drawing">
    <value>876, 429</value>
  </data>
  <data name="labelControl38.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl38.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl39.Location" type="System.Drawing.Point, System.Drawing">
    <value>876, 429</value>
  </data>
  <data name="labelControl39.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl39.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl42.Location" type="System.Drawing.Point, System.Drawing">
    <value>831, 429</value>
  </data>
  <data name="labelControl42.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 13</value>
  </data>
  <data name="labelControl42.Text" xml:space="preserve">
    <value>دفعة مقدمة</value>
  </data>
  <data name="labelControl43.Location" type="System.Drawing.Point, System.Drawing">
    <value>879, 429</value>
  </data>
  <data name="labelControl43.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl44.Location" type="System.Drawing.Point, System.Drawing">
    <value>879, 429</value>
  </data>
  <data name="labelControl44.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl45.Location" type="System.Drawing.Point, System.Drawing">
    <value>875, 429</value>
  </data>
  <data name="labelControl46.Location" type="System.Drawing.Point, System.Drawing">
    <value>875, 429</value>
  </data>
  <data name="txt_retentionR.Location" type="System.Drawing.Point, System.Drawing">
    <value>849, 422</value>
  </data>
  <data name="txt_retentionR.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 20</value>
  </data>
  <data name="txt_RetentionV.Location" type="System.Drawing.Point, System.Drawing">
    <value>797, 422</value>
  </data>
  <data name="txt_RetentionV.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 20</value>
  </data>
  <data name="txt_AdvancePayR.Location" type="System.Drawing.Point, System.Drawing">
    <value>851, 422</value>
  </data>
  <data name="txt_AdvancePayV.Location" type="System.Drawing.Point, System.Drawing">
    <value>798, 422</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>766, 429</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl27.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>729, 429</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>ض الجدول</value>
  </data>
  <data name="labelControl28.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl32.Location" type="System.Drawing.Point, System.Drawing">
    <value>769, 429</value>
  </data>
  <data name="labelControl32.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl34.Location" type="System.Drawing.Point, System.Drawing">
    <value>765, 429</value>
  </data>
  <data name="txt_CusTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>741, 422</value>
  </data>
  <data name="txt_SubAfterTax.Location" type="System.Drawing.Point, System.Drawing">
    <value>481, 448</value>
  </data>
  <data name="txt_SubAfterTax.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>688, 422</value>
  </data>
  <data name="txt_CusTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txt_CusTaxV.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 616</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>124, 616</value>
  </data>
  <data name="xtraTabControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>686, 476</value>
  </data>
  <data name="page_AccInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>482, 148</value>
  </data>
  <data name="page_AccInfo.Text" xml:space="preserve">
    <value>حساب العميل</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>488, 176</value>
  </data>
  <data name="page_JobOrder.Size" type="System.Drawing.Size, System.Drawing">
    <value>482, 148</value>
  </data>
  <data name="Page_LastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>482, 148</value>
  </data>
  <data name="Page_LastPrices.Text" xml:space="preserve">
    <value>اخر اسعار</value>
  </data>
  <data name="tabExtraData.Size" type="System.Drawing.Size, System.Drawing">
    <value>482, 148</value>
  </data>
  <data name="tabExtraData.Text" xml:space="preserve">
    <value>بيانات إضافية</value>
  </data>
  <data name="tabpg_CustomerData.Size" type="System.Drawing.Size, System.Drawing">
    <value>482, 148</value>
  </data>
  <data name="tabpg_CustomerData.Text" xml:space="preserve">
    <value>بيانات العميل</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>752, 429</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>ض.خ</value>
  </data>
  <data name="labelControl16.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>766, 429</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl15.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>766, 429</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl23.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>756, 429</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>ض.أ</value>
  </data>
  <data name="labelControl25.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>752, 429</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>ض.ع</value>
  </data>
  <data name="labelControl1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>766, 429</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl20.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>190, 562</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>خصم ناولون</value>
  </data>
  <data name="panelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 196</value>
  </data>
  <data name="panelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1156, 193</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>769, 429</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl14.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>769, 429</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl22.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>107, 561</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>765, 429</value>
  </data>
  <data name="labelControl2.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>765, 429</value>
  </data>
  <data name="labelControl21.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 561</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 612</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>124, 560</value>
  </data>
  <data name="txt_CommercialDiscounts.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CommercialDiscounts.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 435</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 458</value>
  </data>
  <data name="txt_total_b4_Discounts.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_total_b4_Discounts.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 412</value>
  </data>
  <data name="txt_Total.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Total.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 531</value>
  </data>
  <data name="lbl_totalAfterCommercial_Disc.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 461</value>
  </data>
  <data name="lbl_totalAfterCommercial_Disc.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 13</value>
  </data>
  <data name="lbl_totalAfterCommercial_Disc.Text" xml:space="preserve">
    <value>الإجمالي بعد الخصم</value>
  </data>
  <data name="lbl_CommercialDiscounts.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 438</value>
  </data>
  <data name="lbl_CommercialDiscounts.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 13</value>
  </data>
  <data name="lbl_CommercialDiscounts.Text" xml:space="preserve">
    <value>خصم تجاري</value>
  </data>
  <data name="lbl_total_b4_Discounts.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 415</value>
  </data>
  <data name="lbl_total_b4_Discounts.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 13</value>
  </data>
  <data name="lbl_total_b4_Discounts.Text" xml:space="preserve">
    <value>الإجمالي قبل الخصم والضريبة</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>177, 536</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>الاجمــالي</value>
  </data>
  <data name="txtNet.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 632</value>
  </data>
  <data name="txtNet.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="panelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 80</value>
  </data>
  <data name="panelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1154, 115</value>
  </data>
  <data name="panelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>179, 635</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>الصافي</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>122, 61</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>فواتير البيع</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>62, 58</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 19</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>التالي</value>
  </data>
  <data name="btnPrevious.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 58</value>
  </data>
  <data name="btnPrevious.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 19</value>
  </data>
  <data name="btnPrevious.ToolTip" xml:space="preserve">
    <value>السابق</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>190, 612</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 13</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>إضافي ناولون</value>
  </data>
  <data name="txtExpenses.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 609</value>
  </data>
  <data name="txtExpenses.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 20</value>
  </data>
  <data name="txtDiscountRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 557</value>
  </data>
  <data name="txtDiscountRatio.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtDiscountRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txtDiscountValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 557</value>
  </data>
  <data name="txtDiscountValue.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtDiscountValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="txt_TaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>621, 422</value>
  </data>
  <data name="txt_TaxValue.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>741, 422</value>
  </data>
  <data name="txt_DeductTaxR.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>688, 422</value>
  </data>
  <data name="txt_DeductTaxV.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>741, 422</value>
  </data>
  <data name="txt_AddTaxR.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>688, 422</value>
  </data>
  <data name="txt_AddTaxV.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpensesR.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 609</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>فاتورة البيع</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>نسخ</value>
  </data>
  <data name="batBtnList.Caption" xml:space="preserve">
    <value>القائمة</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>جديد</value>
  </data>
  <data name="barBtnCancel.Caption" xml:space="preserve">
    <value>الغاء</value>
  </data>
  <data name="barBtnCancel.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABF0RVh0VGl0
        bGUAUmVzZXQ7VW5kbzsTgRb/AAAA70lEQVQ4T6XTMWoCURDG8W2EWGgRC7ERDF7AVrDwAN7BRgQheAev
        EbxDAilsFEQQtBQLu1WwsJVYis//LEx4+5jVQIrfsvLNfDwebuSc+5eoM5r4XvAJhxuGCGdS/B8FTKHL
        A/i5SV9esYIu95AazCKPCraQ5SwXHPANOVkRvwUzWEuPnNBCUlDFDtbgI3KqenIMlLGBBFd0oZnKoYEF
        tGTsD8hFriGBXGQffq5q0IJ9GMrlzKEl7whnhBZcrPCZOrQgtgaylNDGElrwYQ2GdDj0gzdrIWQtH9FE
        6lvIIgtnxPiC/M3zSHLzE/07F90Bmo0RPdcw7NUAAAAASUVORK5CYII=
</value>
  </data>
  <data name="barBtnCancel.LargeGlyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABF0RVh0VGl0
        bGUAUmVzZXQ7VW5kbzsTgRb/AAABwklEQVRYR8XVsUuVURzGcSOQyAYjCUJwa3DQv0TbnGpqaGioIQIr
        UBF1cLWxoT/DpUla3BwiiKIlGhp0VEF9+z5xrzy/c39X33uV0/AZfofnnOfAfd/3jjRN81+lizWlizWl
        izWlizWlizWFYX51u585HKExy8iyF/I+CUO2AQs4hpevIMteyvskDMmGJziBl68iy7bifRKGIvwMp/Dy
        NZS5gXifhMGCL3AGL9+AZ4bifRKGTmgRXnwZPR/7+IFP2MIj3MZ5cZf3SRgI6PfNSoZxgCWMofUFNpEd
        dhVfMIVWF5B1ZAddxR5uIfRJGBTo0EemPOQtPONGcRfTeIrPKPe/QuiTMChg3qA85KJLuBv4CN/7FaFP
        wqBA4TX8EGl7iUmU35F73idhIJB5ifKb0PYSv+H7ZrxPwkCgn+cY5hK/4HtmvU/CQOA6PUD5P3Lf+yQM
        BK6LHsIP8PLvCH0SBgWGcBN6BScwC72GO/ByeYfQJ2FQYEBlST/fcAehT8KgwICystJPPMS/Pd4nYeiG
        BpAVdh3iPcZxvsf7JAwebKlbpr/kP9iFvoCPoeeiZ4/3Sc9CbeliTeliTeliTeliPc3IX9pfvQlNxdSA
        AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="barBtnNotesReceivable.Hint" xml:space="preserve">
    <value />
  </data>
  <data name="barSubItemPrint.Caption" xml:space="preserve">
    <value>طباعة</value>
  </data>
  <data name="barbtnPrint.Caption" xml:space="preserve">
    <value>طباعة</value>
  </data>
  <data name="barbtnPrintF.Caption" xml:space="preserve">
    <value>طباعة بالاسم الأجنبي</value>
  </data>
  <data name="btnAttachments.Caption" xml:space="preserve">
    <value>مرفقات</value>
  </data>
  <data name="btnAttachments.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAA10RVh0VGl0
        bGUAQXR0YWNoO3wCt50AAALOSURBVDhPpZLbU1JBGMDXZBjFRDgckIuCCoqI3CVDRVFUlAYlvIF3RHTU
        YfKWKeLdvGRlaZpaPfqfNNP00JP/QA899WzP2/cdc8aXHpp25jd79vv2t/vtniWU0v/iX1oacO8ON805
        8Yk4xz6SCsAx9oE4Ri+II3aOqVsB+7Sy7j2jqffosrz/+MoYPlyDWDqXc8QviB0E+8gZsUXPiHX4PcQ5
        iWfuP8EaefrQZnl53xEt7dqLlQQ3XaVdB6f6zheHkEsnVpAsQ6fEMnBKzIMnnCzWVmYY+445odA3ZzSE
        D6k2sNqEi6GU75kqKe44+CU1t4mIaeAdKe875kBZqLZmGsJvaHHweVzTOGPRd72iuuAOlpUJ4JF4+d65
        em377k+B3CAgZZG3BAROvq80CvRdr2G3jXhe3aStOPSSFga2znKdfaai4D4eR5DnnW8vCOzQ3AdDEVyM
        wA6cnKUoE+hCB7TQvzYG4+yi9n2q8a+fS+3dloLALlV5nyYU7kSH2r9NWXtvFOaI0LuRoZSi9j2qaV4e
        V7onXZpH29/UvtSFxPzYhoLSM/tEXjXRnd+yRVlrBGUW4Cub19EnPI1/41LdsroD3xkq7+KwunWbig1+
        Jwpy9/S0rDLeo/JtUMbSMwJzxABf4V0lcoBksLosdevWdbbmoRQS6UJdPaNqTH1GQVadmGGd0YiicY2K
        TZ0xyDMo59aniMxzAxHpfTKVb/06U2HCsvBM+EDwxsUSRzQsb1ihOcbQKIw5WeZZJtK6JJHWJglbuwQh
        KELekLpiXVNYHoq8HGOHSuwY6YYdqNAQxEuVAHwUWPcikSA1yDMIg8BUxLy5dcnvEldiRWQb9DHO8YS0
        dolml7ahLEMZBQYEpvoGcfUCEVctECL1JHERfo454pbUzH9haxZ+MK7Zr1napjaIczvjZAYmo3AXjN02
        7u0DWYDwT49j7j//vRHyGw3t7WpPD3wwAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnAttachments.LargeGlyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAA10RVh0VGl0
        bGUAQXR0YWNoO3wCt50AAAjlSURBVFhHxZZpUJNJGsc7QEAcrnAGUK5wHwkJgXCFcJ8JhCMQCBAOATnl
        EBQGHEFBLhHFA3S8BQFBUWd2xp0d1wMInuPuuGpN1dSucjif9vMeH3qfTiAEdI+q3XK76ledfvrp5/9/
        +33fzoswxv9XPkWjbELd1AaKDt9QUnx4BhUPbOamBqpYEQFyVcDafhWFQAFQPPgrVHX+sWPdxJvWuvHX
        itqrb/5SPfpKUXnpxzb5wB0nkFQaWTdACqyaULFafE1ME03hVXEirBTvI/0Mqrn8wqt67PVC7vAPOLr1
        JvYrHcERzddxxqACF51+Ni49MMEgumoDBX3XYSFhvRhBVRzEVnvNOXKlZJ2aXtLPoPy+W0aVV169TRl4
        hNlFR//sKe3ocU1uinBPb+v1q7z0Vti/gKW9d9pBVkttIL93Gsk10ShMfn8A5BAxaMr7mt8zjQi5vTeN
        aq6+nk3sVWBW4cA75/gKT5jfAlABHaaspzd43z2c2HXvCYmvG1gtQIx8YEYD9Txs9Y6T9w3rJ3+arbn6
        klTRkvXMGFWPvZoV9c9jZl7/oktcmRfEiThF8vlZIkPhV46E8Vru4qCGG3+FsaHaQG73lAbTKG8NMKVk
        dUzmlePeW0a7xt/Mph59gtklQ++yDk5wKy//OCfsn8M+sp5FRnQJEdcHKBnNp1Ha3mEio53S8VUPd893
        2DWz7ymMaWoDsq5J4BqSHfonkPnOCWWfA1daNfZ6VnTkEfYtHFzkVZwMKDr3QpHYO4e9pF2LjoJcHyip
        FE9tOoXEjSeIBEVyYEYS23FXway6jp1iyrshZqw2kN05qSRnlbVxNohKD44j6YGrKJv0h24YVVx5NSs8
        vIBZ8oFFbskxXtGXPyjiux9gj8yORbtQKRPKbQUoKQ1DKLn+KClPCclrMxUduq/gNdzCtoKyJTt2DBfi
        +moD8FqAEEB6EMsidIyhrPZRlLn/irIXt14yLr34+7kkuFJv2Gbfgr4g+elnitiu+9gtbd+ibWCqL5T6
        DNAS1R5Bwl0DpDQlSNZqKhl4qODWzmDrkOJlGoPHg7gpyVMbkLSPqdg/iiRfXEEZ+y4DF1FG20UyTUlu
        uWC84/zv5uK6H2Kv7K5FZk5nSN6pJwtRB36LXVJalmz8k9mQZwgon3ayhuAv2W2W0f9A4bfrOqYHyZeN
        HfyCIG4GKMXVBtJBML3tEkpvvYjSWi+gtM/PobQW1ZMbW3vcpODMi7noznvYXdKx6CltD80+vrAQuf83
        mCFqWqKz4zmQZ8xNrzPPHppXSAcekKq63IzdZqk99xXsmmls4Z+7bGTnGwxxC0A7TLpbKa42IG45j8TN
        Z5F475coZc8ZlNI0QsKU6OpjJnkjz+Yi27/HLrDNcJjwJYNzjwRtv8aOCfVLlt5R5F6asJMrLMg2BzTe
        xjZhpcuc1LqYlEN3F3yrrmFzjnTZwNY7FPIsAW2+ZBcKSa/ZaEDUOIJEu4eRsOEUSqpXPbURFYMm2Sce
        z/NBjCFsWmIIG8PSDj98FNLyDbaPqV4y9xD4Qx6NKSq3SO17oODWzWA63GPr4LyE2C++eepTPolNWekr
        n9E9wiDPCtAOTqtCQeJyFAhsMJBUexwl1h5DCTWDZEgJKeykZQ4q5oOav8UO8XVLFl6R/on7b98J2XcX
        20aUL5m6hgRAnqlXXJFlcvc95TZbBeYvW/FykqLbvn7qVTqGaT7iFX0rNwHk0QGdwJRyxEveiXiiUhQg
        3LHRQGzlAIqp6CM/KYH5B2hp/bPzAY1f4e1RVUvmnuFkm824u6beMHd/iy18k8m9NPeIKbBM6vx+wady
        Cptzs1cs/KVJEc23n3mWXMEmnqIVfQvncMizBnQChCXIP2kH4iYWI25CIeLGF240EFVGzgVE8ctsNhUe
        uqfgwCtjI9i5RHMNJdtsAugx0rveeFZ/jQUN40cZYdk28e3fLXjvnMCm7MwVM45EJNhz47l78WVs5J60
        ssXMKRLW2AA63AQiWoD84glyFXHyjQagUViiatPEg3BSVU9jenDhsomT8n2lAVqANj1A2m0T34r9qiZw
        9N4b2FV2EtOY6SumLLE4tHH6uWvhRWzoGv9ez9QhCvJtAR1ObAFix8qBfBUxhDwlmw3oRDVNHfeGB4fO
        L182tOOQ91V5WPDSa8k8ZRsnwcHQWTBg6BL1swEjnDBPY4J4/dRzZ/k5bOAc817PxC4acrcDVN+YXPQB
        0WvINhjQCizsi+LW3vzJJrH9nYkLXwoxc0DbP6UCcUXlyE9URvLI4aILkINku76lKyu49tpz5/yz2IAR
        +V7XeFsMxO0AKjMqFzGjZOtE5qwTQcjeYEAnYOf5Ex5lZOtLyOlDnlqqn7AUcRJLEBvuITuhCLHhwfGN
        KyD5WvaBGVahDdOPGbln8FbHCBC3jYO4UtwHRNbJVgGCm9E0oMvaOfHSoWAcW3PEzTA2YcUVIVZsIWLG
        FAD5iBmdB1eSS3K17ALSrIJrpx47yU5jfQcQN7JJgLg9QPWGwmrCCVIVAhVegiw1mgb0HDJP/M0+bwxb
        MROSYWzgHZkLRWTIC7bLKzyH5JDt197ml0wPqpl84iQdAfHwX6hG1okQdwConlBUSVjmRvgfZ4MBc37d
        H7ZnX8ZuKW29MFb+nXrws8gcESZvga5nSgufVzXxxCGLiAt+oRpaCyHuCFA9+BL0AaEZyD1UApD+QzQN
        UOHgGLYSHsEe8rMvHMMKyemlR+Kkdxfu4XCKTu/llI0+tc8cVokb0EUwRz6vqRsKh6hwC0n/t2ga0KY5
        8eIMPTP/uE18DLOKLrzwlZ/qYMmH93CKzx5nl46+dJMNY4vAUqxvrxYnn9W6bsFQjBC0RpoS1/8ATQPK
        10ufzqreyoj/k2VoPXaVDGHPglHMyBjClvw6vNUp4S3VzH1oVdyZ5LsEpSKXQDFy1mDz+F+haYA0pYkt
        Fp6pVAvvMV0r35916X5/14Oeau41qUdzLIF5V4AcTroMnhj9t2w2QBoxQb5mjAFymrkD5APTBSB/KlvX
        Fv0v+WjwU/LR4KcDo38ATm2tT1tVgIEAAAAASUVORK5CYII=
</value>
  </data>
  <data name="barbtnPrintStore.Caption" xml:space="preserve">
    <value>إذن تسليم</value>
  </data>
  <data name="barbtnSamplesPr.Caption" xml:space="preserve">
    <value>طباعة باستخدام نموذج</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="barBtnDelete.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACl0RVh0VGl0
        bGUAUmVtb3ZlO0RlbGV0ZTtCYXJzO1JpYmJvbjtTdGFuZGFyZDtjVkgwAAACeElEQVQ4T6WTW0iTYRjH
        LXdw6nKtm0jaliWlkkrJKrtJb5KROYvMdEtrHqrNQ03zgI1ouF2kUnPqTSeJWVC2K01CyoEHyEGWkYEU
        HcjoYGu1Sq/+vc+3aV91Vw/8vu99/8/z/2/f9/KFAfgvgpdftZQhCN2XkBAqWoczhAzqcz1+AAmCh6WH
        zkwfK/s4uDv7CNvTMOmEcLRYZ6Te+OEiK9uLSecHhI8X623vr3RgfnIIU6fr0ZuZaWS6iIY9hfmmmfaz
        rOfBu0tOjOgL7EwX8gNEE0V63/dRNwK9bZgbvoGJk1W4nKau6tuTW/2yzYZ5bx8CN1sRGOjGxEG9j3kk
        /ACBa1u68YHJgK/udvi7rfhxrwf3K8rxzG7B3JgbX65aOX04X4tz8QnH6Uf5AfSc4s6k5MoR3T74upsx
        21WPQP8FfLt9EZ+6GjDbWYdBTQZssSozm40kDz+AikIkdsVa812tBh8cNXhrNTBKuHv/jq1olK+sZzPR
        DDqloPePgMjzqeraMYMeb6zleFWzf5FRXR5aUtIa2YyU8VcAZ25JSDV7dPmYaa3FdIkG06Uh2Pq19SiG
        8vbCtj558V/wA0TNaxLNd3Ky8cJShicFGZhiXFunxPWEeG5NPD9RiAFNFizKDQ3M89spRHcoEv1PTQcw
        mbsdj3LS0ROngFEQfcoojLL0xKkwqU3neFy0C05lkp95ZPyAiKYVKod78yZ4s9RwKVajUiSl540hTOKY
        JpdKCe/OLbiVshF1cmUH06P4AfRSYmqksQ6bTOmrCJqXhXSuZ4xY3sR6n6ulq5xsLyedH0BFg3S+MoYk
        tF+ohR4Z6QXShxX00uXfQdhPmOi/wI4pGN8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="barBtnDelete.LargeGlyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACl0RVh0VGl0
        bGUAUmVtb3ZlO0RlbGV0ZTtCYXJzO1JpYmJvbjtTdGFuZGFyZDtjVkgwAAAH1klEQVRYR8WWe1RVVR7H
        rzWTJSimiDQoXBB8XEF5KAqooMhDAVEBHQcZy1R8BKmAgoCggshDFDELRkB80SJsQT7CUBDE14gilK6k
        iIsP0CBFc6TVxPnO73cu5ybc66r+mX5rfdibffb+fb97n733uTIAfypaG/+fqP5ojz7EK91w/Y8E9+89
        XiPHywxIA/9K9O2G67/XCPd5lXiNeKMbzsFtPcZrMyAN7lu5cGGoMnlHpzI5qbMiIOA9anu9+1mfG4sX
        y5jaxYEqAlXwM+IvxBtlfvNDGhMTOxsTEjpL585dR239up+pTfQ2IIm/fm6Bf+SdvWn47706EeWeFJzx
        8wujZzwb0UTXg69kIq3Ml9SkFu93ytd3fdOuJPysvIafm2rwHdVPzfGNpmc63X1EE9oMvFbu57e+mcR/
        ulmNJ6WF6Dh+BD/VVaApIwWlvj1n8ktLveyX+/VUVYvrfObtE9aYnoTnV79A+6EP0JadimdVxWjcuR0n
        vLwjqA+vJL9ODQOvTJLLdRvi4zufXzuDjpLDeFyUK/KoKAfPa8rAiY97+fBKvDgTtXjxbK+wb1MT8J/L
        pWjLy8CDfUl4uDcRrRnb0PH5UdyKiu4ca2ioR315FTUMvOplMXLgxWUrnrQX5eOHgqxfOfoR2o98SIlP
        gQWKZ81mE7oEbzRG95jHrPCG5G14duEk2vanozWThbeidVc8WtJicTc5GtVLlz9zNjYZzFqE5goQffdZ
        2bxdGRTY1XYgE2353RzYg+/zmAz8eL4EDTu24hM3z3DqP5ApdPOIuJ20BT9WleBhVipadm3B/fQ43Cfh
        e8mbcGd7JM76enftVox/l/rzPtL6Cngp2Vm/3WMsV1YsWtj1IDsND/+1Ew+zmTQ8yCI+SsHTs0X4OiEO
        h6a7Rh1ycY26lbAZTys+ResHSbi/czPupsTg7g4STtyI5m3hOO3l2ZVsPnoN5e5P8JFmLQ0DHNL71E0Z
        MWp12fx5XS17t4uJuWyhZW3ZQ2Qk4DG905vx0SIdZYViGy8zz/ZOwgY0bw2HMm4dTrrNFBLk5nyMBxBq
        cQ4NAzkWCi64A3fsv93EfPXnPt5dd9PjcY/ZGYd7aTTDVNUsfyg+gEcnDlNbHM10A5Q0W2X8ejSRcGN0
        KEpcnIW4v5mEUC7eeLxXelxmGgay5SNlTyuOcVUyMSB+mHzNCU+PruakKNwhmmmGzYmRUNIsldsioNwS
        hiYWjV2L72JCSTgE325chSInRyFqiFEo5eB9ohbfa2RKhSo0DOwbZibr+KJA1nG6gP9Vm4geahRyzGGS
        cHtNEL6JCEbjhlVojFqDRkkwYiUa1r2L26FLcGtZAD62Hi9EDBr6Po3tId5ekifbPXQYVVWhYWDPW8ay
        RycOEvmyR8fzuUk8GYR+mL5hWKGtjVC/0BNfMgs8UB+goi7AHfX+7qjzd8MRxRjhPb3BfOEYENKl06et
        MEvG7BxsSP+qQsPASRc3Wfun+2Xtx/bzv7wCPJiTDMmwsd98cclifBU0Fzfmu3UzE7XzXFE7nxDLmaj6
        ewDSrOziacxQQjpyfR4czpQxqQPZlyo0DHw2xVX2fcE+rkrivHxvZlrbb7q0fCmUyVGoW+SF63Nn4Lov
        Mx3X5kzHdR8XKp1R4+OMbzatxvklQUi3nBBDY/nS4RUUTbTkpMk+mTCFqqrQMFDs4MKFJM4DB+0eNyG6
        +p1/oikhHLULPFHjTULe04ipqPGaiqte03B1NpUSs6bg6/DlqAxchFSFTSzlYBPqL+nHto5UqELDAEUP
        8XRL25iqoEA0xq1FrZ8b/k3JRTydcIXxYBy7ccAVdwdcdp+My26TcTNkCcoXBCBp1LjNlEufUO8HQoze
        BnqIpylsYug2RAMt6TVack5+RUxOIiRwaeZk5I0wFZiLMybhkqu9ihkTqZyIi1TWBy9C2bx5SDC3jKOc
        Q4geJrQZ4Hc+KGW0dcxZfz9ayhW01C6/Jic48QWXicgzkwsrdPRimQMWI4Rq5wnUbkfY4oKzLaqn2RG2
        uPGOH0q9vbHVbKxkgieoYYAb+B31T7SwCjvtMwe33n8bV2ZNpYSqxNXOlJw4T4lzTU2ENboDN1J/I2a1
        7sDIPPMRQtUUa5xXM16kymkcrv3DB8dd3RErH72B+vOtKH7KexvgS0f/qJ1zR11wIC7TO66eaoPzIpSQ
        qHKyRo7cWBLn88Q/ThgDNpFrZiqcc7BCJXFuMmOJc5NUXPbzQL6V4xPqy8dT/CZoNXDQ2qnt4mxnElO5
        r3JkKBklzTEZLrAQ9WNx6ecZw3WDVbp6UbmmcqHcXoGKiYT9GKjqY3BmoiVyFfbt3I/QaoATDYg0Hhlc
        aGMvVDqw87GoIMqJ/cbDeotLm4nhOrcNXamjtylXbiKcsRuFs0S5LZXE4VFWwjpD09XUh69n8Ui+aICD
        E/Em1I8wMl9bYGUjlNkpcGaCAllGRlrFa5cFyGqX+lO1hwlDNpE9fLhwevxIlI6zwEGLsUKogZx/RfHy
        SxeTSruXAX4gXr1hb5mt/FBu1U48CtbR4+85i6uPUbKevuxFuK37mbgSy3X0QjOHKx5nDlO0hxiY8I+R
        HlczoWGAQ0rCLnm38iCGl01yLg5+Sbw4/k2CvzwM19W3ISGGNgMcUhI+KvxKGK7/lrgUv3v8ywxIwZ1f
        5I/Gb45XG/jzgOx/2ZzKgxEwf90AAAAASUVORK5CYII=
</value>
  </data>
  <data name="txtInvoiceCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 23</value>
  </data>
  <data name="txtInvoiceCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 20</value>
  </data>
  <data name="dtInvoiceDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="dtInvoiceDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>226, 21</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 13</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="lblShipTo.Location" type="System.Drawing.Point, System.Drawing">
    <value>228, 66</value>
  </data>
  <data name="lblShipTo.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="lblShipTo.Text" xml:space="preserve">
    <value>شحن الى</value>
  </data>
  <data name="flowLayoutPanel1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="flowLayoutPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>278, 3</value>
  </data>
  <data name="flowLayoutPanel1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="flowLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>873, 99</value>
  </data>
  <data name="pnlBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>747, 1</value>
  </data>
  <data name="pnlBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 43</value>
  </data>
  <data name="txtTserial.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTserial.EditValue" xml:space="preserve">
    <value>الدفتر</value>
  </data>
  <data name="txtTserial.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 22</value>
  </data>
  <data name="lkp_InvoiceBook.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns1" xml:space="preserve">
    <value>اسم الدفتر</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns8" xml:space="preserve">
    <value>خاضع للضريبة</value>
  </data>
  <data name="lkp_InvoiceBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 20</value>
  </data>
  <data name="pnlInvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>662, 1</value>
  </data>
  <data name="txtTinvCode.EditValue" xml:space="preserve">
    <value>رقم الفاتورة</value>
  </data>
  <data name="txtTinvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 2</value>
  </data>
  <data name="txtTinvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 22</value>
  </data>
  <data name="pnlDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>531, 1</value>
  </data>
  <data name="txtTdate.EditValue" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="pnlBranch.Location" type="System.Drawing.Point, System.Drawing">
    <value>388, 1</value>
  </data>
  <data name="txtTstore.EditValue" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="txtTstore.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 2</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 23</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>كود الفرع</value>
  </data>
  <data name="lkpStore.Properties.Columns50" xml:space="preserve">
    <value>اسم الفرع</value>
  </data>
  <data name="pnlAgeDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>257, 1</value>
  </data>
  <data name="txtTdueDate.EditValue" xml:space="preserve">
    <value>تاريخ استحقاق</value>
  </data>
  <data name="txt_DueDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlDeliveryDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>160, 1</value>
  </data>
  <data name="txtTdeliverDate.EditValue" xml:space="preserve">
    <value>تاريخ تسليم</value>
  </data>
  <data name="dtDeliverDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlCurrency.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 1</value>
  </data>
  <data name="pnlCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 44</value>
  </data>
  <data name="uc_Currency1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 21</value>
  </data>
  <data name="uc_Currency1.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 19</value>
  </data>
  <data name="txtCurrency.EditValue" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="txtCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 22</value>
  </data>
  <data name="pnlPostStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>731, 49</value>
  </data>
  <data name="chk_IsPosted.Properties.Caption" xml:space="preserve">
    <value>مصروف من المخزن</value>
  </data>
  <data name="pnlPO.Location" type="System.Drawing.Point, System.Drawing">
    <value>644, 47</value>
  </data>
  <data name="pnlPO.Size" type="System.Drawing.Size, System.Drawing">
    <value>83, 44</value>
  </data>
  <data name="txtTpo.EditValue" xml:space="preserve">
    <value>أمر شراء</value>
  </data>
  <data name="txtTpo.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 22</value>
  </data>
  <data name="txt_PO_No.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 20</value>
  </data>
  <data name="pnlSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>464, 47</value>
  </data>
  <data name="txtTSalesEmp.EditValue" xml:space="preserve">
    <value>مسئول المبيعات</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns1" xml:space="preserve">
    <value>اسم الموظف</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns3" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="pnlCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>321, 47</value>
  </data>
  <data name="textEdit9.EditValue" xml:space="preserve">
    <value>مركز تكلفة</value>
  </data>
  <data name="gridColumn43.Caption" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="gridColumn42.Caption" xml:space="preserve">
    <value>مركز التكلفة</value>
  </data>
  <data name="txtNotes.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 5</value>
  </data>
  <data name="txtNotes.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 44</value>
  </data>
  <data name="txt_Shipping.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 50</value>
  </data>
  <data name="txt_Shipping.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 44</value>
  </data>
  <data name="lkp_Drawers.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 5</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns1" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns8" xml:space="preserve">
    <value>كود الحساب</value>
  </data>
  <data name="lkp_Drawers.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="lkp_Drawers.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>322, 8</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>حساب سداد 1</value>
  </data>
  <data name="lbl_remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>322, 51</value>
  </data>
  <data name="lbl_remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="lbl_remains.Text" xml:space="preserve">
    <value>متبقي</value>
  </data>
  <data name="txt_Remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 48</value>
  </data>
  <data name="txt_Remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="lbl_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 51</value>
  </data>
  <data name="lbl_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="lbl_Paid.Text" xml:space="preserve">
    <value>اجمالي مدفوع</value>
  </data>
  <data name="txt_paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 48</value>
  </data>
  <data name="txt_paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 20</value>
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="gridColumn20.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="gridColumn20.Width" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn21.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn21.Width" type="System.Int32, mscorlib">
    <value>438</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>الاسم ج</value>
  </data>
  <data name="gridColumn22.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn26.Caption" xml:space="preserve">
    <value>الفئة</value>
  </data>
  <data name="gridColumn27.Caption" xml:space="preserve">
    <value>المدينة</value>
  </data>
  <data name="gridColumn30.Caption" xml:space="preserve">
    <value>المحمول</value>
  </data>
  <data name="gridColumn30.Width" type="System.Int32, mscorlib">
    <value>181</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>227, 180</value>
  </data>
  <data name="colETaxValue.Caption" xml:space="preserve">
    <value>قيمة الضريبة</value>
  </data>
  <data name="colETaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="colETaxValue.Width" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="colETaxRatio.Caption" xml:space="preserve">
    <value>نسبة الضريبة</value>
  </data>
  <data name="colETaxRatio.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="colETaxRatio.Width" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="Col_ETaxType.Caption" xml:space="preserve">
    <value>نوع الضريبة</value>
  </data>
  <data name="Col_ETaxType.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="Col_ETaxType.Width" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="colBonusDiscount.Caption" xml:space="preserve">
    <value>خصم البونص</value>
  </data>
  <data name="colBonusDiscount.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colBonusDiscount.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="col_QC.Caption" xml:space="preserve">
    <value>م.ج</value>
  </data>
  <data name="col_ActualPiecesCount.Caption" xml:space="preserve">
    <value>عدد القطع الحالى</value>
  </data>
  <data name="col_TotalSellPrice.Caption" xml:space="preserve">
    <value>اجمالي</value>
  </data>
  <data name="col_TotalSellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_TotalSellPrice.Width" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="col_CurrentQty.Caption" xml:space="preserve">
    <value>كمية حالية</value>
  </data>
  <data name="col_CurrentQty.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_CurrentQty.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="col_CurrentQty.Width" type="System.Int32, mscorlib">
    <value>89</value>
  </data>
  <data name="col_CommercialDiscountValue.Caption" xml:space="preserve">
    <value>قيمة الخصم</value>
  </data>
  <data name="col_CommercialDiscountValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="col_CommercialDiscountValue.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>ن خصم</value>
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="col_SellPrice.Caption" xml:space="preserve">
    <value>س بيع</value>
  </data>
  <data name="col_SellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="col_SellPrice.Width" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="col_AudiencePrice.Caption" xml:space="preserve">
    <value>سعر الجمهور</value>
  </data>
  <data name="colPurchasePrice.Caption" xml:space="preserve">
    <value>س شراء</value>
  </data>
  <data name="colPurchasePrice.Width" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>وحدة قياس</value>
  </data>
  <data name="gridColumn8.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="gridColumn8.Width" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="col_ItemNameF.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="col_ItemNameF.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_ItemNameF.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="gridColumn10.VisibleIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="gridColumn10.Width" type="System.Int32, mscorlib">
    <value>164</value>
  </data>
  <data name="grdcol_branch.Caption" xml:space="preserve">
    <value>المخزن</value>
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="gridColumn11.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn11.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn11.Width" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="gridColumn31.Caption" xml:space="preserve">
    <value>كود1</value>
  </data>
  <data name="gridColumn31.VisibleIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="gridColumn31.Width" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="col_Expire.Caption" xml:space="preserve">
    <value>تاريخ صلاحية</value>
  </data>
  <data name="col_Expire.Width" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="col_Batch.Caption" xml:space="preserve">
    <value>التشغيلة</value>
  </data>
  <data name="col_Batch.Width" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="col_Length.Caption" xml:space="preserve">
    <value>طول</value>
  </data>
  <data name="col_Width.Caption" xml:space="preserve">
    <value>عرض</value>
  </data>
  <data name="col_Height.Caption" xml:space="preserve">
    <value>ارتفاع</value>
  </data>
  <data name="col_TotalQty.Caption" xml:space="preserve">
    <value>اجمالي كمية</value>
  </data>
  <data name="col_PiecesCount.Caption" xml:space="preserve">
    <value>عدد القطع</value>
  </data>
  <data name="col_ItemDescription.Caption" xml:space="preserve">
    <value>وصف</value>
  </data>
  <data name="col_ItemDescriptionEn.Caption" xml:space="preserve">
    <value>وصف ج</value>
  </data>
  <data name="col_SalesTax.Caption" xml:space="preserve">
    <value>ض . ع</value>
  </data>
  <data name="col_SalesTax.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_SalesTax.VisibleIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="col_DiscountRatio2.Caption" xml:space="preserve">
    <value>ن خصم 2</value>
  </data>
  <data name="col_DiscountRatio3.Caption" xml:space="preserve">
    <value>ن خصم 3</value>
  </data>
  <data name="col_CusTax.Caption" xml:space="preserve">
    <value>ق ض الجدول</value>
  </data>
  <data name="col_CusTax.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CusTax.VisibleIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="col_Location.Caption" xml:space="preserve">
    <value>الموقع</value>
  </data>
  <data name="col_Libra.Caption" xml:space="preserve">
    <value>كمية ليبرا</value>
  </data>
  <data name="col_kg_Weight_libra.Caption" xml:space="preserve">
    <value>الوزن بالكيلو</value>
  </data>
  <data name="ol_Index.VisibleIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="ol_Index.Width" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="col_Pack.Caption" xml:space="preserve">
    <value>حزمة</value>
  </data>
  <data name="btn_AddTaxes.Caption" xml:space="preserve">
    <value>اضافة اكثر من ضريبة</value>
  </data>
  <data name="btn_AddTaxes.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="btn_AddTaxes.Width" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="TotalTaxes.Caption" xml:space="preserve">
    <value>اجمالي الضرايب الاخري</value>
  </data>
  <data name="TotalTaxes.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="TotalTaxes.Width" type="System.Int32, mscorlib">
    <value>95</value>
  </data>
  <data name="salePriceWithTaxTable.Caption" xml:space="preserve">
    <value>صافي السعر متضمن ضرايب الجدول وما شابه</value>
  </data>
  <data name="salePriceWithTaxTable.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="salePriceWithTaxTable.Width" type="System.Int32, mscorlib">
    <value>122</value>
  </data>
  <data name="addTaxValue.Caption" xml:space="preserve">
    <value>ضريبة قيمة مضافة</value>
  </data>
  <data name="addTaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="tableTaxValue.Caption" xml:space="preserve">
    <value>ضريبة جدول</value>
  </data>
  <data name="tableTaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="col_TaxValue.Caption" xml:space="preserve">
    <value>م. الضرائب الفرعيه</value>
  </data>
  <data name="col_TaxValue.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_TaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="col_TotalSubCustomTax.Caption" xml:space="preserve">
    <value>ض الجدول الفرعيه</value>
  </data>
  <data name="col_TotalSubCustomTax.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_TotalSubCustomTax.VisibleIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="col_TotalSubAddTax.Caption" xml:space="preserve">
    <value>ض قيمه مضافه</value>
  </data>
  <data name="col_TotalSubAddTax.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_TotalSubAddTax.VisibleIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="col_TotalSubDiscountTax.Caption" xml:space="preserve">
    <value>ض الخصم الفرعيه</value>
  </data>
  <data name="col_TotalSubDiscountTax.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_TotalSubDiscountTax.VisibleIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="grdPrInvoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>1152, 189</value>
  </data>
  <data name="mi_frm_IC_Item.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_frm_IC_Item.Text" xml:space="preserve">
    <value>بيانات الصنف</value>
  </data>
  <data name="mi_CustLastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_CustLastPrices.Text" xml:space="preserve">
    <value>عرض اخر اسعار لعميل</value>
  </data>
  <data name="mi_LastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_LastPrices.Text" xml:space="preserve">
    <value>عرض اخر اسعار</value>
  </data>
  <data name="mi_PasteRows.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_PasteRows.Text" xml:space="preserve">
    <value>لصق الصفوف</value>
  </data>
  <data name="mi_ExportData.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_ExportData.Text" xml:space="preserve">
    <value>تصدير البيانات</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Text" xml:space="preserve">
    <value>تثبيت خصومات الفاتورة</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Text" xml:space="preserve">
    <value>تثبيت الأبعاد</value>
  </data>
  <data name="mi_ImportExcel.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_ImportExcel.Text" xml:space="preserve">
    <value>تحميل الأصناف من ملف اكسيل</value>
  </data>
  <data name="colTotalPurchasePrice.Caption" xml:space="preserve">
    <value>الاجمالي</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>س بيع</value>
  </data>
  <data name="colUOM.Caption" xml:space="preserve">
    <value>الوحده</value>
  </data>
  <data name="colQty.Caption" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="colCustNameAr.Caption" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="colInvoiceDate.Caption" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="colInvoiceCode.Caption" xml:space="preserve">
    <value>رقم الفاتورة</value>
  </data>
  <data name="grdLastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>482, 148</value>
  </data>
  <data name="txt_Balance_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 56</value>
  </data>
  <data name="txt_Balance_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>127, 13</value>
  </data>
  <data name="txt_Balance_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>165, 32</value>
  </data>
  <data name="txt_Balance_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>127, 13</value>
  </data>
  <data name="txt_MaxCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 9</value>
  </data>
  <data name="txt_MaxCredit.Size" type="System.Drawing.Size, System.Drawing">
    <value>127, 13</value>
  </data>
  <data name="lbl_IsCredit_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>319, 55</value>
  </data>
  <data name="lbl_IsCredit_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lbl_IsCredit_After.Text" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="lblBlncAftr.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblBlncAftr.Location" type="System.Drawing.Point, System.Drawing">
    <value>354, 55</value>
  </data>
  <data name="lblBlncAftr.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 13</value>
  </data>
  <data name="lblBlncAftr.Text" xml:space="preserve">
    <value>رصيد بعد الفاتورة</value>
  </data>
  <data name="lbl_IsCredit_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>319, 32</value>
  </data>
  <data name="lbl_IsCredit_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lbl_IsCredit_Before.Text" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>354, 32</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>رصيد سابق</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>354, 9</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 13</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>حد ائتمان العميل</value>
  </data>
  <data name="lkp_Drawers2.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 27</value>
  </data>
  <data name="lkp_Drawers2.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="lkp_Drawers2.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="txt_PayAcc1_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 4</value>
  </data>
  <data name="txt_PayAcc1_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 20</value>
  </data>
  <data name="txt_PayAcc1_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="labelControl30.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 30</value>
  </data>
  <data name="labelControl30.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl30.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="labelControl31.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 8</value>
  </data>
  <data name="labelControl31.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl31.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="txt_PayAcc2_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 26</value>
  </data>
  <data name="txt_PayAcc2_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 20</value>
  </data>
  <data name="txt_PayAcc2_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="labelControl33.Location" type="System.Drawing.Point, System.Drawing">
    <value>322, 30</value>
  </data>
  <data name="labelControl33.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="labelControl33.Text" xml:space="preserve">
    <value>حساب سداد 2</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 75</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>417, 71</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="btn_ShowAccStatement.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btn_ShowAccStatement.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 18</value>
  </data>
  <data name="btn_ShowAccStatement.Size" type="System.Drawing.Size, System.Drawing">
    <value>157, 70</value>
  </data>
  <data name="btn_ShowAccStatement.Text" xml:space="preserve">
    <value>عرض كشف الحساب</value>
  </data>
  <data name="btn_ShowAccStatement.ToolTip" xml:space="preserve">
    <value>عرض كشف حساب العميل</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>156, 75</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>300, 19</value>
  </data>
  <data name="lkp_Cars.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_Cars.Properties.Columns8" xml:space="preserve">
    <value>السيارة</value>
  </data>
  <data name="lkp_Cars.Properties.Columns10" xml:space="preserve">
    <value>رقم اللوحة</value>
  </data>
  <data name="txtScaleSerial.TabIndex" type="System.Int32, mscorlib">
    <value>242</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 13</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>مسلسل</value>
  </data>
  <data name="txtDestination.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblDestination.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="lblDestination.Text" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="txtVehicleNumber.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblVehicleNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="lblVehicleNumber.Text" xml:space="preserve">
    <value>رقم العربة</value>
  </data>
  <data name="txtDriverName.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblDriverName.Text" xml:space="preserve">
    <value>اسم السائق</value>
  </data>
  <data name="labelControl54.Size" type="System.Drawing.Size, System.Drawing">
    <value>36, 13</value>
  </data>
  <data name="labelControl54.Text" xml:space="preserve">
    <value>التوصيل</value>
  </data>
  <data name="lkpDelivery.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpDelivery.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpDelivery.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 20</value>
  </data>
  <data name="labelControl52.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="labelControl52.Text" xml:space="preserve">
    <value>مندوب البيع</value>
  </data>
  <data name="txt_Sales.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_Sales.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_Sales.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="labelControl51.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl51.Text" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="labelControl48.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="labelControl48.Text" xml:space="preserve">
    <value>الهاتف</value>
  </data>
  <data name="labelControl47.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="labelControl47.Text" xml:space="preserve">
    <value>المحمول</value>
  </data>
  <data name="textEdit3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="textEdit3.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit3.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="textEdit3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit3.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit3.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit3.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit3.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit3.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="textEdit3.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_JOStatus.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="lkp_JOStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lkp_JOStatus.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_JOStatus.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="lkp_JOStatus.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="textEdit1.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="textEdit1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="textEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_JOPriority.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="lkp_JOPriority.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lkp_JOPriority.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_JOPriority.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOPriority.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="lkp_JOPriority.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="textEdit5.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="textEdit5.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit5.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="textEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_JOSalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="lkp_JOSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lkp_JOSalesEmp.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_JOSalesEmp.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="lkp_JOSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="textEdit8.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit8.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="textEdit8.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit8.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit8.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="textEdit8.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_JODept.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="lkp_JODept.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lkp_JODept.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_JODept.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JODept.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="lkp_JODept.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="textEdit4.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit4.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="textEdit4.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit4.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit4.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit4.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit4.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit4.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit4.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="textEdit4.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_JOJob.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="txt_JOJob.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txt_JOJob.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JOJob.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_JOJob.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JOJob.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JOJob.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JOJob.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_JOJob.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_JOJob.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="textEdit2.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="textEdit2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit2.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit2.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit2.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit2.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit2.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="textEdit2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_JODeliveryDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="txt_JODeliveryDate.EditValue" type="System.DateTime, mscorlib">
    <value>2021-12-04</value>
  </data>
  <data name="txt_JODeliveryDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_JODeliveryDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_JODeliveryDate.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="textEdit7.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="textEdit7.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit7.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit7.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="textEdit7.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="textEdit6.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="textEdit6.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit6.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit6.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="textEdit6.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_JORegDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="txt_JORegDate.EditValue" type="System.DateTime, mscorlib">
    <value>2021-12-04</value>
  </data>
  <data name="txt_JORegDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txt_JORegDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_JORegDate.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="txt_JORegDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JORegDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="txt_JORegDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JORegDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JORegDate.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_JORegDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_JORegDate.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_JOCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="txt_JOCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txt_JOCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JOCode.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_JOCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JOCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JOCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_JOCode.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_JOCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_JOCode.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
</root>