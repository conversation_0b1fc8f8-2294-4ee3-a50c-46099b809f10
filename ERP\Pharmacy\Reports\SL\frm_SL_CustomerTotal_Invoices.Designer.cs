﻿namespace Reports
{
    partial class frm_SL_CustomerTotal_Invoices
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_CustomerTotal_Invoices));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.popupMenu1 = new DevExpress.XtraBars.PopupMenu();
            this.btn_Landscape = new DevExpress.XtraBars.BarButtonItem();
            this.btn_Portrait = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            this.grdCategory = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Total = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustomerId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Expenses = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPaid = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRemains = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Net = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalCostPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Profit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ProfitRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CategoryId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_CategoryId = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_CrncId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repCrncy = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_CrncRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_paymethod = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.rep_salesEmp = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.rep_InvoiceBook = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.rep_User = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.rep_Accounts = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CategoryId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCrncy)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_paymethod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_salesEmp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_InvoiceBook)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_User)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Accounts)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh,
            this.btn_Landscape,
            this.btn_Portrait});
            this.barManager1.MaxItemId = 31;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPreview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnPreview
            // 
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.ActAsDropDown = true;
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPreview.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barBtnPreview.DropDownControl = this.popupMenu1;
            this.barBtnPreview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Preview_ItemClick);
            // 
            // popupMenu1
            // 
            this.popupMenu1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btn_Landscape),
            new DevExpress.XtraBars.LinkPersistInfo(this.btn_Portrait)});
            this.popupMenu1.Manager = this.barManager1;
            this.popupMenu1.MenuAppearance.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("popupMenu1.MenuAppearance.HeaderItemAppearance.FontSizeDelta")));
            this.popupMenu1.MenuAppearance.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("popupMenu1.MenuAppearance.HeaderItemAppearance.FontStyleDelta")));
            this.popupMenu1.MenuAppearance.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("popupMenu1.MenuAppearance.HeaderItemAppearance.GradientMode")));
            this.popupMenu1.MenuAppearance.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("popupMenu1.MenuAppearance.HeaderItemAppearance.Image")));
            this.popupMenu1.Name = "popupMenu1";
            // 
            // btn_Landscape
            // 
            resources.ApplyResources(this.btn_Landscape, "btn_Landscape");
            this.btn_Landscape.Id = 29;
            this.btn_Landscape.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.btn_Landscape.Name = "btn_Landscape";
            this.btn_Landscape.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btn_Landscape_ItemClick);
            // 
            // btn_Portrait
            // 
            resources.ApplyResources(this.btn_Portrait, "btn_Portrait");
            this.btn_Portrait.Id = 30;
            this.btn_Portrait.Name = "btn_Portrait";
            this.btn_Portrait.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btn_Portrait_ItemClick);
            // 
            // barBtnRefresh
            // 
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.MenuManager = this.barManager1;
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.AccessibleDescription = resources.GetString("picLogo.Properties.AccessibleDescription");
            this.picLogo.Properties.AccessibleName = resources.GetString("picLogo.Properties.AccessibleName");
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.AccessibleDescription = resources.GetString("lblReportName.Properties.AccessibleDescription");
            this.lblReportName.Properties.AccessibleName = resources.GetString("lblReportName.Properties.AccessibleName");
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblReportName.Properties.Appearance.FontSizeDelta")));
            this.lblReportName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblReportName.Properties.Appearance.FontStyleDelta")));
            this.lblReportName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblReportName.Properties.Appearance.GradientMode")));
            this.lblReportName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblReportName.Properties.Appearance.Image")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblReportName.Properties.AutoHeight = ((bool)(resources.GetObject("lblReportName.Properties.AutoHeight")));
            this.lblReportName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblReportName.Properties.Mask.AutoComplete")));
            this.lblReportName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblReportName.Properties.Mask.BeepOnError")));
            this.lblReportName.Properties.Mask.EditMask = resources.GetString("lblReportName.Properties.Mask.EditMask");
            this.lblReportName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblReportName.Properties.Mask.IgnoreMaskBlank")));
            this.lblReportName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblReportName.Properties.Mask.MaskType")));
            this.lblReportName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblReportName.Properties.Mask.PlaceHolder")));
            this.lblReportName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblReportName.Properties.Mask.SaveLiteral")));
            this.lblReportName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblReportName.Properties.Mask.ShowPlaceHolders")));
            this.lblReportName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblReportName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblReportName.Properties.NullValuePrompt = resources.GetString("lblReportName.Properties.NullValuePrompt");
            this.lblReportName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblReportName.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.AccessibleDescription = resources.GetString("lblDateFilter.Properties.AccessibleDescription");
            this.lblDateFilter.Properties.AccessibleName = resources.GetString("lblDateFilter.Properties.AccessibleName");
            this.lblDateFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblDateFilter.Properties.Appearance.FontSizeDelta")));
            this.lblDateFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblDateFilter.Properties.Appearance.FontStyleDelta")));
            this.lblDateFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblDateFilter.Properties.Appearance.GradientMode")));
            this.lblDateFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblDateFilter.Properties.Appearance.Image")));
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblDateFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblDateFilter.Properties.AutoHeight")));
            this.lblDateFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblDateFilter.Properties.Mask.AutoComplete")));
            this.lblDateFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.BeepOnError")));
            this.lblDateFilter.Properties.Mask.EditMask = resources.GetString("lblDateFilter.Properties.Mask.EditMask");
            this.lblDateFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblDateFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblDateFilter.Properties.Mask.MaskType")));
            this.lblDateFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblDateFilter.Properties.Mask.PlaceHolder")));
            this.lblDateFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.SaveLiteral")));
            this.lblDateFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblDateFilter.Properties.NullValuePrompt = resources.GetString("lblDateFilter.Properties.NullValuePrompt");
            this.lblDateFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblDateFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.AccessibleDescription = resources.GetString("lblFilter.Properties.AccessibleDescription");
            this.lblFilter.Properties.AccessibleName = resources.GetString("lblFilter.Properties.AccessibleName");
            this.lblFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblFilter.Properties.Appearance.FontSizeDelta")));
            this.lblFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblFilter.Properties.Appearance.FontStyleDelta")));
            this.lblFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblFilter.Properties.Appearance.GradientMode")));
            this.lblFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblFilter.Properties.Appearance.Image")));
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblFilter.Properties.AutoHeight")));
            this.lblFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblFilter.Properties.Mask.AutoComplete")));
            this.lblFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblFilter.Properties.Mask.BeepOnError")));
            this.lblFilter.Properties.Mask.EditMask = resources.GetString("lblFilter.Properties.Mask.EditMask");
            this.lblFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblFilter.Properties.Mask.MaskType")));
            this.lblFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblFilter.Properties.Mask.PlaceHolder")));
            this.lblFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblFilter.Properties.Mask.SaveLiteral")));
            this.lblFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblFilter.Properties.NullValuePrompt = resources.GetString("lblFilter.Properties.NullValuePrompt");
            this.lblFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // grdCategory
            // 
            resources.ApplyResources(this.grdCategory, "grdCategory");
            this.grdCategory.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdCategory.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdCategory.EmbeddedNavigator.AccessibleDescription");
            this.grdCategory.EmbeddedNavigator.AccessibleName = resources.GetString("grdCategory.EmbeddedNavigator.AccessibleName");
            this.grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdCategory.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdCategory.EmbeddedNavigator.Anchor")));
            this.grdCategory.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdCategory.EmbeddedNavigator.BackgroundImage")));
            this.grdCategory.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdCategory.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdCategory.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdCategory.EmbeddedNavigator.ImeMode")));
            this.grdCategory.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdCategory.EmbeddedNavigator.MaximumSize")));
            this.grdCategory.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdCategory.EmbeddedNavigator.TextLocation")));
            this.grdCategory.EmbeddedNavigator.ToolTip = resources.GetString("grdCategory.EmbeddedNavigator.ToolTip");
            this.grdCategory.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdCategory.EmbeddedNavigator.ToolTipIconType")));
            this.grdCategory.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdCategory.EmbeddedNavigator.ToolTipTitle");
            this.grdCategory.MainView = this.gridView1;
            this.grdCategory.Name = "grdCategory";
            this.grdCategory.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_paymethod,
            this.rep_salesEmp,
            this.rep_CategoryId,
            this.rep_InvoiceBook,
            this.repCrncy,
            this.rep_User,
            this.rep_Accounts});
            this.grdCategory.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.FooterPanel.FontSizeDelta")));
            this.gridView1.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.FooterPanel.FontStyleDelta")));
            this.gridView1.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.FooterPanel.GradientMode")));
            this.gridView1.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.FooterPanel.Image")));
            this.gridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.GroupPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupPanel.FontSizeDelta")));
            this.gridView1.Appearance.GroupPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupPanel.FontStyleDelta")));
            this.gridView1.Appearance.GroupPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupPanel.GradientMode")));
            this.gridView1.Appearance.GroupPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupPanel.Image")));
            this.gridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupRow.FontSizeDelta")));
            this.gridView1.Appearance.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupRow.FontStyleDelta")));
            this.gridView1.Appearance.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupRow.GradientMode")));
            this.gridView1.Appearance.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupRow.Image")));
            this.gridView1.Appearance.GroupRow.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.gridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Image")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.GradientMode")));
            this.gridView1.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.Image")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupRow.GradientMode")));
            this.gridView1.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupRow.Image")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.gridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Image")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Lines.FontSizeDelta")));
            this.gridView1.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Lines.FontStyleDelta")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Lines.GradientMode")));
            this.gridView1.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Lines.Image")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Row.FontSizeDelta")));
            this.gridView1.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Row.FontStyleDelta")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Row.GradientMode")));
            this.gridView1.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Row.Image")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.ColumnPanelRowHeight = 35;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Total,
            this.col_CustomerId,
            this.col_DiscountRatio,
            this.col_DiscountValue,
            this.col_Expenses,
            this.colPaid,
            this.colRemains,
            this.col_CustId,
            this.col_Net,
            this.col_TotalCostPrice,
            this.col_Profit,
            this.col_ProfitRatio,
            this.col_CategoryId,
            this.col_CrncId,
            this.col_CrncRate,
            this.gridColumn1,
            this.gridColumn2});
            this.gridView1.CustomizationFormBounds = new System.Drawing.Rectangle(959, 364, 210, 277);
            this.gridView1.GridControl = this.grdCategory;
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary"))), resources.GetString("gridView1.GroupSummary1"), this.col_DiscountValue, resources.GetString("gridView1.GroupSummary2")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary3"))), resources.GetString("gridView1.GroupSummary4"), this.col_Expenses, resources.GetString("gridView1.GroupSummary5")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary6"))), resources.GetString("gridView1.GroupSummary7"), this.col_Net, resources.GetString("gridView1.GroupSummary8")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary9"))), resources.GetString("gridView1.GroupSummary10"), this.col_TotalCostPrice, resources.GetString("gridView1.GroupSummary11"))});
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // col_Total
            // 
            resources.ApplyResources(this.col_Total, "col_Total");
            this.col_Total.FieldName = "Total";
            this.col_Total.Name = "col_Total";
            this.col_Total.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Total.Summary"))), resources.GetString("col_Total.Summary1"), resources.GetString("col_Total.Summary2"))});
            // 
            // col_CustomerId
            // 
            resources.ApplyResources(this.col_CustomerId, "col_CustomerId");
            this.col_CustomerId.FieldName = "CustomerId";
            this.col_CustomerId.Name = "col_CustomerId";
            this.col_CustomerId.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_DiscountRatio
            // 
            resources.ApplyResources(this.col_DiscountRatio, "col_DiscountRatio");
            this.col_DiscountRatio.DisplayFormat.FormatString = "n2";
            this.col_DiscountRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountRatio.FieldName = "DiscountRatio";
            this.col_DiscountRatio.GroupFormat.FormatString = "n2";
            this.col_DiscountRatio.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountRatio.Name = "col_DiscountRatio";
            this.col_DiscountRatio.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_DiscountValue
            // 
            resources.ApplyResources(this.col_DiscountValue, "col_DiscountValue");
            this.col_DiscountValue.DisplayFormat.FormatString = "n2";
            this.col_DiscountValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountValue.FieldName = "DiscountValue";
            this.col_DiscountValue.GroupFormat.FormatString = "n2";
            this.col_DiscountValue.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountValue.Name = "col_DiscountValue";
            this.col_DiscountValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DiscountValue.Summary"))), resources.GetString("col_DiscountValue.Summary1"), resources.GetString("col_DiscountValue.Summary2"))});
            // 
            // col_Expenses
            // 
            resources.ApplyResources(this.col_Expenses, "col_Expenses");
            this.col_Expenses.DisplayFormat.FormatString = "n2";
            this.col_Expenses.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Expenses.FieldName = "Expenses";
            this.col_Expenses.GroupFormat.FormatString = "n2";
            this.col_Expenses.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Expenses.Name = "col_Expenses";
            this.col_Expenses.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Expenses.Summary"))), resources.GetString("col_Expenses.Summary1"), resources.GetString("col_Expenses.Summary2"))});
            // 
            // colPaid
            // 
            resources.ApplyResources(this.colPaid, "colPaid");
            this.colPaid.DisplayFormat.FormatString = "n2";
            this.colPaid.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPaid.FieldName = "Paid";
            this.colPaid.GroupFormat.FormatString = "n2";
            this.colPaid.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPaid.Name = "colPaid";
            this.colPaid.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colPaid.Summary"))), resources.GetString("colPaid.Summary1"), resources.GetString("colPaid.Summary2"))});
            // 
            // colRemains
            // 
            resources.ApplyResources(this.colRemains, "colRemains");
            this.colRemains.DisplayFormat.FormatString = "n2";
            this.colRemains.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colRemains.FieldName = "Remains";
            this.colRemains.GroupFormat.FormatString = "n2";
            this.colRemains.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colRemains.Name = "colRemains";
            this.colRemains.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colRemains.Summary"))), resources.GetString("colRemains.Summary1"), resources.GetString("colRemains.Summary2"))});
            // 
            // col_CustId
            // 
            resources.ApplyResources(this.col_CustId, "col_CustId");
            this.col_CustId.FieldName = "CustId";
            this.col_CustId.Name = "col_CustId";
            this.col_CustId.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_Net
            // 
            resources.ApplyResources(this.col_Net, "col_Net");
            this.col_Net.DisplayFormat.FormatString = "n2";
            this.col_Net.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Net.FieldName = "Net";
            this.col_Net.GroupFormat.FormatString = "n2";
            this.col_Net.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Net.Name = "col_Net";
            this.col_Net.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_Net.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Net.Summary"))), resources.GetString("col_Net.Summary1"), resources.GetString("col_Net.Summary2"))});
            // 
            // col_TotalCostPrice
            // 
            resources.ApplyResources(this.col_TotalCostPrice, "col_TotalCostPrice");
            this.col_TotalCostPrice.DisplayFormat.FormatString = "n2";
            this.col_TotalCostPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalCostPrice.FieldName = "TotalCostPrice";
            this.col_TotalCostPrice.GroupFormat.FormatString = "n2";
            this.col_TotalCostPrice.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalCostPrice.Name = "col_TotalCostPrice";
            this.col_TotalCostPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalCostPrice.Summary"))), resources.GetString("col_TotalCostPrice.Summary1"), resources.GetString("col_TotalCostPrice.Summary2"))});
            // 
            // col_Profit
            // 
            resources.ApplyResources(this.col_Profit, "col_Profit");
            this.col_Profit.DisplayFormat.FormatString = "n2";
            this.col_Profit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Profit.FieldName = "Profit";
            this.col_Profit.GroupFormat.FormatString = "n2";
            this.col_Profit.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Profit.Name = "col_Profit";
            this.col_Profit.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Profit.Summary"))), resources.GetString("col_Profit.Summary1"), resources.GetString("col_Profit.Summary2"))});
            // 
            // col_ProfitRatio
            // 
            resources.ApplyResources(this.col_ProfitRatio, "col_ProfitRatio");
            this.col_ProfitRatio.DisplayFormat.FormatString = "p2";
            this.col_ProfitRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ProfitRatio.FieldName = "profitRatio";
            this.col_ProfitRatio.Name = "col_ProfitRatio";
            this.col_ProfitRatio.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ProfitRatio.Summary"))), resources.GetString("col_ProfitRatio.Summary1"), resources.GetString("col_ProfitRatio.Summary2"))});
            // 
            // col_CategoryId
            // 
            resources.ApplyResources(this.col_CategoryId, "col_CategoryId");
            this.col_CategoryId.ColumnEdit = this.rep_CategoryId;
            this.col_CategoryId.FieldName = "CategoryId";
            this.col_CategoryId.Name = "col_CategoryId";
            // 
            // rep_CategoryId
            // 
            resources.ApplyResources(this.rep_CategoryId, "rep_CategoryId");
            this.rep_CategoryId.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_CategoryId.Buttons"))))});
            this.rep_CategoryId.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_CategoryId.Columns"), resources.GetString("rep_CategoryId.Columns1"), ((int)(resources.GetObject("rep_CategoryId.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_CategoryId.Columns3"))), resources.GetString("rep_CategoryId.Columns4"), ((bool)(resources.GetObject("rep_CategoryId.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_CategoryId.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_CategoryId.Columns7"), resources.GetString("rep_CategoryId.Columns8"))});
            this.rep_CategoryId.Name = "rep_CategoryId";
            this.rep_CategoryId.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // col_CrncId
            // 
            resources.ApplyResources(this.col_CrncId, "col_CrncId");
            this.col_CrncId.ColumnEdit = this.repCrncy;
            this.col_CrncId.FieldName = "CrncId";
            this.col_CrncId.Name = "col_CrncId";
            // 
            // repCrncy
            // 
            resources.ApplyResources(this.repCrncy, "repCrncy");
            this.repCrncy.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repCrncy.Buttons"))))});
            this.repCrncy.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("repCrncy.Columns"), resources.GetString("repCrncy.Columns1"), ((int)(resources.GetObject("repCrncy.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("repCrncy.Columns3"))), resources.GetString("repCrncy.Columns4"), ((bool)(resources.GetObject("repCrncy.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("repCrncy.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("repCrncy.Columns7"), resources.GetString("repCrncy.Columns8"))});
            this.repCrncy.Name = "repCrncy";
            // 
            // col_CrncRate
            // 
            resources.ApplyResources(this.col_CrncRate, "col_CrncRate");
            this.col_CrncRate.FieldName = "CrncRate";
            this.col_CrncRate.Name = "col_CrncRate";
            // 
            // rep_paymethod
            // 
            resources.ApplyResources(this.rep_paymethod, "rep_paymethod");
            this.rep_paymethod.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_paymethod.Buttons"))))});
            this.rep_paymethod.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_paymethod.Items"), ((object)(resources.GetObject("rep_paymethod.Items1"))), ((int)(resources.GetObject("rep_paymethod.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_paymethod.Items3"), ((object)(resources.GetObject("rep_paymethod.Items4"))), ((int)(resources.GetObject("rep_paymethod.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_paymethod.Items6"), ((object)(resources.GetObject("rep_paymethod.Items7"))), ((int)(resources.GetObject("rep_paymethod.Items8"))))});
            this.rep_paymethod.Name = "rep_paymethod";
            // 
            // rep_salesEmp
            // 
            resources.ApplyResources(this.rep_salesEmp, "rep_salesEmp");
            this.rep_salesEmp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_salesEmp.Buttons"))))});
            this.rep_salesEmp.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_salesEmp.Columns"), resources.GetString("rep_salesEmp.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_salesEmp.Columns2"), resources.GetString("rep_salesEmp.Columns3"), ((int)(resources.GetObject("rep_salesEmp.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_salesEmp.Columns5"))), resources.GetString("rep_salesEmp.Columns6"), ((bool)(resources.GetObject("rep_salesEmp.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_salesEmp.Columns8"))))});
            this.rep_salesEmp.DisplayMember = "EmpName";
            this.rep_salesEmp.Name = "rep_salesEmp";
            this.rep_salesEmp.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_salesEmp.ValueMember = "EmpId";
            // 
            // rep_InvoiceBook
            // 
            resources.ApplyResources(this.rep_InvoiceBook, "rep_InvoiceBook");
            this.rep_InvoiceBook.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_InvoiceBook.Buttons"))))});
            this.rep_InvoiceBook.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_InvoiceBook.Columns"), resources.GetString("rep_InvoiceBook.Columns1"), ((int)(resources.GetObject("rep_InvoiceBook.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_InvoiceBook.Columns3"))), resources.GetString("rep_InvoiceBook.Columns4"), ((bool)(resources.GetObject("rep_InvoiceBook.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_InvoiceBook.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_InvoiceBook.Columns7"), resources.GetString("rep_InvoiceBook.Columns8"))});
            this.rep_InvoiceBook.Name = "rep_InvoiceBook";
            // 
            // rep_User
            // 
            resources.ApplyResources(this.rep_User, "rep_User");
            this.rep_User.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_User.Buttons"))))});
            this.rep_User.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_User.Columns"), resources.GetString("rep_User.Columns1"), ((int)(resources.GetObject("rep_User.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_User.Columns3"))), resources.GetString("rep_User.Columns4"), ((bool)(resources.GetObject("rep_User.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_User.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_User.Columns7"), resources.GetString("rep_User.Columns8"))});
            this.rep_User.DisplayMember = "UserName";
            this.rep_User.Name = "rep_User";
            this.rep_User.ValueMember = "UserId";
            // 
            // rep_Accounts
            // 
            resources.ApplyResources(this.rep_Accounts, "rep_Accounts");
            this.rep_Accounts.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Accounts.Buttons"))))});
            this.rep_Accounts.Name = "rep_Accounts";
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "City";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.FieldNameSortGroup = "Area";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // frm_SL_CustomerTotal_Invoices
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grdCategory);
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_CustomerTotal_Invoices";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_Rep_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_InvoiceList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CategoryId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCrncy)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_paymethod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_salesEmp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_InvoiceBook)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_User)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Accounts)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraBars.PopupMenu popupMenu1;
        private DevExpress.XtraBars.BarButtonItem btn_Landscape;
        private DevExpress.XtraBars.BarButtonItem btn_Portrait;
        private DevExpress.XtraGrid.GridControl grdCategory;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_InvoiceBook;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustomerId;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox rep_paymethod;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_Expenses;
        private DevExpress.XtraGrid.Columns.GridColumn colPaid;
        private DevExpress.XtraGrid.Columns.GridColumn colRemains;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_User;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_salesEmp;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustId;
        private DevExpress.XtraGrid.Columns.GridColumn col_Net;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalCostPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_Profit;
        private DevExpress.XtraGrid.Columns.GridColumn col_ProfitRatio;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_CategoryId;
        private DevExpress.XtraGrid.Columns.GridColumn col_CrncId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repCrncy;
        private DevExpress.XtraGrid.Columns.GridColumn col_CrncRate;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_Accounts;
        private DevExpress.XtraGrid.Columns.GridColumn col_Total;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
    }
}