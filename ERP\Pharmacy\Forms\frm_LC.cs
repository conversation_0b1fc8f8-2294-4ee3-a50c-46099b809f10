﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using Pharmacy.HR;

namespace Pharmacy.Forms
{
    public partial class frm_LC : DevExpress.XtraEditors.XtraForm
    {
        int lcId;

        UserPriv prvlg;
        List<PR_LCPhoto> lstNewFaPhotos = new List<PR_LCPhoto>();
        List<VendorInfo> lst_Vendors = new List<VendorInfo>();

        bool DataModified;

        public frm_LC(int lcId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.lcId = lcId;
        }        

        private void frm_FA_FixedAsset_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);            

            //check customers Account
            if (!Shared.st_Store.LetterOfCreditAcc.HasValue)
            {
                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.LCSettingAcc : ResAr.LCSettingAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                this.BeginInvoke(new MethodInvoker(this.Close));
                return;
            }

            #region Get Vendors
            int lastVenId = 0;
            int? goldenVendor = MyHelper.GetVendors(out lst_Vendors, out lastVenId);
            lkpVendor.Properties.DisplayMember = "VenNameAr";
            lkpVendor.Properties.ValueMember = "VendorId";
            lkpVendor.Properties.DataSource = lst_Vendors;
            lkpVendor.EditValue = goldenVendor;
            #endregion

            #region Get Currencies
            repCrnc.DisplayMember = lkp_Crnc.Properties.DisplayMember = "crncName";
            repCrnc.ValueMember = lkp_Crnc.Properties.ValueMember = "CrncId";
            repCrnc.DataSource = lkp_Crnc.Properties.DataSource = Shared.lstCurrency;
            lkp_Crnc.EditValue = 0;
            #endregion

            LoadPrivilege();
            GetLC();
            ErpUtils.ColumnChooser(grdDepr);
        }

        private void frm_FA_FixedAsset_Shown(object sender, EventArgs e)
        {
            xtraTabControl1.SelectedTabPage = tab_main;
            txtLcCode.Focus();
        }

        private void frm_FA_FixedAsset_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrev.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
        }

        private void frm_FA_FixedAsset_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }


        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();

            int lastFA = (from x in DB.PR_LCs
                             where x.PR_LcId < lcId
                            orderby x.PR_LcId descending
                            select x.PR_LcId).FirstOrDefault();

            if (lastFA != 0)
            {
                lcId = lastFA;
                GetLC();
            }
            else
            {
                lastFA = (from x in DB.PR_LCs
                            where x.PR_LcId == lcId
                            orderby x.PR_LcId descending
                            select x.PR_LcId).FirstOrDefault();

                if (lastFA != 0)
                {
                    lcId = lastFA;
                    GetLC();
                }
            }    
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();

            int lastFA = (from x in DB.PR_LCs
                            where x.PR_LcId > lcId
                            orderby x.PR_LcId ascending
                            select x.PR_LcId).FirstOrDefault();

            if (lastFA != 0)
            {
                lcId = lastFA;
                GetLC();
            }
            else
            {
                lastFA = (from x in DB.PR_LCs
                            orderby x.PR_LcId ascending
                            select x.PR_LcId).FirstOrDefault();

                if (lastFA != 0)
                {
                    lcId = lastFA;
                    GetLC();
                }
            }
        }


        private void barBtn_List_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_LcList)))
            {
                frm_LcList frm = new frm_LcList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_LcList"].BringToFront();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ValidData())
                return;

            SaveData();
        }

        private void barBtn_Delete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (lcId == 0)
                return;

            DialogResult DR = XtraMessageBox.Show(Shared.IsEnglish ? ResEn.DelLC : ResAr.DelLC,
                Shared.IsEnglish ? ResEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning, 
                MessageBoxDefaultButton.Button2);
            if (DR == DialogResult.Yes)
            {
                DAL.ERPDataContext DB = new DAL.ERPDataContext();

                PR_LC pr_lc = DB.PR_LCs.Where(z => z.PR_LcId == lcId).First();
                
                PR_Invoice prInv = DB.PR_Invoices.Where(x => x.LcAccId == pr_lc.LcAccountId).FirstOrDefault();
                if (prInv != null)
                {
                    XtraMessageBox.Show((Shared.IsEnglish ? ResEn.DelLCDenied : ResAr.DelLCDenied)+prInv.InvoiceCode,
                       "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var journalDetails = (from a in DB.ACC_JournalDetails
                                      where a.AccountId == pr_lc.LcAccountId
                                      select a.AccountId).Count();
                if (journalDetails > 0)
                {
                    XtraMessageBox.Show((Shared.IsEnglish ? ResEn.DelLCDenied2 : ResAr.DelLCDenied2),
                       "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var lcPhotos = from c in DB.PR_LCPhotos
                                 where c.PR_LcId == lcId
                                 select c;

                var Account = (from a in DB.ACC_Accounts
                               where a.AccountId == pr_lc.LcAccountId
                               select a).Single();

                DB.ACC_Accounts.DeleteOnSubmit(Account);
                DB.PR_LCPhotos.DeleteAllOnSubmit(lcPhotos);                
                DB.PR_LCs.DeleteOnSubmit(pr_lc);

                DB.SubmitChanges();
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDel : ResSLAr.MsgDel,
                    "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                NewLC();
            }
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {            
            NewLC();
        }        
        
        private void txtFaCode_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }   


        void DoValidate()
        {
            txtLcCode.DoValidate();
            cmbStatus.DoValidate();
            txtName.DoValidate();
            lkpVendor.DoValidate();
            txtAmount.DoValidate();
            lkp_Crnc.DoValidate();
            dtOpenDate.DoValidate();
            dtCloseDate.DoValidate();
            dtShipDate.DoValidate();
            dtDeliverDate.DoValidate();
            txtPort.DoValidate();
            txtShipMethod.DoValidate();
            txtPayMethod.DoValidate();
            txtBillOfLading.DoValidate();
            txtNotes.DoValidate();

        }

        void Reset()
        {
            txtLcCode.Focus();

            txtLcCode.Text =
            txtName.Text =
            txtPort.Text =
            txtShipMethod.Text =
            txtPayMethod.Text =
            txtBillOfLading.Text =
            txtNotes.Text =
             string.Empty;
            txtAmount.EditValue = 0;
            dtOpenDate.EditValue =
            dtCloseDate.EditValue =
            dtShipDate.EditValue =
            dtDeliverDate.EditValue = null;

            txtImageDesc.Text = txtImagePath.Text = string.Empty;
            lstPhotos.Items.Clear();
            lstNewFaPhotos.Clear();

            cmbStatus.EditValue = true;

            DataModified = false;
        }

        private void NewLC()
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            Reset();
            lcId = 0;

            xtraTabControl1.SelectedTabPage = tab_main;
            txtLcCode.Focus();

            ERPDataContext DB = new ERPDataContext();

            string Code = DB.PR_LCs.OrderByDescending(x=> x.PR_LcId).Select(x => x.LcCode).FirstOrDefault();
            txtLcCode.Text = MyHelper.GetNextNumberInString(Code);
            cmbStatus.EditValue = true;
            cmbStatus.Enabled = false;

            DoValidate();
            DataModified = false;
        }

        private bool Validate_FA_ArName()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtName.Text))
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgNameRequired : ResAr.MsgNameRequired,
                        Shared.IsEnglish ? ResEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }
                if (lcId == 0)
                {
                    var name = (from n in pharm.PR_LCs
                                where n.LcName == txtName.Text
                                select n.LcName).Count();
                    if (name > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNameExist : ResSLAr.MsgNameExist,
                            Shared.IsEnglish ? ResEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtName.Focus();
                        return false;
                    }
                }
                else if (lcId > 0)
                {
                    var name = (from n in pharm.PR_LCs
                                where n.LcName == txtName.Text
                                where n.PR_LcId != lcId
                                select n.LcName).Count();
                    if (name > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNameExist : ResSLAr.MsgNameExist,
                            Shared.IsEnglish ? ResEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtName.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgIncorrectData : ResAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResEn.MsgTError : ResAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }
            return true;
        }

        private bool Validate_FA_Code()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtLcCode.Text))
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgEnterCode : ResAr.MsgEnterCode,
                        Shared.IsEnglish ? ResEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtLcCode.Focus();
                    return false;
                }

                if (lcId == 0)
                {
                    var code_exist = pharm.PR_LCs.Where(c => c.LcCode == txtLcCode.Text.Trim()).Select(c => c.LcCode).Count();
                    if (code_exist > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNumExist : ResSLAr.MsgNumExist,
                            Shared.IsEnglish ? ResEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtLcCode.Focus();
                        return false;
                    }
                }
                else if (lcId > 0)
                {
                    var code_exist = pharm.PR_LCs.Where(c => c.LcCode == txtLcCode.Text.Trim() && c.PR_LcId != lcId).Select(c => c.LcCode).Count();
                    if (code_exist > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNumExist : ResSLAr.MsgNumExist,
                            Shared.IsEnglish ? ResEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtLcCode.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgIncorrectData : ResAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResEn.MsgTError : ResAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtLcCode.Focus();
                return false;
            }
            return true;
        }


        private void GetLC()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var lc = (from i in DB.PR_LCs
                         where i.PR_LcId == lcId
                         select i).SingleOrDefault();
            if (lc == null)
            {
                NewLC();
            }
            else
            {
                lcId = lc.PR_LcId;

                txtLcCode.Text = lc.LcCode;
                cmbStatus.EditValue = lc.LcIsOpen;
                txtName.Text = lc.LcName;
                lkpVendor.EditValue = lc.VendorId;
                lkp_Crnc.EditValue = lc.CrncId;
                txtAmount.EditValue = lc.LcValue;
                dtOpenDate.EditValue = lc.OpenDate;
                dtCloseDate.EditValue = lc.CloseDate;
                dtShipDate.EditValue = lc.ShipDate;
                dtDeliverDate.EditValue = lc.DeliverDate;

                txtPort.Text = lc.ShipPort;
                txtShipMethod.Text = lc.ShipMethod;
                txtPayMethod.Text = lc.PayMethod;
                txtBillOfLading.Text = lc.BillOfLading;
                txtNotes.Text = lc.Notes;
                            
                #region Load Photos
                lstPhotos.Items.Clear();
                lstNewFaPhotos.Clear();
                var codes = (from c in DB.PR_LCPhotos
                             where c.PR_LcId == lcId
                             select c).ToList();
                foreach (var c in codes)
                {
                    lstPhotos.Items.Add(c.Desc);                    
                }
                #endregion

                txtImageDesc.Text = txtImagePath.Text = string.Empty;

                GetLCJournals(lc.LcAccountId);
                lcId = lc.PR_LcId;

                DoValidate();                
                DataModified = false;                
            }
        }

        private void GetLCJournals(int AccId)
        {
            ERPDataContext DB = new ERPDataContext();
            
            var dprcs = (from j in DB.ACC_Journals//.Where(j => j.IsPosted)
                         join jd in DB.ACC_JournalDetails
                         on j.JournalId equals jd.JournalId
                         where jd.AccountId == AccId
                         //where jd.Debit > 0
                         select new {
                             j.JCode,
                             j.InsertDate,
                             Notes = jd.Notes,
                             jd.CrncId,
                             jd.CrncRate,
                             jd.Debit,
                             jd.Credit
                         }).ToList();

            DataTable dt = new DataTable();
            dt.Columns.Add("JCode", typeof(int));
            dt.Columns.Add("InsertDate", typeof(DateTime));
            dt.Columns.Add("Notes");
            dt.Columns.Add("CrncId", typeof(int));

            dt.Columns.Add("CrncRate", typeof(decimal));
            dt.Columns.Add("Fdebit", typeof(decimal));
            dt.Columns.Add("Fcredit", typeof(decimal));

            dt.Columns.Add("Debit", typeof(decimal));
            dt.Columns.Add("Credit", typeof(decimal));

            foreach (var d in dprcs)
            {
                dt.Rows.Add(d.JCode, d.InsertDate, d.Notes, d.CrncId, d.CrncRate, 
                    d.CrncId == 0 ? 0 : d.Debit, 
                    d.CrncId == 0 ? 0 : d.Credit, 
                    d.Debit * d.CrncRate, d.Credit * d.CrncRate);                
            }
            grdDepr.DataSource = dt;
        }

                       
        private void SaveData()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            PR_LC lc = null;
            ACC_Account ac = null;
            bool isNew = false;
            if (lcId == 0)
            {
                isNew = true;
                lc = new PR_LC();

                //create an account                
                var parentAcc = DB.ACC_Accounts.Where(x => x.AccountId == Shared.st_Store.LetterOfCreditAcc.Value).FirstOrDefault();

                ac = new ACC_Account();                
                ac.AcNameAr = txtName.Text.Trim();
                ac.AcType = parentAcc.AcType;
                ac.AllowChild = false;
                ac.AllowEdit = false;
                ac.ParentActId = parentAcc.AccountId;
                ac.Level = parentAcc.Level + 1;
                ac.AcNumber = HelperAcc.AccNumGenerated(parentAcc);

                ac.AccSecurityLevel = 1;   //default

                DB.ACC_Accounts.InsertOnSubmit(ac);
                DB.SubmitChanges();
            }
            else
            {
                lc = (from i in DB.PR_LCs
                              where i.PR_LcId == lcId
                              select i).SingleOrDefault();

                //update account
                ac = (from i in DB.ACC_Accounts
                      where i.AccountId == lc.LcAccountId
                      select i).SingleOrDefault();
                ac.AcNameAr = txtName.Text.Trim();
            }

            lc.LcCode = txtLcCode.Text;
            lc.LcIsOpen = Convert.ToBoolean(cmbStatus.EditValue);
            lc.LcName = Utilities.CleanSingle(txtName.Text);
            lc.VendorId = Convert.ToInt32(lkpVendor.EditValue);
            lc.CrncId = Convert.ToInt32(lkp_Crnc.EditValue);
            lc.CrncRate = 0;
            lc.LcValue = Convert.ToDecimal(txtAmount.EditValue);
            if(dtOpenDate.EditValue != null)
                lc.OpenDate = dtOpenDate.DateTime;
            else
                lc.OpenDate = null;
            if (dtCloseDate.EditValue != null)
                lc.CloseDate = dtCloseDate.DateTime;
            else
                lc.CloseDate = null;
            if (dtShipDate.EditValue != null)
                lc.ShipDate = dtShipDate.DateTime;
            else
                lc.ShipDate = null;
            if (dtDeliverDate.EditValue != null)
                lc.DeliverDate = dtDeliverDate.DateTime;
            else
                lc.DeliverDate = null;

            lc.ShipPort = txtPort.Text;
            lc.ShipMethod = txtShipMethod.Text;
            lc.PayMethod = txtPayMethod.Text;
            lc.BillOfLading = txtBillOfLading.Text;
            lc.Notes = txtNotes.Text;
            lc.LcAccountId = ac.AccountId;
            lc.UserId = Shared.UserId;
            lc.StoreId = 1;

            if(isNew)
                DB.PR_LCs.InsertOnSubmit(lc);

            DB.SubmitChanges();

            //Save new photos            
            foreach (PR_LCPhoto ph in lstNewFaPhotos)
            {
                ph.PR_LcId = lc.PR_LcId;
                DB.PR_LCPhotos.InsertOnSubmit(ph);
            }

            DB.SubmitChanges();
            XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgSave: ResAr.MsgSave , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            lstNewFaPhotos.Clear();
            GetLCJournals(lc.LcAccountId);
            lcId = lc.PR_LcId;
            DoValidate();            
            DataModified = false;            
        }

        private bool ValidData()
        {
            if (lcId == 0)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    // "عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvNew : ResSLAr.MsgPrvNew, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (lcId > 0)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    //"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvEdit : ResSLAr.MsgPrvEdit, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            if (Validate_FA_Code() == false)
                return false;
            if (Validate_FA_ArName() == false)
                return false;

            if (lkpVendor.EditValue == null)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgSelectVendor : ResAr.MsgSelectVendor, "",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                lkpVendor.Focus();
                return false;
            }

            ERPDataContext DB = new ERPDataContext();
            //can't add child to a used account
            if (DB.ACC_JournalDetails.Where(j => j.AccountId ==
                Convert.ToInt32(Shared.st_Store.LetterOfCreditAcc)).Count() > 0)
            {
                XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResAccEn.ValParentAcc : ResAccAr.ValParentAcc,
                "", MessageBoxButtons.OK, MessageBoxIcon.Information);                
                return false;
            }

            return true;
        }

        DialogResult ChangesMade()
        {

            if (lcId > 0 && DataModified)
            {
                DialogResult r = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDataModified : ResSLAr.MsgDataModified, "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);                                
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        #region Photos

        private void btnBrowse_Click(object sender, EventArgs e)
        {
            OpenFileDialog OFD = new OpenFileDialog();
            OFD.Filter = "Images|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
            if (OFD.ShowDialog() == DialogResult.OK)
            {
                txtImagePath.Text = OFD.FileName;
                txtImageDesc.Focus();
            }
        }

        private void btnShowPhotoes_Click(object sender, EventArgs e)
        {
            if (lcId < 1)
                return;

            if (!ErpUtils.IsFormOpen(typeof(frm_HR_EmpPhotos)))
            {
                frm_HR_EmpPhotos f = new frm_HR_EmpPhotos(lcId, 4);
                f.BringToFront();
                f.Show();
            }
            else
                Application.OpenForms["frm_HR_EmpPhotos"].BringToFront();
        }

        private void btnAddEmpPhoho_Click(object sender, EventArgs e)
        {
            if (txtImageDesc.Text.Trim() != string.Empty &&
                txtImagePath.Text != string.Empty)
            {
                System.IO.FileInfo f = new System.IO.FileInfo(txtImagePath.Text.Trim());
                if (f.Length > (10240 * 1024))
                {
                    MessageBox.Show(
                        Shared.IsEnglish == true ? ResHREn.PicSize : ResHRAr.PicSize//"لا يمكن تحميل صوره اكبر من 10 ميجابايت"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (lstPhotos.Items.Count > 0)
                {
                    if (lstPhotos.Items.Contains(txtImageDesc.Text.Trim()))
                    {
                        MessageBox.Show(
                            Shared.IsEnglish == true ? ResHREn.PicExist : ResHRAr.PicExist//"يوجد صوره بهذا الاسم, يجب تغيير اسم الصوره"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                }
                Image srcImage = Image.FromFile(txtImagePath.Text.Trim());
                PR_LCPhoto ai = new PR_LCPhoto();
                ai.Photo = ErpUtils.imageToByteArray(srcImage);
                ai.PR_LcId = lcId;
                ai.PhotoDate = MyHelper.Get_Server_DateTime();
                ai.Desc = txtImageDesc.Text;
                lstNewFaPhotos.Add(ai);

                lstPhotos.Items.Add(txtImageDesc.Text);
                txtImagePath.Text = txtImageDesc.Text = string.Empty;
            }
            else
                MessageBox.Show(
                    Shared.IsEnglish == true ? ResHREn.PicSelect : ResHRAr.PicSelect//"يرجى اختيار صورة وإدخال وصف مناسب لها"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);

        }

        private void btnEditPhoto_Click(object sender, EventArgs e)
        {
            if (lstPhotos.SelectedIndex < 0)
                return;
            if (lstPhotos.Items.Count > 0)
            {
                DialogResult dr = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResHREn.PicEdit : ResHRAr.PicEdit
                    , "", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);

                if (dr == DialogResult.OK)
                {                                            
                    ERPDataContext DB = new ERPDataContext();
                    var empPhoto = (from ic in DB.PR_LCPhotos
                                    where ic.PR_LcId == lcId &&
                                    ic.Desc == lstPhotos.SelectedItem.ToString()
                                    select ic).FirstOrDefault();
                    if (empPhoto != null)
                    {
                        if (txtImagePath.Text != string.Empty)
                        {
                            Image srcImage = Image.FromFile(txtImagePath.Text.Trim());
                            empPhoto.Photo = ErpUtils.imageToByteArray(srcImage);
                        }

                        empPhoto.PR_LcId = lcId;
                        empPhoto.PhotoDate = MyHelper.Get_Server_DateTime();

                        if (txtImageDesc.Text != string.Empty)
                            empPhoto.Desc = txtImageDesc.Text;

                        DB.SubmitChanges();

                        #region Load Photos
                        lstPhotos.Items.Clear();
                        var codes = (from c in DB.PR_LCPhotos
                                     where c.PR_LcId == lcId
                                     select c.Desc).ToList();
                        foreach (var c in codes)
                            lstPhotos.Items.Add(c);
                        #endregion
                    }
                }
            }
        }

        private void btnDeleteEmpPhoto_Click(object sender, EventArgs e)
        {
            DeleteEmpPhoto();
        }

        void DeleteEmpPhoto()
        {
            if (lstPhotos.SelectedIndex < 0)
                return;
            if (lstPhotos.Items.Count > 0)
            {
                DialogResult dr = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResHREn.PicDel : ResHRAr.PicDel//"هل تريد بالغعل حذف هذه الصوره؟"
                    , "", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);

                if (dr == DialogResult.OK)
                {
                    ERPDataContext DB = new ERPDataContext();
                    var empPhoto = (from ic in DB.PR_LCPhotos
                                    where ic.PR_LcId == lcId &&
                                    ic.Desc == lstPhotos.SelectedItem.ToString()
                                    select ic).FirstOrDefault();
                    if (empPhoto != null)
                    {
                        DB.PR_LCPhotos.DeleteOnSubmit(empPhoto);
                        DB.SubmitChanges();
                    }
                    lstPhotos.Items.Remove(lstPhotos.SelectedItem);
                }
            }
        }
        #endregion

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_PR_LC).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
            }
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "تسجيل عميل جديد");
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex >= 0)
                e.Value = e.ListSourceRowIndex + 1;
        }
    }
}