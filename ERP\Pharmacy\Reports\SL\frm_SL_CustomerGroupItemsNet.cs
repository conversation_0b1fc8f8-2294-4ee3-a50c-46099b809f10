﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;
using DevExpress.XtraGrid.Views.BandedGrid;

namespace Reports
{
    public partial class frm_SL_CustomerGroupItemsNet : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, storeId1, storeId2;
        byte FltrTyp_item, fltrTyp_Date, FltrTyp_Customer, FltrTyp_Category, FltrTyp_InvBook, FltrTyp_Store;
        DateTime date1, date2;

        int customerId1, customerId2, custGroupId, salesEmpId, EmpGroupId;
        string categoryNum;
        string custGroupAccNumber;

        byte FltrTyp_Company;
        int companyId;

        bool UsingTaxRatio = false;

        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();

        DataTable dt = new DataTable();


        public frm_SL_CustomerGroupItemsNet(
            string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            int custGroupId, string custGroupAccNumber,
            byte FltrTyp_Category, string categoryNum, int salesEmpId,
            byte FltrTyp_InvBook, string InvBooks, int EmpGroupId, bool UsingTaxRatio,
            byte _fltrTyp_Store, int _storeId1, int _storeId2, byte FltrTyp_Company, int companyId)
        {
            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_Category = FltrTyp_Category;
            this.FltrTyp_Store = _fltrTyp_Store;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;
            this.date1 = date1;
            this.date2 = date2;
            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            this.categoryNum = categoryNum;
            this.salesEmpId = salesEmpId;
            this.EmpGroupId = EmpGroupId;

            this.UsingTaxRatio = UsingTaxRatio;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            this.storeId1 = _storeId1;
            this.storeId2 = _storeId2;

            #region Init_DataTable
            /*dt.Columns.Add("ItemCode1");
            dt.Columns.Add("ItemCode2");
            dt.Columns.Add("ItemNameAr");
            dt.Columns.Add("SalesTaxRatio", typeof(decimal));
            dt.Columns.Add("SoldQty", typeof(decimal));
            dt.Columns.Add("SoldBonus", typeof(decimal));
            dt.Columns.Add("SoldAmount", typeof(decimal));
            dt.Columns.Add("SoldTotalPrice", typeof(decimal));
            dt.Columns.Add("SoldTotalSalesTaxValue", typeof(decimal));
            dt.Columns.Add("ReturnQty", typeof(decimal));
            dt.Columns.Add("ReturnBonus", typeof(decimal));
            dt.Columns.Add("ReturnAmount", typeof(decimal));
            dt.Columns.Add("ReturnTotalPrice", typeof(decimal));
            dt.Columns.Add("ReturnTotalSalesTaxValue", typeof(decimal));
            dt.Columns.Add("NetQty", typeof(decimal));
            dt.Columns.Add("NetBonus", typeof(decimal));
            dt.Columns.Add("NetAmount", typeof(decimal));
            dt.Columns.Add("NetTotalPrice", typeof(decimal));
            dt.Columns.Add("NetTotalSalesTaxValue", typeof(decimal));
            dt.Columns.Add("NetAmountPlusNetTax", typeof(decimal));

            dt.Columns.Add("ExtraBonus", typeof(decimal));
            dt.Columns.Add("TotalBonus", typeof(decimal));
            dt.Columns.Add("BonusRatio", typeof(decimal));
            dt.Columns.Add("TotalBonusRatio", typeof(decimal));
            dt.Columns.Add("NetTotalPrice_Local", typeof(decimal));
            dt.Columns.Add("SellCrncRate", typeof(decimal));
            dt.Columns.Add("ReturnCrncRate", typeof(decimal));
            dt.Columns.Add("SellCrncId", typeof(decimal));
            dt.Columns.Add("ReturnCrncId", typeof(decimal));
            dt.Columns.Add("SLR_EmpName");
            dt.Columns.Add("SL_EmpName");

            dt.Columns.Add("SLR_CustCatName");
            dt.Columns.Add("SL_CustCatName");
            dt.Columns.Add("Category");

            dt.Columns.Add("SoldLibraQty");
            dt.Columns.Add("SoldkgWeight");
            dt.Columns.Add("ReturnLibraQty");
            dt.Columns.Add("ReturnkgWeight");

            dt.Columns.Add("NetPiecesCount");
            dt.Columns.Add("ReturnPiecesCount");
            dt.Columns.Add("SoldPiecesCount");*/


            #endregion

            getReportHeader();
            LoadData();
            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {

        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //grdCategory.MinimumSize = grdCategory.Size;

            //new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
            //        lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();

            //grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, false, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            #region Query
            var stores = DB.IC_Stores.ToList();

            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= storeId2 && store.StoreId >= storeId1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (storeId1 > 0 && (store.StoreId == storeId1 || store.ParentId == storeId1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }
            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var sales_data = (
                from c in DB.SL_Customers
                join a in DB.ACC_Accounts
                        on c.AccountId equals a.AccountId
                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                c.CustomerId >= customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                c.CustomerId <= customerId2 : true

                join gc in DB.SL_Group_Customers
                on c.GroupId equals gc.GroupId

                join i in DB.SL_Invoices on c.CustomerId equals i.CustomerId
                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true
                where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate >= date1 && i.InvoiceDate <= date2 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                i.InvoiceDate >= date1 : true
                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate <= date2 : true

                join hh in DB.HR_Employees on i.SalesEmpId equals hh.EmpId into hq
                from xx in hq.DefaultIfEmpty().Where(x => EmpGroupId == 0 ? true : x.GroupId == EmpGroupId)


                join s in DB.SL_InvoiceDetails on i.SL_InvoiceId equals s.SL_InvoiceId

                where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                s.ItemId >= itemId1 : true
                where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                s.ItemId <= itemId2 : true
                let TotalInvoice = s.SL_Invoice.Net - s.SL_Invoice.TaxValue + s.SL_Invoice.DiscountValue - s.SL_Invoice.Expenses
                                 + s.SL_Invoice.DeductTaxValue - s.SL_Invoice.AddTaxValue

                join t in DB.IC_Items on s.ItemId equals t.ItemId
                where FltrTyp_Company == 1 ? t.Company == companyId : true

                join g in DB.IC_Categories on t.Category equals g.CategoryId
                where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true
                where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                select new
                {
                    t.ItemCode1,
                    t.ItemCode2,
                    t.ItemNameAr,
                    t.IC_Category.CategoryNameAr,
                    s.UOMIndex,
                    t.MediumUOMFactor,
                    t.LargeUOMFactor,
                    s.ItemId,
                    t.is_libra,
                    SoldPiecesCount = s.PiecesCount,
                    Qty = s.SellPrice > 0 ? s.Qty : 0,
                    LibraQty = t.is_libra == true ? s.LibraQty : null,
                    kg_Weight_libra = s.kg_Weight_libra,
                    s.TotalSellPrice,
                    TotalPrice = s.SL_Invoice.Net == 0 ? 0 : (s.TotalSellPrice / TotalInvoice) * s.SL_Invoice.Net,
                    Bonus = s.SellPrice > 0 ? 0 : s.Qty,
                    SalesTaxRatio = UsingTaxRatio ? s.SalesTaxRatio : (t.SalesTaxRatio / 100),
                    s.SalesTax,

                    //mohammad 13-09-2018
                    CrncId = s.SL_Invoice.CrncId,
                    CrncRate = s.SL_Invoice.CrncRate,
                    TotalSellPrice_Local = s.TotalSellPrice * s.SL_Invoice.CrncRate,
                    Category = Shared.IsEnglish ? t.IC_Category.CategoryNameEn : t.IC_Category.CategoryNameAr,
                    t.Height,
                    t.Length,
                    t.Width,
                    CustomerGroupId = gc.GroupId,
                    CGNameAr = Shared.IsEnglish ? gc.NameEn : gc.NameAr
                }).ToList();

            var slReturn_data = (
                                from c in DB.SL_Customers
                                join a in DB.ACC_Accounts
                        on c.AccountId equals a.AccountId
                                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                                c.CustomerId >= customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                                c.CustomerId <= customerId2 : true


                                join gc in DB.SL_Group_Customers
                                on c.GroupId equals gc.GroupId

                                join i in DB.SL_Returns on c.CustomerId equals i.CustomerId
                                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true
                                where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                                where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                                where fltrTyp_Date == 1 ? i.ReturnDate.Date == date1 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                i.ReturnDate >= date1 && i.ReturnDate <= date2 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                i.ReturnDate >= date1 : true
                                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                i.ReturnDate <= date2 : true

                                join hh in DB.HR_Employees on i.SalesEmpId equals hh.EmpId into hq
                                from xx in hq.DefaultIfEmpty().Where(x => EmpGroupId == 0 ? true : x.GroupId == EmpGroupId)


                                join s in DB.SL_ReturnDetails on i.SL_ReturnId equals s.SL_ReturnId

                                where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                                s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                                s.ItemId >= itemId1 : true
                                where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                                s.ItemId <= itemId2 : true

                                let TotalInvoice = s.SL_Return.Net - s.SL_Return.TaxValue + s.SL_Return.DiscountValue + s.SL_Return.Expenses
                                           + s.SL_Return.DeductTaxValue - s.SL_Return.AddTaxValue

                                join t in DB.IC_Items on s.ItemId equals t.ItemId
                                where FltrTyp_Company == 1 ? t.Company == companyId : true

                                join g in DB.IC_Categories on t.Category equals g.CategoryId
                                where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true
                                where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                                select new
                                {
                                    t.ItemCode1,
                                    t.ItemCode2,
                                    t.ItemNameAr,
                                    t.IC_Category.CategoryNameAr,
                                    s.UOMIndex,
                                    t.MediumUOMFactor,
                                    t.LargeUOMFactor,
                                    s.ItemId,
                                    t.is_libra,
                                    ReturnPiecesCount = s.PiecesCount,
                                    Qty = s.SellPrice > 0 ? s.Qty : 0,
                                    LibraQty = t.is_libra == true ? s.LibraQty : null,
                                    kg_Weight_libra = s.kg_Weight_libra,
                                    s.TotalSellPrice,
                                    TotalPrice = s.SL_Return.Net == 0 ? 0 : (s.TotalSellPrice / TotalInvoice) * s.SL_Return.Net,
                                    Bonus = s.SellPrice > 0 ? 0 : s.Qty,
                                    SalesTaxRatio = UsingTaxRatio ? s.SalesTaxRatio : (t.SalesTaxRatio / 100),
                                    s.SalesTax,

                                    //mohammad 13-09-2018
                                    CrncId = s.SL_Return.CrncId,
                                    CrncRate = s.SL_Return.CrncRate,
                                    TotalSellPrice_Local = s.TotalSellPrice * s.SL_Return.CrncRate,
                                    Category = Shared.IsEnglish ? t.IC_Category.CategoryNameEn : t.IC_Category.CategoryNameAr,
                                    t.Height,
                                    t.Length,
                                    t.Width,
                                    CustomerGroupId = gc.GroupId,
                                    CGNameAr = Shared.IsEnglish?gc.NameEn: gc.NameAr
                                }).ToList();

            var ic_Outtrns_data = (                         //EXTRA BONUS
                                from c in DB.SL_Customers
                                join a in DB.ACC_Accounts
                on c.AccountId equals a.AccountId
                                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                                c.CustomerId >= customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                                c.CustomerId <= customerId2 : true


                                join gc in DB.SL_Group_Customers
                                on c.GroupId equals gc.GroupId

                                join i in DB.IC_OutTrns on c.CustomerId equals i.CustomerId
                                where i.IsVendor == (byte)IsVendor.Customer
                                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true
                                where fltrTyp_Date == 1 ? i.OutTrnsDate.Date == date1 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                i.OutTrnsDate >= date1 && i.OutTrnsDate <= date2 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                i.OutTrnsDate >= date1 : true
                                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                i.OutTrnsDate <= date2 : true

                                join s in DB.IC_OutTrnsDetails on i.OutTrnsId equals s.OutTrnsId

                                where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                                s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                                s.ItemId >= itemId1 : true
                                where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                                s.ItemId <= itemId2 : true

                                join t in DB.IC_Items on s.ItemId equals t.ItemId
                                where FltrTyp_Company == 1 ? t.Company == companyId : true

                                join g in DB.IC_Categories on t.Category equals g.CategoryId
                                where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true
                                where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                                select new
                                {
                                    t.ItemCode1,
                                    t.ItemCode2,
                                    t.ItemNameAr,
                                    t.IC_Category.CategoryNameAr,
                                    s.UOMIndex,
                                    t.MediumUOMFactor,
                                    t.LargeUOMFactor,
                                    s.ItemId,
                                    t.is_libra,
                                    ExtraBonus = s.Qty,
                                    SalesTaxRatio = (t.SalesTaxRatio / 100),
                                    Category = Shared.IsEnglish ? t.IC_Category.CategoryNameEn : t.IC_Category.CategoryNameAr,
                                    t.Height,
                                    t.Length,
                                    t.Width,
                                    CustomerGroupId = gc.GroupId,
                                    CGNameAr = Shared.IsEnglish ? gc.NameEn : gc.NameAr
                                }).ToList();

            var lst_Items = sales_data.Select(x => new
            {
                x.ItemId,
                x.ItemCode1,
                x.ItemCode2,
                x.ItemNameAr,
                x.MediumUOMFactor,
                x.LargeUOMFactor,
                x.SalesTaxRatio,
                x.Category,
                x.Height,
                x.Length,
                x.Width,
                x.is_libra,
                x.CustomerGroupId,
                x.CGNameAr,
                x.CategoryNameAr
            })
                .Union(slReturn_data.Select(x => new
                {
                    x.ItemId,
                    x.ItemCode1,
                    x.ItemCode2,
                    x.ItemNameAr,
                    x.MediumUOMFactor,
                    x.LargeUOMFactor,
                    x.SalesTaxRatio,
                    x.Category,
                    x.Height,
                    x.Length,
                    x.Width,
                    x.is_libra,
                    x.CustomerGroupId,
                    x.CGNameAr,
                    x.CategoryNameAr
                }))
                .Union(ic_Outtrns_data.Select(x => new
                {
                    x.ItemId,
                    x.ItemCode1,
                    x.ItemCode2,
                    x.ItemNameAr,
                    x.MediumUOMFactor,
                    x.LargeUOMFactor,
                    x.SalesTaxRatio,
                    x.Category,
                    x.Height,
                    x.Length,
                    x.Width,
                    x.is_libra,
                    x.CustomerGroupId,
                    x.CGNameAr,
                    x.CategoryNameAr
                }))
                .Distinct().OrderBy(x => x.ItemCode1).ToList();
            #endregion

            #region Columns and DataTable
            DataTable dt = new DataTable();

            dt.Columns.Add("ItemCode1");
            dt.Columns.Add("ItemCode2");
            dt.Columns.Add("ItemNameAr");
            dt.Columns.Add("CategoryNameAr");
            dt.Columns.Add("SalesTaxRatio", typeof(decimal));

            //dt.Columns.Add("Total").DataType = typeof(decimal);

            var storesInQry = lst_Items.Select(x => new { x.CustomerGroupId, x.CGNameAr }).Distinct().ToList();

            MainBand.VisibleIndex = storesInQry.Count;

            #region clear columns and bands
            foreach (GridBand c in bandedGridView1.Bands.Where(x => x.Name.Contains("_")))
            {
                c.Columns.Clear();
            }

            List<string> lstbandsNames = bandedGridView1.Bands.Where(x => x.Name.Contains("_")).Select(x => x.Name).ToList();
            for (int z = 0; z < lstbandsNames.Count; z++)
            {
                bandedGridView1.Bands.Remove(bandedGridView1.Bands.Where(x => x.Name == lstbandsNames[z]).Select(x => x).First());
            }
            #endregion

            #region add store columns and bands
            int count = 1;
            foreach (var b in storesInQry)
            {
                //add band
                var band = new GridBand();
                bandedGridView1.Bands.Add(band);

                band.Name = "Band_" + b.CustomerGroupId;
                band.Caption = b.CGNameAr;
                band.Visible = true;
                band.VisibleIndex = storesInQry.Count - count;

                count++;

                #region add column qty
                var col_ = new BandedGridColumn();
                col_.Name = "col_Qty_" + b.CustomerGroupId.ToString();
                col_.FieldName = "col_Qty_" + b.CustomerGroupId.ToString();

                col_.Caption = Shared.IsEnglish ? ResICEn.txt_Qty : ResICAr.txt_Qty;
                col_.Visible = true;
                col_.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                col_.DisplayFormat.FormatString = "n3";
                col_.SummaryItem.DisplayFormat = "{0:n3}";
                col_.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;

                bandedGridView1.GroupSummary.Add(DevExpress.Data.SummaryItemType.Sum, col_.FieldName, col_, "{0:n3}");

                band.Columns.Add(col_);
                dt.Columns.Add(col_.FieldName, typeof(decimal));
                #endregion

                #region add column piecesCount
                if (Shared.st_Store.PiecesCount)
                {
                    var colP_ = new BandedGridColumn();
                    colP_.Name = "col_Pieces_" + b.CustomerGroupId.ToString();
                    colP_.FieldName = "col_Pieces_" + b.CustomerGroupId.ToString();
                    colP_.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;

                    colP_.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
                    colP_.Visible = true;
                    colP_.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                    colP_.DisplayFormat.FormatString = "n2";
                    colP_.SummaryItem.DisplayFormat = "{0:n2}";
                    colP_.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;

                    bandedGridView1.GroupSummary.Add(DevExpress.Data.SummaryItemType.Sum, colP_.FieldName, colP_, "{0:n3}");

                    band.Columns.Add(colP_);
                    dt.Columns.Add(colP_.FieldName, typeof(decimal));
                }
                #endregion

                #region add column Cost
                var colC_ = new BandedGridColumn();
                colC_.Name = "col_Cost_" + b.CustomerGroupId.ToString();
                colC_.FieldName = "col_Cost_" + b.CustomerGroupId.ToString();

                colC_.Caption = "التكلفة";
                colC_.Visible = true;
                colC_.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                colC_.DisplayFormat.FormatString = "n2";
                colC_.SummaryItem.DisplayFormat = "{0:n2}";
                colC_.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;

                bandedGridView1.GroupSummary.Add(DevExpress.Data.SummaryItemType.Sum, colC_.FieldName, colC_, "{0:n3}");

                band.Columns.Add(colC_);
                dt.Columns.Add(colC_.FieldName, typeof(decimal));
                #endregion
            }
            #endregion

            #region add total columns and bands

            //add band
            var Tband = new GridBand();
            bandedGridView1.Bands.Add(Tband);

            Tband.Name = "TBand_";
            Tband.Caption = "الإجمالي";
            Tband.Visible = true;
            Tband.VisibleIndex = storesInQry.Count;

            #region add column qty
            var Tcol_ = new BandedGridColumn();
            Tcol_.Name = "Tcol_Qty_";
            Tcol_.FieldName = "Tcol_Qty_";

            Tcol_.Caption = Shared.IsEnglish ? ResICEn.txt_Qty : ResICAr.txt_Qty;
            Tcol_.Visible = true;
            Tcol_.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            Tcol_.DisplayFormat.FormatString = "n3";
            Tcol_.SummaryItem.DisplayFormat = "{0:n3}";
            Tcol_.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;

            bandedGridView1.GroupSummary.Add(DevExpress.Data.SummaryItemType.Sum, Tcol_.FieldName, Tcol_, "{0:n3}");

            Tband.Columns.Add(Tcol_);
            dt.Columns.Add(Tcol_.FieldName, typeof(decimal));
            #endregion

            #region add column piecesCount
            if (Shared.st_Store.PiecesCount)
            {
                var TcolP_ = new BandedGridColumn();
                TcolP_.Name = "Tcol_Pieces_";
                TcolP_.FieldName = "Tcol_Pieces_";

                TcolP_.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr; ;
                TcolP_.Visible = true;
                TcolP_.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                TcolP_.DisplayFormat.FormatString = "n2";
                TcolP_.SummaryItem.DisplayFormat = "{0:n2}";
                TcolP_.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;

                bandedGridView1.GroupSummary.Add(DevExpress.Data.SummaryItemType.Sum, TcolP_.FieldName, TcolP_, "{0:n3}");

                Tband.Columns.Add(TcolP_);
                dt.Columns.Add(TcolP_.FieldName, typeof(decimal));
            }
            #endregion

            #region add column Cost
            var TcolC_ = new BandedGridColumn();
            TcolC_.Name = "Tcol_Cost_";
            TcolC_.FieldName = "Tcol_Cost_";

            TcolC_.Caption = "القيمة";
            TcolC_.Visible = true;
            TcolC_.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            TcolC_.DisplayFormat.FormatString = "n2";
            TcolC_.SummaryItem.DisplayFormat = "{0:n2}";
            TcolC_.SummaryItem.SummaryType = DevExpress.Data.SummaryItemType.Sum;

            bandedGridView1.GroupSummary.Add(DevExpress.Data.SummaryItemType.Sum, TcolC_.FieldName, TcolC_, "{0:n3}");

            Tband.Columns.Add(TcolC_);
            dt.Columns.Add(TcolC_.FieldName, typeof(decimal));
            #endregion

            #endregion

            #endregion

            #region Add Data
            var data_grouped = (from d in lst_Items
                                group d by new
                                {
                                    d.Category,
                                    d.ItemCode1,
                                    d.ItemCode2,
                                    d.ItemId,
                                    d.ItemNameAr,
                                    d.CustomerGroupId,
                                    d.MediumUOMFactor,
                                    d.LargeUOMFactor,
                                    d.CategoryNameAr
                                } into grp
                                select grp).ToList();

            foreach (var d in data_grouped)
            {
                DataRow dr = dt.NewRow();
                dr["ItemNameAr"] = d.Key.ItemNameAr;
                dr["ItemCode1"] = d.Key.ItemCode1;
                dr["ItemCode2"] = d.Key.ItemCode2;
                dr["CategoryNameAr"] = d.Key.CategoryNameAr;

                decimal Tqty = 0, Tpiece = 0, Tcost = 0;
                foreach (var pd in d)
                {

                    decimal MUOM_Factor = MyHelper.FractionToDouble(pd.MediumUOMFactor);
                    decimal LUOM_Factor = MyHelper.FractionToDouble(pd.LargeUOMFactor);

                    decimal salQty = sales_data.Where(x => x.ItemId == pd.ItemId && x.SalesTaxRatio == pd.SalesTaxRatio && x.CustomerGroupId == pd.CustomerGroupId).Select(x => MyHelper.CalculateUomQty(x.Qty, x.UOMIndex, MUOM_Factor, LUOM_Factor)).DefaultIfEmpty(0).Sum();
                    decimal salCost = sales_data.Where(x => x.ItemId == pd.ItemId && x.SalesTaxRatio == pd.SalesTaxRatio && x.CustomerGroupId == pd.CustomerGroupId).Select(x => x.TotalPrice).DefaultIfEmpty(0).Sum();
                    decimal rQty = slReturn_data.Where(x => x.ItemId == pd.ItemId && x.SalesTaxRatio == pd.SalesTaxRatio && x.CustomerGroupId == pd.CustomerGroupId).Select(x => MyHelper.CalculateUomQty(x.Qty, x.UOMIndex, MUOM_Factor, LUOM_Factor)).DefaultIfEmpty(0).Sum();
                    decimal rCost = slReturn_data.Where(x => x.ItemId == pd.ItemId && x.SalesTaxRatio == pd.SalesTaxRatio && x.CustomerGroupId == pd.CustomerGroupId).Select(x => x.TotalPrice).DefaultIfEmpty(0).Sum();

                    dr["col_Qty_" + pd.CustomerGroupId.ToString()] = Math.Round(salQty - rQty, 3, MidpointRounding.AwayFromZero);
                    dr["col_Cost_" + pd.CustomerGroupId.ToString()] = Math.Round(salCost - rCost, 3, MidpointRounding.AwayFromZero);
                    //if (Shared.st_Store.PiecesCount)
                    //dr["col_Pieces_" + pd.CustomerGroupId.ToString()] = (double)pd.PiecesCount;

                    Tqty += salQty - rQty;
                    //Tpiece += pd.PiecesCount;
                    Tcost += salCost - rCost;
                }

                dr["Tcol_Qty_"] = Math.Round(Tqty, 3, MidpointRounding.AwayFromZero);
                dr["Tcol_Cost_"] = Math.Round(Tcost, 2, MidpointRounding.AwayFromZero);
                //if (Shared.st_Store.PiecesCount)
                //dr["Tcol_Pieces_"] = (double)Tpiece;

                dt.Rows.Add(dr);
            }
            #endregion

            grdCategory.DataSource = dt;

            bandedGridView1.Bands.MoveTo(bandedGridView1.Bands.Count, MainBand);
            BestFitColumns();
        }

        private void BestFitColumns()
        {
            grdCategory.BeginUpdate();
            foreach (BandedGridView band in grdCategory.Views)
            {
                foreach (GridBand gridBand in band.Bands)
                {
                    gridBand.Columns.View.BestFitColumns();
                }
            }
            grdCategory.EndUpdate();
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;


            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;

            //decimal qty = Convert.ToDecimal(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_Qty));
            //decimal Luom = MyHelper.FractionToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "LargeUOMFactor") + "");
            //decimal Muom = MyHelper.FractionToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "MediumUOMFactor") + "");            

            //if (e.Column == col_SmallQty)
            //    e.Value = qty;

            //if (e.Column == col_MediumQty && Muom != 1)
            //    e.Value = qty / Muom;

            //if (e.Column == col_LargeQty && Luom != 1)
            //    e.Value = qty / Luom;
        }


        public bool LoadPrivilege()
        {
            /*if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SL_CustomerGroupItemsNet).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }*/
            return true;
        }
    }
}