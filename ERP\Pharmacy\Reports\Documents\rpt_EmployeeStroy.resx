﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="xrTable21.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.00015, 569.0834</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lbl_absenseWithout.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lbl_absenseWithout.Weight" type="System.Double, mscorlib">
    <value>0.48651804486915334</value>
  </data>
  <data name="xrTableCell40.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell40.Text" xml:space="preserve">
    <value>غياب</value>
  </data>
  <data name="xrTableCell40.Weight" type="System.Double, mscorlib">
    <value>0.25471026441890116</value>
  </data>
  <data name="xrTableRow24.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTableCell41.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="xrTableCell41.Weight" type="System.Double, mscorlib">
    <value>0.48651805044170277</value>
  </data>
  <data name="xrTableCell42.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell42.Text" xml:space="preserve">
    <value>بدل إجازة</value>
  </data>
  <data name="xrTableCell42.Weight" type="System.Double, mscorlib">
    <value>0.25471025884635173</value>
  </data>
  <data name="xrTableRow26.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="lbl_absenseWith.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_absenseWith.Weight" type="System.Double, mscorlib">
    <value>0.48651805044170277</value>
  </data>
  <data name="xrTableCell44.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell44.Text" xml:space="preserve">
    <value>غياب بإذن</value>
  </data>
  <data name="xrTableCell44.Weight" type="System.Double, mscorlib">
    <value>0.25471025884635173</value>
  </data>
  <data name="xrTableRow27.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable21.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>420.97113, 87.5</value>
  </data>
  <data name="xrTable21.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable19.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>430.972046, 569.0834</value>
  </data>
  <data name="lbl_vac1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_vac1.Weight" type="System.Double, mscorlib">
    <value>0.68934056092316531</value>
  </data>
  <data name="xrTableCell30.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell30.Text" xml:space="preserve">
    <value>إجازة</value>
  </data>
  <data name="xrTableCell30.Weight" type="System.Double, mscorlib">
    <value>0.52805582574271492</value>
  </data>
  <data name="xrTableRow19.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="lbl_vac2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_vac2.Weight" type="System.Double, mscorlib">
    <value>0.68934034922753451</value>
  </data>
  <data name="xrTableCell32.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell32.Text" xml:space="preserve">
    <value>عارضة</value>
  </data>
  <data name="xrTableCell32.Weight" type="System.Double, mscorlib">
    <value>0.52805603743834573</value>
  </data>
  <data name="xrTableRow21.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="lbl_vac3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_vac3.Weight" type="System.Double, mscorlib">
    <value>0.68934034922753451</value>
  </data>
  <data name="xrTableCell34.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell34.Text" xml:space="preserve">
    <value>مرضي</value>
  </data>
  <data name="xrTableCell34.Weight" type="System.Double, mscorlib">
    <value>0.52805603743834573</value>
  </data>
  <data name="xrTableRow22.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="lbl_totalVac.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_totalVac.Weight" type="System.Double, mscorlib">
    <value>0.68934034922753451</value>
  </data>
  <data name="xrTableCell36.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell36.Text" xml:space="preserve">
    <value>إجمالى الإجازات</value>
  </data>
  <data name="xrTableCell36.Weight" type="System.Double, mscorlib">
    <value>0.52805603743834573</value>
  </data>
  <data name="xrTableRow23.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable19.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>322.027832, 116.666626</value>
  </data>
  <data name="xrTable19.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel5.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>LightGray</value>
  </data>
  <data name="xrLabel5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt, style=Bold</value>
  </data>
  <data name="xrLabel5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>286.311523, 544.291748</value>
  </data>
  <data name="xrLabel5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>466.688416, 24.791626</value>
  </data>
  <data name="xrLabel5.Text" xml:space="preserve">
    <value>الأجازات خلال العام</value>
  </data>
  <data name="xrLabel5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Qualification.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="lbl_Qualification.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Qualification.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.00015, 507.708374</value>
  </data>
  <data name="lbl_Qualification.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>603.3176, 29.1666565</value>
  </data>
  <data name="lbl_Qualification.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel4.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>LightGray</value>
  </data>
  <data name="xrLabel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt, style=Bold</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>286.311157, 482.916656</value>
  </data>
  <data name="xrLabel4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>466.688782, 24.7916565</value>
  </data>
  <data name="xrLabel4.Text" xml:space="preserve">
    <value>مؤهلات ومهارات الموظف</value>
  </data>
  <data name="xrLabel4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Address.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="lbl_Address.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Address.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999816, 415.2083</value>
  </data>
  <data name="lbl_Address.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>603.317932, 55.2083435</value>
  </data>
  <data name="lbl_Address.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel3.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>WhiteSmoke</value>
  </data>
  <data name="xrLabel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10.2pt, style=Bold, Underline</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>613.3177, 415.2083</value>
  </data>
  <data name="xrLabel3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>139.684448, 29.1666584</value>
  </data>
  <data name="xrLabel3.Text" xml:space="preserve">
    <value>محل وعنوان الإقامة بالتفصيل
</value>
  </data>
  <data name="xrLabel3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable16.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999786, 356.875031</value>
  </data>
  <data name="lbl_emType.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_emType.Weight" type="System.Double, mscorlib">
    <value>1.0593389641090729</value>
  </data>
  <data name="xrTableCell25.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>WhiteSmoke</value>
  </data>
  <data name="xrTableCell25.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell25.Text" xml:space="preserve">
    <value>صفته</value>
  </data>
  <data name="xrTableCell25.Weight" type="System.Double, mscorlib">
    <value>0.55460225824539433</value>
  </data>
  <data name="xrTableRow16.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable16.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>420.971436, 29.1666584</value>
  </data>
  <data name="xrTable16.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable15.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.0001221, 327.708374</value>
  </data>
  <data name="lbl_emName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_emName.Weight" type="System.Double, mscorlib">
    <value>1.0593376923919897</value>
  </data>
  <data name="xrTableCell21.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>WhiteSmoke</value>
  </data>
  <data name="xrTableCell21.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell21.Text" xml:space="preserve">
    <value>اسمه</value>
  </data>
  <data name="xrTableCell21.Weight" type="System.Double, mscorlib">
    <value>0.55460225824539433</value>
  </data>
  <data name="xrTableRow15.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable15.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>420.9711, 29.1666584</value>
  </data>
  <data name="xrTable15.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable14.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>430.972076, 386.0417</value>
  </data>
  <data name="lbl_Telephone.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Telephone.Weight" type="System.Double, mscorlib">
    <value>0.69908275963442534</value>
  </data>
  <data name="xrTableCell24.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell24.Text" xml:space="preserve">
    <value>رقم تليفون أرضي</value>
  </data>
  <data name="xrTableCell24.Weight" type="System.Double, mscorlib">
    <value>0.53552918328298715</value>
  </data>
  <data name="xrTableRow14.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable14.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>322.0293, 29.1666584</value>
  </data>
  <data name="xrTable14.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable13.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>430.972076, 356.875</value>
  </data>
  <data name="lbl_MobileComp.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_MobileComp.Weight" type="System.Double, mscorlib">
    <value>0.69908275963442534</value>
  </data>
  <data name="xrTableCell22.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell22.Text" xml:space="preserve">
    <value>رقم الموبايل الشركة</value>
  </data>
  <data name="xrTableCell22.Weight" type="System.Double, mscorlib">
    <value>0.53552918328298715</value>
  </data>
  <data name="xrTableRow13.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable13.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>322.0293, 29.1666584</value>
  </data>
  <data name="xrTable13.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable11.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>430.972076, 327.708344</value>
  </data>
  <data name="lbl_Mobile2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Mobile2.Weight" type="System.Double, mscorlib">
    <value>0.69908275963442534</value>
  </data>
  <data name="xrTableCell16.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell16.Text" xml:space="preserve">
    <value>رقم الموبايل الشخصى 2</value>
  </data>
  <data name="xrTableCell16.Weight" type="System.Double, mscorlib">
    <value>0.53552918328298715</value>
  </data>
  <data name="xrTableRow11.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable11.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>322.0293, 29.1666584</value>
  </data>
  <data name="xrTable11.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>WhiteSmoke</value>
  </data>
  <data name="xrLabel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10.5pt, style=Bold, Underline</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>286.3116, 298.541718</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>144.659668, 29.1667175</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>شخص يرجع اليه في الطوارئ</value>
  </data>
  <data name="xrLabel2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable10.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>430.971222, 298.5417</value>
  </data>
  <data name="lbl_Mobile1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Mobile1.Weight" type="System.Double, mscorlib">
    <value>0.69908637452282041</value>
  </data>
  <data name="xrTableCell19.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell19.Text" xml:space="preserve">
    <value>رقم الموبايل الأساسى</value>
  </data>
  <data name="xrTableCell19.Weight" type="System.Double, mscorlib">
    <value>0.53552754528565261</value>
  </data>
  <data name="xrTableRow10.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable10.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>322.029816, 29.1666584</value>
  </data>
  <data name="xrTable10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>LightGray</value>
  </data>
  <data name="xrLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt, style=Bold</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>286.311523, 273.75</value>
  </data>
  <data name="xrLabel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>466.688477, 24.791626</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>طرق الاتصال بالموظف</value>
  </data>
  <data name="xrLabel1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTable9.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999807, 233.958282</value>
  </data>
  <data name="lbl_Children.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Children.Weight" type="System.Double, mscorlib">
    <value>0.680375176394145</value>
  </data>
  <data name="xrTableCell14.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell14.Text" xml:space="preserve">
    <value>عدد الأولاد إن وجد</value>
  </data>
  <data name="xrTableCell14.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell14.Weight" type="System.Double, mscorlib">
    <value>0.35620230968803512</value>
  </data>
  <data name="lbl_Marital.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Marital.Weight" type="System.Double, mscorlib">
    <value>0.44899937846756516</value>
  </data>
  <data name="xrTableCell17.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell17.Text" xml:space="preserve">
    <value>الحالة الإجتماعية</value>
  </data>
  <data name="xrTableCell17.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell17.Weight" type="System.Double, mscorlib">
    <value>0.34394795159022851</value>
  </data>
  <data name="xrTableRow9.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable9.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>743.000549, 29.1666565</value>
  </data>
  <data name="xrTable9.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable8.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.0001259, 204.791641</value>
  </data>
  <data name="lbl_InsturanceDate.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_InsturanceDate.Weight" type="System.Double, mscorlib">
    <value>0.68037521396657119</value>
  </data>
  <data name="xrTableCell12.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell12.Text" xml:space="preserve">
    <value>تاريخ التعيين في التامينات</value>
  </data>
  <data name="xrTableCell12.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell12.Weight" type="System.Double, mscorlib">
    <value>0.35620223454318278</value>
  </data>
  <data name="lbl_SalaryBasic.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_SalaryBasic.Weight" type="System.Double, mscorlib">
    <value>0.448998667853029</value>
  </data>
  <data name="xrTableCell15.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell15.Text" xml:space="preserve">
    <value>اساسى الراتب في التامينات</value>
  </data>
  <data name="xrTableCell15.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell15.Weight" type="System.Double, mscorlib">
    <value>0.34394794832866832</value>
  </data>
  <data name="xrTableRow8.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable8.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>743.000244, 29.1666565</value>
  </data>
  <data name="xrTable8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999807, 175.625</value>
  </data>
  <data name="lbl_Age.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Age.Weight" type="System.Double, mscorlib">
    <value>0.68037520463022283</value>
  </data>
  <data name="xrTableCell10.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell10.Text" xml:space="preserve">
    <value>العمر عند الطباعة</value>
  </data>
  <data name="xrTableCell10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell10.Weight" type="System.Double, mscorlib">
    <value>0.35620231634535149</value>
  </data>
  <data name="lbl_Insturance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Insturance.Weight" type="System.Double, mscorlib">
    <value>0.44899846649279546</value>
  </data>
  <data name="xrTableCell13.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell13.Text" xml:space="preserve">
    <value>الرقم التأمينى</value>
  </data>
  <data name="xrTableCell13.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell13.Weight" type="System.Double, mscorlib">
    <value>0.34394777664367249</value>
  </data>
  <data name="xrTableRow7.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable7.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>743.0001, 29.1666565</value>
  </data>
  <data name="xrTable7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.0001259, 146.458344</value>
  </data>
  <data name="lbl_AgeOnStart.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_AgeOnStart.Weight" type="System.Double, mscorlib">
    <value>0.68037527977507506</value>
  </data>
  <data name="xrTableCell8.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell8.Text" xml:space="preserve">
    <value>العمر عند التعيين</value>
  </data>
  <data name="xrTableCell8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell8.Weight" type="System.Double, mscorlib">
    <value>0.35620224120049915</value>
  </data>
  <data name="lbl_birthDate.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_birthDate.Weight" type="System.Double, mscorlib">
    <value>0.44899846649279551</value>
  </data>
  <data name="xrTableCell11.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell11.Text" xml:space="preserve">
    <value>تاريخ الميلاد</value>
  </data>
  <data name="xrTableCell11.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell11.Weight" type="System.Double, mscorlib">
    <value>0.34394777664367249</value>
  </data>
  <data name="xrTableRow6.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable6.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>743.0001, 29.1666565</value>
  </data>
  <data name="xrTable6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999807, 117.291672</value>
  </data>
  <data name="lbl_Salaryf.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Salaryf.Weight" type="System.Double, mscorlib">
    <value>0.68037520463022283</value>
  </data>
  <data name="xrTableCell6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell6.Text" xml:space="preserve">
    <value>الأجر المعين عليه</value>
  </data>
  <data name="xrTableCell6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell6.Weight" type="System.Double, mscorlib">
    <value>0.35620231634535149</value>
  </data>
  <data name="lbl_Beginning.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Beginning.Weight" type="System.Double, mscorlib">
    <value>0.44899846649279546</value>
  </data>
  <data name="xrTableCell9.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell9.Text" xml:space="preserve">
    <value>تاريخ التعيين</value>
  </data>
  <data name="xrTableCell9.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell9.Weight" type="System.Double, mscorlib">
    <value>0.34394777664367249</value>
  </data>
  <data name="xrTableRow5.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>743.0001, 29.1666565</value>
  </data>
  <data name="xrTable5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.0000935, 88.12501</value>
  </data>
  <data name="lbl_Salarybf.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Salarybf.Weight" type="System.Double, mscorlib">
    <value>0.68037527977507506</value>
  </data>
  <data name="xrTableCell5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell5.Text" xml:space="preserve">
    <value>الراتب قبل اخر زيادة</value>
  </data>
  <data name="xrTableCell5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell5.Weight" type="System.Double, mscorlib">
    <value>0.35620231634535149</value>
  </data>
  <data name="lbl_nationalId.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_nationalId.Weight" type="System.Double, mscorlib">
    <value>0.44899839134794323</value>
  </data>
  <data name="xrTableCell7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell7.Text" xml:space="preserve">
    <value>الرقم القومى</value>
  </data>
  <data name="xrTableCell7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell7.Weight" type="System.Double, mscorlib">
    <value>0.34394777664367249</value>
  </data>
  <data name="xrTableRow4.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>743.0001, 29.1666565</value>
  </data>
  <data name="xrTable4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999998, 58.95834</value>
  </data>
  <data name="lbl_Salary.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Salary.Weight" type="System.Double, mscorlib">
    <value>0.68037560938803154</value>
  </data>
  <data name="xrTableCell4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell4.Text" xml:space="preserve">
    <value>الراتب الحالى</value>
  </data>
  <data name="xrTableCell4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell4.Weight" type="System.Double, mscorlib">
    <value>0.3562022547100997</value>
  </data>
  <data name="lbl_nationality.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_nationality.Weight" type="System.Double, mscorlib">
    <value>0.44899809412028541</value>
  </data>
  <data name="xrTableCell3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>الجنسية</value>
  </data>
  <data name="xrTableCell3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell3.Weight" type="System.Double, mscorlib">
    <value>0.34394792693336707</value>
  </data>
  <data name="xrTableRow2.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>743.0001, 29.16666</value>
  </data>
  <data name="xrTable2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.0001259, 29.7916737</value>
  </data>
  <data name="lbl_job.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_job.Weight" type="System.Double, mscorlib">
    <value>0.94680169392905833</value>
  </data>
  <data name="xrTableCell2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>الوظيفة</value>
  </data>
  <data name="xrTableCell2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell2.Weight" type="System.Double, mscorlib">
    <value>0.49568656612663309</value>
  </data>
  <data name="xrTableCell31.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="xrTableCell31.Weight" type="System.Double, mscorlib">
    <value>0.62482196616984154</value>
  </data>
  <data name="lbl_empCode.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_empCode.Text" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="lbl_empCode.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_empCode.Weight" type="System.Double, mscorlib">
    <value>0.47863365695134896</value>
  </data>
  <data name="xrTableRow1.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>743.000549, 29.1666584</value>
  </data>
  <data name="xrTable1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.0001259, 0.6250064</value>
  </data>
  <data name="lbl_empName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_empName.Weight" type="System.Double, mscorlib">
    <value>0.94680105474524034</value>
  </data>
  <data name="cell_Length.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="cell_Length.Text" xml:space="preserve">
    <value>الاسم بالكامل</value>
  </data>
  <data name="cell_Length.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_Length.Weight" type="System.Double, mscorlib">
    <value>0.49568666407088718</value>
  </data>
  <data name="xrTableCell1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrTableCell1.Weight" type="System.Double, mscorlib">
    <value>0.62482179077484523</value>
  </data>
  <data name="cell_Serial.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="cell_Serial.Text" xml:space="preserve">
    <value>كود الموظف</value>
  </data>
  <data name="cell_Serial.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_Serial.Weight" type="System.Double, mscorlib">
    <value>0.47863363756404376</value>
  </data>
  <data name="xrTableRow3.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>743.0002, 29.16666</value>
  </data>
  <data name="xrTable3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable18.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.0001221, 386.0417</value>
  </data>
  <data name="lbl_emTele.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_emTele.Weight" type="System.Double, mscorlib">
    <value>1.0593376923919897</value>
  </data>
  <data name="xrTableCell29.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>WhiteSmoke</value>
  </data>
  <data name="xrTableCell29.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell29.Text" xml:space="preserve">
    <value>رقم تليفونه</value>
  </data>
  <data name="xrTableCell29.Weight" type="System.Double, mscorlib">
    <value>0.55460225824539433</value>
  </data>
  <data name="xrTableRow18.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable18.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>420.9711, 29.1666584</value>
  </data>
  <data name="xrTable18.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>875.4585</value>
  </data>
  <data name="Detail.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lblCompName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 16pt, style=Bold</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>420.9719, 36.2916641</value>
  </data>
  <data name="lblCompName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>332.027954, 29.9999943</value>
  </data>
  <data name="lblCompName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="picLogo.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAQEAAAAAAAD/2wBDAAMCAgICAgMCAgIDAwMDBAYEBAQEBAgGBgUGCQgKCgkICQkK
        DA8MCgsOCwkJDRENDg8QEBEQCgwSExIQEw8QEBD/2wBDAQMDAwQDBAgEBAgQCwkLEBAQEBAQEBAQEBAQ
        EBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBD/wAARCAD1AXIDASIAAhEBAxEB/8QA
        HQABAAIDAAMBAAAAAAAAAAAAAAcIAQUGAgMECf/EAFoQAAECBQMBBQQFBAsKCwkAAAECAwAEBQYRBxIh
        MQgTQVFhFCJxgRUyUnKRFiNCoRckMzhidYKSsbKzNTY3RldzdJSixBgmQ1OEk7TBw9HSJSc0RFRVVnaF
        /8QAGwEBAAIDAQEAAAAAAAAAAAAAAAQFAgMGBwH/xAA8EQABAwIEBAIIBgEDBAMAAAABAAIDBBEFEiEx
        BkFRYRNxIjKBkaGxwdEUFSNC4fBSM3LxBxaSomKywv/aAAwDAQACEQMRAD8A/VOEIRgiQhCCJCEIIkIR
        jpBFmEYzmGYIsxiIn1n11kNOEfQlGaanq+6jd3az+blUkcKcxySeoSCOOSQMZqtc2oV63g+t64rknZsL
        J/M94UMp9A2nCR+EcnjPF9JhUhgYDI8bgGwHYnXXyB7q7oMDnrGeI45WnbqfYv0BzDOI/N9txxpYcaWp
        C08hSTgiJDsXXjUCyZhtJqztWpwIC5KecLg2/wABZypB8sHHmDFVS/8AUCnkkDaiEtHUHN8LBTJuGZGt
        vE8E9CLfUq70I5uwr8oWodvtV+hPHafcfYX+6S7oGShQ+fB6Ecx0eY72GaOojEsRu06ghc3JG6JxY8WI
        WYQhGxYJCEIIkIQgiQhCCJCEIIkIQgiQhCCJCEIIkIQgiQhCCJCEIIkIQgiQhCCJCEIIkIQgiQhCCJCE
        IIkIQgiQhCCJHw1ms0u36Y/Wa1PNSclKo3uvOnCUj/vJOAAOSTgR9pinvaQ1Kmbru161pCZUKRQ3SyUJ
        PD0yOHFnz2nKR8CR9aKXHsZZglIZyLuOjR1PfsOfu5qww2gdiE3hjQDUnst/fnatrU2+5JafyTchKpOB
        OzTYcfX/AAkoOUoHoQo/DpEWzurWp1QdLz9+1xKic4YnFsj8GyAI5KEeNVmOYhXPL5pXeQNgPIDRd3Bh
        9LTtysYPmfevfP1Ceqs49UanOPTU1MK3uvPLKlrV5knkx6IQiqJLjc7qaAALBIQhHxFsqNc1yW6HRb9w
        VKme0bS77HNLZ37c43bCM4yfxjqaLrlqrQ3kusXjOzSQRubnSJhKh5HeCR8iD6xwkIlQ11TTW8GRzbdC
        QtMlPFL67QfMBW10u7S1GuyZZoV3yzVHqTuENPpX+1n1/Z97lsnwBJB884Bm6PzcMW97NWpkxeFtvW1W
        ZguVOiJQlLiz7z8seEk+ZTjaT5FPiTHpfCnFMtdIKGtN3H1Xdbcj3tsfquUxnB2U7PxFONOY6dwpmhGI
        zHoC5hIQhBEhCMZgizCMQgizCMZjMESEep+ZlpZAcmZhtpJO0KWoJGfLmDE1LTKSuWmG3kg4JbWFAH5R
        8zC+W+q+2Nrr2wjGYzH1fEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQReiefVLS
        UxMoAKmWluAHoSATH5zPPOzLzkw+4pbrqitaldVKJySfnH6OutoebU06kKQtJSoHoQesfnndNAm7WuOp
        W7OpIep8ytgkj6wB91XwIwR6GPNv+oTHkU7/ANvpD26fZdZww5v6jeen1WrhCEeZrrEhCN1Ztp1K97lk
        LZpQHfzrm0rUMpaQOVrV6JSCfXp4xnFG+Z4jjFyTYDuVi9zWNLnGwC0sIsXd/ZqtZq3Kiqxbgm5+vURK
        VTco46253h2BZRsSkFCinlPXPTnqK6CJ+JYVU4U8MqANdiDcaaHXqDoVHpayKsaXRHb2f26QhCK1SkiW
        OzDPvymrEpLtKIROycyw6M9UhHef0tpiJ4m/so209Ub4nLkW2fZqRKKQF447533Uj+YHP1ecXPDzHyYr
        TiPfMD7BqfhdQMTc1tHIXdD/AB8VbMRmMRmPfF5skIRgwReuampaSl3ZycfbYYYQXHXXFBKUJAyVEngA
        Dxit+oXasmEzTtN07kWSyglJqM2gkrPm23xgeqs5+yI13aZ1YXVqgrT2gTZ9ikl5qTiFcPPjo1kdUoPU
        eKvuxE2n1h1jUW5GLepA2bvzkzMKGUS7II3LPn1AA8SQPWPNeIeJ6qaq/LsKOt7EjcnoOgHX6b9ZheEQ
        xw/iqwd7HYDqfst69r/q886XlXo+knwRLsJSPkEYic+zzqJqbf7k69cpk5mkSY7szhl+6eW+QCEJ2YQQ
        Byr3cjKfOIA1DtKgUq/BZVivTdQUwWpFxx5SVF6dJwsIwBgZITg5wQrnEXMsCz5Kw7Sp1sSYSoyrQ79x
        Ix3ryuVr8+VZxnoMDwjXwrFiVRiL/HncWR+t6RILumuh5kn7rPGX0sVK3w4wC/bQAgdV0OIrprT2jpim
        zsxaWnz6A8wotTdTwFbVjhSGQeMjoV/HHgqOu7RGqf5DW39AUiZ2VustqShST70vL9Fu+hPKU+uSPqxF
        Ol+ntFtayJ7WTUCnNzTDDJVSafMJBQ8o+6ha0nruUQEg+GVc8EXPEGK1E05wygdlIBdI/wDxaNd+Wnt2
        A5qBhlFHHH+LqRe5s1vU/wB+qhqp1eq1qaVPVipTU9ML5U7MPKcWfmokx0GmFuV+671p1Ft2fmpGYdXv
        cm5dakKl2U8rcykjGB055JA8Y5h95T7zj6koCnFlZCE4SCTngDoPSLU9lOyBSrYmr1m2sTNYWWZYkcpl
        mzgkfeWD8kJMeeYBhzsXxFkRJt6zjzsPvt7V0+JVQoaVzxvsPM/26nGUlxKSrMoHXXQy2lsOOrK1rwMZ
        Uo9SepPiYhm9O1HbFsVyZodKokzWFSbhZefS+lpreOFBBwoqweM4AyDjIwYmSfamX5GZYk3wzMONLS04
        f0FkEJV8jzFDrg0t1DtuadYq1pVPCFEF9qXU6yr1DiQUn8Y9I4sxSvwuKMUDTY3u617WtYcxr36aLlcF
        o6ase41B22F7X7q0OnvaMtC+6vL285ITlKqM0SlhLxStpxQGdoWD14OMgZ6dTiJXin+gGl1z1O+qbcVQ
        o01J0ulOe0rfmGSgOOJB2JRuHvHdgkjoB54i4ETOF6+uxGjM1cNb2Bta4sNbefNaMYpqelnDKc6W11vY
        rMIQjpFUpCEIIkIQgiQjGYzBEhCEESEIQRIQhBEhCEESEIQRIR81RnDT5F+dEpMTXcIK+5l0b3V48Epy
        Mn0iIZ3tVWFTpt6QnrcumXmGFlt1p2TZQtCh1BSp0EH0IiDWYnSYfYVUgbfa6kwUk9TfwW3su81PvZrT
        6yqhcqkpcfaSGpVtXRx9fCAfMD6x9EmKIVKpT1YqExVanMuTE3NuKdedcOVLUTkkxaSt6qaFayUtu3ro
        qdSpSUTCXGPaQpghzapIXvQVtjAUfrnHMR3fHZprVLkfp+wami5KWtPepQ3gv7OoKdpKXRjxTgnwTHnn
        FTanG3CahcJIWDZpuQeZLd/4XT4MYsPaY6gFj3HciwPQA7KFoRlaFtrU24hSVoJSpKhggjqCIxHna6hI
        sVoZISWmumVd1irLAU++0pmRQr9JtKglKQRyO8ewDxwEA9Ir3Jyj9QnGJCVRvemXUtNp81KOAPxMT/2m
        5+Xtq27U0upSwJeTYTMvJCcFSW0900ePM96T6gGOjwG1IyfE3bxNs3/e7QH2alVeJXmdHSD951/2jU+/
        RcTo1qjP23qZ9MVyfUuVuF4tVRazhJU4rKXj0A2rOc+CSoDrHj2g7ERZWoEw7JMd3TqwDPSwAwlCifzi
        B5YVkgeAUmIxixupK1aj9nSgXw6e8qFGUhEy4pe5ahu9ndJ9VqDaz6RlROOJYZPSSG7o/wBRvls8fG/m
        sahopKuOZugf6B//AD9lXOEIRzStkMd5o1qPN6dXhLza5hSaVPKTL1JrkpLRPDmPtIJyD1xuHiY5W3ba
        rt2VRqjW7TXp6bd5DbY+qPFSj0SkZGScCJypvZ4sqyqc3WtYb0Ylt4JTJy7obSSMZSFEFbpAPIQkH1MX
        eDUNfLKKqkGUMN8xNmjzJ+I6KBXVFMxhhn1zchqT7FZ5JCgFJIIPII8YzEKudp/SigttUimMVyelpRtL
        LTrEsCkoSABy8tKzwOpGY3tla+Wzf1WRSLftq5lrJAcfVJNllgHopxSHFbRweT5R7HFj2HTPETJmlx0s
        DfXt1XCPw2qY0vLCB30UmxHGuOpyNOLSUuScT9MVLcxIpPJQce86R5IBGP4RT4ZjvqhUJKlSMxU6jMIY
        lZRpTzzq+iEJGVKPwAiiWqN/Tmo93zdwP7kSw/MSTKj+5MJJ2j4nJUfVR8MRWcV43+U0nhxH9R+g7Dmf
        t38lLwXD/wAbPmePQbv36D7rlFF2YdKlFbjriskkkqUo/wBJJi1dEpbHZ80Una7NoSi4qm2kq3DJEysE
        NNeobBUojxIX6RHPZm05/Ki6DdtTY3U2hrCmgoe67N9UD+QMLPrs84+/tX3ialdElZsq7+YpDQfmADwZ
        hwAgH4I24++Y4bCYPyjDJMZk9d3ox+Z0LvnbyPVdFWyfjatlC31R6TvZsP71C+Lsv2mq5L9mLpqCVOs0
        Rvvt6+d0y7kIJz1IHeKz5gRai4q9TbXok5cFXf7qTkWi86rxwOgHmScADxJER52a7ZFv6YSc461smKy6
        uecyOdp91v5bEhQ+8YjLtTaj/SNTZ06pT2WJFSX6gUH675HuN/yQcn1UPFMdTQzM4Z4fFQ7136ju523u
        Fr+RVPUxuxbEzEPVbp5Ab/FcpaVJq/aB1ZeqdaCxI957TO7SdrMsk4Qwk+BPCQev1leBjr+1bdzTb9K0
        6pZS1LyTaZyZbbGEg4KWm8DptTuOOnvJ8olTRaxZbTDT5LtWSlifm2/b6m4sctYTkIPohPUeZV5xUC9b
        lmLwuyq3NMlW6fmVuJST9RvohPyQEj5Rz+Jsfg+EBkp/XqTmeedhrb3kX8yrOkc2urszP9OIWb0vtf8A
        vZfBSKXN1yrSVGkEbpmfmG5Zoea1qCR+sx+hFBo8nb1EkaFIJ2y9Pl25ZvjkpQkDJ9TjJ9YqN2ZLaFd1
        NZqDzYUxRZZycO4cFw4QgfHKyofci3lZqkrQ6RO1qeViXkJdyZdP8FCSo/qEXPAdG2CklrpNMxt7G7/H
        5KBxHOZJmU7eWvtP9+KjDVTtD0XTqrqtySo7lWqTSEqfT34ZaY3DKQVYUSrBBxgcEcxxlL7YLBeCK3Y6
        22ieXJWdC1AfcUgAnr+kIrzXqzOXFW5+uz6t0xUJhyZc9FKUTgegzgfCOv0TsQX/AH7JU6aa306T/bk9
        kcKaQRhB+8opT8CT4RQ/91YviFeI6N9g51mtsNr6XuCfMqy/JqGmps07bkC5Nz8Nfcrr0Ory9fo0lW5V
        iYaYn2ETDaH0bHAlQyNw8DgxFt/dpizLRmHKZRGF16eaJSvuHAiXQoeBdwcn7oI9RHz9pu/pq07Slrbp
        D6mJuulba3GzgtyyAN4GOm7clPw3RV+yrTnr4umn2tTXENPz7hT3i+jaEpKlqPnhKVHHjjEdDxDxLVUd
        Q3DaHWXQE25nYAbXPe+9lV4XhMM8ZqqjRmth2HMlS452ur0LiixbNEQjPuhXeqIHqQsZ/CJA0q7SMnfF
        ZYtm4aOim1CaJTLPMuFTLqwM7CFcpJxxyQTxxxnS1Psg0k08ijXhNpngngzLCS0tWPJOCkE+OVY9Yi2y
        LBui3NbKBbFUkVMzspUWZpW33kLYbVvU4lXQpKUK5+XBGIrBV8SYVVRfjCS17gORBudtNj02UzwcKrIX
        +AACATzB0567hXVdeaYaW++6ltttJWta1YSlIGSSTwABEJ1jtY2JIzjkrTKRVai22rb36UobQvnkp3Hd
        jHTIEb7tH3Uba0xnJZh0omay4mnN4POxWS58tiVJ/lCKWxZ8WcTVOGVDaWjIBtdxtffYa+/2hQ8FwmKr
        iM04uL2H3V1LJ7RFgXtVJehyyajIz80rYy1My4IcX5BSCoefJxHb3XdtAsqjO124qgiVlWuBnlTij0Qh
        PVSjjoPU8AExAvZ0tSkWfatQ1juxSGWw04iTWsZLbKTtWsD7S1DYnHPBH6URXqBed46w1ybrLVNnnqdT
        UqUzKy7K3G5JjP1llIwCcZUs9fgAA/7nq6LDGS1QDp5NWNA/b/k75i1r++338ognq3MhJEbdHE9egUsz
        nbAlk1PbIWS45TwrBW9OBDyk+e0JKQfTJ+MWCo1Vkq7SZOtU5wuSs+wiYZURglC0hQyPA4PSPzni92ir
        bjelVspd+sZFKhzngkkfqIjHg/Hq7FKmWKrdmFrjQC2oFtAOvwX3HMOp6OFj4RY3tuddF20IQj0BcwkI
        QgiQhCCJCEIIkIQgiwRHAaq6PW9qbTip5CJKsMpxLVBCPeH8BzH10eh5HJGMkGQIxEeqpIa2IwVDczTy
        /vPutsM8lO8SRmxC/PK6LYrVnVuZt+vyhl5uWVgjqlaT0Wk+KSOQf++N3p3qpdmm0+l+izinZFSszFPe
        USw6DjJx+irgYUOeBnIyDa7WjSeS1Mt5Rlm0NV2RQVSEwcDd4llZ8UK8PsqwemQaTTcpNSE09Izsu4xM
        S7imnmnElK21pOFJUD0IIwRHjGM4VU8NVofA4hp1a4b+R7jnyI9y72grIsWgLZAL8x9VY+8LKtbX22Va
        hadNty1xsjE/IEhKnl45Qvw38e6voocH+DW6Yl35SYclJtlxl5lZbcbcSUqQoHBSQeQQfCN/Yd9VzT24
        WK/RHyCkhMwwVYRMNZ5bV8fA9QcERNmqFj0TWG1EauacNb6ilGKlJITlxwpSNySkf8qgY++nBGfdzsni
        j4ghdVQC1Q0Xe0bPH+bR1/yHtHfGN7sMkEMhvEfVP+J/xPboVD+kUs3OanWwy59UVNhz5oUFD9Yjqe07
        OzE1qzOMPKyiTlJZhoeSCjvMfznFRw+n1aat6+aDWphexiUqDDjyvJveN5/m5iTO1db7lOv+VrqWlhir
        yKCXD0LzRKFJHwR3R+cR4AX4DMGbtkaT5EED4rZIcuIszc2kDzuD8lChixekkuKj2ab3lnlZSy5PuJyM
        42SrTg/2hFdIsZaSXLL7LNcqE4vu119x8sIPCiHtkuBz5pQpX3eYy4ZsKiZ7vVEbyfK1kxbWJjRuXtt7
        1XIcdY6KxrGr+oNeaoNAl9y1e888rPdy7eeVrPgP1k8DmPhtu26vdtalaBQpRUxOTawlCR0SPFSj4JA5
        J8AIsLdVxUPs5WWiyLPW3MXXUmw7NzhSD3RPHeqBzz1DaOg5Uc9FxcKw1lSHVdWcsDNzzJ5Nb3PwGpW2
        sqnREQwi8jth07nsPis3Vedp9ne3/wAhbCYYnbmfQlU7OOJBLSiOFu46qwTsbzhIOTnPv10rddrFx1F2
        r16pPz04+creeWVE+QHkB4AYAHSPlmJiYm5h2bm33Hn31lx11xRUtayclRJ5JJySY6jTHTyp6l3QzQZE
        lmXQO+nZkjhhkEZPqo5ASPEnwAJGVZX1ONzMpoG2Zsxg2H3PUn5L5BTRUEbpZDd27nHc/wAdAtzo9o5V
        dUKkp55bklQpRe2bnAnlSsZ7prPBWQQSeiQcnOUhVybctmhWlSWqLbtNakZNrkIbHKleKlE8qUcDJJJO
        I87ft+k2vRpWg0SUTLSUm2G20JH4qJ8VE5JPUkkxyGs+p0vppay5phaF1ee3M09k4Pv45cUPsoyD6kge
        MeoYXhNHwvROqJyM4F3O+g7ch1PstyFZWz4vOIo/VvoPqf7ooo7UOqffOfsbUOZBQ2UuVVxCvrK6oY+X
        Cleu0eBEV/pNKn65VJSjUuXL83OvJYZbH6S1HA+A9Y9E1NTE7Muzk4+t599anHXHFEqWsnJUSepJOYsZ
        2VtNsl7UiqsdN8tSwofJx0frQP5fpHnLfxHFuLeloHf+rB/faT3XUnwsFotOXxcf77lNlmWxSdNLIlqM
        24lLFNl1PTT+Md4vG51w+PJzgeAAHhFHK3Up+9rtm6mtJVNVmeUtKCc4Li/dQPQZAHoIuPr7XFUDSmuP
        NL2uzjSZFHhnvVBK/wDYK4qPpay3Mak2u06MpNXlCR1Bw6k8+nEX/GTmGopsLi0a0DTzOUe4D4qtwIO8
        KWsfqT9NT81cm87jp2lGnTs+0lBTSpRuUkWVf8o6EhDSceXAJx4AnwitGgNmzWoupC7hrW6ZlaY79JTr
        jnPfTClEtpPqV5UfMII8Y23amvw1u6WbLkXsydEG+YweFzS08/HYkgehUsROOhdi/kJp9JSs0zsqFQHt
        07lOFJWsDag/dTtBHnu84sJAMfx1tO3/AEKffoSP508geqjNJw3DjKf9SX5f8a+ZXxdou5lW3pdUG2XC
        l+rLTTmyDjheS5+LaVj5xSvpFhe17XVO1egW0hWEy8s5PODzLitifw7tX86K9Ry3GlYanFXMB0YA0fM/
        E29it8Bg8GjDubiT9PorT9kWhey2vWricQAufnUSyCeuxpGcj0y6R/JjfdqC5TRdNF0xl3a9WppuVwOv
        dJy4s/D3UpP3o3XZ+pgpeklBQUYXMtuzSzz73eOqUDz/AAdo+URD2vqm45cFv0bcdkvJuzWPDLi9v/hR
        19S44XwqGt3cwD/zNz8CVSRD8ZjJJ2BP/rt8gq/Rb3svWcKBYa7imG8Tdfd73nqGEZS2PmStXwUIqlb1
        Gmbir1OoMoPz1Rmm5ZB8itQGfgM5+UfoTTafKUqnStKkWu7lpNlEuyj7KEJCUj8AIouAsPE1U+scNGCw
        8z9h81Y8SVOSFsA/dqfIfz8lXvteW9Ous0G6GWSuWY72SmFDPuKUQpvPocLGfPHmIrzQK9VLZrEpXqJN
        GXnZJwOMuAZwehBHiCCQR4gmL7uLo15t1m3ZyRbnacwoSU1vOUOOlO5bY+4C37wPCiRwUmKv6t9nWsWU
        h+v2qp6qURAK3UEZmJRPiVAfXQPtDkc5AAzG3ivA6g1DsVovSadTbdpbpcdtNxtvssMGxCIRCiqNDyvs
        QdbfFS5pb2ircvdTNFuBCKPWlgJSFK/a8wryQo/VJ+yrzwCoxKq6TTHKo3WnJFlU+yyqXbmCj30tqIJS
        D5EgR+dHI6cYi4HZp1EqV6WvN0iuTK5ifoa22w+s5U6wsHZuPioFCgT4jbnnmLLhfid+JSChrgC/drut
        tde/MEfNRcYwhtIw1FPo3mOl+nbso97XdcMxcdDttCvckpNc2sA/pOr2gH4Br/aiEKBRpq4q5T6DJDL9
        QmW5Zv0K1BOT6DOY73tHzC39Ya4hSgpLCZVtGPAeztqI/FRjk7Du1VjXPLXQ1TWp56TQ73LbqtqUuKbU
        lKzwc7SrOPTqOo4rGpmVWNyunNm57E9A05fkFf0DHQ0DBGLnLceZ1+ZU36mIm76umi6A2GsNUuiNtife
        T7yEbEgZXjr3aT0yMrVg8gRONu23a+mlqGnyCG5OmyDSn5l9zqvCcrdcV4nA58gABgACOO7P9iPW3ayr
        nrQU5XblV7dNOuD30tqO5CCfM53q9VYPQR3l12rSLzoUxbtdbeXJzWN4adU2rIOQcjyIBwcjjkGPT8Jo
        ZBG/EnMHjSD0QdA1tvRZ20tdchXVLS9tK136bTqRzPM/ZUSodAmr5vNmh0JgpVU5xQbATwy2VElRA/RS
        nJPoDF+qVTZWjUuUpEigplpFhuWZSeoQhISkfgBHMWBpNZum4eXbsk4ZmYG1yamV948UfYBwAE55wAM8
        ZzgR2IjDhjAHYNG985BkfvbYAcvv/CyxfEm172tj9VvXmVmEIR1KpkhCEESEIQRIQhBEhCEESEIQRIrb
        2mtIpl55epFtyZcBSBVmGk5UMDAmAB1GAAvywFfaIslGDFbi2Fw4xSmmm05g9D1/vJS6KsfQzCVntHUL
        83I7jSfVGqaX3B7fLpVMU6a2tz0puwHEA8KT4BacnB9SOhiwuofZktO6nnKpbL4t+fXlSm22t0q6rB/Q
        yO7JOOU8YH1SYhOvdm7VaiuOez0ViqsNgHvpGZQoK9Ahe1ZPwTHk0/D+MYJUCaFhdlNw5uvw3He4su1j
        xOhxCMxyOAvuDp/fYu61H0VpmossNRtHZiVm257K5iRQoNBbn6SkbsBC/tIVjnkc8He1Wh1+6OztPS2o
        tK9grFuNrXKvTQ2LUlhKSlQPiVoy3/CPr0hm3KTrjp/MOVC3aBdFO93c9sp7q2lAA8rQUlCsDPUHHpHy
        3XrPqReVJXQLgr5cknFJU6y3LNtd4UnICilIJAIBx0yB5CJn5pQwNlllgeySVpaWgDwyT+7XUWOul7cl
        o/B1EhYxkjXNYQQf3AdNNDp71orIo8rcF5UOhTyiJafqMvLvbVbSW1OAKAPgSCQPWLEa8WjqNe1YpVi2
        fbZRb1OZQ4lxKktMd7gpGSSAAhGAABn3ldeAKwS8xMScw1Nyj7jL7Kw4042opWhYOQpJHIIIyCIk+R11
        1zr5TRqRWpmcmXUFIRKU1pb6hjBI2tk59RyIrMIrqOKllo6oP9Mt1Za5A/br36KXW0875mTwlvog+tew
        J56KS/8Aih2YLVVtVL1i9qo1geSR5nxQyk/BTih4Y9yttXq1RrtTmazV5tczOzjhdedX1Uo/qHkAOAOB
        xHVK0x1duKormpyz7hmJyaXlx+cl3EFavNS3MfiTHaWx2Vb/AKq4ldxTMlQ2ArCwpwTD2PNKWzsPzWI3
        1UWI4yWU9JTubEz1W2Nh3c42BJ6n731wvpaEGWaUF7tzfXyAHJQ9TqdP1ieYplKknpqbmF7GmWUFS1q8
        gBF3dGtM2NMrTRT3g25VZ1QfqDyBkFf6LYPUpQDgepUeN2I9+nWkVn6ay5NGlC/UHEbXqhMYU8seIT4I
        T6JxnAzkjMbS979tnT+kqq1yVBLKTkMsJ5efUP0UJ8fj0GeSI7Th7h2PAWmurnDPb2NHPXry+AuqHE8U
        diThTUwOW/tP8L6rtuui2VQZm4q/NBmWlk8AY3urx7raB4qOOB8zgAmKM6g31VtRLlmLiqytu/8ANy7A
        VlMuyCdqB+JJPiST4x9+qGqNd1OrXt9QJl5CXJTJSKVZQynzP2lnxVj0GAAI41tC3VpaaQpa1kJSlIyS
        T4AeJjj+J+IzjEngQaRNOn/yPU/Qe09BeYRhQoWeJJ65+Hb7rpNOrHqGoV2SdtyIUlDqu8mngM9wwkje
        s/jgeZIHjF86TS5CiUyVpFLl0sSkm0lhltPRKEjAHr8fGI70H0sRp3bAmakyn6bqiUuzZxyynGUsg+mc
        nHVRPUARJwGI7vhLBPyql8WYfqP1PYch9T305LnMbxD8ZNkYfQb8TzP2/lQ12riRpkxjxqzH9m7FTKXU
        56i1KVq9MfLE5JPJfYdCQrYtJyk4IIPI6EYi5XaOoD9e0qqJlUFbtNcbnwkeKUEhZ+SFKPyilccXxy18
        WKiQaXaCD5Eq+4eLX0Zb3N1IWj9tTWpOqUp9LKVNoD66pUnHOe8SlW5W77yylJ+9F4Ir/wBka20S9ArN
        1utp7ydmUyTRI5DbaQpRHoVLH8yLAR2fBlD+Fw4Tu9aQ5j5bD7+1UOPVHi1XhjZmn3+3sVLO0lPmd1dq
        zW/cmTalpdOFZA/MpWR6e8s/PMRhEh9oKVelNXrgS8P3Vxl1BHQpUygj/wAviDEeZjyrGS52Izl2+d3/
        ANiuyoABSxgf4j5K/umbAltObXZDewpo0nuT/CLKSf15iuPa3acTqFS3yghtdGbQlXgVB97I+W4fjFg9
        H6mzV9L7YmmVZCKazLKOc+80nu1frQY53XfSOY1No0rM0Z1lqsUwrLIdO1D7asbmyrHByAUk8ZyDjOR6
        rjVFJieAtZTC5ysIHUADb2LjaCdtJiJdMbC7gVAPZqpbNS1Yp7rwSUyDD80AfFQQUj8CsH5RZ/Ue85i2
        5FqkW8yJu5atuapkqBuKSASp9Y8G0DKj54x5xWO1dFNcqVcLMzRqQ/R5thRT7aqabQhsHgnIUdwweQAr
        I8Iszp3pu1ZqH6rV6o9W7jnwBO1SZJUspHRpvJJSgeXjgE9ABU8JxV0dG+jEToyXEl5FrAgDQHUu005D
        c9DNxp9O6ds5eHWGjRr7+3xO3dcd2Z73o9astNvOzyfpyUmJh6aQ6v8AOzAccU53wzyr62CfAjnqImRZ
        SEkrICQOc9MRWbUzszXC3Xn7h02W06xMOF8SReDLsu4TkhtRwkpz0yQR05xmORmNLO0bWmjS6hLVyZl0
        Ap7qarCC0B0OAp3aR8IkU2MYnhEIoZ6NzywWDm3s4DbYHl7eoC1y0NJXSGojnDQ7Ug7j4hcTqS3QWr9r
        qLZdacpntzhly1ju8E5IRjjaFZAxxgCJ37IdvzbFOr1zPtqSxNuMykuTwFlvcpZ9R76Rn70aOyuybXJm
        Zam75qsvJSiSFLlZNfePLH2SvG1HxG75RZejUam2/S5ai0eUblZKTbDTLSBwlI/WSepJ5JJJiDwxw9VN
        r/zKrZ4YFyG87m/LkBfnrspGL4pCab8JC7MdLny+ZKpj2iJJUjq9XfdVtf8AZ30E+IUwjP8AtBQ+UarR
        +0EXvqHSKJMN75QO+0zYI4LLY3KSfvYCf5UT92jNHazfCpO6rUlkzNRk2TLzMtuCVPNAlSVJzgFSSVcd
        SCMdMHXdl/Tm4bYqFcrtzUSapzy2GpSVTMN7FLSVFThwecZQ38YrZeHp38Q+HIw+G55de2hHrEX27KUz
        E424Xna70g21ud9v5VggAAABgDpiMwhHra4lIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEEWDDEZhB
        FjEVb7WNm06lVel3dTpcMuVbvWZ0IGErdRtKV/eIUQfujxzFpYjfWvSuf1TpVNkJCqy8iuRmFvKU8hSg
        oFOMDEUPE1A7EcNkijbmfoW+dxt7LqzwipFLVte91m63933VJIu3oFZVPtPTmlzTTDPt1ZYbqE0+ke8s
        ODc2gk84SgpGOmdxHUkxH/wQLk//ADCm/wDUORZC16Q5b9s0iguvJeXTZGXlFOJGAsttpSVAeAOMxy/B
        2BVVBVPnrIsulmk25nXmrfHcRhqYWxwPvrqtniOQ1Fv2a0/poqwtKpVeUAPfOyZThjHi4D7wTjPvYI88
        ZGewjBEegVEcksZbE/I7kbA29h3XMxPax4L25h02+Sqvc3a2uaeZXL2tb0pStwx377hmHE+qRhKQfiFC
        IUrtwVq5qi5Vq/U5ifnHfrOvLyceAHgB5AcCLwXJo3ppdbqpir2nJ+0Kzuel90utR8yWyncfvZjmh2YN
        KO/736PqG3/m/bV7en4+vWPOsU4Zx7EH/rTte3lqWj/xAt811NHi+G0zf04y0+/43uqahJUQkAkngAeM
        WZ7PmhUxTX2L9vSSLcwj36dIupwpo+DzgPRX2Unp****************************************
        +GI64ROwLgsUMwqa1wc4bAbA9STa/uUfEcfNQwxU4IB3J3QRmEI71c0vB5lqYaWw+2lxtxJQtCxlKkng
        gg9QYrteHZJbmqg7O2VcLUpLurKhJzqFENZ8EuJySPIFOcdSYsZCK3EsIo8WYGVbL22OxHtCl0ldPREm
        F1r7ridILGqWndltW1VZyVmX233Xd8uCE4Wc4yQCT15+EdrGYRLpaaOkhbBF6rRYeS0TSunkMj9zqos1
        i0Mp+p7jFXk6iKbWJZruQ6pve2+*****************************************+LYMW9hFHiHC
        uG4lMZ5WkOO5Btf5hWNLjNVSsEbCCB1CjDQzT69dN6PN0O5anTZqTdd7+WblVrUplZ4WMqSng4BwOhz5
        xJ0ZhFzRUcdBA2nivlbtc3UCondUyGV+56LGICMwiUtKwYRmEEWIzCEEWDCMwgiQhCCJCEIIkIQgiQhC
        CJCEIIkIQgiQhCCLBOIZjW3PNVSRtqrTtEly/UZeRfdk2thX3j6W1FCdo5OVADHjHM6Q12+7gtqZndQq
        OabUUTy2mmjLKY3MBtshW1RJPvFYz6ekRX1bWVDaYg3cCb2006nqtzYS6Iy3Fgbd/cu5hGMxmJS0pCEc
        Ncmsdn2vd9OsuoKmHJyolCQ8yWizLlSykB1RWCnBGTweCI0VFVDSND53BoJtr1K2xQyTnLGLldzCPFK0
        LSFoUFJUAQQcgiM5jetSzGMx8NeTUnaHUW6MrbPqlHkyqsgYeKDs5PH1sdYialUvtAIYs8VGeWXGKs6u
        vfn5c75PvGdg46+4HuE88/CIFXXOpXhgic+4vdouNwPrfyBUqCmEzS4vDfM9ifpbzKmeEc1qTcM/ati1
        m4qWGjNyEsXWu9TuTuyByMjPWIXqOvd9yuk1JvRpNN+kZ2rPSTuZc933aUEjCd3Bz45iNiGOUuGyGKe9
        w3NoOV7dd7rbTYdNVsD47Wvb22urGxmNXbFRmKtbVJq03t7+dkWJh3YMJ3rbSo4HgMmNnmLaN4kYHjY6
        qE5pa4tPJZhGMiI91bktUpz6F/Y0mlM7HnDUMOtI3I9zb+6df0+kaKuoNLC6YML7cmi5OttB8fJbIIvG
        kDC4NvzOykKMxHlnSWqTGolwzN0zRXbLve/RSC40oJ/OjZwn3h7mesSEIUtQalheWFupFnCx05+R5JNE
        IXZQ4HTkswhGOkSVpSGREZ6tXTqjb9Uo7Gn9vKqMtMhftqxJrf7shSQOUn3eCrrEiVB5ctITMy3je0yt
        ac9MhJIiJFWsllliAIMdrkjQ3F9Dz7re+ncxjHkj0u+unVfRGYiPs/aoXNqXKVp65BJhVPcYS17O0UcL
        Cyc5Jz9URLeY+UFdFiVM2qhvlde199CR9Eqad9JKYZNx/wArMIxDMTFoWYxDMRJ2gdULm00lKK9bYk90
        +4+l72hor4QEEYwRj6xiHiFdFhtM6qmvlba9t9SB9Vvpqd9XKIY9z/ypbzGY1U3Uplm13qwjZ36JBUyM
        j3d4b3dPLMR9oBqXcmpVJq07cYlA5JTLbTXs7RQNpTk5yTGL8RhjqYqU3zSAkdNBc3WTaWR0T5hs2wPt
        UrQjEOsTlGWYQjGYIswjGYQRZhHxt1ikuzZp7VTlFzSSQWEvJLgI6+7nPEfXHxrmu9U3X0tLdwswhCPq
        +JCEIIkIQgiQhCCJCEIIuc1IccZ08uh5lxTbjdFnVIWkkKSQwsggjoYjrspzs7P6eVF6em3phYrTqQp1
        wrIHcMcZPxMSDqe4hvTa6lOLSgGizqQVHAyWFgD5kgfOKxaO3pqzZlAmVWfZbtdo01OFSgmTde2PhKQv
        BbORlISOQRwMeOePxeuZQY1BLICW5HXsCee9gr2hp3VNBIxtgcw30XbVypVBHavkpBE/MJllLZyyHVd2
        f2pn6ucdYsbFRX6RrtULyGsyrEeTOsTCFIlDLkHalvYAGCrvSnaME9cnj07hvXHXEISHdEp9SwBuKadO
        AE+OBtOPxMRMIxmKifUOqWPAkkc5voO9U7clvrqB9Q2IROacrQD6Q3C6ntK3nV7RsJtqiTDktM1WaEop
        9tW1bbexSlbSOQTgDI8CYgSraSydJu+ybYm6rMOrueWlpiceQAO6U84QQjI5wMcnqcnjpHQao3Hq7qlS
        pOlVDSSsySJOY9oStmmzSio7SnByn1j46zUdWqzdNs3U7pRW23rZZl2WWk02ZKXg0oqBUduRnPhFFjVV
        DilY+VzHOaMgbdrtBe79LKxw+F9HA1gc0H0r6je3oru+z7Va7a1/XDpFVqkqclKcl12UUonCFNuJB2Dn
        aFpXuKc4BT6nPjeOmWslkzNSvOx7+nJ5kPuzq5AKWkpQVFZAaUVNuBIJ44JHQc4jiaHVtW6HqRUdSGdJ
        627M1FLiVSyqbMhtG/bnB25/Rjvf2ctbf8iFR/1Cb/8ATEukqKOSi/DVRkaWOdkID7hp2238itE8U7aj
        xYchDgMwJbYnmu90U1STqfbTkzNstsVWnLSzOtt52K3AlDifIKwrjwKT6RG2v1SqMnrHZsvKz8yy06iV
        3obdUlKv20ocgHB4jitOqhqxptXKrWKVpNWn0VUEKl3KZMpQ2N5UMYT4ZIj6bqpWuWplwNX4/YMxIuUV
        DQlpdTBZPuObxhDpC3DkknAxgfiqMYnr8JZTPY90wIvZp1DTe+3TfukVDHTVrpmuaIyDbUbkbe9WB1t/
        wUXL/oR/rJisVZ/e829/+wzP9mY7m5rx1/1JoztmjTJ+mtzwS3MvGSeZChuzgLeISlJwM9Tjxjz1N0qu
        G39G7XtKkUqcrE/LVBczO+wSy3sLWhZJwkE7RkJBPXA88R9xuR2LyS1VNG7I2LLctIuS8Gw66Jh7RQsZ
        DK4Zi++hB0y81sdXtQq7ammVk27bU47KTlaprHePsna4hpDLY2pV1SVKWORz7p6ZjgdVtOLr0to1Jqc7
        f89PzFRcLbrKC4hLKwkE4WXDvGeM7RHba42LcM9p/Y9wUylzLsxQZBpmdlw0outgtNkEo6gJUhQV4jcM
        9I4fWLVOrap0Gmu/kk/S5SlOkzL7jhUhb60gBCSUjwSo45JGTwAYh445macVZdnDY/CHpWtYZjpp1vfu
        t+Hh1ozBbKS7PtffTv5WVtLZUpdt0pS1FSlSLBJJyT+bEVwnHby121ZrVsyV3TNFpNGU93Qa3FCUNrDY
        VsSpO5aic5J4BI8hFkLZSpFt0pCgUqTIsAgjBB7tMV6rdq6maP6oVK87Ftp6uU2sKdUpplhb2EuLC1Nq
        Sj30lKxwrpgfER1XEDHup6bOHGG48QNve1tL21tff2c1T4Y5ollykB9jlva1799Lr49HpKrW5r7NWjOX
        DN1NqnNTLXeOrUEuEIHvbCpWOvmYtHFP6PUdWqRqVO6ltaUVtyanS6VSyqbMhtO8AHB254xEgfs5a2/5
        EKj/AKhN/wDpiu4exemw2nkhla8Xe4j0XH0dLclJxShlq5WvYW+qAdQNeasDHonptmQk356Yz3Us0p5e
        Bk7UjJx8hEC/s5a2/wCRCo/6hN/+mPVNa06zzss7KTGh1RU0+hTbifYJzlKhgj6sXjuJ6LKcoff/AGO+
        yrm4RPcXLbf7h91yFn2hdfaIn6/ddWu56QVKOBMm0Apbba1BRShIyNiEgAZGSc55Oc2DtS3LgtOw36Hc
        dzGuPy7DobmFMlCkt7OGySoleDnCjg4IGOIrdphVNZ9LX54UjTSuTcnPlJclpilTOApOdqkqSkYOCQfP
        jyET5pjfF4X2mq0+97DnbfDbaO5Lss80h9KtwWNzgHI93geB9IoeGZKSw8RrxUvDg4nNY7nX9u211ZYu
        2bXKW+E21gLXGw81HnY9/ufdH+elP6rseGmlSqL3aVuiSdn5lcshc/tZU6ooTh1OMJJwI0tGo+tGglZq
        sjbFoqr9NqDiC28iUcmQtKCrarDRCkK2kghXGfMYzrKHSNdbQul3VdNhvTU3VXJgvy3s6lqw4oKVlpB7
        xAz0z9nn1roKmSlp6OmfE8OgeS/0TYDMffupUsTZpZ5WvbaRoDdRvYfZTF2mZuaktL3X5OZdYcE9LgLa
        WUqxk+IiL7auvU7VWSpFgWDVX6VJUenMip1QuKQtbm3By4Pe65CUjlWFEnA4xqNeusOo9tLtqe0fq8m0
        t5t8utU6aUrKc8YKceMejTK59VtMKG9RqXozVppUxMGYemHqdNBazgADhPQAcD1PnGyvr2VuK+IC9sDm
        gOIa4E2JNtri5tc9FjTUxp6PIQ0yAki5FhfmtpTavqDojqhRrSuK7X67SKyWgoOOLWEpccLe5IWSUKSo
        Z4OCPU8bLthf3Ptf/PTf9VqODuOe1huu/KdfVc0urLq6Wtoy8kikzKWQlte9KTlJJyo5Pn06cRs77l9b
        9bnpZE1p1MUyXpDbrjbbjC5fcpQG73niN5O0ABI/84jyVZkoKrD4WyODnDwwWuJABBOp5aaBbWwBtTDU
        yFoIBzEEDWxt81ZOoH/iFM/xQv8AsTEPdkD+964f9Na/qGNRNajdoWp2+q0GtMJmXfdljJuTv0c+nKNu
        0kFR7tKiPHJHPAESdoPpvP6b2e5J1ot/SdQmDMzCEEKDQ2hKW9w4JABJI4yogZ6no6ef81xWnngY4Nia
        7MXNIFyLAa7lVUsf4OiljkcLvIsAb7G91ou1ZOzshp5TnpGbel3DWmUlTThQSO4fOMjw4ESJpu449p5a
        7zziluLoskpSlHJUSwjJJ8TEZdrhxsad0torSFqrTSgnPJAYeycemR+IjlLP1T1ztm3KbRhpbNVaTl5N
        lMlMNyEwSpjb7mVN5Sr3doGADxzzHyXE4sOx2YzBxBY3YE2PeyMpH1WHRhlrhx3NlInaZm5qS0vdfk5l
        1h0T0uAtpZSrGT4iIutq69TtVZKkWBYNVfpUlR6cyKnVC4pC1ubcHLg97rkJSOVYUScDjGo97aw6j20u
        2p7R+rybS3m3i41TppSspzxgpx4x6NMrn1W0wob1GpejNWmlTEwZh6Yep00FrOAAOE9ABwPU+cUtfXsr
        cV8QGRsDmgOIa4E2JNtri5tc9FYU1MaeiyENMgJIuRYX5raU2r6g6I6oUa0riu1+u0msloKDji1hKXHC
        3uSFklCkqGTg4I9TxInaTvCpWnp4G6RNrl5qrTaZPvUEhaGilSllJHQnaE/BRxEI3HP6wXXflNvqu6XV
        l1VLW0ZeSRSZlLIS2velJykk5UST59OBHR35Ia1a21CSpDtlrpMjIsiZSZhlyXbLq2UqUFLc5UQSUAJH
        BJz4kIsRkjo6qjpWyHMbRghxIB0OvIdNdEfStdPDPMWiwu83FrjbRauuaIyFA0ZkdTpCszwrQl5WouBK
        wGwh5SMBOAFJUkLSc7jyFemLB6OXPOXhpvRa7UXe8nHGlMzCyeVLbWpsqPqraFfOIHqEzrncdiymj7un
        E4y2wGZVydUwtKHGWlJLaS4r82MFKcqCjkJixGnNp/kNZNJtYvB5ciyQ6tP1VOLUVrI9NylY9MRa8Nwt
        bXF9JG5kXhtDrgi8l++5te5+6h4q8mmyzODn5yRYg+jbt3XSQhCO5XOJCEIIkIQgiQhCCJCEIIo41i0w
        rmpzdKp9PupVIkJZTxnmwFqEwFbNhKAoJUU7VY3dN3HjHhWdOq9bemUvZulFSMjPSzyF+0uu7FO5JLil
        KAPJz0x0AHhElRgxWS4RSyyyT2IfIMpcCbgabdNuSmMrpmMZHplabgW0v36qMKJbersvfFv1Kq3Eh2hS
        1MZZqcv7RkuzQlSla9u3nLuFZyPP0iT4QiRSUbaNrmtcTc31N+gsO2my1TzmcguAFhbQWWYQhEtaEiON
        YLf1Trv0T+xpXkU3uPaPbdz/AHXebu77v9E5xhf4xI2YRGrKVtbC6B7iAbatNjob6FboJjTyCRoBI6i4
        SGIRmJK0rGIYjMYgiYiIK5oTUbyvp2v3leExO0Jqa7+UpAK1JSn7BKjhAOOdoyRxkdYlpM3KuTDkq3MN
        KeaALjYWCpAPQkdRmPbEKsoKfEWtbOMwab2ubXHW2/kVIgqZaUkxmxI/tkAAAAAAHlCMwiao6xGYQgiQ
        jGYzBEjBGYzGMwRMeER3QKBqhK6oVOsViupetV5Lvskp3+4oJ27fc28YwrxiRMw4PSI1RStqXMc5xGU3
        0Nr9j1HZbopjEHAAG4tqPl3SMwhElaVgwxGYQRYhiMwgih689BZvUDUF247kux9VBSWlMUxBWSkJbQla
        Ukna2FKSVEpGTk9DzEusMtS7KJdhtLbbaQhCEjASkDAAHliPZCIVLh9PRySSxNs6Q3cdST7+XQDRSJqq
        WdrWPOjRYBIQj1S01KzjXfycy0+3kje2sKTkcEZETbi9lHtzXsMMecZhBFjBhGYQRIQhBEhCEESEIQRI
        QhBEhCEEVbe2N/ij/wBP/wB3jGmnZuqMjULavtV1S62kKlan7MJVQUUkJXs3buvOM4jPbG/xR/8A6H+7
        x9GkerWqdSrFuWnULOl2aIWm5Yzgp0ylQZQ0dqt5WUc7U84xzHmtQygfxJMK1pJvHltfQ5W725ea6yI1
        LcKjNOQPWve21z1U/VKr0qiy3tlYqcpIsZwXZl5LSM+W5RAjNNq1KrMqJ6j1KVn5YnAelnkuoJ8tySR4
        iKtXYmZ1m7QZs2pTzzVKkJh6SSlpYHdtsJUXSnORuUpJGcHqOoTGdPnJvSPtAP2LKzz7lLnJkSS21qHv
        pcQFsKV4bwVI5AHBV0ziLocUvNSP0v0DJ4ea+ubrbp9PcoBwdvhev+plz2tpbz6qzf5UWyKwbe/KKmfS
        o6yPtbftH1N/7nnd9X3unTnpH0U2r0qtMLmaPVJSeZbcU0tyWeS6lKx1SSkkAjIyOvMVD1mlqxPdoKq0
        y33VNz9QclJFkpXtz30m00pJPgClZB9CYslpHp27plaZt1+qpqDjkyuaW4lktpSVJSCkAk5A29eM56CJ
        mGY1U4hXTU3hWZG5zS6/Q2GnU636aLRV0EVLTslz+k4Agee6rprDR37s7Q81ay6guXbnpqQlUOFJcDO+
        XZG4IyM8knGRHuXU767N1+StKnK09U6K8lLxa3K7qYlyopUUoJPduAg9D4DOQY+y8/32Uv8AxrS/7BiN
        z2xGmQ/ajqQA6pE6lRzztBZx+sqjiain8NldicTi2WKY2IJ2LrWttz/oXQRSZzT0jxdj2ajvbdT05fNl
        sTbdPfu2jMzbwb7uXcnmkOq7wAowgq3e8FAjjnIj3Va7bVoEwiUrty0qmvup3oam5xtla05xkBRBIyCM
        +kVQ1opM1SZHTy92myh6coEkl1R/+oZbQrJHgcLSP5Pxjor8LWo/aKtumsnvZVDEitSR/wAztM0oH4oW
        fxHpHSv4oqGOfF4Yzh0bWjXUPBIPuVS3B4nBr85y2cT2y6KzFSq1Lo0qqerFSlZGWScF6ZeS0gHy3KIE
        YptXpNalhO0eqSk/Lk4Dsq8l1BP3kkiKt33VKdqbruu3LuuBqmW3RnnZUqdmUMoSGknfhSyEha3Btz1A
        x12x7tJ5+RsXXyZtG2a4ioW/U1LYQ4zMB5tY7nvWjuT7qlJPuEjzUPONw4pLqwRhg8IyeHmv6Wbrl/xv
        z/4WBwYCDMXenlzWtpbpfqu2sqxZGm671m6RqNQpxyYfnFIpkvOpXOblklTTjecpDfpk+4MgYiXZe7rU
        mpubkJa56S9NSKVrmmW51tTjCUHasrSFZSEkgHPQnmK0aa/vpar/ABrWP6Xo52VtN2+dergtIVR6Ql6l
        WKkmbcaPKmkPLcKcdDktp68ZAPhFXRY26ggtSwgl8zm2zHU6WNztcnyUuow8VMl5pNGxg3ty1VwKRcFB
        r7Tj9BrUhUm2lbVrlJlDyUnyJQTgx5Vau0Sgy4m67WJKnMKVtDs3MIZQT5ZUQMxVbRRibsjtAv2dLTin
        ZdTs9TnVdO9Q0ha0KI8DlsfDJ5jxvdioay6/uWg5PrYk5SZckGyBuDDTCVF1QHTcpSV/MgdBFg3iqV1E
        JRF+sZPDDb6ZtOfTUe1RjgzBUFhf6AbmvbkrQuXlaLU5LU526aQibnQgyzCp5oOPBZwgoTuyrceBjr4R
        tzFMZK1DY/aDo9oifcnGqbVpNLLi+vdr2OgY6D905xxnPnFyn3mZVhyZmXUttNJK3FrOEpSBkkk9ABFr
        geLS4mJvHYGGN2Ui99t9fNQ8QoWUhj8N2YOF/sqqzbdX131br9v1e5H6ZS6O3Nrlmt2G20MuBtJKc43E
        kKUeuAcHgY7nso3bXK7QKzRavNvzTVJeYMq68oqUlLoXlvJ8AW8geG7yxEEX5c7dRvevXhZDdQp9MqDy
        5dx4EgOF1B7wEjoHMLVtJz1+U9SNw2d2ftLqLVKFLKrwrzyHHH0uhpT6i2VFfRWAnATs8M8nOc8TglU0
        Yi+skfZsZeXvuTna42ZYb2B12+iv8QhcaVsDW6uyhrbD0SNTc9SFOXWKWvWlM6k66120na25JperFSUl
        9SC8Gw2txQARuT9kDqMRcWiVFdXo0hVXJYy6p2WamFMlW4tlaArbnAzjOM4EU2Xex0812uC6xTPbzK1i
        po7jvu63b3HE53bVdM56RecYPp3tpHVH+kXjNv6ul9tdumqr8CbK0zCP1w3Tbf5Lp7Arl6aRawsadVmt
        Oz0hNTbUk60XVLaV3wHdOoCvqHK05x6g54MWc/Ki2hWDb35Q0z6VH/yPtbftH1N/7nnd9X3unTnpFbNN
        bZvPVjVlGp9x0pcjT5WZbnN62lIQpTYAZaa3fWxtTlXoSeSBHPazS1Znu0FVaZb7y25+oOSkiyUr2576
        TaaUCfAFKyD6ExX4fis+DUBmjYXROlysDr3ykE6e7Tle6lVNHHX1Ijc4B4ZdxG19N/f7lbmTr1DqMm7U
        adWJGalZdSkOvszCFttqT9YKUDgEeIPSPXSrntuuuuMUS4KbUHGeXESs226pHxCScdR+MVM1Wo9X0xt2
        kaQy9cE4mdcVV5wtNloOLWQ2239Y5SktrOTjJIOBtjy1OsV/QS4rYrlq1eaU+40pwuuKHL7RT3gGAPza
        gtI2nPBIJMWMvFVVAXF9P6MWXxNdQXch1t8+iiMwaGQANk1ffLpuB1Vw1KSkFSiAAMknwjVyN2WtU55V
        Mpty0qbnE53S7E4246MdcpBJ8D4RAXaY1JqExS6DbFCfcYYrckipzIbUd7rS+G2zj9EkKJHiQPLnqbD0
        TomkUorUCeqc3N1Sm0x96aaJQlgHu9ygjA3DGFDJJznOBFocclnrnU1JGHRx2L3E2sDroOenyUT8uZHT
        iWZ1nOvlFt7dfapWqtz23QXGma5cFNpzj37kibm22VL+6FEZ+UbBl5mYaQ+w6h1txIUhaFBSVA9CCOoi
        m9jUe2dVJ257m1OveUp9QeTiSEzPtMbnlhRCsLOe7RhKQkcYOPCNnplfNyy+it9UWnTb5fpLTD8mpKju
        ZZec2v7T+iEpBUMdCokRW0/FxkeHSRWjcHltjd3oAmxHK4Gn1UqXBAxtmPu4FoOmnpdDzsrQG8LSTUvo
        dV0UgT4Vs9lM61327y2bt2fTEfZU6xSaJKmdrNUlJCXBwXpp9LSAfLcogRVjQCyNMrsckpqo16bbuinz
        6ZsSKlpS0622oKSEgp98HHOFZHPGOTjXecl6jrizSb5np2WtyVTLJQpgZKGFtpUtaRg8lzcCQCcJ6HAE
        Zf8Ac87cPFc+NvpODW2doL39Y8rW1+i+flEZqvw7XHQEnTp063Vkq1PUS6rNrKaXc8gJSYkJhhVRZmUL
        altzSgXCtKsDaDu6jpEfdn62qZYNt1xb9+0GrNl5MxMrp86l2XlEJSfeWvjaSAScgABHU4j6ZKzbSs/R
        S70WXU11Cm1KmT86iYW4lwkGVKdu5IAIG3pjIJOeYirQgf8Auo1T/ipz/s0xCprS3EaWSeMeJkebhxIF
        gTpyINvikVODSzMjccuZo1Gu49ysqm9rMXTFVpF3UVVPQ73CpsT7RZDmAdhXu27sEHGc4IjZyc9JVGVb
        nqfNszUs8NzbzLgWhY8wocEfCKUacabOX7aN01OYrb8szbcsuclpZI3IW+ptSiSD9UFLKUkjnp9nB6/R
        O/Kna+k1/Psvn/2QmXektxyGnpjc0CAfJSUHHTPxjXQcWyzPjNVDlY9rnAg39QEnT2FZVGCMja4RPu5p
        AIt/la3zVm1XRbSKmqiruGmJqCUlSpQzbYeAAySUZ3Yxz0jNGua27j778n7gptT9n2997HNtvd3uzt3b
        CcZ2nGeuD5RT6yNKJm7dP7k1LfrkxLzNH756XA5U6402HXFKX1yQcAjxzzEldjz+590f56U/quxtw3iW
        qrauGCWEMbKHEG9zYA2+Swq8Jhp4JJGSZiywItzNvurFQhCOyVCkIQgiQhCCJCEIIkIQgirb2xv8Uf8A
        p/8Au8TrYv8AeRb38VSn9imPnvLTmzdQPZPyto/t/sHeez/th1rZv27v3NSc52J656RvZCRlqZIy9Nkm
        +7lpRpDDKNxO1CQEpGTycADrFHSYZLBitRXOIyyBoG99AAb6W5dSrGarZJRxU4Bu0m/TUqqUxOsaVdpe
        arNxBbNPmJ6YmlPbCodzMoWQsAZJCVLwcZ+qoR5W6+nVHtLm4qIlTlPYnEThe2EAMy7aUIWR1G4oQBn7
        Qiyl12HaN7y7cvdVCl59LOe6UvKXG89QlaSFAH0PgI87Wsi1LKlXJO1qJL09t0guFAKluY6blqJUrHOM
        njJ84pRwxUioEZkb+HEni21zX6dLcr3VgcXi8PPlPi5Mna3XrdVsvL99lL/xrS/7BiLXCOUndK7DqF2p
        vmboXeVxLrT4mvanhhbaUpQdgXs4CUj6vhzHV4i6wfDJsPlqXykESSFwtfY9dBr71X11WyqZE1gPoNAN
        +o6Ko2pNWp9C7T6qzVZjuJOSqFOffc2qVsQmXZJOEgk8eAGY9Gptyr161MpdEs2Xfck2kCVYcW2QTlWX
        X1DqlAGOvOE+ZxFjbj0b02u2rvV24bZRNz0wUl172l5sr2pCU5CFgcJSB08I3Fs2RadmtLatigSdP7zh
        xbSPfWPJSzlRHoTFA/hiunlmikkaIJJC82uXHW4GwA+OvVWbcXp42Rva0mRjcova22/VR32iLPlpnSAt
        yLX97imHmAeoaSO6UM/dVk/diO+y9IzNyX1UbtnklYpNLl5JCic+/sS0g/Hu2VfjHc9pPVA2tQnLJkZJ
        *********************************/2frCmLGsFkVKXUzU6q4Z2aQsYU2CMNoPqEgEjwKlCMpaSK
        t4kYYNomgv6Bwvl9uo9gXxkz6fCneJu8kN8ja/s0Kr5elDt239dKtLaiS84KJUJ16cU6wSFhD5K0OJwP
        eSlSsEDng9SMRLGmFr9n5+85V+wqvOztXkGVzqErW8EJSMIJO9CQT+c6A54OYlq6LHtO9ZduWuihStQS
        0SW1OAhaM9dq0kKTn0PgI19raU6f2VOfSNs24zJzWxTYe71xxYSeoytRMbabhqWkrTI1sb4y/NdwOca3
        sOWnK61y4syenDCXNcG2sCMp8+ar9pr++lqv8a1j+l6Gm376Wq/xrWP6XosFTdK7DpF0u3rTqF3VZfde
        fcmfanlZW7nvDsKyjncfDAzxiFN0rsOj3S7etOoXc1p9159yZ9qeVlbue8OwrKOdx8MDPGI1wcM1cXhX
        c30ZjIdT6umm2+nl3WUmLQPz2B1jy8t9e+2qr7Zv77OY/jWqf2D8fDM1VnSvtKT1Zrza0SS6jMTDiwkq
        IZmkqIWMdQnvMkDn3SOsWPktK7Dp92qvmUoXd1tbrr5mvanj77iVJWdhXs5CleHGeI5HVeu6EvV9qhao
        MIXPSksl5p3uXyQhajhG9j3vDO08c+piPUYFLRUueSZjHiYyNJJy6gWBJA10vz+26LEWVE2VrHOaY8pA
        GvmPeoVeuil3n2lZC46K4tyRmqxIpZWpJSVpbS23uwcEZKCeRnBi0modNn6xYlwUulhRm5qmzDTKUjJW
        otkBI+PT5xV6xmadfXaDk6jaNI9ko0nMtzDTaG9iWZeXbSlKlDnBUUJ6/pL56xcHr0iTwpG6rgq3SG4k
        e7UaA3GpHbXRacZcIJIA0WytGh5diqgaUXRpuNO7j061DmTTTOzBm2Zoyy3FBexKU7doJCkKRkA4zuI8
        SI4ez1Iua4Ldsy4a8WaCzUFbO9JDbYcUCsD7O8pSOeAVZ45MXCrei+mFxVRVZq9oyrs44vvHFtuONBxX
        iVJQoJUT4kg55jwrWiWltfVLKqNnyv7UZEuyJd12XSlsEkDDSkg8knJ55ivk4RxGRscbnRkR6D1gXtzX
        s+w5crX8+alMxulaXOAcC7XkbG1rhduhKG0BCEhKUjAAGAB5RVLTbntS1XH/AN1rH9L0Wok5NiQk2JGW
        Cw1LtpabC3FOK2pGBlSiVKOB1JJMczTNKrDo90u3rTqF3NafdefcmfanlZW7nvDsKyjncfDjPGI6rFsM
        mxCWmkjIHhvDje+wttp87KloquOmZK11znaQP5/pXWRVG8v32Uv/ABrS/wCwYi12I5Sd0rsOoXYm+Zyh
        d5W0utPia9qeGFtpSlB2BezgJT4c45j7juGTYpFEyEgFj2uN77C+1gddUw2rZRve6QHVpGnU2UGdrWiz
        srctBu5toql1SvsRVjKUuNuKcSD8Q4f5pjR68X9SdWqvatLszvZxxDSh3YaUlQmH1IHdcgZI2J5HHPBM
        WtrFFpVwU52k1unsTsm+MOMvICknyPoQeQeo8I0Fs6T6eWdPmqW7a8tKznvYeUtbq0Z67S4pW3jj3ccZ
        EU+I8NVVVPMIJGiKYtL73zDL/jy131U+lxaGGKPxGkvjvlttr1UAdpm0Z+3nbSrTSS5LydLZpBdAJSl1
        glSc5+0FKI+6Ylek6qWjrDbE/aNFm3mq3VaNNNuyi2VJ7lSm9h98jYRlfGD06gRJNWpFMrtPdpVZkGJ2
        TfTtcZeQFJUPgfHxB6gxzls6Tae2dVDWrbttqSnSlSO9Dzq8JPUALUQPkIltwSqpa+SWlc3wZbB4N7iw
        t6Nudr79VoOIQzUzWTA52XykWtrrr/CqbpnT9K/bqlSNWzUKdMNLCWHUFYShQJC21pSkkKzjBxjg5xxm
        erBGiFlWtcN6WpOzMzR0Lbkai4+FuA8pASG1pBIPfAdOecZjuLn0k06vGcVUbhtWVmJpQG95C1srXjpu
        U2pJV88x7ZTS3T+SoE1bEta8oimTq0uPsHcQ4tOMKKid2RgYOYhYZw5V4Y6zWxOyh2VxBz3INr8ra687
        KRV4rBVi5Lxe1xcZbaXtz/lVS1ARYsrfdIntEqi868+6hxLLSHUpYmd42BveArB+zzjp44E1a3VfRWur
        qFtXpUVSdepDAMs+2w4XUqWgLQlKkghQOU5SrzyMdY7y2dIdOLPnxVLetaXl5tOdjy3HHlozx7pcUrbx
        xxjxjwuLRvTW66qut161mJidcIU46l51orIAAKghQCuAOoj5Dw9XQQTNaIs0pF22d4YAB253uQe1kkxS
        mkkjJL7MBsdMxJtvytb5qDNDHqsvRvUxqYU4ac3TX/Zsk7Q6ZZ7vQPl3WY9GhH+CjVP+KnP+zTEWXatO
        3Ze3XbSlaSxL0h+XclXJVkFtJbWkhYynByQTk5zznOY1NC0rsO2aTVaHQ6F7NJVposTzXtTy++QUqSRu
        UslPC1DKSDzGcPDVTA6ms8ERse03ve7g61tNhe3kNl8kxaKQS3aRnc0jyFt+5soG7O/+DjUn/QP/AAH4
        5vSeizVf0r1Qp0m2XHvZqdMJSnqruXHnSB5nCDx4xZ+3dL7GtSm1KkW/RPZZSsN91Ot+0vL71O1ScZWs
        lPCldCOseVq2BY+mzFQmbapQprUyhK5tRmHXQUtBRBPeKVjAUrp5xpg4XqGspmTPbljZI11if3hw0uBt
        fW9lnJjERdK6MG7nMI2/bbfXt3VaNONVrctzRy7LIq63EVCdRMiRSltSg937AbxkDCdpGTkjIPHSO27H
        n/wF0f56U/quxpr0u/s5qt+r1m06I2u5KnKvMNNKlX0BhbySlTm1X5lJSFEgpyc4xHW9kqhTkhaFVrcy
        yptupziUsFQxvQ0kgqHpuUoZ80mKvBmyDF6WDxWSCNjrFmtm2Nsx662ty05qZXlhoZpMjmF5Gjutxt20
        U7QhCPTlyCQhCCJCEIIkIQgiQhCCJCEIIkIQgiQhCCJCEIItVVLWt6tVKn1erUiWm5ullapNx5O7uSrG
        SAeM+6nBIyCOMRtBGYRi2NjCXNABO/flr7FkXOcACdAkIQjJYpCEIIsGOQunSTTy9agarcttNTc2UpSX
        g+60pQAwAS2pOePOOwhGmenhqmeHOwOHQgEfFbI5ZITmjcQexstJbFl2tZkouSteiS1PbcILhbSStwjp
        uWSVKxz1J6xuozCM4omQsEcbQANgBYe5Yve6Q5nm57pCEIzWKQhCCJCEIIkIQgiQhCCJCEIIkIQgiQhC
        CJHqmZdmcl3ZSZaS4y8hTbiFDIUkjBB9CDHthAgEWKA21Cj1vQHSFqcE8my5cuBW7aqYfU3/ANWV7fli
        O+l5eXlGG5WVYbZZaSENttpCUoSBgAAcAAeEeyERqeipqS/4eNrb72AF/ctstRLNbxHE26klIQhElakh
        CEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQ
        hBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEI
        QRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCE
        ESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhBEhCEESEIQRIQhB
        EhCEESEIQRIQhBEhCEESEIQRf//Z
</value>
  </data>
  <data name="picLogo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.00015, 25.24999</value>
  </data>
  <data name="picLogo.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>237.77, 100.416672</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>168</value>
  </data>
  <data name="lbl_date.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_date.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999451, 0</value>
  </data>
  <data name="lbl_date.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>276.3117, 26.1249981</value>
  </data>
  <data name="lbl_date.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel14.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="xrLabel14.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>286.311157, 1.33336389</value>
  </data>
  <data name="xrLabel14.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>144.660065, 24.7916546</value>
  </data>
  <data name="xrLabel14.Text" xml:space="preserve">
    <value>تاريخ الطباعة</value>
  </data>
  <data name="xrLabel14.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>34</value>
  </data>
  <data name="BottomMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="EmployeePic.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        wwAADsMBx2+oZAAAB/BJREFUWEd1V21sHMUZPkJa/HG3u7efd7s3s+fUISUS0JKABGpEQWqpqiqgijQS
        BTWyY5vEcT7sOI4TO2c7VdpItKpaJPoDRHHzw5FRVUpVUVUqakNRkUBNSdLQoiIVaEwT8oHTBkuxZ/q8
        s7PnO/vySo/ubue9eZ55Z3bmmUwaYsvnmOiMj4lOPiM64j+KLn6fbmoUN5mmucUyzbOWYb6fN83xUqnU
        rNuWBXK/bBrG62bOmDGzxqTd3FzSTUmIJ1f5ID8uO2NZRUf8ptwaf0Gn1IVtmpss0/rItixJyJvWp/gc
        RdNnk4zFsA3jHoh8GyIlPiWESDOXm8pms55KEE+EjuzgPwOZVOgCuhOIHv5nYK1K1GFZ1kZ09m/byksn
        T7ATWPlr+L0vjuMmpN0MrHBN84t2Pv8W5WqhSoQFEUbWeN4wDDsjtsZHRVc8L3tA+iSwDdi+CNEb/0r0
        tRtEbtv2bXnL+kdKnHbs2rb0HEd6tj0LvIzvU57tTLm2c5raEpGUm1cikmoY86jEdzOiJz6tSHuBHUAf
        sHMRYmd8SfSy9STAsazH0NF80mlelTOXzapOfceVvlsD+g2QMAhRImtFkwgI+GtGbOevK2Ii3AXsBvYs
        QuyOP5T98ecTAc5Gx7bnMDrpojMib21uUQICz5MFzydch4A5kM8FrlcVo/5TI4L+gwGcyIg+/m2xK/5Y
        EfYDA1zKvQnEAF8Avie7132GBBSzWRcd/SYdbVJWSxEXfZD7/lkI2eI7/kYCvv8UmFdCqtWoTsf5vGFs
        zshKZoXoj7eJATYrB0G8DxgC+RAXYh9/RgyuyRF5Go7jrAkc9zU1YpAW/UCGgcI/oyB4QKepQG4O5M9A
        oFgi4oqbz3chZYVKlJsyN4vBeACEV+V+kA+DfD9/Ad/zKmFJ+L5/R8Hz3kjICzL0C+9FhcLXdXNdcNPM
        B77/Qo2ITyBgJ5roTVkMsafULIbZr+VBCDjAL4jh6A7d1DBKntce+YU9KP0BViioRXqjiCAYFbtAVQtc
        9xft7e236KbFUFMxwqfkKASMsBkxEq3WTTcMKjHnjatUG5Hrrka1ZmitQMAkHt2UtNSEOBgyMcrfkIcg
        4BCbFZW4YUnTQMkfBU6UwvAtFob9egNqGDQ9mKpZmrKC6/8J+0mkm5IQw22BGGXTsoIFqCHG2BkxHt+r
        U+oCC25zFBTPl4qh5GEkWRjN8VJpZN265G2pjTAM70XuGVorSgCq4DveVKFQ0FvxcMED+aQiHtMYTyAq
        7KSo8LtUog7M+SPFoHCuVCiCGAKiSMYlJsuM/a+Nsf77M5mVOjXDi8W7WLF4slQsyqhGQLIYnefDXM7J
        iEP8B8DCUvKqiDH+iqzEFnUI1WsLfvAuSiqpU4wcAkqyDAFtjMtVjF9eFSdThymxUJ1XKIcqRf+h1zUV
        4Dku7ajfz2D0Z6qjT4knNJQAdgmVUKsco38Mo5i/oQAe4zM+TLmMsfVxGF2qFVCsF0Bb9NuoAHutTkBK
        riHG+QdioryGOg2C4BvoYI7mM6qbgkUR5RIfpNxyGK7hYekDyiGxtWsgFYBt/Q8kYDPwn0YCQC5QgaN4
        RdW8wnTYBS94KRVAI0uroEX8pS0Mb6VcxEq0HUWOoNxlAmz7HN6Gbybvf4VvlRV2ZZmACfZsOv9p0AaE
        qXi1Og36TYCIv7Mi26DTVNA6QN6z6fzTeUGbkee6F7EIH9dpehMa431inH2SzDtGPsGOiSOho1PqAovx
        NnR4IhURFcP3sNq/opvrAq+hg9xjGL3Qo7+Mg6wbTfWbEUbahHK/rASMYyueKN+pmxoGzoKv4Qz4L4kA
        fqIfN4zI8+6sbsWO+yIs1jLrlhxIY/w4rQUImUkX3o0in/fu873gCiA9L/ihftwwQjpBXXcmmXv353iU
        nIK1Adt1C7bil9QuWOHnxUjpdt3UMPZvKH718IPh1SMPRrJrffhj/bhh4PS8HafgeTqOXceZxqP6HVNi
        PsRB1oND6LI6C0ZhRA6x55YuwDTEYLR6YYD9Xg4krun6jvhdsa2MTXB5wJJbWHDPgXxBu6KPYWK+o5uT
        wNn/BI7gi3IEq5+gTkSIGGFPy4qX1WkqxEA5FoP8t8o5kYsiKwc/Cff8N9Fdf3bQaenb7tN451Py1JKR
        G/qWSoIJeVTsZzNyGB0e0IAnUL5gmF+HsB/JIfYlAkzLAyD/ZZWcPCT5STK1ZOW3xm+KzlK76hiGA+7n
        MIix5dZZsdQPfmhlsw9nxF5+IrVh5IYUSAyB3NEQKjGE43kfsJdfhUcUVXJy0GTfydLTnQIXGtHB9xK7
        67q3gvhf6ahT/0jk1btBLvcqSspPKxOa+sFGoDbKqR05kZOVp7sEXWb0jQo3LHUWoPx3g/RSOuql5LDk
        uJzkTmXELv4UrPdC1RHXuGL6DrN6VvTz4zCux5H3ouiLz1VHvoycz4oOtpEEYPHlcYn5XUpcvZrlUvLs
        Aq5nT9Hr58GWTza8E+yJT4k9bffQTikr969Ue0Uf1sw2/pEqez35pyAfSi08hWMYd2OuTy3eCVXZ1X0i
        25qdXLwf0uV0RzxdeysC0Ttid/3enobo5o/jSnehnpxX5Ka1y3Y4LLQNZjb7DhGj5Io819I63dra6uuU
        JMR2vF698bTYHl/Dbemk6C0/pJuWhTo7Oss9IH5fdMQXQX5EdhdbdPOyMFqMh0B8Mteavdba0jLd1NQU
        Jy2ZzP8Bc1mzj2z0+P8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="EmployeePic.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.99945, 0</value>
  </data>
  <data name="EmployeePic.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>155.478333, 100.416672</value>
  </data>
  <data name="xrLabel22.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>LightGray</value>
  </data>
  <data name="xrLabel22.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt, style=Bold</value>
  </data>
  <data name="xrLabel22.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>286.3104, 34.6666641</value>
  </data>
  <data name="xrLabel22.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>466.689484, 24.7916679</value>
  </data>
  <data name="xrLabel22.Text" xml:space="preserve">
    <value>بيانات الموظف بالتفصيل</value>
  </data>
  <data name="xrLabel22.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="PageHeader.HeightF" type="System.Single, mscorlib">
    <value>100.416672</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>29, 35, 168, 34</value>
  </data>
  <data name="$this.PageHeight" type="System.Int32, mscorlib">
    <value>1169</value>
  </data>
  <data name="$this.PageWidth" type="System.Int32, mscorlib">
    <value>827</value>
  </data>
  <data name="$this.PaperKind" type="System.Drawing.Printing.PaperKind, System.Drawing">
    <value>A4</value>
  </data>
</root>