using System;
using System.Linq;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;

using DevExpress.XtraGrid;
using DevExpress.XtraPrinting;
using DevExpress.XtraCharts;

namespace Reports
{
    public partial class rpt_Template2 : DevExpress.XtraReports.UI.XtraReport
    {
        string reportName, dateFilter, otherFilters;
        XtraReport rpt;
        bool DataOnly; 
        public rpt_Template2()
        {
            InitializeComponent();
        }

        public rpt_Template2(bool IsLandScape)
        {
            InitializeComponent();
            this.Landscape = IsLandScape;
        }

        public rpt_Template2(string _reportName, string _dateFilter, string _otherFilters, XtraReport _rpt, bool DataOnly=false)
        {
            InitializeComponent();
            this.DataOnly = DataOnly;

            this.reportName = _reportName;
            this.dateFilter = _dateFilter;
            this.otherFilters = _otherFilters;            

            this.rpt = _rpt;

            if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");

            getReportHeader();
            lblReportName.Text = this.Name = reportName;
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            HideExport(this);
        }

        public rpt_Template2(string _reportName, string _dateFilter, string _otherFilters, XtraReport _rpt, string _FooterText, bool DataOnly = false)
        {
            InitializeComponent();
            this.DataOnly = DataOnly;

            this.reportName = _reportName;
            this.dateFilter = _dateFilter;
            this.otherFilters = _otherFilters;

            this.rpt = _rpt;

            if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");

            getReportHeader();
            lblReportName.Text = this.Name = reportName;
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            lbl_FooterText.Text = _FooterText;

            HideExport(this);
        }                

        public rpt_Template2(string _reportName, string _dateFilter, string _otherFilters, string _FooterText, GridControl grid,
            bool _LandScape, bool DataOnly = false)
        {
            InitializeComponent();
            this.DataOnly = DataOnly;

            this.reportName = this.Name = _reportName;
            this.dateFilter = _dateFilter;
            this.otherFilters = _otherFilters;

            if (_LandScape)
            {
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_TemplateLandScape.repx"))
                    this.LoadLayout(Shared.ReportsPath + "rpt_TemplateLandScape.repx");
                else
                {
                    if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                        this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");

                    //lblCompName.WidthF += 300;
                    //lblReportName.WidthF += 300;
                    //lblDateFilter.WidthF += 300;
                    //lblFilter.WidthF += 300;
                }
            }
            else
            {
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                    this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");
            }

            //this.Landscape = _LandScape;

            getReportHeader();
            lblReportName.Text = this.Name = reportName;
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            lbl_FooterText.Text = _FooterText;

            var container = new WinControlContainer();
            Detail.Controls.Add(container);
            container.WinControl = grid;
            HideExport(this);

        }


        public rpt_Template2(string _reportName, string _dateFilter, string _otherFilters, string _FooterText, ChartControl chart,
            bool _LandScape, bool check, bool DataOnly = false)
        {
            InitializeComponent();
            this.DataOnly = DataOnly;

            this.reportName = this.Name = _reportName;
            this.dateFilter = _dateFilter;
            this.otherFilters = _otherFilters;

            if (_LandScape)
            {
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_TemplateLandScape.repx"))
                    this.LoadLayout(Shared.ReportsPath + "rpt_TemplateLandScape.repx");
                else
                {
                    if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                        this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");

                    lblCompName.WidthF += 300;
                    lblReportName.WidthF += 300;
                    lblDateFilter.WidthF += 300;
                    lblFilter.WidthF += 300;
                }
            }
            else
            {
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                    this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");
            }

            this.Landscape = _LandScape;

            getReportHeader();
            lblReportName.Text = this.Name = reportName;
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            lbl_FooterText.Text = _FooterText;

            var container = new WinControlContainer();
            Detail.Controls.Add(container);
            container.WinControl = chart;
            HideExport(this);

        }



        public rpt_Template2(string _reportName, string _dateFilter, string _otherFilters, string _FooterText, ChartControl chart,
             GridControl grid,bool _LandScape, bool DataOnly = false)
        {
            InitializeComponent();
            this.DataOnly = DataOnly;

            this.reportName = this.Name = _reportName;
            this.dateFilter = _dateFilter;
            this.otherFilters = _otherFilters;

            if (_LandScape)
            {
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_TemplateLandScape.repx"))
                    this.LoadLayout(Shared.ReportsPath + "rpt_TemplateLandScape.repx");
                else
                {
                    if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                        this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");

                    lblCompName.WidthF += 300;
                    lblReportName.WidthF += 300;
                    lblDateFilter.WidthF += 300;
                    lblFilter.WidthF += 300;
                }
            }
            else
            {
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                    this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");
            }

            this.Landscape = _LandScape;

            getReportHeader();
            lblReportName.Text = this.Name = reportName;
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            lbl_FooterText.Text = _FooterText;

           


            var container = new WinControlContainer();
            Detail.Controls.Add(container);
            // grid.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
          
            container.WinControl = grid;

            var container1 = new WinControlContainer();
            Detail1.Controls.Add(container1);
            container1.WinControl = chart;



            HideExport(this);

        }


        public rpt_Template2(string _reportName, string _dateFilter, string _otherFilters, string _FooterText, GridControl grid,
              bool _LandScape, System.Drawing.Printing.PaperKind paperKind, bool DataOnly = false)
        {
            InitializeComponent();
            this.DataOnly = DataOnly;

            this.reportName = _reportName;
            this.dateFilter = _dateFilter;
            this.otherFilters = _otherFilters;
            this.PaperKind = paperKind;            

            if (_LandScape)
            {
                lblCompName.WidthF += 650;
                lblReportName.WidthF += 650;
                lblDateFilter.WidthF += 650;
                lblFilter.WidthF += 650;
            }
            if (_LandScape)
            {
                lblCompName.WidthF += 350;
                lblReportName.WidthF += 350;
                lblDateFilter.WidthF += 350;
                lblFilter.WidthF += 350;
            }

            if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");


            this.Landscape = _LandScape;

            getReportHeader();
            lblReportName.Text = this.Name = reportName;
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            lbl_FooterText.Text = _FooterText;

            var container = new WinControlContainer();
            Detail.Controls.Add(container);
            container.WinControl = grid;
            HideExport(this);

        }

        public rpt_Template2(string _reportName, string _dateFilter, string _otherFilters, string _FooterText,
            DevExpress.XtraPivotGrid.PivotGridControl pivotGrid, bool _LandScape, bool DataOnly = false)
        {
            InitializeComponent();
            this.DataOnly = DataOnly;

            this.reportName = this.Name = _reportName;
            this.dateFilter = _dateFilter;
            this.otherFilters = _otherFilters;
            

            if (_LandScape)
            {
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_TemplateLandScape.repx"))
                    this.LoadLayout(Shared.ReportsPath + "rpt_TemplateLandScape.repx");
                else
                {
                    if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                        this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");

                    lblCompName.WidthF += 300;
                    lblReportName.WidthF += 300;
                    lblDateFilter.WidthF += 300;
                    lblFilter.WidthF += 300;
                }
            }
            else
            {
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                    this.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");
            }

            this.Landscape = _LandScape;

            getReportHeader();
            lblReportName.Text = this.Name = reportName;
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            lbl_FooterText.Text = _FooterText;

            var container = new WinControlContainer();
            Detail.Controls.Add(container);
            container.WinControl = pivotGrid;
            HideExport(this);

        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void HideExport(XtraReport rep)
        {
            if (Shared.user.ExportReport == true)
                return;

            ReportPrintTool pt = new ReportPrintTool(rep);            
            PrintingSystemBase ps = pt.PrintingSystem;
            ps.SetCommandVisibility(new PrintingSystemCommand[] { 
                PrintingSystemCommand.ExportFile, 
                PrintingSystemCommand.SendFile,
                },
                CommandVisibility.None);
        }

        private void xrSubreport1_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
            xrSubreport1.ReportSource = rpt;
        }

        private void TopMargin_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
            e.Cancel = DataOnly;
        }

        private void BottomMargin_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
            e.Cancel = DataOnly;
        }

    }
}
