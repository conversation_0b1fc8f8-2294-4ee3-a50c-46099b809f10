{"RootPath": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\ERP\\Pharmacy", "ProjectFileName": "ERP.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Customers.cs"}, {"SourceFile": "Customers.Designer.cs"}, {"SourceFile": "Form1.cs"}, {"SourceFile": "Form1.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_AddNotesList.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_AddNotesList.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_DebitNotesList.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_DebitNotesList.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_RecievedInvoices.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_RecievedInvoices.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_StoreCode.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_StoreCode.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_TaxableSubtypes.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_TaxableSubtypes.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_TaxableTypes.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_TaxableTypes.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_InvoiceList_Rejected.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_InvoiceList_Rejected.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_ItemCodes.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_ItemCodes.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_Currency.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_Currency.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_ActivityTypes.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_ActivityTypes.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_UOM.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_UOM.Designer.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_InvoiceList.cs"}, {"SourceFile": "Forms\\E-Invoice\\frm_E_InvoiceList.Designer.cs"}, {"SourceFile": "Forms\\frm_ReleaseNotes.cs"}, {"SourceFile": "Forms\\frm_ReleaseNotes.Designer.cs"}, {"SourceFile": "Forms\\frm_Excel_Import.cs"}, {"SourceFile": "Forms\\frm_Excel_Import.designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Customer_Items.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Customer_Items.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_ItemReplacment.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_ItemReplacment.Designer.cs"}, {"SourceFile": "Forms\\frm_LcList.cs"}, {"SourceFile": "Forms\\frm_LcList.Designer.cs"}, {"SourceFile": "Forms\\frm_LC.cs"}, {"SourceFile": "Forms\\frm_LC.Designer.cs"}, {"SourceFile": "Forms\\frm_InvoiceDimenstions.cs"}, {"SourceFile": "Forms\\frm_InvoiceDimenstions.Designer.cs"}, {"SourceFile": "Forms\\frm_CloseInventory.cs"}, {"SourceFile": "Forms\\frm_CloseInventory.Designer.cs"}, {"SourceFile": "Forms\\frm_ReEvaluate.cs"}, {"SourceFile": "Forms\\frm_ReEvaluate.Designer.cs"}, {"SourceFile": "Forms\\frm_SelectExpire.cs"}, {"SourceFile": "Forms\\frm_SelectExpire.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_CatPosting_Peroidic.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_CatPosting_Peroidic.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_CatPosting.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_CatPosting.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_ImportItems.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_ImportItems.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_UOM.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_UOM.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Cat.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Cat.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_Sl_Add_Add_Taxes.cs"}, {"SourceFile": "Forms\\SL\\frm_Sl_Add_Add_Taxes.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_Sl_Return_Add_Taxes .cs"}, {"SourceFile": "Forms\\SL\\frm_Sl_Return_Add_Taxes .Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Add_Taxes.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Add_Taxes.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_ImportCustomers.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_ImportCustomers.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Delivery.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Delivery.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_CustomerGroup.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_CustomerGroup.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_CustomerCategory.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_CustomerCategory.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_InvoiceArchive.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_InvoiceArchive.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_InvoiceArchiveList.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_InvoiceArchiveList.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Add.cs"}, {"SourceFile": "Forms\\SL\\.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_ReturnArchive.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_ReturnArchive.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_ReturnArchiveList.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_ReturnArchiveList.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_CustomerRegion.cs"}, {"SourceFile": "Forms\\SL\\frm_CustomerRegion.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_AddList .cs"}, {"SourceFile": "Forms\\SL\\frm_SL_AddList .Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_BarcodeMatchTable.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_BarcodeMatchTable.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_InsertAssets.cs"}, {"SourceFile": "Forms\\ST\\frm_InsertAssets.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_Cars.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_Cars.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_E_InvoiceInfo.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_E_InvoiceInfo.Designer.cs"}, {"SourceFile": "Forms\\uc_Currency.cs"}, {"SourceFile": "Forms\\uc_Currency.Designer.cs"}, {"SourceFile": "Forms\\frm_InvoiceDiscs.cs"}, {"SourceFile": "Forms\\frm_InvoiceDiscs.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_ChangeUserPass.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_ChangeUserPass.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_InvoiceBook.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_InvoiceBook.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_MatrixAddInv.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_MatrixAddInv.Designer.cs"}, {"SourceFile": "Forms\\ST\\frmActivation.cs"}, {"SourceFile": "Forms\\ST\\frmActivation.designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_InvBooksList.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_InvBooksList.Designer.cs"}, {"SourceFile": "Forms\\uc_CurrencyValue.cs"}, {"SourceFile": "Forms\\uc_CurrencyValue.Designer.cs"}, {"SourceFile": "Forms\\uc_InvPayments.cs"}, {"SourceFile": "Forms\\uc_InvPayments.Designer.cs"}, {"SourceFile": "Forms\\frm_WaitingScreen.cs"}, {"SourceFile": "Forms\\frm_WaitingScreen.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_ItemMatrixCreate.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_ItemMatrixCreate.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_MatrixList.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_MatrixList.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Matrix.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Matrix.Designer.cs"}, {"SourceFile": "Forms\\frmAbout.cs"}, {"SourceFile": "Forms\\frmAbout.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_ReturnList.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_ReturnList.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_InvoiceList.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_InvoiceList.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Return.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Return.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Invoice.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Invoice.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Customer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_Customer.Designer.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_CustomerList.cs"}, {"SourceFile": "Forms\\SL\\frm_SL_CustomerList.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_NewYear.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_NewYear.designer.cs"}, {"SourceFile": "Forms\\IC\\frm_AddUOM.cs"}, {"SourceFile": "Forms\\IC\\frm_AddUOM.Designer.cs"}, {"SourceFile": "Forms\\frmLogin.cs"}, {"SourceFile": "Forms\\frmLogin.designer.cs"}, {"SourceFile": "Forms\\uc_AddExpenses.cs"}, {"SourceFile": "Forms\\uc_AddExpenses.Designer.cs"}, {"SourceFile": "Forms\\uc_Charts.cs"}, {"SourceFile": "Forms\\uc_Charts.Designer.cs"}, {"SourceFile": "Forms\\uc_LinkAccount.cs"}, {"SourceFile": "Forms\\uc_LinkAccount.Designer.cs"}, {"SourceFile": "Helper.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Item.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Item.Designer.cs"}, {"SourceFile": "Forms\\frmSplash.cs"}, {"SourceFile": "Forms\\frmSplash.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_ItemsList.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_ItemsList.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Company.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Company.Designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_CompaniesList.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_CompaniesList.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_Barcode.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_Barcode.Designer.cs"}, {"SourceFile": "Program.cs"}, {"SourceFile": "Properties\\Settings2.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_ACC_Journal.cs"}, {"SourceFile": "Reports\\Documents\\rpt_ACC_Journal.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_ACC_Statment.cs"}, {"SourceFile": "Reports\\Documents\\rpt_ACC_Statment.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_CashNote.cs"}, {"SourceFile": "Reports\\Documents\\rpt_CashNote.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_CashTransfer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_CashTransfer.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_EmployeeDetails.cs"}, {"SourceFile": "Reports\\Documents\\rpt_EmployeeDetails.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_EmployeeStroy.cs"}, {"SourceFile": "Reports\\Documents\\rpt_EmployeeStroy.designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_HR_Penalty.cs"}, {"SourceFile": "Reports\\Documents\\rpt_HR_Penalty.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_HR_Reward.cs"}, {"SourceFile": "Reports\\Documents\\rpt_HR_Reward.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_BOM.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_BOM.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_Damaged.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_Damaged.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_InTrns.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_InTrns.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_Multiple_Weights_Sub.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_Multiple_Weights_Sub.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_OutTrns.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_OutTrns.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_StoreMove.cs"}, {"SourceFile": "Reports\\Documents\\rpt_IC_StoreMove.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_JO_JobOrder.cs"}, {"SourceFile": "Reports\\Documents\\rpt_JO_JobOrder.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_JO_JobOrderListCustomer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_JO_JobOrderListCustomer.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_JO_JobOrderListDept.cs"}, {"SourceFile": "Reports\\Documents\\rpt_JO_JobOrderListDept.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_Manf_QC.cs"}, {"SourceFile": "Reports\\Documents\\rpt_Manf_QC.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_manufacture.cs"}, {"SourceFile": "Reports\\Documents\\rpt_manufacture.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_manufacture_actualRows.cs"}, {"SourceFile": "Reports\\Documents\\rpt_manufacture_actualRows.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_multiple_weights.cs"}, {"SourceFile": "Reports\\Documents\\rpt_multiple_weights.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_multiple_weights_dup.cs"}, {"SourceFile": "Reports\\Documents\\rpt_multiple_weights_dup.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PayNotes.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PayNotes.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PriceLevel.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PriceLevel.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_Printed_Receipt.cs"}, {"SourceFile": "Reports\\Documents\\rpt_Printed_Receipt.designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Invoice.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Invoice.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Invoice_Store.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Invoice_Store.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Invoice_Store_Sub.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Invoice_Store_Sub.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_PriceLevel.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_PriceLevel.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_PurchaseOrder.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_PurchaseOrder.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Quote.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Quote.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Request.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_Request.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_ReturnInvoice.cs"}, {"SourceFile": "Reports\\Documents\\rpt_PR_ReturnInvoice.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_RecieveNotes.cs"}, {"SourceFile": "Reports\\Documents\\rpt_RecieveNotes.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_RevExp.cs"}, {"SourceFile": "Reports\\Documents\\rpt_RevExp.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Invoice.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Invoice.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Invoice_Store.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Invoice_Store.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Invoice_Store_Sub.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Invoice_Store_Sub.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Quote.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Quote.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Add.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_Add.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_ReturnInvoice.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_ReturnInvoice.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_SalesOrder.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_SalesOrder.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_SalesOrder_Manufacturing.cs"}, {"SourceFile": "Reports\\Documents\\rpt_SL_SalesOrder_Manufacturing.Designer.cs"}, {"SourceFile": "Reports\\Documents\\rpt_Weight.cs"}, {"SourceFile": "Reports\\Documents\\rpt_Weight.Designer.cs"}, {"SourceFile": "Reports\\ExtensionsHelper.cs"}, {"SourceFile": "Reports\\frm_ReportViewer.cs"}, {"SourceFile": "Reports\\frm_ReportViewer.designer.cs"}, {"SourceFile": "Reports\\Properties\\Resources1.Designer.cs"}, {"SourceFile": "Reports\\ReportsRTL.cs"}, {"SourceFile": "Reports\\ReportsUtils.cs"}, {"SourceFile": "Reports\\rpt_barcodeLabels.cs"}, {"SourceFile": "Reports\\rpt_barcodeLabels.designer.cs"}, {"SourceFile": "Reports\\rpt_Sub_Tree.cs"}, {"SourceFile": "Reports\\rpt_Sub_Tree.designer.cs"}, {"SourceFile": "Reports\\rpt_Template.cs"}, {"SourceFile": "Reports\\rpt_Template.designer.cs"}, {"SourceFile": "Reports\\rpt_Template2.cs"}, {"SourceFile": "Reports\\rpt_Template2.designer.cs"}, {"SourceFile": "Reports\\SL\\frm_CapitalInDeferredInvoices.cs"}, {"SourceFile": "Reports\\SL\\frm_CapitalInDeferredInvoices.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_E_Invoice.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_E_Invoice.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SubTaxTotal.cs"}, {"SourceFile": "Reports\\SL\\frm_SubTaxTotal.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SubTaxNet.cs"}, {"SourceFile": "Reports\\SL\\frm_SubTaxNet.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_CustomsCertifecationWarning.cs"}, {"SourceFile": "Reports\\SL\\frm_CustomsCertifecationWarning.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_IC_StoreSales.cs"}, {"SourceFile": "Reports\\SL\\frm_IC_StoreSales.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_MrAllSales.cs"}, {"SourceFile": "Reports\\SL\\frm_MrAllSales.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_averagesellingpriceoftheitems.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_averagesellingpriceoftheitems.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_Car_Weights.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_Car_Weights.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerGroupItemsNet.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerGroupItemsNet.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerInvoicesHeaders.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerInvoicesHeaders.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerItemsSales.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerItemsSales.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerItemsSalesReturns.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerItemsSalesReturns.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerItemsSales_OutTrns.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerItemsSales_OutTrns.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerTotal_Invoices.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_CustomerTotal_Invoices.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_DelegatesSales.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_DelegatesSales.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_DeliveryOfficialsSales.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_DeliveryOfficialsSales.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_InvoiceDetails.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_InvoiceDetails.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_InvoicesHeaders.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_InvoicesHeaders.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_Invoices_Due.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_Invoices_Due.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsNetSalesDetails.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsNetSalesDetails.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsReturn.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsReturn.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsSales.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsSales.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsSalesDetails.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsSalesDetails.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsSalesTotals.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ItemsSalesTotals.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_JobOrderInv.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_JobOrderInv.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_Sl_JobOrderStatus.cs"}, {"SourceFile": "Reports\\SL\\frm_Sl_JobOrderStatus.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_Profit_Loss.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_Profit_Loss.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ReturnHeaders.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_ReturnHeaders.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_SalesOrderItems.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_SalesOrderItems.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_SalesOrderItemsAndBalance.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_SalesOrderItemsAndBalance.Designer.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_Warranty.cs"}, {"SourceFile": "Reports\\SL\\frm_SL_Warranty.Designer.cs"}, {"SourceFile": "Reports\\SL\\rpt_SL_CustomerSale.cs"}, {"SourceFile": "Reports\\SL\\rpt_SL_CustomerSale.Designer.cs"}, {"SourceFile": "Reports\\SL\\rpt_SL_CustomerTrans.cs"}, {"SourceFile": "Reports\\SL\\rpt_SL_CustomerTrans.Designer.cs"}, {"SourceFile": "Reports\\SL\\rpt_SL_CustomerTrans_Sub.cs"}, {"SourceFile": "Reports\\SL\\rpt_SL_CustomerTrans_Sub.Designer.cs"}, {"SourceFile": "Reports\\SL\\rpt_SL_ItemTrade.cs"}, {"SourceFile": "Reports\\SL\\rpt_SL_ItemTrade.Designer.cs"}, {"SourceFile": "Reports\\uc_Currency.cs"}, {"SourceFile": "Reports\\uc_Currency.designer.cs"}, {"SourceFile": "Reports\\uc_CurrencyValue.cs"}, {"SourceFile": "Reports\\uc_CurrencyValue.designer.cs"}, {"SourceFile": "RTL.cs"}, {"SourceFile": "Translator.cs"}, {"SourceFile": "ErpUtils.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_StoresList.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_StoresList.Designer.cs"}, {"SourceFile": "Forms\\frmMain.cs"}, {"SourceFile": "Forms\\frmMain.designer.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Store.cs"}, {"SourceFile": "Forms\\IC\\frm_IC_Store.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_Store.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_Store.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_Print.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_Print.Designer.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_CompInfo.cs"}, {"SourceFile": "Forms\\ST\\frm_ST_CompInfo.Designer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\DalBL\\bin\\Debug\\DAL.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\DalBL\\bin\\Debug\\DAL.dll"}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.BonusSkins.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.Charts.v15.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.Data.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.Office.v15.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.PivotGrid.v15.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.Printing.v15.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.RichEdit.v15.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.Sparkline.v15.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.Utils.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.Utils.v15.1.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.Xpo.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraBars.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraCharts.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraCharts.v15.1.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraCharts.v15.1.Wizard.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraEditors.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraGrid.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraLayout.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraNavBar.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraPivotGrid.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraPrinting.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraReports.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraReports.v15.1.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraRichEdit.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraRichEdit.v15.1.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraTreeList.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\DevExpress 15.1\\Components\\Bin\\Framework\\DevExpress.XtraVerticalGrid.v15.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\ERP\\Pharmacy\\ExcelDataReader.DataSet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\ERP\\Pharmacy\\ExcelDataReader.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\Packages\\GlobalHR.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\McDRSigniture\\bin\\Debug\\McDRSigniture.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\McDRSigniture\\bin\\Debug\\McDRSigniture.dll"}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\Models_1\\bin\\Debug\\Models_1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\Models_1\\bin\\Debug\\Models_1.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Facades\\netstandard.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\packages\\Newtonsoft.Json.11.0.1\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\SignuatuerGenerator\\bin\\Debug\\SignuatuerGenerator.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\SignuatuerGenerator\\bin\\Debug\\SignuatuerGenerator.dll"}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\ERP\\Pharmacy\\StringParser.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.DirectoryServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Management.Instrumentation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Runtime.Remoting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ServiceModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\packages\\SqlTableDependency.7.5.0\\lib\\TableDependency.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\packages\\SqlTableDependency.7.5.0\\lib\\TableDependency.SqlClient.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\packages\\SqlTableDependency.7.5.0\\lib\\TableDependency.SqlClient.Where.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\ERP\\Pharmacy\\TextRuler.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\packages\\Tulpep.NotificationWindow.1.1.38\\lib\\net40\\Tulpep.NotificationWindow.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\UIAutomationClient.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\ERP\\Pharmacy\\WebResourceProvider.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\Zatca\\bin\\Debug\\Zatca.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\Zatca\\bin\\Debug\\Zatca.dll"}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\ERP\\Pharmacy\\bin\\Debug\\LinkIT ERP System.exe", "OutputItemRelativePath": "LinkIT ERP System.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}