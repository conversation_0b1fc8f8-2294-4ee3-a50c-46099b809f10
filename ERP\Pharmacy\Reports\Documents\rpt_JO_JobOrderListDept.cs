using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_JO_JobOrderListDept : DevExpress.XtraReports.UI.XtraReport
    {
        string Department,RegdateFrom,RegDateTo,DueDateFrom,DueDateTo;

        DataTable dt_inv_details;

        public rpt_JO_JobOrderListDept()
        {
            InitializeComponent();
        }
        public rpt_JO_JobOrderListDept(string _Department, string _RegdateFrom, string _RegDateTo, string _DueDateFrom, string _DueDateTo, DataTable dt)
        {
            InitializeComponent();

            Department = _Department;
            RegdateFrom = _RegdateFrom;
            RegDateTo = _RegDateTo;
            DueDateFrom = _DueDateFrom;
            DueDateTo = _DueDateTo;

            dt_inv_details = dt;
            this.DataSource = dt_inv_details;
            getReportHeader();
            //LoadData();            
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_Department.Text = Department;
            lbl_RegdateFrom.Text = RegdateFrom;
            lbl_RegdateTo.Text = RegDateTo;
            lbl_DuedateFrom.Text = DueDateFrom;
            lbl_DuedateTo.Text = DueDateTo;
            
            this.DataSource = dt_inv_details;

            cell_DeliverDate.DataBindings.Add("Text", this.DataSource, "DeliveryDate");
            cell_CustomerAr.DataBindings.Add("Text", this.DataSource, "CustomerAr");
            cell_CustomerEn.DataBindings.Add("Text", this.DataSource, "CustomerEn");            
            cell_Job.DataBindings.Add("Text", this.DataSource, "Job");
            cell_JOCode.DataBindings.Add("Text", this.DataSource, "JOCode");
            cell_Notes.DataBindings.Add("Text", this.DataSource, "Notes");
            cell_Priority.DataBindings.Add("Text", this.DataSource, "Priority");
            cell_RegDate.DataBindings.Add("Text", this.DataSource, "RegDate");
            cell_SalesEmpAr.DataBindings.Add("Text", this.DataSource, "SalesEmpAr");
            cell_SalesEmpEn.DataBindings.Add("Text", this.DataSource, "SalesEmpEn");
            cell_Status.DataBindings.Add("Text", this.DataSource, "Status");
            
        }
    }
}
