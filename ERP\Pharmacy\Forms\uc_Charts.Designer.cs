﻿namespace Pharmacy.Forms
{
    partial class uc_Charts
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(uc_Charts));
            DevExpress.XtraCharts.XYDiagram xyDiagram7 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series9 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel9 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.StackedAreaSeriesView stackedAreaSeriesView9 = new DevExpress.XtraCharts.StackedAreaSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel10 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.StackedAreaSeriesView stackedAreaSeriesView10 = new DevExpress.XtraCharts.StackedAreaSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram8 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series10 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel11 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.StackedAreaSeriesView stackedAreaSeriesView11 = new DevExpress.XtraCharts.StackedAreaSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel12 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.StackedAreaSeriesView stackedAreaSeriesView12 = new DevExpress.XtraCharts.StackedAreaSeriesView();
            DevExpress.XtraCharts.XYDiagram xyDiagram9 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series11 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel7 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.Series series12 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel8 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            DevExpress.XtraCharts.SideBySideBarSeriesLabel sideBySideBarSeriesLabel9 = new DevExpress.XtraCharts.SideBySideBarSeriesLabel();
            this.chTaxes = new DevExpress.XtraCharts.ChartControl();
            this.chSales = new DevExpress.XtraCharts.ChartControl();
            this.treeChilds = new System.Windows.Forms.TreeView();
            this.btnRefresh = new DevExpress.XtraEditors.SimpleButton();
            this.chTrade = new DevExpress.XtraCharts.ChartControl();
            ((System.ComponentModel.ISupportInitialize)(this.chTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedAreaSeriesView9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedAreaSeriesView10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chSales)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedAreaSeriesView11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(stackedAreaSeriesView12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chTrade)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).BeginInit();
            this.SuspendLayout();
            // 
            // chTaxes
            // 
            resources.ApplyResources(this.chTaxes, "chTaxes");
            xyDiagram7.AxisX.Label.Angle = ((int)(resources.GetObject("resource.Angle")));
            xyDiagram7.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram7.AxisX.WholeRange.AutoSideMargins = false;
            xyDiagram7.AxisX.WholeRange.SideMarginsValue = 0D;
            xyDiagram7.AxisY.VisibleInPanesSerializable = "-1";
            this.chTaxes.Diagram = xyDiagram7;
            this.chTaxes.Name = "chTaxes";
            pointSeriesLabel9.LineVisibility = DevExpress.Utils.DefaultBoolean.True;
            series9.Label = pointSeriesLabel9;
            series9.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            resources.ApplyResources(series9, "series9");
            series9.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series9.View = stackedAreaSeriesView9;
            this.chTaxes.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series9};
            this.chTaxes.SeriesSorting = DevExpress.XtraCharts.SortingMode.Ascending;
            pointSeriesLabel10.LineVisibility = DevExpress.Utils.DefaultBoolean.True;
            this.chTaxes.SeriesTemplate.Label = pointSeriesLabel10;
            this.chTaxes.SeriesTemplate.View = stackedAreaSeriesView10;
            this.chTaxes.TabStop = false;
            // 
            // chSales
            // 
            resources.ApplyResources(this.chSales, "chSales");
            this.chSales.AppearanceNameSerializable = "In A Fog";
            xyDiagram8.AxisX.Label.Angle = ((int)(resources.GetObject("resource.Angle1")));
            xyDiagram8.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram8.AxisX.WholeRange.AutoSideMargins = false;
            xyDiagram8.AxisX.WholeRange.SideMarginsValue = 0D;
            xyDiagram8.AxisY.VisibleInPanesSerializable = "-1";
            xyDiagram8.LabelsResolveOverlappingMinIndent = 2;
            this.chSales.Diagram = xyDiagram8;
            this.chSales.Name = "chSales";
            pointSeriesLabel11.LineVisibility = DevExpress.Utils.DefaultBoolean.True;
            series10.Label = pointSeriesLabel11;
            series10.LabelsVisibility = DevExpress.Utils.DefaultBoolean.False;
            resources.ApplyResources(series10, "series10");
            series10.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            series10.View = stackedAreaSeriesView11;
            this.chSales.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series10};
            this.chSales.SeriesSorting = DevExpress.XtraCharts.SortingMode.Ascending;
            this.chSales.SeriesTemplate.ArgumentScaleType = DevExpress.XtraCharts.ScaleType.DateTime;
            pointSeriesLabel12.LineVisibility = DevExpress.Utils.DefaultBoolean.True;
            this.chSales.SeriesTemplate.Label = pointSeriesLabel12;
            this.chSales.SeriesTemplate.View = stackedAreaSeriesView12;
            this.chSales.TabStop = false;
            // 
            // treeChilds
            // 
            resources.ApplyResources(this.treeChilds, "treeChilds");
            this.treeChilds.Name = "treeChilds";
            this.treeChilds.TabStop = false;
            // 
            // btnRefresh
            // 
            resources.ApplyResources(this.btnRefresh, "btnRefresh");
            this.btnRefresh.Image = global::Pharmacy.Properties.Resources.refresh;
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.TabStop = false;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // chTrade
            // 
            resources.ApplyResources(this.chTrade, "chTrade");
            this.chTrade.AppearanceNameSerializable = "Chameleon";
            xyDiagram9.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram9.AxisY.VisibleInPanesSerializable = "-1";
            this.chTrade.Diagram = xyDiagram9;
            this.chTrade.Name = "chTrade";
            sideBySideBarSeriesLabel7.LineVisibility = DevExpress.Utils.DefaultBoolean.True;
            series11.Label = sideBySideBarSeriesLabel7;
            resources.ApplyResources(series11, "series11");
            series11.SeriesPointsSorting = DevExpress.XtraCharts.SortingMode.Descending;
            sideBySideBarSeriesLabel8.LineVisibility = DevExpress.Utils.DefaultBoolean.True;
            series12.Label = sideBySideBarSeriesLabel8;
            resources.ApplyResources(series12, "series12");
            this.chTrade.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series11,
        series12};
            this.chTrade.SeriesSorting = DevExpress.XtraCharts.SortingMode.Ascending;
            sideBySideBarSeriesLabel9.LineVisibility = DevExpress.Utils.DefaultBoolean.True;
            this.chTrade.SeriesTemplate.Label = sideBySideBarSeriesLabel9;
            this.chTrade.TabStop = false;
            // 
            // uc_Charts
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.Controls.Add(this.chTrade);
            this.Controls.Add(this.btnRefresh);
            this.Controls.Add(this.chSales);
            this.Controls.Add(this.chTaxes);
            this.Controls.Add(this.treeChilds);
            this.Name = "uc_Charts";
            this.Load += new System.EventHandler(this.uc_Charts_Load);
            ((System.ComponentModel.ISupportInitialize)(xyDiagram7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedAreaSeriesView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedAreaSeriesView10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedAreaSeriesView11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(stackedAreaSeriesView12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chSales)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(sideBySideBarSeriesLabel9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chTrade)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion
        private DevExpress.XtraCharts.ChartControl chTaxes;
        private DevExpress.XtraCharts.ChartControl chSales;
        private System.Windows.Forms.TreeView treeChilds;
        private DevExpress.XtraEditors.SimpleButton btnRefresh;
        private DevExpress.XtraCharts.ChartControl chTrade;
    }
}
