﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;

namespace Pharmacy.Forms
{
    public partial class uc_InvPayments : DevExpress.XtraEditors.XtraUserControl
    {
        public static List<acc> lstAcc;
        public uc_InvPayments(DataTable dt_payments)
        {
            InitializeComponent();
            RTL.EnCulture(Shared.IsEnglish);

            if (lstAcc == null)
            {
                var lstdrawerAcc = HelperAcc.LoadAccountsTree(Shared.st_Store.DrawersAcc.Value, true);
                var lstbankAcc = HelperAcc.LoadAccountsTree(Shared.st_Store.BanksAcc.Value, true);

                lstAcc = (lstdrawerAcc.Union(lstbankAcc)).ToList();
            }

            rep_ExpenseAccount.DisplayMember = "AccName";
            rep_ExpenseAccount.ValueMember = "AccId";
            rep_ExpenseAccount.DataSource = lstAcc;            

            gridControl1.DataSource = dt_payments;
        }

        private void gridView1_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column == col_Amount)
                e.DisplayText = Convert.ToDouble(e.Value).ToString();
        }

        private void gridView1_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            if ((e.Row as DataRowView).Row["PayAccountId"] == DBNull.Value)
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore;
            else
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;            
        }

        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            if (gridView1.GetRowCellValue(e.RowHandle, col_PayAccountId)==DBNull.Value ||
                gridView1.GetRowCellValue(e.RowHandle, col_PayAccountId).ToString() == string.Empty)
            {
                e.Valid = false;
                gridView1.SetColumnError(col_PayAccountId, Shared.IsEnglish ? ResAccEn.ValtxtAcc : ResAccAr.ValtxtAcc);//"يجب اختيار الحساب";
            }
            if (gridView1.GetRowCellValue(e.RowHandle, col_PayAccountId) != DBNull.Value ||
                gridView1.GetRowCellValue(e.RowHandle, col_PayAccountId).ToString() != string.Empty)            
            {
                int accId = Convert.ToInt32(gridView1.GetRowCellValue(e.RowHandle, col_PayAccountId));
                if (HelperAcc.Is_AccountHasChilds(accId) == true)
                {
                    e.Valid = false;
                    gridView1.SetColumnError(col_PayAccountId,
                    Shared.IsEnglish == true ? ResAccEn.valtxtChildAcc : ResAccAr.valtxtChildAcc);//"يجب اختيار حساب فرعي"
                }
            }            
            if (gridView1.GetRowCellValue(e.RowHandle, col_Amount) == DBNull.Value ||
                gridView1.GetRowCellValue(e.RowHandle, col_Amount).ToString() == string.Empty||
                Convert.ToDecimal(gridView1.GetRowCellValue(e.RowHandle, col_Amount)) <= 0)
                {
                    e.Valid = false;
                    gridView1.SetColumnError(col_Amount, Shared.IsEnglish ? ResAccEn.ValtxtAmount : ResAccAr.ValtxtAmount);//"يجب تسجيل المبلغ";
                }
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(Shared.IsEnglish == true ? ResPrEn.MsgDelRow : ResPrAr.MsgDelRow,
                    Shared.IsEnglish == true ? ResPrEn.MsgTQues : ResPrAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;
                gridView1.DeleteRow(gridView1.FocusedRowHandle);
            }
            if (e.KeyCode == Keys.Enter)
            {
                try
                {
                    if (gridView1.FocusedColumn == col_PayAccountId)
                    {
                        gridView1.FocusedColumn = col_Amount;
                        if (gridView1.GetFocusedRowCellValue(col_PayAccountId) == null ||
                            string.IsNullOrEmpty(gridView1.GetFocusedRowCellValue(col_PayAccountId).ToString()))
                            gridView1.FocusedColumn = col_PayAccountId;
                        return;
                    }                    
                    if (gridView1.FocusedColumn == col_Amount)
                        gridView1_KeyDown(sender, new KeyEventArgs(Keys.Tab));

                    if (gridView1.FocusedRowHandle < 0)
                    {
                        gridView1.AddNewRow();
                        gridView1.FocusedColumn = col_PayAccountId;
                    }
                    else
                    {
                        gridView1.FocusedRowHandle = gridView1.FocusedRowHandle + 1;
                        gridView1.FocusedColumn = gridView1.FocusedColumn;
                    }

                    e.Handled = true;
                    return;
                }
                catch
                { }
            }
            if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
            {
                if (gridView1.FocusedColumn.VisibleIndex == 0)
                    gridView1.FocusedColumn = gridView1.VisibleColumns[gridView1.VisibleColumns.Count - 1];
                else
                    gridView1.FocusedColumn = gridView1.VisibleColumns[gridView1.FocusedColumn.VisibleIndex - 1];
                e.Handled = true;
                return;
            }
            if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
            {
                if (gridView1.FocusedColumn.VisibleIndex == gridView1.VisibleColumns.Count)
                    gridView1.FocusedColumn = gridView1.VisibleColumns[0];
                else
                    gridView1.FocusedColumn = gridView1.VisibleColumns[gridView1.FocusedColumn.VisibleIndex + 1];
                e.Handled = true;
                return;
            }
            try
            {

                if ((gridView1.GetFocusedRow() as DataRowView).IsNew == true && gridView1.GetFocusedRowCellValue(col_PayAccountId).ToString() == string.Empty)
                {
                    if (e.KeyCode == Keys.Up)
                        gridView1.DeleteRow(gridView1.FocusedRowHandle);
                }
            }
            catch
            { }
        }        
    }
}
