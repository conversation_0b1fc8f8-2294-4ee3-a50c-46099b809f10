﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraPrinting;
using Reports;
using DevExpress.XtraNavBar;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_IC_MatrixList: DevExpress.XtraEditors.XtraForm
    {
        public frm_IC_MatrixList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_IC_MatrixList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            ErpUtils.Tab_Enter_Process(grdMatrix);
            ErpUtils.ColumnChooser(grdMatrix);

            LoadPrivilege();
            Get_Metrices();
        }

        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_IC_Matrix)))
                Application.OpenForms["frm_IC_Matrix"].Close();

            new frm_IC_Matrix(0, FormAction.Add).Show();            
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_matrix();
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        
        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Get_Metrices();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdMatrix.MinimumSize = grdMatrix.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdMatrix, false).ShowPreview();
            grdMatrix.MinimumSize = new Size(0, 0);
        }
        

        private void grdMatrix_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_matrix();            
        }

        private void Get_Metrices()
        {
            int focusedIndex = (grdMatrix.FocusedView as GridView).FocusedRowHandle;
            ERPDataContext pharm = new ERPDataContext();
            var Matrixes = (from c in pharm.IC_Matrixes 
                             select new
                             {
                                 c.MatrixId,
                                 c.MatrixCode,
                                 c.MatrixName
                             }).ToList();

            grdMatrix.DataSource = Matrixes;
            (grdMatrix.FocusedView as GridView).FocusedRowHandle = focusedIndex;
        }

        private void Open_Selected_matrix()
        {
            var view = grdMatrix.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;
            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, colMatrixId));
            if (ErpUtils.IsFormOpen(typeof(frm_IC_Matrix)))
                Application.OpenForms["frm_IC_Matrix"].Close();

            new frm_IC_Matrix(inv_id, FormAction.Edit).Show();
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_IC_MatrixList).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;                
                if (!p.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            
        }        
    }
}