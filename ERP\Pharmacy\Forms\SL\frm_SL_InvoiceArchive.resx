﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="barBtnHelp.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barSubItemPrint.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="barbtnPrint.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="barbtnPrintF.Caption" xml:space="preserve">
    <value>Print with Foriegn Name</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="barBtnDelete.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="barBtnSave.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="batBtnList.Caption" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="batBtnList.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1193, 31</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>65</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 540</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1193, 0</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>64</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 31</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 509</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>62</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1193, 31</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 509</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>63</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>54</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1193, 540</value>
  </data>
  <data name="labelControl27.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>175, 445</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="labelControl27.TabIndex" type="System.Int32, mscorlib">
    <value>317</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl27.Name" xml:space="preserve">
    <value>labelControl27</value>
  </data>
  <data name="&gt;&gt;labelControl27.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl27.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl27.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl28.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>187, 445</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="labelControl28.TabIndex" type="System.Int32, mscorlib">
    <value>318</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>Cus Tax</value>
  </data>
  <data name="&gt;&gt;labelControl28.Name" xml:space="preserve">
    <value>labelControl28</value>
  </data>
  <data name="&gt;&gt;labelControl28.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl28.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl28.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txt_CusTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_CusTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_CusTaxV.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 442</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_CusTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_CusTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>316</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.Name" xml:space="preserve">
    <value>txt_CusTaxV</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl37.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl37.Location" type="System.Drawing.Point, System.Drawing">
    <value>186, 467</value>
  </data>
  <data name="labelControl37.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="labelControl37.TabIndex" type="System.Int32, mscorlib">
    <value>310</value>
  </data>
  <data name="labelControl37.Text" xml:space="preserve">
    <value>Retention</value>
  </data>
  <data name="&gt;&gt;labelControl37.Name" xml:space="preserve">
    <value>labelControl37</value>
  </data>
  <data name="&gt;&gt;labelControl37.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl37.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl37.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl38.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl38.Location" type="System.Drawing.Point, System.Drawing">
    <value>103, 465</value>
  </data>
  <data name="labelControl38.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl38.TabIndex" type="System.Int32, mscorlib">
    <value>308</value>
  </data>
  <data name="labelControl38.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl38.Name" xml:space="preserve">
    <value>labelControl38</value>
  </data>
  <data name="&gt;&gt;labelControl38.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl38.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl38.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelControl39.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl39.Location" type="System.Drawing.Point, System.Drawing">
    <value>103, 488</value>
  </data>
  <data name="labelControl39.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl39.TabIndex" type="System.Int32, mscorlib">
    <value>309</value>
  </data>
  <data name="labelControl39.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl39.Name" xml:space="preserve">
    <value>labelControl39</value>
  </data>
  <data name="&gt;&gt;labelControl39.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl39.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl39.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelControl42.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl42.Location" type="System.Drawing.Point, System.Drawing">
    <value>186, 490</value>
  </data>
  <data name="labelControl42.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 13</value>
  </data>
  <data name="labelControl42.TabIndex" type="System.Int32, mscorlib">
    <value>311</value>
  </data>
  <data name="labelControl42.Text" xml:space="preserve">
    <value>Advance Pay</value>
  </data>
  <data name="&gt;&gt;labelControl42.Name" xml:space="preserve">
    <value>labelControl42</value>
  </data>
  <data name="&gt;&gt;labelControl42.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl42.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl42.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="labelControl43.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl43.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 467</value>
  </data>
  <data name="labelControl43.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl43.TabIndex" type="System.Int32, mscorlib">
    <value>312</value>
  </data>
  <data name="labelControl43.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl43.Name" xml:space="preserve">
    <value>labelControl43</value>
  </data>
  <data name="&gt;&gt;labelControl43.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl43.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl43.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="labelControl44.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl44.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 490</value>
  </data>
  <data name="labelControl44.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl44.TabIndex" type="System.Int32, mscorlib">
    <value>313</value>
  </data>
  <data name="labelControl44.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl44.Name" xml:space="preserve">
    <value>labelControl44</value>
  </data>
  <data name="&gt;&gt;labelControl44.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl44.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl44.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelControl45.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl45.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 466</value>
  </data>
  <data name="labelControl45.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl45.TabIndex" type="System.Int32, mscorlib">
    <value>314</value>
  </data>
  <data name="labelControl45.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl45.Name" xml:space="preserve">
    <value>labelControl45</value>
  </data>
  <data name="&gt;&gt;labelControl45.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl45.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl45.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="labelControl46.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl46.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 489</value>
  </data>
  <data name="labelControl46.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl46.TabIndex" type="System.Int32, mscorlib">
    <value>315</value>
  </data>
  <data name="labelControl46.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl46.Name" xml:space="preserve">
    <value>labelControl46</value>
  </data>
  <data name="&gt;&gt;labelControl46.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl46.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl46.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="txt_retentionR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_retentionR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_retentionR.Location" type="System.Drawing.Point, System.Drawing">
    <value>132, 464</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txt_retentionR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_retentionR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_retentionR.TabIndex" type="System.Int32, mscorlib">
    <value>304</value>
  </data>
  <data name="&gt;&gt;txt_retentionR.Name" xml:space="preserve">
    <value>txt_retentionR</value>
  </data>
  <data name="&gt;&gt;txt_retentionR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_retentionR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_retentionR.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="txt_RetentionV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_RetentionV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_RetentionV.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 464</value>
  </data>
  <data name="txt_RetentionV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_RetentionV.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txt_RetentionV.TabIndex" type="System.Int32, mscorlib">
    <value>305</value>
  </data>
  <data name="&gt;&gt;txt_RetentionV.Name" xml:space="preserve">
    <value>txt_RetentionV</value>
  </data>
  <data name="&gt;&gt;txt_RetentionV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_RetentionV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_RetentionV.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="txt_AdvancePayR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_AdvancePayR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AdvancePayR.Location" type="System.Drawing.Point, System.Drawing">
    <value>132, 487</value>
  </data>
  <data name="txt_AdvancePayR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_AdvancePayR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_AdvancePayR.TabIndex" type="System.Int32, mscorlib">
    <value>306</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayR.Name" xml:space="preserve">
    <value>txt_AdvancePayR</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayR.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="txt_AdvancePayV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_AdvancePayV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AdvancePayV.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 487</value>
  </data>
  <data name="txt_AdvancePayV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_AdvancePayV.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txt_AdvancePayV.TabIndex" type="System.Int32, mscorlib">
    <value>307</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayV.Name" xml:space="preserve">
    <value>txt_AdvancePayV</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayV.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="chk_IsOutTrns.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_IsOutTrns.Location" type="System.Drawing.Point, System.Drawing">
    <value>326, 33</value>
  </data>
  <data name="chk_IsOutTrns.Properties.AppearanceReadOnly.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="chk_IsOutTrns.Properties.Caption" xml:space="preserve">
    <value>Outgoing from store</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="chk_IsOutTrns.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_IsOutTrns.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 19</value>
  </data>
  <data name="chk_IsOutTrns.TabIndex" type="System.Int32, mscorlib">
    <value>272</value>
  </data>
  <data name="&gt;&gt;chk_IsOutTrns.Name" xml:space="preserve">
    <value>chk_IsOutTrns</value>
  </data>
  <data name="&gt;&gt;chk_IsOutTrns.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsOutTrns.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;chk_IsOutTrns.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="labelControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 422</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>269</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="labelControl3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl3.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Underline</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>596, 36</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>263</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Pay Method</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="labelControl8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>121, 422</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl8.TabIndex" type="System.Int32, mscorlib">
    <value>270</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl8.Name" xml:space="preserve">
    <value>labelControl8</value>
  </data>
  <data name="&gt;&gt;labelControl8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl8.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="txt_AttnMr.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_AttnMr.Location" type="System.Drawing.Point, System.Drawing">
    <value>662, 32</value>
  </data>
  <data name="txt_AttnMr.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_AttnMr.Properties.Appearance.BackColor2" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_AttnMr.Properties.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Underline</value>
  </data>
  <data name="txt_AttnMr.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_AttnMr.Properties.AppearanceFocused.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_AttnMr.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 20</value>
  </data>
  <data name="txt_AttnMr.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txt_AttnMr.Name" xml:space="preserve">
    <value>txt_AttnMr</value>
  </data>
  <data name="&gt;&gt;txt_AttnMr.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AttnMr.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AttnMr.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="groupControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lkp_Drawers2.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 25</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkp_Drawers2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns" xml:space="preserve">
    <value>AccountName</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns1" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns8" xml:space="preserve">
    <value>كود الحساب</value>
  </data>
  <data name="lkp_Drawers2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers2.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="lkp_Drawers2.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Name" xml:space="preserve">
    <value>lkp_Drawers2</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_PayAcc1_Paid.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc1_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 3</value>
  </data>
  <data name="txt_PayAcc1_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_PayAcc1_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Name" xml:space="preserve">
    <value>txt_PayAcc1_Paid</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl30.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 29</value>
  </data>
  <data name="labelControl30.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl30.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl30.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="&gt;&gt;labelControl30.Name" xml:space="preserve">
    <value>labelControl30</value>
  </data>
  <data name="&gt;&gt;labelControl30.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl30.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl30.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl31.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 7</value>
  </data>
  <data name="labelControl31.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl31.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl31.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="&gt;&gt;labelControl31.Name" xml:space="preserve">
    <value>labelControl31</value>
  </data>
  <data name="&gt;&gt;labelControl31.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl31.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl31.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lkp_Drawers.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 4</value>
  </data>
  <data name="lkp_Drawers.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns" xml:space="preserve">
    <value>AccountName</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns1" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns8" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_Drawers.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="lkp_Drawers.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Name" xml:space="preserve">
    <value>lkp_Drawers</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 6</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="labelControl17.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>Pay Account 1</value>
  </data>
  <data name="&gt;&gt;labelControl17.Name" xml:space="preserve">
    <value>labelControl17</value>
  </data>
  <data name="&gt;&gt;labelControl17.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl17.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl17.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txt_PayAcc2_Paid.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc2_Paid.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 25</value>
  </data>
  <data name="txt_PayAcc2_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 20</value>
  </data>
  <data name="txt_PayAcc2_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Name" xml:space="preserve">
    <value>txt_PayAcc2_Paid</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelControl33.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 27</value>
  </data>
  <data name="labelControl33.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="labelControl33.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="labelControl33.Text" xml:space="preserve">
    <value>Pay Account 2</value>
  </data>
  <data name="&gt;&gt;labelControl33.Name" xml:space="preserve">
    <value>labelControl33</value>
  </data>
  <data name="&gt;&gt;labelControl33.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl33.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl33.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txt_paid.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 47</value>
  </data>
  <data name="txt_paid.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>247, 245, 241</value>
  </data>
  <data name="txt_paid.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 20</value>
  </data>
  <data name="txt_paid.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;txt_paid.Name" xml:space="preserve">
    <value>txt_paid</value>
  </data>
  <data name="&gt;&gt;txt_paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;txt_paid.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="lbl_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 50</value>
  </data>
  <data name="lbl_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="lbl_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>92</value>
  </data>
  <data name="lbl_Paid.Text" xml:space="preserve">
    <value>total Paid</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Name" xml:space="preserve">
    <value>lbl_Paid</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lbl_remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>330, 48</value>
  </data>
  <data name="lbl_remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="lbl_remains.TabIndex" type="System.Int32, mscorlib">
    <value>94</value>
  </data>
  <data name="lbl_remains.Text" xml:space="preserve">
    <value>Remains</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Name" xml:space="preserve">
    <value>lbl_remains</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;lbl_remains.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="txt_Remains.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 46</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>247, 245, 241</value>
  </data>
  <data name="txt_Remains.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_Remains.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_Remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="txt_Remains.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Name" xml:space="preserve">
    <value>txt_Remains</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;txt_Remains.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>252, 467</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>422, 70</value>
  </data>
  <data name="groupControl1.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="&gt;&gt;groupControl1.Name" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;groupControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupControl1.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="xtraTabControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="xtraTabControl1.HeaderAutoFill" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>680, 383</value>
  </data>
  <data name="txt_Balance_After.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Balance_After.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Balance_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>171, 55</value>
  </data>
  <data name="txt_Balance_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 13</value>
  </data>
  <data name="txt_Balance_After.TabIndex" type="System.Int32, mscorlib">
    <value>220</value>
  </data>
  <data name="txt_Balance_After.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.Name" xml:space="preserve">
    <value>txt_Balance_After</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_Balance_Before.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Balance_Before.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Balance_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>171, 32</value>
  </data>
  <data name="txt_Balance_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 13</value>
  </data>
  <data name="txt_Balance_Before.TabIndex" type="System.Int32, mscorlib">
    <value>219</value>
  </data>
  <data name="txt_Balance_Before.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.Name" xml:space="preserve">
    <value>txt_Balance_Before</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt, style=Bold</value>
  </data>
  <data name="lbl_Validate_MaxLimit.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>126, 74</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>352, 19</value>
  </data>
  <data name="lbl_Validate_MaxLimit.TabIndex" type="System.Int32, mscorlib">
    <value>175</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Text" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="&gt;&gt;lbl_Validate_MaxLimit.Name" xml:space="preserve">
    <value>lbl_Validate_MaxLimit</value>
  </data>
  <data name="&gt;&gt;lbl_Validate_MaxLimit.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_Validate_MaxLimit.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;lbl_Validate_MaxLimit.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txt_MaxCredit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_MaxCredit.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_MaxCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>171, 9</value>
  </data>
  <data name="txt_MaxCredit.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 13</value>
  </data>
  <data name="txt_MaxCredit.TabIndex" type="System.Int32, mscorlib">
    <value>218</value>
  </data>
  <data name="txt_MaxCredit.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.Name" xml:space="preserve">
    <value>txt_MaxCredit</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lbl_IsCredit_After.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lbl_IsCredit_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>327, 55</value>
  </data>
  <data name="lbl_IsCredit_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="lbl_IsCredit_After.TabIndex" type="System.Int32, mscorlib">
    <value>217</value>
  </data>
  <data name="lbl_IsCredit_After.Text" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.Name" xml:space="preserve">
    <value>lbl_IsCredit_After</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lbl_IsCredit_Before.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lbl_IsCredit_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>327, 32</value>
  </data>
  <data name="lbl_IsCredit_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="lbl_IsCredit_Before.TabIndex" type="System.Int32, mscorlib">
    <value>215</value>
  </data>
  <data name="lbl_IsCredit_Before.Text" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.Name" xml:space="preserve">
    <value>lbl_IsCredit_Before</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="lblBlncAftr.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 55</value>
  </data>
  <data name="lblBlncAftr.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 13</value>
  </data>
  <data name="lblBlncAftr.TabIndex" type="System.Int32, mscorlib">
    <value>216</value>
  </data>
  <data name="lblBlncAftr.Text" xml:space="preserve">
    <value>Balance After Invoice</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.Name" xml:space="preserve">
    <value>lblBlncAftr</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelControl10.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>370, 9</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>213</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>Max Credit</value>
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="labelControl24.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>370, 32</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 13</value>
  </data>
  <data name="labelControl24.TabIndex" type="System.Int32, mscorlib">
    <value>214</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>Past Balance</value>
  </data>
  <data name="&gt;&gt;labelControl24.Name" xml:space="preserve">
    <value>labelControl24</value>
  </data>
  <data name="&gt;&gt;labelControl24.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl24.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;labelControl24.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="page_AccInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>496, 106</value>
  </data>
  <data name="page_AccInfo.Text" xml:space="preserve">
    <value>Account Information</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.Name" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>502, 134</value>
  </data>
  <data name="xtraTabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>231</value>
  </data>
  <data name="textEdit3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit3.EditValue" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="textEdit3.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 49</value>
  </data>
  <data name="textEdit3.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit3.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 22</value>
  </data>
  <data name="textEdit3.TabIndex" type="System.Int32, mscorlib">
    <value>287</value>
  </data>
  <data name="&gt;&gt;textEdit3.Name" xml:space="preserve">
    <value>textEdit3</value>
  </data>
  <data name="&gt;&gt;textEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit3.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;textEdit3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkp_JOStatus.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_JOStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 70</value>
  </data>
  <data name="lkp_JOStatus.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns1" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOStatus.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns7" xml:space="preserve">
    <value>StatusId</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns8" xml:space="preserve">
    <value>StatusId</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOStatus.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_JOStatus.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 20</value>
  </data>
  <data name="lkp_JOStatus.TabIndex" type="System.Int32, mscorlib">
    <value>278</value>
  </data>
  <data name="&gt;&gt;lkp_JOStatus.Name" xml:space="preserve">
    <value>lkp_JOStatus</value>
  </data>
  <data name="&gt;&gt;lkp_JOStatus.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_JOStatus.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;lkp_JOStatus.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="textEdit1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit1.EditValue" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="textEdit1.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 49</value>
  </data>
  <data name="textEdit1.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit1.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 22</value>
  </data>
  <data name="textEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>286</value>
  </data>
  <data name="&gt;&gt;textEdit1.Name" xml:space="preserve">
    <value>textEdit1</value>
  </data>
  <data name="&gt;&gt;textEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit1.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;textEdit1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkp_JOPriority.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_JOPriority.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 70</value>
  </data>
  <data name="lkp_JOPriority.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns1" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOPriority.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns7" xml:space="preserve">
    <value>PriorityId</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns8" xml:space="preserve">
    <value>PriorityId</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOPriority.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_JOPriority.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOPriority.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 20</value>
  </data>
  <data name="lkp_JOPriority.TabIndex" type="System.Int32, mscorlib">
    <value>277</value>
  </data>
  <data name="&gt;&gt;lkp_JOPriority.Name" xml:space="preserve">
    <value>lkp_JOPriority</value>
  </data>
  <data name="&gt;&gt;lkp_JOPriority.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_JOPriority.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;lkp_JOPriority.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="textEdit5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit5.EditValue" xml:space="preserve">
    <value>Sales Employee</value>
  </data>
  <data name="textEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 49</value>
  </data>
  <data name="textEdit5.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit5.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>179, 22</value>
  </data>
  <data name="textEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>285</value>
  </data>
  <data name="&gt;&gt;textEdit5.Name" xml:space="preserve">
    <value>textEdit5</value>
  </data>
  <data name="&gt;&gt;textEdit5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit5.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;textEdit5.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lkp_JOSalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_JOSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 70</value>
  </data>
  <data name="lkp_JOSalesEmp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_JOSalesEmp.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="lkp_JOSalesEmp.Properties.Columns1" xml:space="preserve">
    <value>الموظف</value>
  </data>
  <data name="lkp_JOSalesEmp.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JOSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>179, 20</value>
  </data>
  <data name="lkp_JOSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>276</value>
  </data>
  <data name="&gt;&gt;lkp_JOSalesEmp.Name" xml:space="preserve">
    <value>lkp_JOSalesEmp</value>
  </data>
  <data name="&gt;&gt;lkp_JOSalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_JOSalesEmp.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;lkp_JOSalesEmp.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="textEdit8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit8.EditValue" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="textEdit8.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit8.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 49</value>
  </data>
  <data name="textEdit8.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit8.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit8.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 22</value>
  </data>
  <data name="textEdit8.TabIndex" type="System.Int32, mscorlib">
    <value>284</value>
  </data>
  <data name="&gt;&gt;textEdit8.Name" xml:space="preserve">
    <value>textEdit8</value>
  </data>
  <data name="&gt;&gt;textEdit8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit8.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;textEdit8.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lkp_JODept.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_JODept.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 70</value>
  </data>
  <data name="lkp_JODept.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_JODept.Properties.Columns" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="lkp_JODept.Properties.Columns1" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="lkp_JODept.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_JODept.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_JODept.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JODept.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_JODept.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_JODept.Properties.Columns7" xml:space="preserve">
    <value>DeptId</value>
  </data>
  <data name="lkp_JODept.Properties.Columns8" xml:space="preserve">
    <value>DeptId</value>
  </data>
  <data name="lkp_JODept.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_JODept.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_JODept.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JODept.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_JODept.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_JODept.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_JODept.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 20</value>
  </data>
  <data name="lkp_JODept.TabIndex" type="System.Int32, mscorlib">
    <value>279</value>
  </data>
  <data name="&gt;&gt;lkp_JODept.Name" xml:space="preserve">
    <value>lkp_JODept</value>
  </data>
  <data name="&gt;&gt;lkp_JODept.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_JODept.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;lkp_JODept.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="textEdit4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit4.EditValue" xml:space="preserve">
    <value>Job</value>
  </data>
  <data name="textEdit4.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit4.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 5</value>
  </data>
  <data name="textEdit4.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit4.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit4.Size" type="System.Drawing.Size, System.Drawing">
    <value>205, 22</value>
  </data>
  <data name="textEdit4.TabIndex" type="System.Int32, mscorlib">
    <value>279</value>
  </data>
  <data name="&gt;&gt;textEdit4.Name" xml:space="preserve">
    <value>textEdit4</value>
  </data>
  <data name="&gt;&gt;textEdit4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit4.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;textEdit4.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txt_JOJob.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_JOJob.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 26</value>
  </data>
  <data name="txt_JOJob.Size" type="System.Drawing.Size, System.Drawing">
    <value>205, 20</value>
  </data>
  <data name="txt_JOJob.TabIndex" type="System.Int32, mscorlib">
    <value>275</value>
  </data>
  <data name="&gt;&gt;txt_JOJob.Name" xml:space="preserve">
    <value>txt_JOJob</value>
  </data>
  <data name="&gt;&gt;txt_JOJob.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_JOJob.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;txt_JOJob.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="textEdit2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit2.EditValue" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="textEdit2.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 5</value>
  </data>
  <data name="textEdit2.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit2.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 22</value>
  </data>
  <data name="textEdit2.TabIndex" type="System.Int32, mscorlib">
    <value>278</value>
  </data>
  <data name="&gt;&gt;textEdit2.Name" xml:space="preserve">
    <value>textEdit2</value>
  </data>
  <data name="&gt;&gt;textEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit2.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;textEdit2.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="txt_JODeliveryDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_JODeliveryDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="txt_JODeliveryDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 26</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_JODeliveryDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 20</value>
  </data>
  <data name="txt_JODeliveryDate.TabIndex" type="System.Int32, mscorlib">
    <value>274</value>
  </data>
  <data name="&gt;&gt;txt_JODeliveryDate.Name" xml:space="preserve">
    <value>txt_JODeliveryDate</value>
  </data>
  <data name="&gt;&gt;txt_JODeliveryDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_JODeliveryDate.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;txt_JODeliveryDate.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="textEdit7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit7.EditValue" xml:space="preserve">
    <value>JO Code</value>
  </data>
  <data name="textEdit7.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Location" type="System.Drawing.Point, System.Drawing">
    <value>411, 5</value>
  </data>
  <data name="textEdit7.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit7.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit7.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 22</value>
  </data>
  <data name="textEdit7.TabIndex" type="System.Int32, mscorlib">
    <value>277</value>
  </data>
  <data name="&gt;&gt;textEdit7.Name" xml:space="preserve">
    <value>textEdit7</value>
  </data>
  <data name="&gt;&gt;textEdit7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit7.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;textEdit7.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="textEdit6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit6.EditValue" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="textEdit6.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 5</value>
  </data>
  <data name="textEdit6.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit6.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit6.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 22</value>
  </data>
  <data name="textEdit6.TabIndex" type="System.Int32, mscorlib">
    <value>276</value>
  </data>
  <data name="&gt;&gt;textEdit6.Name" xml:space="preserve">
    <value>textEdit6</value>
  </data>
  <data name="&gt;&gt;textEdit6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit6.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;textEdit6.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="txt_JORegDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_JORegDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="txt_JORegDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 26</value>
  </data>
  <data name="txt_JORegDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_JORegDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 20</value>
  </data>
  <data name="txt_JORegDate.TabIndex" type="System.Int32, mscorlib">
    <value>273</value>
  </data>
  <data name="&gt;&gt;txt_JORegDate.Name" xml:space="preserve">
    <value>txt_JORegDate</value>
  </data>
  <data name="&gt;&gt;txt_JORegDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_JORegDate.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;txt_JORegDate.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="txt_JOCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_JOCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>411, 26</value>
  </data>
  <data name="txt_JOCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 20</value>
  </data>
  <data name="txt_JOCode.TabIndex" type="System.Int32, mscorlib">
    <value>272</value>
  </data>
  <data name="&gt;&gt;txt_JOCode.Name" xml:space="preserve">
    <value>txt_JOCode</value>
  </data>
  <data name="&gt;&gt;txt_JOCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_JOCode.Parent" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;txt_JOCode.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="page_JobOrder.Size" type="System.Drawing.Size, System.Drawing">
    <value>496, 106</value>
  </data>
  <data name="page_JobOrder.Text" xml:space="preserve">
    <value>Job Order</value>
  </data>
  <data name="&gt;&gt;page_JobOrder.Name" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;page_JobOrder.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;page_JobOrder.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;page_JobOrder.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>665, 17</value>
  </metadata>
  <data name="mi_frm_IC_Item.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_frm_IC_Item.Text" xml:space="preserve">
    <value>Item Info</value>
  </data>
  <data name="mi_CustLastPrices.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>F12</value>
  </data>
  <data name="mi_CustLastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_CustLastPrices.Text" xml:space="preserve">
    <value>View Last Customer Prices</value>
  </data>
  <data name="mi_LastPrices.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>F11</value>
  </data>
  <data name="mi_LastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_LastPrices.Text" xml:space="preserve">
    <value>View Last Prices</value>
  </data>
  <data name="mi_PasteRows.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_PasteRows.Text" xml:space="preserve">
    <value>Paste Rows</value>
  </data>
  <data name="mi_ExportData.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_ExportData.Text" xml:space="preserve">
    <value>Export Data</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Text" xml:space="preserve">
    <value>Static Invoice Discount</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Text" xml:space="preserve">
    <value>Static Invoice Dimenstions</value>
  </data>
  <data name="mi_ImportExcel.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_ImportExcel.Text" xml:space="preserve">
    <value>Import from Excel Sheet</value>
  </data>
  <data name="contextMenuStrip1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>238, 180</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Name" xml:space="preserve">
    <value>contextMenuStrip1</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="grdLastPrices.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="grdLastPrices.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="grdLastPrices.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="colTotalPurchasePrice.Caption" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="colTotalPurchasePrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colTotalPurchasePrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colTotalPurchasePrice.Width" type="System.Int32, mscorlib">
    <value>69</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>Sell P</value>
  </data>
  <data name="gridColumn6.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn6.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn6.Width" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="colUOM.Caption" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="colUOM.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colUOM.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colUOM.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="colQty.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="colQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colQty.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="colQty.Width" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="colCustNameAr.Caption" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="colCustNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colCustNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="colCustNameAr.Width" type="System.Int32, mscorlib">
    <value>205</value>
  </data>
  <data name="colInvoiceDate.Caption" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="colInvoiceDate.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colInvoiceDate.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="colInvoiceDate.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="colInvoiceCode.Caption" xml:space="preserve">
    <value>Invoice Code</value>
  </data>
  <data name="colInvoiceCode.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colInvoiceCode.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="colInvoiceCode.Width" type="System.Int32, mscorlib">
    <value>92</value>
  </data>
  <data name="grdLastPrices.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grdLastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>496, 106</value>
  </data>
  <data name="grdLastPrices.TabIndex" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="&gt;&gt;grdLastPrices.Name" xml:space="preserve">
    <value>grdLastPrices</value>
  </data>
  <data name="&gt;&gt;grdLastPrices.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdLastPrices.Parent" xml:space="preserve">
    <value>Page_LastPrices</value>
  </data>
  <data name="&gt;&gt;grdLastPrices.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="Page_LastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>496, 106</value>
  </data>
  <data name="Page_LastPrices.Text" xml:space="preserve">
    <value>Last Prices</value>
  </data>
  <data name="&gt;&gt;Page_LastPrices.Name" xml:space="preserve">
    <value>Page_LastPrices</value>
  </data>
  <data name="&gt;&gt;Page_LastPrices.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Page_LastPrices.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;Page_LastPrices.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtScaleSerial.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtScaleSerial.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 8</value>
  </data>
  <data name="txtScaleSerial.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="txtScaleSerial.TabIndex" type="System.Int32, mscorlib">
    <value>249</value>
  </data>
  <data name="&gt;&gt;txtScaleSerial.Name" xml:space="preserve">
    <value>txtScaleSerial</value>
  </data>
  <data name="&gt;&gt;txtScaleSerial.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtScaleSerial.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;txtScaleSerial.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="labelControl26.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl26.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 11</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="labelControl26.TabIndex" type="System.Int32, mscorlib">
    <value>250</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="&gt;&gt;labelControl26.Name" xml:space="preserve">
    <value>labelControl26</value>
  </data>
  <data name="&gt;&gt;labelControl26.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl26.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;labelControl26.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtDestination.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtDestination.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 77</value>
  </data>
  <data name="txtDestination.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="txtDestination.TabIndex" type="System.Int32, mscorlib">
    <value>247</value>
  </data>
  <data name="&gt;&gt;txtDestination.Name" xml:space="preserve">
    <value>txtDestination</value>
  </data>
  <data name="&gt;&gt;txtDestination.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDestination.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;txtDestination.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lblDestination.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblDestination.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 80</value>
  </data>
  <data name="lblDestination.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 13</value>
  </data>
  <data name="lblDestination.TabIndex" type="System.Int32, mscorlib">
    <value>248</value>
  </data>
  <data name="lblDestination.Text" xml:space="preserve">
    <value>Destination</value>
  </data>
  <data name="&gt;&gt;lblDestination.Name" xml:space="preserve">
    <value>lblDestination</value>
  </data>
  <data name="&gt;&gt;lblDestination.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblDestination.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lblDestination.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtVehicleNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtVehicleNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 54</value>
  </data>
  <data name="txtVehicleNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="txtVehicleNumber.TabIndex" type="System.Int32, mscorlib">
    <value>245</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.Name" xml:space="preserve">
    <value>txtVehicleNumber</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lblVehicleNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblVehicleNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 57</value>
  </data>
  <data name="lblVehicleNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 13</value>
  </data>
  <data name="lblVehicleNumber.TabIndex" type="System.Int32, mscorlib">
    <value>246</value>
  </data>
  <data name="lblVehicleNumber.Text" xml:space="preserve">
    <value>Vehicle Number</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.Name" xml:space="preserve">
    <value>lblVehicleNumber</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtDriverName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtDriverName.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 31</value>
  </data>
  <data name="txtDriverName.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="txtDriverName.TabIndex" type="System.Int32, mscorlib">
    <value>243</value>
  </data>
  <data name="&gt;&gt;txtDriverName.Name" xml:space="preserve">
    <value>txtDriverName</value>
  </data>
  <data name="&gt;&gt;txtDriverName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDriverName.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;txtDriverName.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lblDriverName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblDriverName.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 34</value>
  </data>
  <data name="lblDriverName.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 13</value>
  </data>
  <data name="lblDriverName.TabIndex" type="System.Int32, mscorlib">
    <value>244</value>
  </data>
  <data name="lblDriverName.Text" xml:space="preserve">
    <value>Driver Name</value>
  </data>
  <data name="&gt;&gt;lblDriverName.Name" xml:space="preserve">
    <value>lblDriverName</value>
  </data>
  <data name="&gt;&gt;lblDriverName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblDriverName.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lblDriverName.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="tabExtraData.Size" type="System.Drawing.Size, System.Drawing">
    <value>496, 106</value>
  </data>
  <data name="tabExtraData.Text" xml:space="preserve">
    <value>Extra Data</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Name" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;tabExtraData.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Name" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="labelControl41.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl41.Location" type="System.Drawing.Point, System.Drawing">
    <value>866, 36</value>
  </data>
  <data name="labelControl41.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="labelControl41.TabIndex" type="System.Int32, mscorlib">
    <value>242</value>
  </data>
  <data name="labelControl41.Text" xml:space="preserve">
    <value>Attn Mr.</value>
  </data>
  <data name="&gt;&gt;labelControl41.Name" xml:space="preserve">
    <value>labelControl41</value>
  </data>
  <data name="&gt;&gt;labelControl41.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl41.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl41.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="cmbPayMethod.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cmbPayMethod.EditValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>485, 33</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Bold</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Bold, Underline</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Down</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons1" xml:space="preserve">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons3" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons4" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons6" type="DevExpress.XtraEditors.ImageLocation, DevExpress.XtraEditors.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons8" xml:space="preserve">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons9" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons10" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons11" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbPayMethod.Properties.Items" xml:space="preserve">
    <value>On Credit</value>
  </data>
  <data name="cmbPayMethod.Properties.Items1" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbPayMethod.Properties.Items3" xml:space="preserve">
    <value>Cash</value>
  </data>
  <data name="cmbPayMethod.Properties.Items4" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbPayMethod.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbPayMethod.Properties.Items6" xml:space="preserve">
    <value>Cash / On Credit</value>
  </data>
  <data name="cmbPayMethod.Properties.Items7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbPayMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>105, 18</value>
  </data>
  <data name="cmbPayMethod.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Name" xml:space="preserve">
    <value>cmbPayMethod</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="btnAddCustomer.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddCustomer.Location" type="System.Drawing.Point, System.Drawing">
    <value>915, 33</value>
  </data>
  <data name="btnAddCustomer.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="btnAddCustomer.TabIndex" type="System.Int32, mscorlib">
    <value>149</value>
  </data>
  <data name="btnAddCustomer.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="btnAddCustomer.ToolTip" xml:space="preserve">
    <value>Add Customer</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Name" xml:space="preserve">
    <value>btnAddCustomer</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="labelControl16.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 376</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 13</value>
  </data>
  <data name="labelControl16.TabIndex" type="System.Int32, mscorlib">
    <value>191</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>Deduct Tax</value>
  </data>
  <data name="&gt;&gt;labelControl16.Name" xml:space="preserve">
    <value>labelControl16</value>
  </data>
  <data name="&gt;&gt;labelControl16.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl16.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl16.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="labelControl15.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 374</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl15.TabIndex" type="System.Int32, mscorlib">
    <value>190</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl15.Name" xml:space="preserve">
    <value>labelControl15</value>
  </data>
  <data name="&gt;&gt;labelControl15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl15.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl15.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="labelControl23.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 397</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl23.TabIndex" type="System.Int32, mscorlib">
    <value>190</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl23.Name" xml:space="preserve">
    <value>labelControl23</value>
  </data>
  <data name="&gt;&gt;labelControl23.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl23.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl23.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="labelControl25.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 399</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="labelControl25.TabIndex" type="System.Int32, mscorlib">
    <value>191</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>Add Tax</value>
  </data>
  <data name="&gt;&gt;labelControl25.Name" xml:space="preserve">
    <value>labelControl25</value>
  </data>
  <data name="&gt;&gt;labelControl25.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl25.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl25.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 354</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>191</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>S. Tax</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="labelControl20.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 354</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl20.TabIndex" type="System.Int32, mscorlib">
    <value>190</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl20.Name" xml:space="preserve">
    <value>labelControl20</value>
  </data>
  <data name="&gt;&gt;labelControl20.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl20.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl20.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="labelControl7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 332</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="labelControl7.TabIndex" type="System.Int32, mscorlib">
    <value>57</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>Disc</value>
  </data>
  <data name="&gt;&gt;labelControl7.Name" xml:space="preserve">
    <value>labelControl7</value>
  </data>
  <data name="&gt;&gt;labelControl7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl7.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="panelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="grdPrInvoice.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="grdPrInvoice.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="gridColumn29.Caption" xml:space="preserve">
    <value>LargeUOMFactor</value>
  </data>
  <data name="gridColumn29.Width" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="gridColumn28.Caption" xml:space="preserve">
    <value>MediumUOMFactor</value>
  </data>
  <data name="gridColumn28.Width" type="System.Int32, mscorlib">
    <value>102</value>
  </data>
  <data name="col_TotalSellPrice.Caption" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="col_TotalSellPrice.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_TotalSellPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_TotalSellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_TotalSellPrice.Width" type="System.Int32, mscorlib">
    <value>70</value>
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>Disc V</value>
  </data>
  <data name="repSpin.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn2.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>Disc R</value>
  </data>
  <data name="repDiscountRatio.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repDiscountRatio.Mask.EditMask" xml:space="preserve">
    <value>p2</value>
  </data>
  <data name="repDiscountRatio.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="repDiscountRatio.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="col_SellPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_SellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="col_SellPrice.Width" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="colPurchasePrice.Caption" xml:space="preserve">
    <value>Purchase Price</value>
  </data>
  <data name="colPurchasePrice.Width" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="gridColumn7.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>Unit Of Measure</value>
  </data>
  <data name="repUOM.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repUOM.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn9.Caption" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="gridColumn16.Caption" xml:space="preserve">
    <value>Factor</value>
  </data>
  <data name="gridColumn16.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn16.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn17.Caption" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="gridColumn17.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn17.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn3.Caption" xml:space="preserve">
    <value>gridColumn3</value>
  </data>
  <data name="gridColumn8.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="gridColumn8.Width" type="System.Int32, mscorlib">
    <value>83</value>
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="repItems.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repItems.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="gridColumn12.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn12.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn12.Width" type="System.Int32, mscorlib">
    <value>70</value>
  </data>
  <data name="gridColumn13.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="gridColumn13.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn13.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn13.Width" type="System.Int32, mscorlib">
    <value>350</value>
  </data>
  <data name="gridColumn14.Caption" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="gridColumn14.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn14.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="gridColumn14.Width" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridColumn15.Caption" xml:space="preserve">
    <value>itemId</value>
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="gridColumn5.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn5.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn5.Width" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="gridColumn4.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn4.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn4.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="gridColumn25.Caption" xml:space="preserve">
    <value>Sell Price</value>
  </data>
  <data name="gridColumn25.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn25.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn25.Width" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="col_CompanyNameAr.Caption" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="col_CategoryNameAr.Caption" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="gridColumn10.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn10.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="gridColumn10.Width" type="System.Int32, mscorlib">
    <value>211</value>
  </data>
  <data name="grdcol_branch.Caption" xml:space="preserve">
    <value>Branch</value>
  </data>
  <data name="lkp_storee.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_storee.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="grdcol_branch.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grdcol_branch.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="gridColumn11.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn11.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="gridColumn11.Width" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="gridColumn31.Caption" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="gridColumn31.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn31.VisibleIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="gridColumn31.Width" type="System.Int32, mscorlib">
    <value>135</value>
  </data>
  <data name="gridColumn41.Caption" xml:space="preserve">
    <value>gridColumn41</value>
  </data>
  <data name="col_Expire.Caption" xml:space="preserve">
    <value>Expire Date</value>
  </data>
  <data name="rep_expireDate.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_expireDate.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn18.Caption" xml:space="preserve">
    <value>Expire Date</value>
  </data>
  <data name="gridColumn18.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn18.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn23.Caption" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="gridColumn23.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn23.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn24.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="gridColumn24.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn24.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Expire.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_Batch.Caption" xml:space="preserve">
    <value>Batch </value>
  </data>
  <data name="rep_Batch.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_Batch.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_Batch.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn33.Caption" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="gridColumn33.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn33.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn34.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="gridColumn34.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn34.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Batch.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_Length.Caption" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="col_Length.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_Width.Caption" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="col_Width.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_Height.Caption" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="col_Height.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_TotalQty.Caption" xml:space="preserve">
    <value>Total Qty</value>
  </data>
  <data name="col_TotalQty.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_PiecesCount.Caption" xml:space="preserve">
    <value>Pieces Count</value>
  </data>
  <data name="col_PiecesCount.Width" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="col_ItemDescription.Caption" xml:space="preserve">
    <value>Item Description</value>
  </data>
  <data name="col_ItemDescription.Width" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="col_ItemDescriptionEn.Caption" xml:space="preserve">
    <value>Item Description En</value>
  </data>
  <data name="col_SalesTax.Caption" xml:space="preserve">
    <value>Sales Tax</value>
  </data>
  <data name="col_SalesTax.Width" type="System.Int32, mscorlib">
    <value>94</value>
  </data>
  <data name="col_DiscountRatio2.Caption" xml:space="preserve">
    <value>Disc R2</value>
  </data>
  <data name="col_DiscountRatio2.Width" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="col_DiscountRatio3.Caption" xml:space="preserve">
    <value>Disc R3</value>
  </data>
  <data name="col_Serial.Caption" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="repManufactureDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repManufactureDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CusTax.Caption" xml:space="preserve">
    <value>Custom V</value>
  </data>
  <data name="lkp_store.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_store.NullText" xml:space="preserve">
    <value>اختر الفرع</value>
  </data>
  <data name="grdPrInvoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>1165, 133</value>
  </data>
  <data name="grdPrInvoice.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Name" xml:space="preserve">
    <value>grdPrInvoice</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Parent" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="panelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 164</value>
  </data>
  <data name="panelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1169, 137</value>
  </data>
  <data name="panelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;panelControl2.Name" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;panelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;panelControl2.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="labelControl14.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 376</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl14.TabIndex" type="System.Int32, mscorlib">
    <value>192</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl14.Name" xml:space="preserve">
    <value>labelControl14</value>
  </data>
  <data name="&gt;&gt;labelControl14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl14.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl14.ZOrder" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="labelControl22.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 399</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl22.TabIndex" type="System.Int32, mscorlib">
    <value>192</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl22.Name" xml:space="preserve">
    <value>labelControl22</value>
  </data>
  <data name="&gt;&gt;labelControl22.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl22.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl22.ZOrder" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="labelControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 332</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="labelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 375</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>193</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="labelControl21.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 398</value>
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl21.TabIndex" type="System.Int32, mscorlib">
    <value>193</value>
  </data>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl21.Name" xml:space="preserve">
    <value>labelControl21</value>
  </data>
  <data name="&gt;&gt;labelControl21.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl21.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl21.ZOrder" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="labelControl11.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>175, 332</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl11.TabIndex" type="System.Int32, mscorlib">
    <value>126</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl11.Name" xml:space="preserve">
    <value>labelControl11</value>
  </data>
  <data name="&gt;&gt;labelControl11.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl11.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl11.ZOrder" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="labelControl12.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>175, 422</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl12.TabIndex" type="System.Int32, mscorlib">
    <value>126</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl12.Name" xml:space="preserve">
    <value>labelControl12</value>
  </data>
  <data name="&gt;&gt;labelControl12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl12.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl12.ZOrder" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="labelControl19.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 331</value>
  </data>
  <data name="labelControl19.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl19.TabIndex" type="System.Int32, mscorlib">
    <value>139</value>
  </data>
  <data name="labelControl19.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl19.Name" xml:space="preserve">
    <value>labelControl19</value>
  </data>
  <data name="&gt;&gt;labelControl19.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl19.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl19.ZOrder" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="txt_Total.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_Total.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 307</value>
  </data>
  <data name="txt_Total.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_Total.TabIndex" type="System.Int32, mscorlib">
    <value>134</value>
  </data>
  <data name="&gt;&gt;txt_Total.Name" xml:space="preserve">
    <value>txt_Total</value>
  </data>
  <data name="&gt;&gt;txt_Total.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Total.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_Total.ZOrder" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="labelControl18.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 310</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl18.TabIndex" type="System.Int32, mscorlib">
    <value>133</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="&gt;&gt;labelControl18.Name" xml:space="preserve">
    <value>labelControl18</value>
  </data>
  <data name="&gt;&gt;labelControl18.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl18.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl18.ZOrder" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="txtNet.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtNet.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 508</value>
  </data>
  <data name="txtNet.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 20</value>
  </data>
  <data name="txtNet.TabIndex" type="System.Int32, mscorlib">
    <value>116</value>
  </data>
  <data name="&gt;&gt;txtNet.Name" xml:space="preserve">
    <value>txtNet</value>
  </data>
  <data name="&gt;&gt;txtNet.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtNet.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtNet.ZOrder" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="panelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="flowLayoutPanel1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTserial.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTserial.EditValue" xml:space="preserve">
    <value>Book</value>
  </data>
  <data name="txtTserial.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTserial.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTserial.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTserial.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTserial.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 22</value>
  </data>
  <data name="txtTserial.TabIndex" type="System.Int32, mscorlib">
    <value>255</value>
  </data>
  <data name="&gt;&gt;txtTserial.Name" xml:space="preserve">
    <value>txtTserial</value>
  </data>
  <data name="&gt;&gt;txtTserial.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTserial.Parent" xml:space="preserve">
    <value>pnlBook</value>
  </data>
  <data name="&gt;&gt;txtTserial.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkp_InvoiceBook.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_InvoiceBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns" xml:space="preserve">
    <value>InvoiceBookName</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns1" xml:space="preserve">
    <value>Book Name</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns7" xml:space="preserve">
    <value>IsTaxable</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns8" xml:space="preserve">
    <value>Taxable</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns14" xml:space="preserve">
    <value>PrintFileName</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns15" xml:space="preserve">
    <value>PrintFileName</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 20</value>
  </data>
  <data name="lkp_InvoiceBook.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.Name" xml:space="preserve">
    <value>lkp_InvoiceBook</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.Parent" xml:space="preserve">
    <value>pnlBook</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>758, 1</value>
  </data>
  <data name="pnlBook.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 43</value>
  </data>
  <data name="pnlBook.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;pnlBook.Name" xml:space="preserve">
    <value>pnlBook</value>
  </data>
  <data name="&gt;&gt;pnlBook.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlBook.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlBook.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtTinvCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTinvCode.EditValue" xml:space="preserve">
    <value>Invoice Code</value>
  </data>
  <data name="txtTinvCode.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTinvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTinvCode.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTinvCode.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTinvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 22</value>
  </data>
  <data name="txtTinvCode.TabIndex" type="System.Int32, mscorlib">
    <value>254</value>
  </data>
  <data name="&gt;&gt;txtTinvCode.Name" xml:space="preserve">
    <value>txtTinvCode</value>
  </data>
  <data name="&gt;&gt;txtTinvCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTinvCode.Parent" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="&gt;&gt;txtTinvCode.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtInvoiceCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtInvoiceCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="txtInvoiceCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 20</value>
  </data>
  <data name="txtInvoiceCode.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Name" xml:space="preserve">
    <value>txtInvoiceCode</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Parent" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlInvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>673, 1</value>
  </data>
  <data name="pnlInvCode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlInvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>83, 44</value>
  </data>
  <data name="pnlInvCode.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Name" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtTdate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTdate.EditValue" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="txtTdate.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTdate.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTdate.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTdate.Size" type="System.Drawing.Size, System.Drawing">
    <value>126, 22</value>
  </data>
  <data name="txtTdate.TabIndex" type="System.Int32, mscorlib">
    <value>253</value>
  </data>
  <data name="&gt;&gt;txtTdate.Name" xml:space="preserve">
    <value>txtTdate</value>
  </data>
  <data name="&gt;&gt;txtTdate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTdate.Parent" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;txtTdate.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="dtInvoiceDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="dtInvoiceDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="dtInvoiceDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="dtInvoiceDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.EditMask" xml:space="preserve">
    <value>g</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>126, 20</value>
  </data>
  <data name="dtInvoiceDate.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Name" xml:space="preserve">
    <value>dtInvoiceDate</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Parent" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>542, 1</value>
  </data>
  <data name="pnlDate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>129, 44</value>
  </data>
  <data name="pnlDate.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;pnlDate.Name" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;pnlDate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlDate.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlDate.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtTstore.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTstore.EditValue" xml:space="preserve">
    <value>Branch</value>
  </data>
  <data name="txtTstore.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTstore.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTstore.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTstore.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTstore.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 22</value>
  </data>
  <data name="txtTstore.TabIndex" type="System.Int32, mscorlib">
    <value>252</value>
  </data>
  <data name="&gt;&gt;txtTstore.Name" xml:space="preserve">
    <value>txtTstore</value>
  </data>
  <data name="&gt;&gt;txtTstore.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTstore.Parent" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="&gt;&gt;txtTstore.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpStore.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="lkpStore.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpStore.Properties.Columns" xml:space="preserve">
    <value>StoreNameAr</value>
  </data>
  <data name="lkpStore.Properties.Columns1" xml:space="preserve">
    <value>Branch Name</value>
  </data>
  <data name="lkpStore.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpStore.Properties.Columns7" xml:space="preserve">
    <value>StoreCode</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>Branch Code</value>
  </data>
  <data name="lkpStore.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpStore.Properties.Columns14" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="lkpStore.Properties.Columns15" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="lkpStore.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns21" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns22" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns23" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns24" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns25" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns26" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns27" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns28" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns29" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns30" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns31" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns32" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns33" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns34" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns35" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns36" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns37" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns38" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns39" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns40" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns41" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns42" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns43" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns44" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns45" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns46" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns47" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns48" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 20</value>
  </data>
  <data name="lkpStore.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;lkpStore.Name" xml:space="preserve">
    <value>lkpStore</value>
  </data>
  <data name="&gt;&gt;lkpStore.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpStore.Parent" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="&gt;&gt;lkpStore.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlBranch.Location" type="System.Drawing.Point, System.Drawing">
    <value>399, 1</value>
  </data>
  <data name="pnlBranch.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlBranch.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 44</value>
  </data>
  <data name="pnlBranch.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Name" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlBranch.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtTdueDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTdueDate.EditValue" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="txtTdueDate.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdueDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTdueDate.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTdueDate.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTdueDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 22</value>
  </data>
  <data name="txtTdueDate.TabIndex" type="System.Int32, mscorlib">
    <value>249</value>
  </data>
  <data name="&gt;&gt;txtTdueDate.Name" xml:space="preserve">
    <value>txtTdueDate</value>
  </data>
  <data name="&gt;&gt;txtTdueDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTdueDate.Parent" xml:space="preserve">
    <value>pnlAgeDate</value>
  </data>
  <data name="&gt;&gt;txtTdueDate.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_DueDays.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_DueDays.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DueDays.Location" type="System.Drawing.Point, System.Drawing">
    <value>89, 23</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.EditMask" xml:space="preserve">
    <value>N00</value>
  </data>
  <data name="txt_DueDays.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 20</value>
  </data>
  <data name="txt_DueDays.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.Name" xml:space="preserve">
    <value>txt_DueDays</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.Parent" xml:space="preserve">
    <value>pnlAgeDate</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txt_DueDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_DueDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="txt_DueDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="txt_DueDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_DueDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 20</value>
  </data>
  <data name="txt_DueDate.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;txt_DueDate.Name" xml:space="preserve">
    <value>txt_DueDate</value>
  </data>
  <data name="&gt;&gt;txt_DueDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DueDate.Parent" xml:space="preserve">
    <value>pnlAgeDate</value>
  </data>
  <data name="&gt;&gt;txt_DueDate.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pnlAgeDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 1</value>
  </data>
  <data name="pnlAgeDate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlAgeDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>129, 44</value>
  </data>
  <data name="pnlAgeDate.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;pnlAgeDate.Name" xml:space="preserve">
    <value>pnlAgeDate</value>
  </data>
  <data name="&gt;&gt;pnlAgeDate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlAgeDate.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlAgeDate.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txtTdeliverDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTdeliverDate.EditValue" xml:space="preserve">
    <value>Deliver Date</value>
  </data>
  <data name="txtTdeliverDate.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdeliverDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTdeliverDate.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTdeliverDate.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTdeliverDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 22</value>
  </data>
  <data name="txtTdeliverDate.TabIndex" type="System.Int32, mscorlib">
    <value>251</value>
  </data>
  <data name="&gt;&gt;txtTdeliverDate.Name" xml:space="preserve">
    <value>txtTdeliverDate</value>
  </data>
  <data name="&gt;&gt;txtTdeliverDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTdeliverDate.Parent" xml:space="preserve">
    <value>pnlDeliveryDate</value>
  </data>
  <data name="&gt;&gt;txtTdeliverDate.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="dtDeliverDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="dtDeliverDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="dtDeliverDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="dtDeliverDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dtDeliverDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="dtDeliverDate.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;dtDeliverDate.Name" xml:space="preserve">
    <value>dtDeliverDate</value>
  </data>
  <data name="&gt;&gt;dtDeliverDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dtDeliverDate.Parent" xml:space="preserve">
    <value>pnlDeliveryDate</value>
  </data>
  <data name="&gt;&gt;dtDeliverDate.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlDeliveryDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>171, 1</value>
  </data>
  <data name="pnlDeliveryDate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlDeliveryDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>95, 44</value>
  </data>
  <data name="pnlDeliveryDate.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;pnlDeliveryDate.Name" xml:space="preserve">
    <value>pnlDeliveryDate</value>
  </data>
  <data name="&gt;&gt;pnlDeliveryDate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlDeliveryDate.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlDeliveryDate.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtCurrency.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtCurrency.EditValue" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="txtCurrency.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 22</value>
  </data>
  <data name="txtCurrency.TabIndex" type="System.Int32, mscorlib">
    <value>273</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Name" xml:space="preserve">
    <value>txtCurrency</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Parent" xml:space="preserve">
    <value>pnlCurrency</value>
  </data>
  <data name="&gt;&gt;txtCurrency.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="pnlCurrency.Location" type="System.Drawing.Point, System.Drawing">
    <value>36, 1</value>
  </data>
  <data name="pnlCurrency.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 43</value>
  </data>
  <data name="pnlCurrency.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;pnlCurrency.Name" xml:space="preserve">
    <value>pnlCurrency</value>
  </data>
  <data name="&gt;&gt;pnlCurrency.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlCurrency.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlCurrency.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txt_Post_Date.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_Post_Date.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="txt_Post_Date.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 23</value>
  </data>
  <data name="txt_Post_Date.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_Post_Date.Properties.Mask.EditMask" xml:space="preserve">
    <value>g</value>
  </data>
  <data name="txt_Post_Date.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 20</value>
  </data>
  <data name="txt_Post_Date.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txt_Post_Date.Name" xml:space="preserve">
    <value>txt_Post_Date</value>
  </data>
  <data name="&gt;&gt;txt_Post_Date.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Post_Date.Parent" xml:space="preserve">
    <value>pnlPostStore</value>
  </data>
  <data name="&gt;&gt;txt_Post_Date.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="chk_IsPosted.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="chk_IsPosted.EditValue" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_IsPosted.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="chk_IsPosted.Properties.Caption" xml:space="preserve">
    <value>Post to Store</value>
  </data>
  <data name="chk_IsPosted.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_IsPosted.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 19</value>
  </data>
  <data name="chk_IsPosted.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_IsPosted.Name" xml:space="preserve">
    <value>chk_IsPosted</value>
  </data>
  <data name="&gt;&gt;chk_IsPosted.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsPosted.Parent" xml:space="preserve">
    <value>pnlPostStore</value>
  </data>
  <data name="&gt;&gt;chk_IsPosted.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlPostStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>719, 49</value>
  </data>
  <data name="pnlPostStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 44</value>
  </data>
  <data name="pnlPostStore.TabIndex" type="System.Int32, mscorlib">
    <value>301</value>
  </data>
  <data name="&gt;&gt;pnlPostStore.Name" xml:space="preserve">
    <value>pnlPostStore</value>
  </data>
  <data name="&gt;&gt;pnlPostStore.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlPostStore.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlPostStore.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txtTpo.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTpo.EditValue" xml:space="preserve">
    <value>P.O</value>
  </data>
  <data name="txtTpo.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTpo.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 2</value>
  </data>
  <data name="txtTpo.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTpo.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTpo.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 22</value>
  </data>
  <data name="txtTpo.TabIndex" type="System.Int32, mscorlib">
    <value>271</value>
  </data>
  <data name="&gt;&gt;txtTpo.Name" xml:space="preserve">
    <value>txtTpo</value>
  </data>
  <data name="&gt;&gt;txtTpo.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTpo.Parent" xml:space="preserve">
    <value>pnlPO</value>
  </data>
  <data name="&gt;&gt;txtTpo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_PO_No.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_PO_No.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 23</value>
  </data>
  <data name="txt_PO_No.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="txt_PO_No.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 20</value>
  </data>
  <data name="txt_PO_No.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;txt_PO_No.Name" xml:space="preserve">
    <value>txt_PO_No</value>
  </data>
  <data name="&gt;&gt;txt_PO_No.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_PO_No.Parent" xml:space="preserve">
    <value>pnlPO</value>
  </data>
  <data name="&gt;&gt;txt_PO_No.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlPO.Location" type="System.Drawing.Point, System.Drawing">
    <value>613, 47</value>
  </data>
  <data name="pnlPO.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlPO.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 44</value>
  </data>
  <data name="pnlPO.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;pnlPO.Name" xml:space="preserve">
    <value>pnlPO</value>
  </data>
  <data name="&gt;&gt;pnlPO.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlPO.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlPO.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txtTSalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTSalesEmp.EditValue" xml:space="preserve">
    <value>Sales Employee</value>
  </data>
  <data name="txtTSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTSalesEmp.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTSalesEmp.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>174, 22</value>
  </data>
  <data name="txtTSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>256</value>
  </data>
  <data name="&gt;&gt;txtTSalesEmp.Name" xml:space="preserve">
    <value>txtTSalesEmp</value>
  </data>
  <data name="&gt;&gt;txtTSalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTSalesEmp.Parent" xml:space="preserve">
    <value>pnlSalesEmp</value>
  </data>
  <data name="&gt;&gt;txtTSalesEmp.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkp_SalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkp_SalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns1" xml:space="preserve">
    <value>الموظف</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns2" xml:space="preserve">
    <value>EmpCode</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns3" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns4" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns5" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns6" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns7" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns8" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Columns9" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns10" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_SalesEmp.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>174, 20</value>
  </data>
  <data name="lkp_SalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Name" xml:space="preserve">
    <value>lkp_SalesEmp</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Parent" xml:space="preserve">
    <value>pnlSalesEmp</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>433, 47</value>
  </data>
  <data name="pnlSalesEmp.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>178, 44</value>
  </data>
  <data name="pnlSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.Name" xml:space="preserve">
    <value>pnlSalesEmp</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lkpCostCenter.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkpCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="lkpCostCenter.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns1" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns7" xml:space="preserve">
    <value>CostCenterName</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns8" xml:space="preserve">
    <value>Cost Center Name</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns14" xml:space="preserve">
    <value>CostCenterCode</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns15" xml:space="preserve">
    <value>Cost Center Code</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpCostCenter.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>137, 20</value>
  </data>
  <data name="lkpCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Name" xml:space="preserve">
    <value>lkpCostCenter</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Parent" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textEdit9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="textEdit9.EditValue" xml:space="preserve">
    <value>Cost Center</value>
  </data>
  <data name="textEdit9.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit9.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="textEdit9.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit9.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit9.Size" type="System.Drawing.Size, System.Drawing">
    <value>137, 22</value>
  </data>
  <data name="textEdit9.TabIndex" type="System.Int32, mscorlib">
    <value>259</value>
  </data>
  <data name="&gt;&gt;textEdit9.Name" xml:space="preserve">
    <value>textEdit9</value>
  </data>
  <data name="&gt;&gt;textEdit9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit9.Parent" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="&gt;&gt;textEdit9.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>290, 47</value>
  </data>
  <data name="pnlCostCenter.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 44</value>
  </data>
  <data name="pnlCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Name" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="pnlSrcPrc.Location" type="System.Drawing.Point, System.Drawing">
    <value>86, 49</value>
  </data>
  <data name="pnlSrcPrc.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 100</value>
  </data>
  <data name="pnlSrcPrc.TabIndex" type="System.Int32, mscorlib">
    <value>302</value>
  </data>
  <data name="&gt;&gt;pnlSrcPrc.Name" xml:space="preserve">
    <value>pnlSrcPrc</value>
  </data>
  <data name="&gt;&gt;pnlSrcPrc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlSrcPrc.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlSrcPrc.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="flowLayoutPanel1.FlowDirection" type="System.Windows.Forms.FlowDirection, System.Windows.Forms">
    <value>RightToLeft</value>
  </data>
  <data name="flowLayoutPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>302, 3</value>
  </data>
  <data name="flowLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>861, 97</value>
  </data>
  <data name="flowLayoutPanel1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Name" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.FlowLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>236, 21</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>269</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lblShipTo.Location" type="System.Drawing.Point, System.Drawing">
    <value>236, 67</value>
  </data>
  <data name="lblShipTo.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="lblShipTo.TabIndex" type="System.Int32, mscorlib">
    <value>268</value>
  </data>
  <data name="lblShipTo.Text" xml:space="preserve">
    <value>Ship to</value>
  </data>
  <data name="&gt;&gt;lblShipTo.Name" xml:space="preserve">
    <value>lblShipTo</value>
  </data>
  <data name="&gt;&gt;lblShipTo.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblShipTo.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;lblShipTo.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtNotes.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="txtNotes.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 5</value>
  </data>
  <data name="txtNotes.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtNotes.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtNotes.Size" type="System.Drawing.Size, System.Drawing">
    <value>230, 44</value>
  </data>
  <data name="txtNotes.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;txtNotes.Name" xml:space="preserve">
    <value>txtNotes</value>
  </data>
  <data name="&gt;&gt;txtNotes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtNotes.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;txtNotes.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_Shipping.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 51</value>
  </data>
  <data name="txt_Shipping.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_Shipping.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txt_Shipping.Size" type="System.Drawing.Size, System.Drawing">
    <value>229, 44</value>
  </data>
  <data name="txt_Shipping.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;txt_Shipping.Name" xml:space="preserve">
    <value>txt_Shipping</value>
  </data>
  <data name="&gt;&gt;txt_Shipping.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Shipping.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;txt_Shipping.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="panelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 55</value>
  </data>
  <data name="panelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1169, 105</value>
  </data>
  <data name="panelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;panelControl1.Name" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;panelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;panelControl1.ZOrder" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="labelControl13.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>173, 511</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 13</value>
  </data>
  <data name="labelControl13.TabIndex" type="System.Int32, mscorlib">
    <value>115</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="&gt;&gt;labelControl13.Name" xml:space="preserve">
    <value>labelControl13</value>
  </data>
  <data name="&gt;&gt;labelControl13.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl13.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl13.ZOrder" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 33</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>62, 13</value>
  </data>
  <data name="labelControl36.TabIndex" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>Sell Invoices </value>
  </data>
  <data name="&gt;&gt;labelControl36.Name" xml:space="preserve">
    <value>labelControl36</value>
  </data>
  <data name="&gt;&gt;labelControl36.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl36.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl36.ZOrder" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="lkp_Customers.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_Customers.Location" type="System.Drawing.Point, System.Drawing">
    <value>941, 32</value>
  </data>
  <data name="lkp_Customers.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Customers.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn19.Caption" xml:space="preserve">
    <value>CustomerId</value>
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn20.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn20.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn20.Width" type="System.Int32, mscorlib">
    <value>143</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn21.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn21.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn21.Width" type="System.Int32, mscorlib">
    <value>404</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>F Name </value>
  </data>
  <data name="gridColumn22.Width" type="System.Int32, mscorlib">
    <value>209</value>
  </data>
  <data name="gridColumn26.Caption" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="gridColumn26.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn26.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn26.Width" type="System.Int32, mscorlib">
    <value>196</value>
  </data>
  <data name="gridColumn27.Caption" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="gridColumn27.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn27.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn27.Width" type="System.Int32, mscorlib">
    <value>182</value>
  </data>
  <data name="gridColumn30.Caption" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="gridColumn30.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn30.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn30.Width" type="System.Int32, mscorlib">
    <value>182</value>
  </data>
  <data name="lkp_Customers.Size" type="System.Drawing.Size, System.Drawing">
    <value>181, 20</value>
  </data>
  <data name="lkp_Customers.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Name" xml:space="preserve">
    <value>lkp_Customers</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.ZOrder" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>42, 30</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="btnNext.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="btnNext.Text" xml:space="preserve">
    <value>=&gt;</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="&gt;&gt;btnNext.Name" xml:space="preserve">
    <value>btnNext</value>
  </data>
  <data name="&gt;&gt;btnNext.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnNext.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnNext.ZOrder" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="labelControl35.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>1128, 36</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="labelControl35.TabIndex" type="System.Int32, mscorlib">
    <value>85</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="&gt;&gt;labelControl35.Name" xml:space="preserve">
    <value>labelControl35</value>
  </data>
  <data name="&gt;&gt;labelControl35.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl35.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl35.ZOrder" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="btnPrevious.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 30</value>
  </data>
  <data name="btnPrevious.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="btnPrevious.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="btnPrevious.Text" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="btnPrevious.ToolTip" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Name" xml:space="preserve">
    <value>btnPrevious</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnPrevious.ZOrder" xml:space="preserve">
    <value>51</value>
  </data>
  <data name="labelControl9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 422</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 13</value>
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>Other Charges</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>52</value>
  </data>
  <data name="txtExpenses.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtExpenses.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtExpenses.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 419</value>
  </data>
  <data name="txtExpenses.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txtExpenses.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txtExpenses.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Name" xml:space="preserve">
    <value>txtExpenses</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtExpenses.ZOrder" xml:space="preserve">
    <value>53</value>
  </data>
  <data name="txtDiscountRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtDiscountRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 329</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtDiscountRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txtDiscountRatio.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Name" xml:space="preserve">
    <value>txtDiscountRatio</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.ZOrder" xml:space="preserve">
    <value>54</value>
  </data>
  <data name="txtDiscountValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtDiscountValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 329</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txtDiscountValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txtDiscountValue.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Name" xml:space="preserve">
    <value>txtDiscountValue</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.ZOrder" xml:space="preserve">
    <value>55</value>
  </data>
  <data name="txt_TaxValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_TaxValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_TaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 351</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_TaxValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 20</value>
  </data>
  <data name="txt_TaxValue.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.Name" xml:space="preserve">
    <value>txt_TaxValue</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.ZOrder" xml:space="preserve">
    <value>56</value>
  </data>
  <data name="txt_DeductTaxR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_DeductTaxR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 373</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_DeductTaxR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_DeductTaxR.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Name" xml:space="preserve">
    <value>txt_DeductTaxR</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.ZOrder" xml:space="preserve">
    <value>57</value>
  </data>
  <data name="txt_DeductTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_DeductTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 373</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_DeductTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txt_DeductTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Name" xml:space="preserve">
    <value>txt_DeductTaxV</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.ZOrder" xml:space="preserve">
    <value>58</value>
  </data>
  <data name="txt_AddTaxR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_AddTaxR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 396</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_AddTaxR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_AddTaxR.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Name" xml:space="preserve">
    <value>txt_AddTaxR</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.ZOrder" xml:space="preserve">
    <value>59</value>
  </data>
  <data name="txt_AddTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_AddTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 396</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_AddTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txt_AddTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Name" xml:space="preserve">
    <value>txt_AddTaxV</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.ZOrder" xml:space="preserve">
    <value>60</value>
  </data>
  <data name="txtExpensesR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtExpensesR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtExpensesR.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 419</value>
  </data>
  <data name="txtExpensesR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtExpensesR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txtExpensesR.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;txtExpensesR.Name" xml:space="preserve">
    <value>txtExpensesR</value>
  </data>
  <data name="&gt;&gt;txtExpensesR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtExpensesR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtExpensesR.ZOrder" xml:space="preserve">
    <value>61</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Sales Invoice</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Name" xml:space="preserve">
    <value>barBtnHelp</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_ConvertTo.Name" xml:space="preserve">
    <value>barBtn_ConvertTo</value>
  </data>
  <data name="&gt;&gt;barBtn_ConvertTo.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarSubItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barSubItemPrint.Name" xml:space="preserve">
    <value>barSubItemPrint</value>
  </data>
  <data name="&gt;&gt;barSubItemPrint.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarSubItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtnPrint.Name" xml:space="preserve">
    <value>barbtnPrint</value>
  </data>
  <data name="&gt;&gt;barbtnPrint.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtnPrintF.Name" xml:space="preserve">
    <value>barbtnPrintF</value>
  </data>
  <data name="&gt;&gt;barbtnPrintF.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Name" xml:space="preserve">
    <value>barBtnDelete</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Name" xml:space="preserve">
    <value>barBtnSave</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;batBtnList.Name" xml:space="preserve">
    <value>batBtnList</value>
  </data>
  <data name="&gt;&gt;batBtnList.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnNotesReceivable.Name" xml:space="preserve">
    <value>barBtnNotesReceivable</value>
  </data>
  <data name="&gt;&gt;barBtnNotesReceivable.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_Sl_Qoute.Name" xml:space="preserve">
    <value>barBtnLoad_Sl_Qoute</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_Sl_Qoute.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_SalesOrder.Name" xml:space="preserve">
    <value>barBtnLoad_SalesOrder</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_SalesOrder.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_IC_OutTrns.Name" xml:space="preserve">
    <value>barBtnLoad_IC_OutTrns</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_IC_OutTrns.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_JO.Name" xml:space="preserve">
    <value>barBtnLoad_JO</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_JO.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_PR_Invoice.Name" xml:space="preserve">
    <value>barBtnLoad_PR_Invoice</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_PR_Invoice.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_IC_Transfer.Name" xml:space="preserve">
    <value>barBtnLoad_IC_Transfer</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_IC_Transfer.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_OutTrns.Name" xml:space="preserve">
    <value>barBtn_OutTrns</value>
  </data>
  <data name="&gt;&gt;barBtn_OutTrns.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_CashNote.Name" xml:space="preserve">
    <value>barBtn_CashNote</value>
  </data>
  <data name="&gt;&gt;barBtn_CashNote.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Name" xml:space="preserve">
    <value>gridColumn19</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn20.Name" xml:space="preserve">
    <value>gridColumn20</value>
  </data>
  <data name="&gt;&gt;gridColumn20.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Name" xml:space="preserve">
    <value>gridColumn21</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Name" xml:space="preserve">
    <value>gridColumn22</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn26.Name" xml:space="preserve">
    <value>gridColumn26</value>
  </data>
  <data name="&gt;&gt;gridColumn26.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn27.Name" xml:space="preserve">
    <value>gridColumn27</value>
  </data>
  <data name="&gt;&gt;gridColumn27.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn30.Name" xml:space="preserve">
    <value>gridColumn30</value>
  </data>
  <data name="&gt;&gt;gridColumn30.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;mi_frm_IC_Item.Name" xml:space="preserve">
    <value>mi_frm_IC_Item</value>
  </data>
  <data name="&gt;&gt;mi_frm_IC_Item.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_CustLastPrices.Name" xml:space="preserve">
    <value>mi_CustLastPrices</value>
  </data>
  <data name="&gt;&gt;mi_CustLastPrices.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_LastPrices.Name" xml:space="preserve">
    <value>mi_LastPrices</value>
  </data>
  <data name="&gt;&gt;mi_LastPrices.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_PasteRows.Name" xml:space="preserve">
    <value>mi_PasteRows</value>
  </data>
  <data name="&gt;&gt;mi_PasteRows.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_ExportData.Name" xml:space="preserve">
    <value>mi_ExportData</value>
  </data>
  <data name="&gt;&gt;mi_ExportData.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDisc.Name" xml:space="preserve">
    <value>mi_InvoiceStaticDisc</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDisc.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDimensions.Name" xml:space="preserve">
    <value>mi_InvoiceStaticDimensions</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDimensions.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_ImportExcel.Name" xml:space="preserve">
    <value>mi_ImportExcel</value>
  </data>
  <data name="&gt;&gt;mi_ImportExcel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gridView2.Name" xml:space="preserve">
    <value>gridView2</value>
  </data>
  <data name="&gt;&gt;gridView2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn29.Name" xml:space="preserve">
    <value>gridColumn29</value>
  </data>
  <data name="&gt;&gt;gridColumn29.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Name" xml:space="preserve">
    <value>gridColumn28</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalSellPrice.Name" xml:space="preserve">
    <value>col_TotalSellPrice</value>
  </data>
  <data name="&gt;&gt;col_TotalSellPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Name" xml:space="preserve">
    <value>gridColumn2</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repSpin.Name" xml:space="preserve">
    <value>repSpin</value>
  </data>
  <data name="&gt;&gt;repSpin.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Name" xml:space="preserve">
    <value>gridColumn1</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repDiscountRatio.Name" xml:space="preserve">
    <value>repDiscountRatio</value>
  </data>
  <data name="&gt;&gt;repDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SellPrice.Name" xml:space="preserve">
    <value>col_SellPrice</value>
  </data>
  <data name="&gt;&gt;col_SellPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colPurchasePrice.Name" xml:space="preserve">
    <value>colPurchasePrice</value>
  </data>
  <data name="&gt;&gt;colPurchasePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Name" xml:space="preserve">
    <value>gridColumn7</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Name" xml:space="preserve">
    <value>gridColumn8</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repUOM.Name" xml:space="preserve">
    <value>repUOM</value>
  </data>
  <data name="&gt;&gt;repUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView4.Name" xml:space="preserve">
    <value>gridView4</value>
  </data>
  <data name="&gt;&gt;gridView4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Name" xml:space="preserve">
    <value>gridColumn9</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Name" xml:space="preserve">
    <value>gridColumn16</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Name" xml:space="preserve">
    <value>gridColumn17</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Name" xml:space="preserve">
    <value>gridColumn3</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Name" xml:space="preserve">
    <value>gridColumn10</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repItems.Name" xml:space="preserve">
    <value>repItems</value>
  </data>
  <data name="&gt;&gt;repItems.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Name" xml:space="preserve">
    <value>repositoryItemGridLookUpEdit1View</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Name" xml:space="preserve">
    <value>gridColumn12</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Name" xml:space="preserve">
    <value>gridColumn13</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Name" xml:space="preserve">
    <value>gridColumn14</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Name" xml:space="preserve">
    <value>gridColumn15</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Name" xml:space="preserve">
    <value>gridColumn5</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Name" xml:space="preserve">
    <value>gridColumn4</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Name" xml:space="preserve">
    <value>gridColumn25</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CompanyNameAr.Name" xml:space="preserve">
    <value>col_CompanyNameAr</value>
  </data>
  <data name="&gt;&gt;col_CompanyNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CategoryNameAr.Name" xml:space="preserve">
    <value>col_CategoryNameAr</value>
  </data>
  <data name="&gt;&gt;col_CategoryNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdcol_branch.Name" xml:space="preserve">
    <value>grdcol_branch</value>
  </data>
  <data name="&gt;&gt;grdcol_branch.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_storee.Name" xml:space="preserve">
    <value>lkp_storee</value>
  </data>
  <data name="&gt;&gt;lkp_storee.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView7.Name" xml:space="preserve">
    <value>gridView7</value>
  </data>
  <data name="&gt;&gt;gridView7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Name" xml:space="preserve">
    <value>gridColumn11</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn31.Name" xml:space="preserve">
    <value>gridColumn31</value>
  </data>
  <data name="&gt;&gt;gridColumn31.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Name" xml:space="preserve">
    <value>gridColumn41</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Expire.Name" xml:space="preserve">
    <value>col_Expire</value>
  </data>
  <data name="&gt;&gt;col_Expire.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_expireDate.Name" xml:space="preserve">
    <value>rep_expireDate</value>
  </data>
  <data name="&gt;&gt;rep_expireDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView5.Name" xml:space="preserve">
    <value>gridView5</value>
  </data>
  <data name="&gt;&gt;gridView5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Name" xml:space="preserve">
    <value>gridColumn18</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Name" xml:space="preserve">
    <value>gridColumn23</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn24.Name" xml:space="preserve">
    <value>gridColumn24</value>
  </data>
  <data name="&gt;&gt;gridColumn24.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Batch.Name" xml:space="preserve">
    <value>col_Batch</value>
  </data>
  <data name="&gt;&gt;col_Batch.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_Batch.Name" xml:space="preserve">
    <value>rep_Batch</value>
  </data>
  <data name="&gt;&gt;rep_Batch.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView6.Name" xml:space="preserve">
    <value>gridView6</value>
  </data>
  <data name="&gt;&gt;gridView6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Name" xml:space="preserve">
    <value>gridColumn33</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn34.Name" xml:space="preserve">
    <value>gridColumn34</value>
  </data>
  <data name="&gt;&gt;gridColumn34.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Length.Name" xml:space="preserve">
    <value>col_Length</value>
  </data>
  <data name="&gt;&gt;col_Length.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Width.Name" xml:space="preserve">
    <value>col_Width</value>
  </data>
  <data name="&gt;&gt;col_Width.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Height.Name" xml:space="preserve">
    <value>col_Height</value>
  </data>
  <data name="&gt;&gt;col_Height.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalQty.Name" xml:space="preserve">
    <value>col_TotalQty</value>
  </data>
  <data name="&gt;&gt;col_TotalQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_PiecesCount.Name" xml:space="preserve">
    <value>col_PiecesCount</value>
  </data>
  <data name="&gt;&gt;col_PiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItemDescription.Name" xml:space="preserve">
    <value>col_ItemDescription</value>
  </data>
  <data name="&gt;&gt;col_ItemDescription.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItemDescriptionEn.Name" xml:space="preserve">
    <value>col_ItemDescriptionEn</value>
  </data>
  <data name="&gt;&gt;col_ItemDescriptionEn.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SalesTax.Name" xml:space="preserve">
    <value>col_SalesTax</value>
  </data>
  <data name="&gt;&gt;col_SalesTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio2.Name" xml:space="preserve">
    <value>col_DiscountRatio2</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio3.Name" xml:space="preserve">
    <value>col_DiscountRatio3</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Serial.Name" xml:space="preserve">
    <value>col_Serial</value>
  </data>
  <data name="&gt;&gt;col_Serial.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn32.Name" xml:space="preserve">
    <value>gridColumn32</value>
  </data>
  <data name="&gt;&gt;gridColumn32.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repManufactureDate.Name" xml:space="preserve">
    <value>repManufactureDate</value>
  </data>
  <data name="&gt;&gt;repManufactureDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemDateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CusTax.Name" xml:space="preserve">
    <value>col_CusTax</value>
  </data>
  <data name="&gt;&gt;col_CusTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_store.Name" xml:space="preserve">
    <value>lkp_store</value>
  </data>
  <data name="&gt;&gt;lkp_store.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView3.Name" xml:space="preserve">
    <value>gridView3</value>
  </data>
  <data name="&gt;&gt;gridView3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colTotalPurchasePrice.Name" xml:space="preserve">
    <value>colTotalPurchasePrice</value>
  </data>
  <data name="&gt;&gt;colTotalPurchasePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Name" xml:space="preserve">
    <value>gridColumn6</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colUOM.Name" xml:space="preserve">
    <value>colUOM</value>
  </data>
  <data name="&gt;&gt;colUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colQty.Name" xml:space="preserve">
    <value>colQty</value>
  </data>
  <data name="&gt;&gt;colQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colCustNameAr.Name" xml:space="preserve">
    <value>colCustNameAr</value>
  </data>
  <data name="&gt;&gt;colCustNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colInvoiceDate.Name" xml:space="preserve">
    <value>colInvoiceDate</value>
  </data>
  <data name="&gt;&gt;colInvoiceDate.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colInvoiceCode.Name" xml:space="preserve">
    <value>colInvoiceCode</value>
  </data>
  <data name="&gt;&gt;colInvoiceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_SL_InvoiceArchive</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="txtSourceCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txtSourceCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txtSourceCode.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtSourceCode.Name" xml:space="preserve">
    <value>txtSourceCode</value>
  </data>
  <data name="&gt;&gt;txtSourceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnSourceId.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="btnSourceId.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="btnSourceId.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btnSourceId.Name" xml:space="preserve">
    <value>btnSourceId</value>
  </data>
  <data name="&gt;&gt;btnSourceId.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ButtonEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmdProcess.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="cmdProcess.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="cmdProcess.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cmdProcess.Name" xml:space="preserve">
    <value>cmdProcess</value>
  </data>
  <data name="&gt;&gt;cmdProcess.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
</root>