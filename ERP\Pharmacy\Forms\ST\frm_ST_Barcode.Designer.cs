﻿namespace Pharmacy.Forms
{
    partial class frm_ST_Barcode
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_ST_Barcode));
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Preview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Cancel = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtCurrency = new DevExpress.XtraEditors.TextEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.txtRowsCount = new DevExpress.XtraEditors.SpinEdit();
            this.txtColumnsCount = new DevExpress.XtraEditors.SpinEdit();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.chk_PrintBatchNo = new DevExpress.XtraEditors.CheckEdit();
            this.chk_ShowBacrodeText = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.chk_3 = new DevExpress.XtraEditors.CheckEdit();
            this.chk_2 = new DevExpress.XtraEditors.CheckEdit();
            this.chk_4 = new DevExpress.XtraEditors.CheckEdit();
            this.chk_1 = new DevExpress.XtraEditors.CheckEdit();
            this.spn_1 = new DevExpress.XtraEditors.SpinEdit();
            this.spn_4 = new DevExpress.XtraEditors.SpinEdit();
            this.spn_3 = new DevExpress.XtraEditors.SpinEdit();
            this.spn_2 = new DevExpress.XtraEditors.SpinEdit();
            this.chklst_Line3 = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.chklst_Line2 = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.chklst_Line1 = new DevExpress.XtraEditors.CheckedComboBoxEdit();
            this.panel2 = new System.Windows.Forms.Panel();
            this.lbl_Line_1 = new DevExpress.XtraEditors.LabelControl();
            this.Lbl_Line_3 = new DevExpress.XtraEditors.LabelControl();
            this.lbl_Line_4 = new DevExpress.XtraEditors.LabelControl();
            this.lbl_line_2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Line_4 = new DevExpress.XtraEditors.TextEdit();
            this.txt_TemplateName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.barButtonItem8 = new DevExpress.XtraBars.BarButtonItem();
            this.chkIsDefaultTemplate = new DevExpress.XtraEditors.CheckEdit();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.cb_Papers = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Paperwidth = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Paperheight = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrev = new DevExpress.XtraEditors.SimpleButton();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.txt_MarginLeft = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.txt_MarginRight = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.txt_MarginBottom = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.txt_MarginTop = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.cb_Fonts = new DevExpress.XtraEditors.FontEdit();
            this.txt_BatchPrefix = new DevExpress.XtraEditors.TextEdit();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.txt_QtyPrefix = new DevExpress.XtraEditors.TextEdit();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtRowsCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtColumnsCount.Properties)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_PrintBatchNo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_ShowBacrodeText.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_4.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_4.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chklst_Line3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chklst_Line2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chklst_Line1.Properties)).BeginInit();
            this.panel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Line_4.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TemplateName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsDefaultTemplate.Properties)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cb_Papers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Paperwidth.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Paperheight.Properties)).BeginInit();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MarginLeft.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MarginRight.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MarginBottom.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MarginTop.Properties)).BeginInit();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cb_Fonts.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_BatchPrefix.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_QtyPrefix.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtn_Cancel,
            this.barBtnNew,
            this.barBtn_Preview,
            this.barButtonItem1,
            this.barBtnHelp});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Preview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Cancel)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 28;
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtn_Preview
            // 
            resources.ApplyResources(this.barBtn_Preview, "barBtn_Preview");
            this.barBtn_Preview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Preview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtn_Preview.Id = 26;
            this.barBtn_Preview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtn_Preview.Name = "barBtn_Preview";
            this.barBtn_Preview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Preview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Preview_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 24;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_New_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtn_Cancel
            // 
            resources.ApplyResources(this.barBtn_Cancel, "barBtn_Cancel");
            this.barBtn_Cancel.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Cancel.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtn_Cancel.Id = 1;
            this.barBtn_Cancel.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtn_Cancel.Name = "barBtn_Cancel";
            this.barBtn_Cancel.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Cancel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Cancel_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // barButtonItem1
            // 
            resources.ApplyResources(this.barButtonItem1, "barButtonItem1");
            this.barButtonItem1.Id = 27;
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // txtCurrency
            // 
            resources.ApplyResources(this.txtCurrency, "txtCurrency");
            this.txtCurrency.EnterMoveNextControl = true;
            this.txtCurrency.Name = "txtCurrency";
            this.txtCurrency.Properties.AccessibleDescription = resources.GetString("txtCurrency.Properties.AccessibleDescription");
            this.txtCurrency.Properties.AccessibleName = resources.GetString("txtCurrency.Properties.AccessibleName");
            this.txtCurrency.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCurrency.Properties.Appearance.FontSizeDelta")));
            this.txtCurrency.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCurrency.Properties.Appearance.FontStyleDelta")));
            this.txtCurrency.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCurrency.Properties.Appearance.GradientMode")));
            this.txtCurrency.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCurrency.Properties.Appearance.Image")));
            this.txtCurrency.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCurrency.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCurrency.Properties.AutoHeight = ((bool)(resources.GetObject("txtCurrency.Properties.AutoHeight")));
            this.txtCurrency.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCurrency.Properties.Mask.AutoComplete")));
            this.txtCurrency.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.BeepOnError")));
            this.txtCurrency.Properties.Mask.EditMask = resources.GetString("txtCurrency.Properties.Mask.EditMask");
            this.txtCurrency.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.IgnoreMaskBlank")));
            this.txtCurrency.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCurrency.Properties.Mask.MaskType")));
            this.txtCurrency.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCurrency.Properties.Mask.PlaceHolder")));
            this.txtCurrency.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.SaveLiteral")));
            this.txtCurrency.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.ShowPlaceHolders")));
            this.txtCurrency.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCurrency.Properties.NullValuePrompt = resources.GetString("txtCurrency.Properties.NullValuePrompt");
            this.txtCurrency.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCurrency.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCurrency.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // groupBox1
            // 
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Controls.Add(this.labelControl5);
            this.groupBox1.Controls.Add(this.labelControl15);
            this.groupBox1.Controls.Add(this.txtRowsCount);
            this.groupBox1.Controls.Add(this.txtColumnsCount);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // txtRowsCount
            // 
            resources.ApplyResources(this.txtRowsCount, "txtRowsCount");
            this.txtRowsCount.EnterMoveNextControl = true;
            this.txtRowsCount.Name = "txtRowsCount";
            this.txtRowsCount.Properties.AccessibleDescription = resources.GetString("txtRowsCount.Properties.AccessibleDescription");
            this.txtRowsCount.Properties.AccessibleName = resources.GetString("txtRowsCount.Properties.AccessibleName");
            this.txtRowsCount.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtRowsCount.Properties.Appearance.FontSizeDelta")));
            this.txtRowsCount.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtRowsCount.Properties.Appearance.FontStyleDelta")));
            this.txtRowsCount.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtRowsCount.Properties.Appearance.GradientMode")));
            this.txtRowsCount.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtRowsCount.Properties.Appearance.Image")));
            this.txtRowsCount.Properties.Appearance.Options.UseTextOptions = true;
            this.txtRowsCount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtRowsCount.Properties.AutoHeight = ((bool)(resources.GetObject("txtRowsCount.Properties.AutoHeight")));
            this.txtRowsCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txtRowsCount.Properties.IsFloatValue = false;
            this.txtRowsCount.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtRowsCount.Properties.Mask.AutoComplete")));
            this.txtRowsCount.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtRowsCount.Properties.Mask.BeepOnError")));
            this.txtRowsCount.Properties.Mask.EditMask = resources.GetString("txtRowsCount.Properties.Mask.EditMask");
            this.txtRowsCount.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtRowsCount.Properties.Mask.IgnoreMaskBlank")));
            this.txtRowsCount.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtRowsCount.Properties.Mask.MaskType")));
            this.txtRowsCount.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtRowsCount.Properties.Mask.PlaceHolder")));
            this.txtRowsCount.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtRowsCount.Properties.Mask.SaveLiteral")));
            this.txtRowsCount.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtRowsCount.Properties.Mask.ShowPlaceHolders")));
            this.txtRowsCount.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtRowsCount.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtRowsCount.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtRowsCount.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.txtRowsCount.Properties.NullValuePrompt = resources.GetString("txtRowsCount.Properties.NullValuePrompt");
            this.txtRowsCount.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtRowsCount.Properties.NullValuePromptShowForEmptyValue")));
            this.txtRowsCount.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // txtColumnsCount
            // 
            resources.ApplyResources(this.txtColumnsCount, "txtColumnsCount");
            this.txtColumnsCount.EnterMoveNextControl = true;
            this.txtColumnsCount.Name = "txtColumnsCount";
            this.txtColumnsCount.Properties.AccessibleDescription = resources.GetString("txtColumnsCount.Properties.AccessibleDescription");
            this.txtColumnsCount.Properties.AccessibleName = resources.GetString("txtColumnsCount.Properties.AccessibleName");
            this.txtColumnsCount.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtColumnsCount.Properties.Appearance.FontSizeDelta")));
            this.txtColumnsCount.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtColumnsCount.Properties.Appearance.FontStyleDelta")));
            this.txtColumnsCount.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtColumnsCount.Properties.Appearance.GradientMode")));
            this.txtColumnsCount.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtColumnsCount.Properties.Appearance.Image")));
            this.txtColumnsCount.Properties.Appearance.Options.UseTextOptions = true;
            this.txtColumnsCount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtColumnsCount.Properties.AutoHeight = ((bool)(resources.GetObject("txtColumnsCount.Properties.AutoHeight")));
            this.txtColumnsCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txtColumnsCount.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtColumnsCount.Properties.Mask.AutoComplete")));
            this.txtColumnsCount.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtColumnsCount.Properties.Mask.BeepOnError")));
            this.txtColumnsCount.Properties.Mask.EditMask = resources.GetString("txtColumnsCount.Properties.Mask.EditMask");
            this.txtColumnsCount.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtColumnsCount.Properties.Mask.IgnoreMaskBlank")));
            this.txtColumnsCount.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtColumnsCount.Properties.Mask.MaskType")));
            this.txtColumnsCount.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtColumnsCount.Properties.Mask.PlaceHolder")));
            this.txtColumnsCount.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtColumnsCount.Properties.Mask.SaveLiteral")));
            this.txtColumnsCount.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtColumnsCount.Properties.Mask.ShowPlaceHolders")));
            this.txtColumnsCount.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtColumnsCount.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtColumnsCount.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtColumnsCount.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.txtColumnsCount.Properties.NullValuePrompt = resources.GetString("txtColumnsCount.Properties.NullValuePrompt");
            this.txtColumnsCount.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtColumnsCount.Properties.NullValuePromptShowForEmptyValue")));
            this.txtColumnsCount.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // groupBox2
            // 
            resources.ApplyResources(this.groupBox2, "groupBox2");
            this.groupBox2.Controls.Add(this.chk_PrintBatchNo);
            this.groupBox2.Controls.Add(this.chk_ShowBacrodeText);
            this.groupBox2.Controls.Add(this.labelControl11);
            this.groupBox2.Controls.Add(this.labelControl10);
            this.groupBox2.Controls.Add(this.chk_3);
            this.groupBox2.Controls.Add(this.chk_2);
            this.groupBox2.Controls.Add(this.chk_4);
            this.groupBox2.Controls.Add(this.chk_1);
            this.groupBox2.Controls.Add(this.spn_1);
            this.groupBox2.Controls.Add(this.spn_4);
            this.groupBox2.Controls.Add(this.spn_3);
            this.groupBox2.Controls.Add(this.spn_2);
            this.groupBox2.Controls.Add(this.chklst_Line3);
            this.groupBox2.Controls.Add(this.labelControl8);
            this.groupBox2.Controls.Add(this.chklst_Line2);
            this.groupBox2.Controls.Add(this.labelControl7);
            this.groupBox2.Controls.Add(this.labelControl4);
            this.groupBox2.Controls.Add(this.chklst_Line1);
            this.groupBox2.Controls.Add(this.panel2);
            this.groupBox2.Controls.Add(this.labelControl9);
            this.groupBox2.Controls.Add(this.labelControl6);
            this.groupBox2.Controls.Add(this.labelControl2);
            this.groupBox2.Controls.Add(this.txt_Line_4);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.TabStop = false;
            // 
            // chk_PrintBatchNo
            // 
            resources.ApplyResources(this.chk_PrintBatchNo, "chk_PrintBatchNo");
            this.chk_PrintBatchNo.MenuManager = this.barManager1;
            this.chk_PrintBatchNo.Name = "chk_PrintBatchNo";
            this.chk_PrintBatchNo.Properties.AccessibleDescription = resources.GetString("chk_PrintBatchNo.Properties.AccessibleDescription");
            this.chk_PrintBatchNo.Properties.AccessibleName = resources.GetString("chk_PrintBatchNo.Properties.AccessibleName");
            this.chk_PrintBatchNo.Properties.AutoHeight = ((bool)(resources.GetObject("chk_PrintBatchNo.Properties.AutoHeight")));
            this.chk_PrintBatchNo.Properties.Caption = resources.GetString("chk_PrintBatchNo.Properties.Caption");
            this.chk_PrintBatchNo.Properties.DisplayValueChecked = resources.GetString("chk_PrintBatchNo.Properties.DisplayValueChecked");
            this.chk_PrintBatchNo.Properties.DisplayValueGrayed = resources.GetString("chk_PrintBatchNo.Properties.DisplayValueGrayed");
            this.chk_PrintBatchNo.Properties.DisplayValueUnchecked = resources.GetString("chk_PrintBatchNo.Properties.DisplayValueUnchecked");
            // 
            // chk_ShowBacrodeText
            // 
            resources.ApplyResources(this.chk_ShowBacrodeText, "chk_ShowBacrodeText");
            this.chk_ShowBacrodeText.MenuManager = this.barManager1;
            this.chk_ShowBacrodeText.Name = "chk_ShowBacrodeText";
            this.chk_ShowBacrodeText.Properties.AccessibleDescription = resources.GetString("chk_ShowBacrodeText.Properties.AccessibleDescription");
            this.chk_ShowBacrodeText.Properties.AccessibleName = resources.GetString("chk_ShowBacrodeText.Properties.AccessibleName");
            this.chk_ShowBacrodeText.Properties.AutoHeight = ((bool)(resources.GetObject("chk_ShowBacrodeText.Properties.AutoHeight")));
            this.chk_ShowBacrodeText.Properties.Caption = resources.GetString("chk_ShowBacrodeText.Properties.Caption");
            this.chk_ShowBacrodeText.Properties.DisplayValueChecked = resources.GetString("chk_ShowBacrodeText.Properties.DisplayValueChecked");
            this.chk_ShowBacrodeText.Properties.DisplayValueGrayed = resources.GetString("chk_ShowBacrodeText.Properties.DisplayValueGrayed");
            this.chk_ShowBacrodeText.Properties.DisplayValueUnchecked = resources.GetString("chk_ShowBacrodeText.Properties.DisplayValueUnchecked");
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl11.Appearance.Font")));
            this.labelControl11.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl11.Appearance.FontSizeDelta")));
            this.labelControl11.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl11.Appearance.FontStyleDelta")));
            this.labelControl11.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl11.Appearance.GradientMode")));
            this.labelControl11.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl11.Appearance.Image")));
            this.labelControl11.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl11.Name = "labelControl11";
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl10.Appearance.Font")));
            this.labelControl10.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl10.Appearance.FontSizeDelta")));
            this.labelControl10.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl10.Appearance.FontStyleDelta")));
            this.labelControl10.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl10.Appearance.GradientMode")));
            this.labelControl10.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl10.Appearance.Image")));
            this.labelControl10.Name = "labelControl10";
            // 
            // chk_3
            // 
            resources.ApplyResources(this.chk_3, "chk_3");
            this.chk_3.Name = "chk_3";
            this.chk_3.Properties.AccessibleDescription = resources.GetString("chk_3.Properties.AccessibleDescription");
            this.chk_3.Properties.AccessibleName = resources.GetString("chk_3.Properties.AccessibleName");
            this.chk_3.Properties.AutoHeight = ((bool)(resources.GetObject("chk_3.Properties.AutoHeight")));
            this.chk_3.Properties.Caption = resources.GetString("chk_3.Properties.Caption");
            this.chk_3.Properties.DisplayValueChecked = resources.GetString("chk_3.Properties.DisplayValueChecked");
            this.chk_3.Properties.DisplayValueGrayed = resources.GetString("chk_3.Properties.DisplayValueGrayed");
            this.chk_3.Properties.DisplayValueUnchecked = resources.GetString("chk_3.Properties.DisplayValueUnchecked");
            this.chk_3.CheckedChanged += new System.EventHandler(this.chk_3_CheckedChanged);
            this.chk_3.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // chk_2
            // 
            resources.ApplyResources(this.chk_2, "chk_2");
            this.chk_2.Name = "chk_2";
            this.chk_2.Properties.AccessibleDescription = resources.GetString("chk_2.Properties.AccessibleDescription");
            this.chk_2.Properties.AccessibleName = resources.GetString("chk_2.Properties.AccessibleName");
            this.chk_2.Properties.AutoHeight = ((bool)(resources.GetObject("chk_2.Properties.AutoHeight")));
            this.chk_2.Properties.Caption = resources.GetString("chk_2.Properties.Caption");
            this.chk_2.Properties.DisplayValueChecked = resources.GetString("chk_2.Properties.DisplayValueChecked");
            this.chk_2.Properties.DisplayValueGrayed = resources.GetString("chk_2.Properties.DisplayValueGrayed");
            this.chk_2.Properties.DisplayValueUnchecked = resources.GetString("chk_2.Properties.DisplayValueUnchecked");
            this.chk_2.CheckedChanged += new System.EventHandler(this.chk_2_CheckedChanged);
            this.chk_2.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // chk_4
            // 
            resources.ApplyResources(this.chk_4, "chk_4");
            this.chk_4.Name = "chk_4";
            this.chk_4.Properties.AccessibleDescription = resources.GetString("chk_4.Properties.AccessibleDescription");
            this.chk_4.Properties.AccessibleName = resources.GetString("chk_4.Properties.AccessibleName");
            this.chk_4.Properties.AutoHeight = ((bool)(resources.GetObject("chk_4.Properties.AutoHeight")));
            this.chk_4.Properties.Caption = resources.GetString("chk_4.Properties.Caption");
            this.chk_4.Properties.DisplayValueChecked = resources.GetString("chk_4.Properties.DisplayValueChecked");
            this.chk_4.Properties.DisplayValueGrayed = resources.GetString("chk_4.Properties.DisplayValueGrayed");
            this.chk_4.Properties.DisplayValueUnchecked = resources.GetString("chk_4.Properties.DisplayValueUnchecked");
            this.chk_4.CheckedChanged += new System.EventHandler(this.chk_4_CheckedChanged);
            this.chk_4.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // chk_1
            // 
            resources.ApplyResources(this.chk_1, "chk_1");
            this.chk_1.MenuManager = this.barManager1;
            this.chk_1.Name = "chk_1";
            this.chk_1.Properties.AccessibleDescription = resources.GetString("chk_1.Properties.AccessibleDescription");
            this.chk_1.Properties.AccessibleName = resources.GetString("chk_1.Properties.AccessibleName");
            this.chk_1.Properties.AutoHeight = ((bool)(resources.GetObject("chk_1.Properties.AutoHeight")));
            this.chk_1.Properties.Caption = resources.GetString("chk_1.Properties.Caption");
            this.chk_1.Properties.DisplayValueChecked = resources.GetString("chk_1.Properties.DisplayValueChecked");
            this.chk_1.Properties.DisplayValueGrayed = resources.GetString("chk_1.Properties.DisplayValueGrayed");
            this.chk_1.Properties.DisplayValueUnchecked = resources.GetString("chk_1.Properties.DisplayValueUnchecked");
            this.chk_1.CheckedChanged += new System.EventHandler(this.chk_1_CheckedChanged);
            this.chk_1.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // spn_1
            // 
            resources.ApplyResources(this.spn_1, "spn_1");
            this.spn_1.EnterMoveNextControl = true;
            this.spn_1.Name = "spn_1";
            this.spn_1.Properties.AccessibleDescription = resources.GetString("spn_1.Properties.AccessibleDescription");
            this.spn_1.Properties.AccessibleName = resources.GetString("spn_1.Properties.AccessibleName");
            this.spn_1.Properties.AutoHeight = ((bool)(resources.GetObject("spn_1.Properties.AutoHeight")));
            this.spn_1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spn_1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("spn_1.Properties.Mask.AutoComplete")));
            this.spn_1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("spn_1.Properties.Mask.BeepOnError")));
            this.spn_1.Properties.Mask.EditMask = resources.GetString("spn_1.Properties.Mask.EditMask");
            this.spn_1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("spn_1.Properties.Mask.IgnoreMaskBlank")));
            this.spn_1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("spn_1.Properties.Mask.MaskType")));
            this.spn_1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("spn_1.Properties.Mask.PlaceHolder")));
            this.spn_1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("spn_1.Properties.Mask.SaveLiteral")));
            this.spn_1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("spn_1.Properties.Mask.ShowPlaceHolders")));
            this.spn_1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("spn_1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.spn_1.Properties.NullValuePrompt = resources.GetString("spn_1.Properties.NullValuePrompt");
            this.spn_1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("spn_1.Properties.NullValuePromptShowForEmptyValue")));
            this.spn_1.EditValueChanged += new System.EventHandler(this.spn_1_EditValueChanged);
            this.spn_1.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // spn_4
            // 
            resources.ApplyResources(this.spn_4, "spn_4");
            this.spn_4.EnterMoveNextControl = true;
            this.spn_4.Name = "spn_4";
            this.spn_4.Properties.AccessibleDescription = resources.GetString("spn_4.Properties.AccessibleDescription");
            this.spn_4.Properties.AccessibleName = resources.GetString("spn_4.Properties.AccessibleName");
            this.spn_4.Properties.AutoHeight = ((bool)(resources.GetObject("spn_4.Properties.AutoHeight")));
            this.spn_4.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spn_4.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("spn_4.Properties.Mask.AutoComplete")));
            this.spn_4.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("spn_4.Properties.Mask.BeepOnError")));
            this.spn_4.Properties.Mask.EditMask = resources.GetString("spn_4.Properties.Mask.EditMask");
            this.spn_4.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("spn_4.Properties.Mask.IgnoreMaskBlank")));
            this.spn_4.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("spn_4.Properties.Mask.MaskType")));
            this.spn_4.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("spn_4.Properties.Mask.PlaceHolder")));
            this.spn_4.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("spn_4.Properties.Mask.SaveLiteral")));
            this.spn_4.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("spn_4.Properties.Mask.ShowPlaceHolders")));
            this.spn_4.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("spn_4.Properties.Mask.UseMaskAsDisplayFormat")));
            this.spn_4.Properties.NullValuePrompt = resources.GetString("spn_4.Properties.NullValuePrompt");
            this.spn_4.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("spn_4.Properties.NullValuePromptShowForEmptyValue")));
            this.spn_4.EditValueChanged += new System.EventHandler(this.spn_4_EditValueChanged);
            this.spn_4.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // spn_3
            // 
            resources.ApplyResources(this.spn_3, "spn_3");
            this.spn_3.EnterMoveNextControl = true;
            this.spn_3.Name = "spn_3";
            this.spn_3.Properties.AccessibleDescription = resources.GetString("spn_3.Properties.AccessibleDescription");
            this.spn_3.Properties.AccessibleName = resources.GetString("spn_3.Properties.AccessibleName");
            this.spn_3.Properties.AutoHeight = ((bool)(resources.GetObject("spn_3.Properties.AutoHeight")));
            this.spn_3.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spn_3.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("spn_3.Properties.Mask.AutoComplete")));
            this.spn_3.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("spn_3.Properties.Mask.BeepOnError")));
            this.spn_3.Properties.Mask.EditMask = resources.GetString("spn_3.Properties.Mask.EditMask");
            this.spn_3.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("spn_3.Properties.Mask.IgnoreMaskBlank")));
            this.spn_3.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("spn_3.Properties.Mask.MaskType")));
            this.spn_3.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("spn_3.Properties.Mask.PlaceHolder")));
            this.spn_3.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("spn_3.Properties.Mask.SaveLiteral")));
            this.spn_3.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("spn_3.Properties.Mask.ShowPlaceHolders")));
            this.spn_3.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("spn_3.Properties.Mask.UseMaskAsDisplayFormat")));
            this.spn_3.Properties.NullValuePrompt = resources.GetString("spn_3.Properties.NullValuePrompt");
            this.spn_3.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("spn_3.Properties.NullValuePromptShowForEmptyValue")));
            this.spn_3.EditValueChanged += new System.EventHandler(this.spn_3_EditValueChanged);
            this.spn_3.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // spn_2
            // 
            resources.ApplyResources(this.spn_2, "spn_2");
            this.spn_2.EnterMoveNextControl = true;
            this.spn_2.MenuManager = this.barManager1;
            this.spn_2.Name = "spn_2";
            this.spn_2.Properties.AccessibleDescription = resources.GetString("spn_2.Properties.AccessibleDescription");
            this.spn_2.Properties.AccessibleName = resources.GetString("spn_2.Properties.AccessibleName");
            this.spn_2.Properties.AutoHeight = ((bool)(resources.GetObject("spn_2.Properties.AutoHeight")));
            this.spn_2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spn_2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("spn_2.Properties.Mask.AutoComplete")));
            this.spn_2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("spn_2.Properties.Mask.BeepOnError")));
            this.spn_2.Properties.Mask.EditMask = resources.GetString("spn_2.Properties.Mask.EditMask");
            this.spn_2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("spn_2.Properties.Mask.IgnoreMaskBlank")));
            this.spn_2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("spn_2.Properties.Mask.MaskType")));
            this.spn_2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("spn_2.Properties.Mask.PlaceHolder")));
            this.spn_2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("spn_2.Properties.Mask.SaveLiteral")));
            this.spn_2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("spn_2.Properties.Mask.ShowPlaceHolders")));
            this.spn_2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("spn_2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.spn_2.Properties.NullValuePrompt = resources.GetString("spn_2.Properties.NullValuePrompt");
            this.spn_2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("spn_2.Properties.NullValuePromptShowForEmptyValue")));
            this.spn_2.EditValueChanged += new System.EventHandler(this.spn_2_EditValueChanged);
            this.spn_2.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // chklst_Line3
            // 
            resources.ApplyResources(this.chklst_Line3, "chklst_Line3");
            this.chklst_Line3.EnterMoveNextControl = true;
            this.chklst_Line3.MenuManager = this.barManager1;
            this.chklst_Line3.Name = "chklst_Line3";
            this.chklst_Line3.Properties.AccessibleDescription = resources.GetString("chklst_Line3.Properties.AccessibleDescription");
            this.chklst_Line3.Properties.AccessibleName = resources.GetString("chklst_Line3.Properties.AccessibleName");
            this.chklst_Line3.Properties.AutoHeight = ((bool)(resources.GetObject("chklst_Line3.Properties.AutoHeight")));
            this.chklst_Line3.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("chklst_Line3.Properties.Buttons"))))});
            this.chklst_Line3.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("chklst_Line3.Properties.Mask.AutoComplete")));
            this.chklst_Line3.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("chklst_Line3.Properties.Mask.BeepOnError")));
            this.chklst_Line3.Properties.Mask.EditMask = resources.GetString("chklst_Line3.Properties.Mask.EditMask");
            this.chklst_Line3.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("chklst_Line3.Properties.Mask.IgnoreMaskBlank")));
            this.chklst_Line3.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("chklst_Line3.Properties.Mask.MaskType")));
            this.chklst_Line3.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("chklst_Line3.Properties.Mask.PlaceHolder")));
            this.chklst_Line3.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("chklst_Line3.Properties.Mask.SaveLiteral")));
            this.chklst_Line3.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("chklst_Line3.Properties.Mask.ShowPlaceHolders")));
            this.chklst_Line3.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("chklst_Line3.Properties.Mask.UseMaskAsDisplayFormat")));
            this.chklst_Line3.Properties.NullValuePrompt = resources.GetString("chklst_Line3.Properties.NullValuePrompt");
            this.chklst_Line3.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("chklst_Line3.Properties.NullValuePromptShowForEmptyValue")));
            this.chklst_Line3.EditValueChanged += new System.EventHandler(this.chklst_Line3_EditValueChanged);
            this.chklst_Line3.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // chklst_Line2
            // 
            resources.ApplyResources(this.chklst_Line2, "chklst_Line2");
            this.chklst_Line2.EnterMoveNextControl = true;
            this.chklst_Line2.MenuManager = this.barManager1;
            this.chklst_Line2.Name = "chklst_Line2";
            this.chklst_Line2.Properties.AccessibleDescription = resources.GetString("chklst_Line2.Properties.AccessibleDescription");
            this.chklst_Line2.Properties.AccessibleName = resources.GetString("chklst_Line2.Properties.AccessibleName");
            this.chklst_Line2.Properties.AutoHeight = ((bool)(resources.GetObject("chklst_Line2.Properties.AutoHeight")));
            this.chklst_Line2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("chklst_Line2.Properties.Buttons"))))});
            this.chklst_Line2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("chklst_Line2.Properties.Mask.AutoComplete")));
            this.chklst_Line2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("chklst_Line2.Properties.Mask.BeepOnError")));
            this.chklst_Line2.Properties.Mask.EditMask = resources.GetString("chklst_Line2.Properties.Mask.EditMask");
            this.chklst_Line2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("chklst_Line2.Properties.Mask.IgnoreMaskBlank")));
            this.chklst_Line2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("chklst_Line2.Properties.Mask.MaskType")));
            this.chklst_Line2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("chklst_Line2.Properties.Mask.PlaceHolder")));
            this.chklst_Line2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("chklst_Line2.Properties.Mask.SaveLiteral")));
            this.chklst_Line2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("chklst_Line2.Properties.Mask.ShowPlaceHolders")));
            this.chklst_Line2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("chklst_Line2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.chklst_Line2.Properties.NullValuePrompt = resources.GetString("chklst_Line2.Properties.NullValuePrompt");
            this.chklst_Line2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("chklst_Line2.Properties.NullValuePromptShowForEmptyValue")));
            this.chklst_Line2.EditValueChanged += new System.EventHandler(this.chklst_Line2_EditValueChanged);
            this.chklst_Line2.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Name = "labelControl7";
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // chklst_Line1
            // 
            resources.ApplyResources(this.chklst_Line1, "chklst_Line1");
            this.chklst_Line1.EnterMoveNextControl = true;
            this.chklst_Line1.MenuManager = this.barManager1;
            this.chklst_Line1.Name = "chklst_Line1";
            this.chklst_Line1.Properties.AccessibleDescription = resources.GetString("chklst_Line1.Properties.AccessibleDescription");
            this.chklst_Line1.Properties.AccessibleName = resources.GetString("chklst_Line1.Properties.AccessibleName");
            this.chklst_Line1.Properties.AutoHeight = ((bool)(resources.GetObject("chklst_Line1.Properties.AutoHeight")));
            this.chklst_Line1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("chklst_Line1.Properties.Buttons"))))});
            this.chklst_Line1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("chklst_Line1.Properties.Mask.AutoComplete")));
            this.chklst_Line1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("chklst_Line1.Properties.Mask.BeepOnError")));
            this.chklst_Line1.Properties.Mask.EditMask = resources.GetString("chklst_Line1.Properties.Mask.EditMask");
            this.chklst_Line1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("chklst_Line1.Properties.Mask.IgnoreMaskBlank")));
            this.chklst_Line1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("chklst_Line1.Properties.Mask.MaskType")));
            this.chklst_Line1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("chklst_Line1.Properties.Mask.PlaceHolder")));
            this.chklst_Line1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("chklst_Line1.Properties.Mask.SaveLiteral")));
            this.chklst_Line1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("chklst_Line1.Properties.Mask.ShowPlaceHolders")));
            this.chklst_Line1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("chklst_Line1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.chklst_Line1.Properties.NullValuePrompt = resources.GetString("chklst_Line1.Properties.NullValuePrompt");
            this.chklst_Line1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("chklst_Line1.Properties.NullValuePromptShowForEmptyValue")));
            this.chklst_Line1.EditValueChanged += new System.EventHandler(this.chklst_Line1_EditValueChanged);
            this.chklst_Line1.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // panel2
            // 
            resources.ApplyResources(this.panel2, "panel2");
            this.panel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panel2.Controls.Add(this.lbl_Line_1);
            this.panel2.Controls.Add(this.Lbl_Line_3);
            this.panel2.Controls.Add(this.lbl_Line_4);
            this.panel2.Controls.Add(this.lbl_line_2);
            this.panel2.Name = "panel2";
            // 
            // lbl_Line_1
            // 
            resources.ApplyResources(this.lbl_Line_1, "lbl_Line_1");
            this.lbl_Line_1.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("lbl_Line_1.Appearance.BackColor")));
            this.lbl_Line_1.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lbl_Line_1.Appearance.Font")));
            this.lbl_Line_1.Appearance.FontSizeDelta = ((int)(resources.GetObject("lbl_Line_1.Appearance.FontSizeDelta")));
            this.lbl_Line_1.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lbl_Line_1.Appearance.FontStyleDelta")));
            this.lbl_Line_1.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lbl_Line_1.Appearance.GradientMode")));
            this.lbl_Line_1.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lbl_Line_1.Appearance.Image")));
            this.lbl_Line_1.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lbl_Line_1.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.lbl_Line_1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.lbl_Line_1.Name = "lbl_Line_1";
            this.lbl_Line_1.Resize += new System.EventHandler(this.lbl_Line_1_Resize);
            // 
            // Lbl_Line_3
            // 
            resources.ApplyResources(this.Lbl_Line_3, "Lbl_Line_3");
            this.Lbl_Line_3.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("Lbl_Line_3.Appearance.BackColor")));
            this.Lbl_Line_3.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("Lbl_Line_3.Appearance.Font")));
            this.Lbl_Line_3.Appearance.FontSizeDelta = ((int)(resources.GetObject("Lbl_Line_3.Appearance.FontSizeDelta")));
            this.Lbl_Line_3.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("Lbl_Line_3.Appearance.FontStyleDelta")));
            this.Lbl_Line_3.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("Lbl_Line_3.Appearance.GradientMode")));
            this.Lbl_Line_3.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("Lbl_Line_3.Appearance.Image")));
            this.Lbl_Line_3.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.Lbl_Line_3.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.Lbl_Line_3.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.Lbl_Line_3.Name = "Lbl_Line_3";
            this.Lbl_Line_3.Resize += new System.EventHandler(this.lbl_Line_1_Resize);
            // 
            // lbl_Line_4
            // 
            resources.ApplyResources(this.lbl_Line_4, "lbl_Line_4");
            this.lbl_Line_4.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("lbl_Line_4.Appearance.BackColor")));
            this.lbl_Line_4.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lbl_Line_4.Appearance.Font")));
            this.lbl_Line_4.Appearance.FontSizeDelta = ((int)(resources.GetObject("lbl_Line_4.Appearance.FontSizeDelta")));
            this.lbl_Line_4.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lbl_Line_4.Appearance.FontStyleDelta")));
            this.lbl_Line_4.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lbl_Line_4.Appearance.GradientMode")));
            this.lbl_Line_4.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lbl_Line_4.Appearance.Image")));
            this.lbl_Line_4.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lbl_Line_4.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.lbl_Line_4.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.lbl_Line_4.Name = "lbl_Line_4";
            this.lbl_Line_4.Resize += new System.EventHandler(this.lbl_Line_1_Resize);
            // 
            // lbl_line_2
            // 
            resources.ApplyResources(this.lbl_line_2, "lbl_line_2");
            this.lbl_line_2.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("lbl_line_2.Appearance.BackColor")));
            this.lbl_line_2.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lbl_line_2.Appearance.Font")));
            this.lbl_line_2.Appearance.FontSizeDelta = ((int)(resources.GetObject("lbl_line_2.Appearance.FontSizeDelta")));
            this.lbl_line_2.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lbl_line_2.Appearance.FontStyleDelta")));
            this.lbl_line_2.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lbl_line_2.Appearance.GradientMode")));
            this.lbl_line_2.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lbl_line_2.Appearance.Image")));
            this.lbl_line_2.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lbl_line_2.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.lbl_line_2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.lbl_line_2.Name = "lbl_line_2";
            this.lbl_line_2.Resize += new System.EventHandler(this.lbl_Line_1_Resize);
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl9.Appearance.Font")));
            this.labelControl9.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl9.Appearance.FontSizeDelta")));
            this.labelControl9.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl9.Appearance.FontStyleDelta")));
            this.labelControl9.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl9.Appearance.GradientMode")));
            this.labelControl9.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl9.Appearance.Image")));
            this.labelControl9.Name = "labelControl9";
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl6.Appearance.Font")));
            this.labelControl6.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl6.Appearance.FontSizeDelta")));
            this.labelControl6.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl6.Appearance.FontStyleDelta")));
            this.labelControl6.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl6.Appearance.GradientMode")));
            this.labelControl6.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl6.Appearance.Image")));
            this.labelControl6.Name = "labelControl6";
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // txt_Line_4
            // 
            resources.ApplyResources(this.txt_Line_4, "txt_Line_4");
            this.txt_Line_4.EnterMoveNextControl = true;
            this.txt_Line_4.MenuManager = this.barManager1;
            this.txt_Line_4.Name = "txt_Line_4";
            this.txt_Line_4.Properties.AccessibleDescription = resources.GetString("txt_Line_4.Properties.AccessibleDescription");
            this.txt_Line_4.Properties.AccessibleName = resources.GetString("txt_Line_4.Properties.AccessibleName");
            this.txt_Line_4.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_Line_4.Properties.Appearance.FontSizeDelta")));
            this.txt_Line_4.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_Line_4.Properties.Appearance.FontStyleDelta")));
            this.txt_Line_4.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_Line_4.Properties.Appearance.GradientMode")));
            this.txt_Line_4.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_Line_4.Properties.Appearance.Image")));
            this.txt_Line_4.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Line_4.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Line_4.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Line_4.Properties.AutoHeight")));
            this.txt_Line_4.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Line_4.Properties.Mask.AutoComplete")));
            this.txt_Line_4.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Line_4.Properties.Mask.BeepOnError")));
            this.txt_Line_4.Properties.Mask.EditMask = resources.GetString("txt_Line_4.Properties.Mask.EditMask");
            this.txt_Line_4.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Line_4.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Line_4.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Line_4.Properties.Mask.MaskType")));
            this.txt_Line_4.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Line_4.Properties.Mask.PlaceHolder")));
            this.txt_Line_4.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Line_4.Properties.Mask.SaveLiteral")));
            this.txt_Line_4.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Line_4.Properties.Mask.ShowPlaceHolders")));
            this.txt_Line_4.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Line_4.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Line_4.Properties.NullValuePrompt = resources.GetString("txt_Line_4.Properties.NullValuePrompt");
            this.txt_Line_4.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Line_4.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Line_4.Properties.ReadOnly = true;
            // 
            // txt_TemplateName
            // 
            resources.ApplyResources(this.txt_TemplateName, "txt_TemplateName");
            this.txt_TemplateName.EnterMoveNextControl = true;
            this.txt_TemplateName.Name = "txt_TemplateName";
            this.txt_TemplateName.Properties.AccessibleDescription = resources.GetString("txt_TemplateName.Properties.AccessibleDescription");
            this.txt_TemplateName.Properties.AccessibleName = resources.GetString("txt_TemplateName.Properties.AccessibleName");
            this.txt_TemplateName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_TemplateName.Properties.Appearance.FontSizeDelta")));
            this.txt_TemplateName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_TemplateName.Properties.Appearance.FontStyleDelta")));
            this.txt_TemplateName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_TemplateName.Properties.Appearance.GradientMode")));
            this.txt_TemplateName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_TemplateName.Properties.Appearance.Image")));
            this.txt_TemplateName.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_TemplateName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_TemplateName.Properties.AutoHeight = ((bool)(resources.GetObject("txt_TemplateName.Properties.AutoHeight")));
            this.txt_TemplateName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_TemplateName.Properties.Mask.AutoComplete")));
            this.txt_TemplateName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_TemplateName.Properties.Mask.BeepOnError")));
            this.txt_TemplateName.Properties.Mask.EditMask = resources.GetString("txt_TemplateName.Properties.Mask.EditMask");
            this.txt_TemplateName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_TemplateName.Properties.Mask.IgnoreMaskBlank")));
            this.txt_TemplateName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_TemplateName.Properties.Mask.MaskType")));
            this.txt_TemplateName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_TemplateName.Properties.Mask.PlaceHolder")));
            this.txt_TemplateName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_TemplateName.Properties.Mask.SaveLiteral")));
            this.txt_TemplateName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_TemplateName.Properties.Mask.ShowPlaceHolders")));
            this.txt_TemplateName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_TemplateName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_TemplateName.Properties.NullValuePrompt = resources.GetString("txt_TemplateName.Properties.NullValuePrompt");
            this.txt_TemplateName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_TemplateName.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_TemplateName.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // barButtonItem8
            // 
            resources.ApplyResources(this.barButtonItem8, "barButtonItem8");
            this.barButtonItem8.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barButtonItem8.Id = 2;
            this.barButtonItem8.Name = "barButtonItem8";
            // 
            // chkIsDefaultTemplate
            // 
            resources.ApplyResources(this.chkIsDefaultTemplate, "chkIsDefaultTemplate");
            this.chkIsDefaultTemplate.MenuManager = this.barManager1;
            this.chkIsDefaultTemplate.Name = "chkIsDefaultTemplate";
            this.chkIsDefaultTemplate.Properties.AccessibleDescription = resources.GetString("chkIsDefaultTemplate.Properties.AccessibleDescription");
            this.chkIsDefaultTemplate.Properties.AccessibleName = resources.GetString("chkIsDefaultTemplate.Properties.AccessibleName");
            this.chkIsDefaultTemplate.Properties.AutoHeight = ((bool)(resources.GetObject("chkIsDefaultTemplate.Properties.AutoHeight")));
            this.chkIsDefaultTemplate.Properties.Caption = resources.GetString("chkIsDefaultTemplate.Properties.Caption");
            this.chkIsDefaultTemplate.Properties.DisplayValueChecked = resources.GetString("chkIsDefaultTemplate.Properties.DisplayValueChecked");
            this.chkIsDefaultTemplate.Properties.DisplayValueGrayed = resources.GetString("chkIsDefaultTemplate.Properties.DisplayValueGrayed");
            this.chkIsDefaultTemplate.Properties.DisplayValueUnchecked = resources.GetString("chkIsDefaultTemplate.Properties.DisplayValueUnchecked");
            this.chkIsDefaultTemplate.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chkIsDefaultTemplate.Properties.GlyphAlignment")));
            this.chkIsDefaultTemplate.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // groupBox3
            // 
            resources.ApplyResources(this.groupBox3, "groupBox3");
            this.groupBox3.Controls.Add(this.cb_Papers);
            this.groupBox3.Controls.Add(this.labelControl18);
            this.groupBox3.Controls.Add(this.txt_Paperwidth);
            this.groupBox3.Controls.Add(this.labelControl17);
            this.groupBox3.Controls.Add(this.labelControl16);
            this.groupBox3.Controls.Add(this.labelControl13);
            this.groupBox3.Controls.Add(this.labelControl14);
            this.groupBox3.Controls.Add(this.txt_Paperheight);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.TabStop = false;
            // 
            // cb_Papers
            // 
            resources.ApplyResources(this.cb_Papers, "cb_Papers");
            this.cb_Papers.EnterMoveNextControl = true;
            this.cb_Papers.MenuManager = this.barManager1;
            this.cb_Papers.Name = "cb_Papers";
            this.cb_Papers.Properties.AccessibleDescription = resources.GetString("cb_Papers.Properties.AccessibleDescription");
            this.cb_Papers.Properties.AccessibleName = resources.GetString("cb_Papers.Properties.AccessibleName");
            this.cb_Papers.Properties.AutoHeight = ((bool)(resources.GetObject("cb_Papers.Properties.AutoHeight")));
            this.cb_Papers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cb_Papers.Properties.Buttons"))))});
            this.cb_Papers.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cb_Papers.Properties.GlyphAlignment")));
            this.cb_Papers.Properties.NullValuePrompt = resources.GetString("cb_Papers.Properties.NullValuePrompt");
            this.cb_Papers.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cb_Papers.Properties.NullValuePromptShowForEmptyValue")));
            this.cb_Papers.Properties.PopupSizeable = true;
            this.cb_Papers.EditValueChanged += new System.EventHandler(this.cb_Papers_EditValueChanged);
            this.cb_Papers.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl18
            // 
            resources.ApplyResources(this.labelControl18, "labelControl18");
            this.labelControl18.Name = "labelControl18";
            // 
            // txt_Paperwidth
            // 
            resources.ApplyResources(this.txt_Paperwidth, "txt_Paperwidth");
            this.txt_Paperwidth.EnterMoveNextControl = true;
            this.txt_Paperwidth.Name = "txt_Paperwidth";
            this.txt_Paperwidth.Properties.AccessibleDescription = resources.GetString("txt_Paperwidth.Properties.AccessibleDescription");
            this.txt_Paperwidth.Properties.AccessibleName = resources.GetString("txt_Paperwidth.Properties.AccessibleName");
            this.txt_Paperwidth.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_Paperwidth.Properties.Appearance.FontSizeDelta")));
            this.txt_Paperwidth.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_Paperwidth.Properties.Appearance.FontStyleDelta")));
            this.txt_Paperwidth.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_Paperwidth.Properties.Appearance.GradientMode")));
            this.txt_Paperwidth.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_Paperwidth.Properties.Appearance.Image")));
            this.txt_Paperwidth.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Paperwidth.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Paperwidth.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Paperwidth.Properties.AutoHeight")));
            this.txt_Paperwidth.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Paperwidth.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_Paperwidth.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Paperwidth.Properties.Mask.AutoComplete")));
            this.txt_Paperwidth.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Paperwidth.Properties.Mask.BeepOnError")));
            this.txt_Paperwidth.Properties.Mask.EditMask = resources.GetString("txt_Paperwidth.Properties.Mask.EditMask");
            this.txt_Paperwidth.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Paperwidth.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Paperwidth.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Paperwidth.Properties.Mask.MaskType")));
            this.txt_Paperwidth.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Paperwidth.Properties.Mask.PlaceHolder")));
            this.txt_Paperwidth.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Paperwidth.Properties.Mask.SaveLiteral")));
            this.txt_Paperwidth.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Paperwidth.Properties.Mask.ShowPlaceHolders")));
            this.txt_Paperwidth.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Paperwidth.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Paperwidth.Properties.NullValuePrompt = resources.GetString("txt_Paperwidth.Properties.NullValuePrompt");
            this.txt_Paperwidth.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Paperwidth.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Paperwidth.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Name = "labelControl17";
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Name = "labelControl16";
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Name = "labelControl13";
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Name = "labelControl14";
            // 
            // txt_Paperheight
            // 
            resources.ApplyResources(this.txt_Paperheight, "txt_Paperheight");
            this.txt_Paperheight.EnterMoveNextControl = true;
            this.txt_Paperheight.Name = "txt_Paperheight";
            this.txt_Paperheight.Properties.AccessibleDescription = resources.GetString("txt_Paperheight.Properties.AccessibleDescription");
            this.txt_Paperheight.Properties.AccessibleName = resources.GetString("txt_Paperheight.Properties.AccessibleName");
            this.txt_Paperheight.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_Paperheight.Properties.Appearance.FontSizeDelta")));
            this.txt_Paperheight.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_Paperheight.Properties.Appearance.FontStyleDelta")));
            this.txt_Paperheight.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_Paperheight.Properties.Appearance.GradientMode")));
            this.txt_Paperheight.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_Paperheight.Properties.Appearance.Image")));
            this.txt_Paperheight.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Paperheight.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Paperheight.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Paperheight.Properties.AutoHeight")));
            this.txt_Paperheight.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Paperheight.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_Paperheight.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Paperheight.Properties.Mask.AutoComplete")));
            this.txt_Paperheight.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Paperheight.Properties.Mask.BeepOnError")));
            this.txt_Paperheight.Properties.Mask.EditMask = resources.GetString("txt_Paperheight.Properties.Mask.EditMask");
            this.txt_Paperheight.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Paperheight.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Paperheight.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Paperheight.Properties.Mask.MaskType")));
            this.txt_Paperheight.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Paperheight.Properties.Mask.PlaceHolder")));
            this.txt_Paperheight.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Paperheight.Properties.Mask.SaveLiteral")));
            this.txt_Paperheight.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Paperheight.Properties.Mask.ShowPlaceHolders")));
            this.txt_Paperheight.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Paperheight.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Paperheight.Properties.NullValuePrompt = resources.GetString("txt_Paperheight.Properties.NullValuePrompt");
            this.txt_Paperheight.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Paperheight.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Paperheight.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl36
            // 
            resources.ApplyResources(this.labelControl36, "labelControl36");
            this.labelControl36.Name = "labelControl36";
            // 
            // btnNext
            // 
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            this.btnNext.Name = "btnNext";
            this.btnNext.TabStop = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnPrev
            // 
            resources.ApplyResources(this.btnPrev, "btnPrev");
            this.btnPrev.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrev.Image = global::Pharmacy.Properties.Resources.prev32;
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.TabStop = false;
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // groupBox4
            // 
            resources.ApplyResources(this.groupBox4, "groupBox4");
            this.groupBox4.Controls.Add(this.labelControl25);
            this.groupBox4.Controls.Add(this.txt_MarginLeft);
            this.groupBox4.Controls.Add(this.labelControl24);
            this.groupBox4.Controls.Add(this.txt_MarginRight);
            this.groupBox4.Controls.Add(this.labelControl23);
            this.groupBox4.Controls.Add(this.txt_MarginBottom);
            this.groupBox4.Controls.Add(this.labelControl22);
            this.groupBox4.Controls.Add(this.txt_MarginTop);
            this.groupBox4.Controls.Add(this.labelControl21);
            this.groupBox4.Controls.Add(this.labelControl20);
            this.groupBox4.Controls.Add(this.labelControl12);
            this.groupBox4.Controls.Add(this.labelControl19);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.TabStop = false;
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Name = "labelControl25";
            // 
            // txt_MarginLeft
            // 
            resources.ApplyResources(this.txt_MarginLeft, "txt_MarginLeft");
            this.txt_MarginLeft.EnterMoveNextControl = true;
            this.txt_MarginLeft.MenuManager = this.barManager1;
            this.txt_MarginLeft.Name = "txt_MarginLeft";
            this.txt_MarginLeft.Properties.AccessibleDescription = resources.GetString("txt_MarginLeft.Properties.AccessibleDescription");
            this.txt_MarginLeft.Properties.AccessibleName = resources.GetString("txt_MarginLeft.Properties.AccessibleName");
            this.txt_MarginLeft.Properties.AutoHeight = ((bool)(resources.GetObject("txt_MarginLeft.Properties.AutoHeight")));
            this.txt_MarginLeft.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_MarginLeft.Properties.IsFloatValue = false;
            this.txt_MarginLeft.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_MarginLeft.Properties.Mask.AutoComplete")));
            this.txt_MarginLeft.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_MarginLeft.Properties.Mask.BeepOnError")));
            this.txt_MarginLeft.Properties.Mask.EditMask = resources.GetString("txt_MarginLeft.Properties.Mask.EditMask");
            this.txt_MarginLeft.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_MarginLeft.Properties.Mask.IgnoreMaskBlank")));
            this.txt_MarginLeft.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_MarginLeft.Properties.Mask.MaskType")));
            this.txt_MarginLeft.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_MarginLeft.Properties.Mask.PlaceHolder")));
            this.txt_MarginLeft.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_MarginLeft.Properties.Mask.SaveLiteral")));
            this.txt_MarginLeft.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_MarginLeft.Properties.Mask.ShowPlaceHolders")));
            this.txt_MarginLeft.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_MarginLeft.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_MarginLeft.Properties.NullValuePrompt = resources.GetString("txt_MarginLeft.Properties.NullValuePrompt");
            this.txt_MarginLeft.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_MarginLeft.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_MarginLeft.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl24
            // 
            resources.ApplyResources(this.labelControl24, "labelControl24");
            this.labelControl24.Name = "labelControl24";
            // 
            // txt_MarginRight
            // 
            resources.ApplyResources(this.txt_MarginRight, "txt_MarginRight");
            this.txt_MarginRight.EnterMoveNextControl = true;
            this.txt_MarginRight.MenuManager = this.barManager1;
            this.txt_MarginRight.Name = "txt_MarginRight";
            this.txt_MarginRight.Properties.AccessibleDescription = resources.GetString("txt_MarginRight.Properties.AccessibleDescription");
            this.txt_MarginRight.Properties.AccessibleName = resources.GetString("txt_MarginRight.Properties.AccessibleName");
            this.txt_MarginRight.Properties.AutoHeight = ((bool)(resources.GetObject("txt_MarginRight.Properties.AutoHeight")));
            this.txt_MarginRight.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_MarginRight.Properties.IsFloatValue = false;
            this.txt_MarginRight.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_MarginRight.Properties.Mask.AutoComplete")));
            this.txt_MarginRight.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_MarginRight.Properties.Mask.BeepOnError")));
            this.txt_MarginRight.Properties.Mask.EditMask = resources.GetString("txt_MarginRight.Properties.Mask.EditMask");
            this.txt_MarginRight.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_MarginRight.Properties.Mask.IgnoreMaskBlank")));
            this.txt_MarginRight.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_MarginRight.Properties.Mask.MaskType")));
            this.txt_MarginRight.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_MarginRight.Properties.Mask.PlaceHolder")));
            this.txt_MarginRight.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_MarginRight.Properties.Mask.SaveLiteral")));
            this.txt_MarginRight.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_MarginRight.Properties.Mask.ShowPlaceHolders")));
            this.txt_MarginRight.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_MarginRight.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_MarginRight.Properties.NullValuePrompt = resources.GetString("txt_MarginRight.Properties.NullValuePrompt");
            this.txt_MarginRight.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_MarginRight.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_MarginRight.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl23
            // 
            resources.ApplyResources(this.labelControl23, "labelControl23");
            this.labelControl23.Name = "labelControl23";
            // 
            // txt_MarginBottom
            // 
            resources.ApplyResources(this.txt_MarginBottom, "txt_MarginBottom");
            this.txt_MarginBottom.EnterMoveNextControl = true;
            this.txt_MarginBottom.MenuManager = this.barManager1;
            this.txt_MarginBottom.Name = "txt_MarginBottom";
            this.txt_MarginBottom.Properties.AccessibleDescription = resources.GetString("txt_MarginBottom.Properties.AccessibleDescription");
            this.txt_MarginBottom.Properties.AccessibleName = resources.GetString("txt_MarginBottom.Properties.AccessibleName");
            this.txt_MarginBottom.Properties.AutoHeight = ((bool)(resources.GetObject("txt_MarginBottom.Properties.AutoHeight")));
            this.txt_MarginBottom.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_MarginBottom.Properties.IsFloatValue = false;
            this.txt_MarginBottom.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_MarginBottom.Properties.Mask.AutoComplete")));
            this.txt_MarginBottom.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_MarginBottom.Properties.Mask.BeepOnError")));
            this.txt_MarginBottom.Properties.Mask.EditMask = resources.GetString("txt_MarginBottom.Properties.Mask.EditMask");
            this.txt_MarginBottom.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_MarginBottom.Properties.Mask.IgnoreMaskBlank")));
            this.txt_MarginBottom.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_MarginBottom.Properties.Mask.MaskType")));
            this.txt_MarginBottom.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_MarginBottom.Properties.Mask.PlaceHolder")));
            this.txt_MarginBottom.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_MarginBottom.Properties.Mask.SaveLiteral")));
            this.txt_MarginBottom.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_MarginBottom.Properties.Mask.ShowPlaceHolders")));
            this.txt_MarginBottom.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_MarginBottom.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_MarginBottom.Properties.NullValuePrompt = resources.GetString("txt_MarginBottom.Properties.NullValuePrompt");
            this.txt_MarginBottom.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_MarginBottom.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_MarginBottom.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Name = "labelControl22";
            // 
            // txt_MarginTop
            // 
            resources.ApplyResources(this.txt_MarginTop, "txt_MarginTop");
            this.txt_MarginTop.EnterMoveNextControl = true;
            this.txt_MarginTop.MenuManager = this.barManager1;
            this.txt_MarginTop.Name = "txt_MarginTop";
            this.txt_MarginTop.Properties.AccessibleDescription = resources.GetString("txt_MarginTop.Properties.AccessibleDescription");
            this.txt_MarginTop.Properties.AccessibleName = resources.GetString("txt_MarginTop.Properties.AccessibleName");
            this.txt_MarginTop.Properties.AutoHeight = ((bool)(resources.GetObject("txt_MarginTop.Properties.AutoHeight")));
            this.txt_MarginTop.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_MarginTop.Properties.IsFloatValue = false;
            this.txt_MarginTop.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_MarginTop.Properties.Mask.AutoComplete")));
            this.txt_MarginTop.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_MarginTop.Properties.Mask.BeepOnError")));
            this.txt_MarginTop.Properties.Mask.EditMask = resources.GetString("txt_MarginTop.Properties.Mask.EditMask");
            this.txt_MarginTop.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_MarginTop.Properties.Mask.IgnoreMaskBlank")));
            this.txt_MarginTop.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_MarginTop.Properties.Mask.MaskType")));
            this.txt_MarginTop.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_MarginTop.Properties.Mask.PlaceHolder")));
            this.txt_MarginTop.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_MarginTop.Properties.Mask.SaveLiteral")));
            this.txt_MarginTop.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_MarginTop.Properties.Mask.ShowPlaceHolders")));
            this.txt_MarginTop.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_MarginTop.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_MarginTop.Properties.NullValuePrompt = resources.GetString("txt_MarginTop.Properties.NullValuePrompt");
            this.txt_MarginTop.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_MarginTop.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_MarginTop.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl21
            // 
            resources.ApplyResources(this.labelControl21, "labelControl21");
            this.labelControl21.Name = "labelControl21";
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Name = "labelControl20";
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Name = "labelControl12";
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Name = "labelControl19";
            // 
            // groupBox5
            // 
            resources.ApplyResources(this.groupBox5, "groupBox5");
            this.groupBox5.Controls.Add(this.labelControl26);
            this.groupBox5.Controls.Add(this.cb_Fonts);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.TabStop = false;
            // 
            // labelControl26
            // 
            resources.ApplyResources(this.labelControl26, "labelControl26");
            this.labelControl26.Name = "labelControl26";
            // 
            // cb_Fonts
            // 
            resources.ApplyResources(this.cb_Fonts, "cb_Fonts");
            this.cb_Fonts.MenuManager = this.barManager1;
            this.cb_Fonts.Name = "cb_Fonts";
            this.cb_Fonts.Properties.AccessibleDescription = resources.GetString("cb_Fonts.Properties.AccessibleDescription");
            this.cb_Fonts.Properties.AccessibleName = resources.GetString("cb_Fonts.Properties.AccessibleName");
            this.cb_Fonts.Properties.AutoHeight = ((bool)(resources.GetObject("cb_Fonts.Properties.AutoHeight")));
            this.cb_Fonts.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cb_Fonts.Properties.Buttons"))))});
            this.cb_Fonts.Properties.NullValuePrompt = resources.GetString("cb_Fonts.Properties.NullValuePrompt");
            this.cb_Fonts.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cb_Fonts.Properties.NullValuePromptShowForEmptyValue")));
            this.cb_Fonts.TabStop = false;
            this.cb_Fonts.SelectedIndexChanged += new System.EventHandler(this.cb_Fonts_SelectedIndexChanged);
            // 
            // txt_BatchPrefix
            // 
            resources.ApplyResources(this.txt_BatchPrefix, "txt_BatchPrefix");
            this.txt_BatchPrefix.EnterMoveNextControl = true;
            this.txt_BatchPrefix.Name = "txt_BatchPrefix";
            this.txt_BatchPrefix.Properties.AccessibleDescription = resources.GetString("txt_BatchPrefix.Properties.AccessibleDescription");
            this.txt_BatchPrefix.Properties.AccessibleName = resources.GetString("txt_BatchPrefix.Properties.AccessibleName");
            this.txt_BatchPrefix.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_BatchPrefix.Properties.Appearance.FontSizeDelta")));
            this.txt_BatchPrefix.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_BatchPrefix.Properties.Appearance.FontStyleDelta")));
            this.txt_BatchPrefix.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_BatchPrefix.Properties.Appearance.GradientMode")));
            this.txt_BatchPrefix.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_BatchPrefix.Properties.Appearance.Image")));
            this.txt_BatchPrefix.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_BatchPrefix.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_BatchPrefix.Properties.AutoHeight = ((bool)(resources.GetObject("txt_BatchPrefix.Properties.AutoHeight")));
            this.txt_BatchPrefix.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_BatchPrefix.Properties.Mask.AutoComplete")));
            this.txt_BatchPrefix.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_BatchPrefix.Properties.Mask.BeepOnError")));
            this.txt_BatchPrefix.Properties.Mask.EditMask = resources.GetString("txt_BatchPrefix.Properties.Mask.EditMask");
            this.txt_BatchPrefix.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_BatchPrefix.Properties.Mask.IgnoreMaskBlank")));
            this.txt_BatchPrefix.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_BatchPrefix.Properties.Mask.MaskType")));
            this.txt_BatchPrefix.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_BatchPrefix.Properties.Mask.PlaceHolder")));
            this.txt_BatchPrefix.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_BatchPrefix.Properties.Mask.SaveLiteral")));
            this.txt_BatchPrefix.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_BatchPrefix.Properties.Mask.ShowPlaceHolders")));
            this.txt_BatchPrefix.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_BatchPrefix.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_BatchPrefix.Properties.NullValuePrompt = resources.GetString("txt_BatchPrefix.Properties.NullValuePrompt");
            this.txt_BatchPrefix.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_BatchPrefix.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_BatchPrefix.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl27
            // 
            resources.ApplyResources(this.labelControl27, "labelControl27");
            this.labelControl27.Name = "labelControl27";
            // 
            // txt_QtyPrefix
            // 
            resources.ApplyResources(this.txt_QtyPrefix, "txt_QtyPrefix");
            this.txt_QtyPrefix.EnterMoveNextControl = true;
            this.txt_QtyPrefix.Name = "txt_QtyPrefix";
            this.txt_QtyPrefix.Properties.AccessibleDescription = resources.GetString("txt_QtyPrefix.Properties.AccessibleDescription");
            this.txt_QtyPrefix.Properties.AccessibleName = resources.GetString("txt_QtyPrefix.Properties.AccessibleName");
            this.txt_QtyPrefix.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_QtyPrefix.Properties.Appearance.FontSizeDelta")));
            this.txt_QtyPrefix.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_QtyPrefix.Properties.Appearance.FontStyleDelta")));
            this.txt_QtyPrefix.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_QtyPrefix.Properties.Appearance.GradientMode")));
            this.txt_QtyPrefix.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_QtyPrefix.Properties.Appearance.Image")));
            this.txt_QtyPrefix.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_QtyPrefix.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_QtyPrefix.Properties.AutoHeight = ((bool)(resources.GetObject("txt_QtyPrefix.Properties.AutoHeight")));
            this.txt_QtyPrefix.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_QtyPrefix.Properties.Mask.AutoComplete")));
            this.txt_QtyPrefix.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_QtyPrefix.Properties.Mask.BeepOnError")));
            this.txt_QtyPrefix.Properties.Mask.EditMask = resources.GetString("txt_QtyPrefix.Properties.Mask.EditMask");
            this.txt_QtyPrefix.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_QtyPrefix.Properties.Mask.IgnoreMaskBlank")));
            this.txt_QtyPrefix.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_QtyPrefix.Properties.Mask.MaskType")));
            this.txt_QtyPrefix.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_QtyPrefix.Properties.Mask.PlaceHolder")));
            this.txt_QtyPrefix.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_QtyPrefix.Properties.Mask.SaveLiteral")));
            this.txt_QtyPrefix.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_QtyPrefix.Properties.Mask.ShowPlaceHolders")));
            this.txt_QtyPrefix.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_QtyPrefix.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_QtyPrefix.Properties.NullValuePrompt = resources.GetString("txt_QtyPrefix.Properties.NullValuePrompt");
            this.txt_QtyPrefix.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_QtyPrefix.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_QtyPrefix.EditValueChanged += new System.EventHandler(this.txt_QtyPrefix_EditValueChanged);
            this.txt_QtyPrefix.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl28
            // 
            resources.ApplyResources(this.labelControl28, "labelControl28");
            this.labelControl28.Name = "labelControl28";
            // 
            // frm_ST_Barcode
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.txt_QtyPrefix);
            this.Controls.Add(this.labelControl28);
            this.Controls.Add(this.txt_BatchPrefix);
            this.Controls.Add(this.labelControl27);
            this.Controls.Add(this.groupBox5);
            this.Controls.Add(this.groupBox4);
            this.Controls.Add(this.labelControl36);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrev);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.chkIsDefaultTemplate);
            this.Controls.Add(this.txt_TemplateName);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.txtCurrency);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_ST_Barcode";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_ST_Barcode_FormClosing);
            this.Load += new System.EventHandler(this.frm_ST_Barcode_Load);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.frm_ST_Barcode_KeyDown);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtRowsCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtColumnsCount.Properties)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_PrintBatchNo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_ShowBacrodeText.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_4.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_4.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spn_2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chklst_Line3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chklst_Line2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chklst_Line1.Properties)).EndInit();
            this.panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txt_Line_4.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TemplateName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsDefaultTemplate.Properties)).EndInit();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cb_Papers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Paperwidth.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Paperheight.Properties)).EndInit();
            this.groupBox4.ResumeLayout(false);
            this.groupBox4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MarginLeft.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MarginRight.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MarginBottom.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MarginTop.Properties)).EndInit();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cb_Fonts.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_BatchPrefix.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_QtyPrefix.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtn_Cancel;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.TextEdit txtCurrency;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraEditors.TextEdit txt_TemplateName;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.CheckedComboBoxEdit chklst_Line3;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.CheckedComboBoxEdit chklst_Line2;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.CheckedComboBoxEdit chklst_Line1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private System.Windows.Forms.Panel panel2;
        private DevExpress.XtraEditors.LabelControl Lbl_Line_3;
        private DevExpress.XtraEditors.LabelControl lbl_Line_4;
        private DevExpress.XtraEditors.LabelControl lbl_line_2;
        private DevExpress.XtraEditors.LabelControl lbl_Line_1;
        private DevExpress.XtraEditors.TextEdit txt_Line_4;
        private DevExpress.XtraEditors.SpinEdit spn_1;
        private DevExpress.XtraEditors.SpinEdit spn_4;
        private DevExpress.XtraEditors.SpinEdit spn_3;
        private DevExpress.XtraEditors.SpinEdit spn_2;
        private DevExpress.XtraEditors.CheckEdit chk_3;
        private DevExpress.XtraEditors.CheckEdit chk_2;
        private DevExpress.XtraEditors.CheckEdit chk_4;
        private DevExpress.XtraEditors.CheckEdit chk_1;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraBars.BarButtonItem barBtn_Preview;
        private DevExpress.XtraBars.BarButtonItem barButtonItem8;
        private DevExpress.XtraEditors.CheckEdit chkIsDefaultTemplate;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.SpinEdit txt_Paperheight;
        private DevExpress.XtraEditors.SpinEdit txt_Paperwidth;
        private DevExpress.XtraEditors.ImageComboBoxEdit cb_Papers;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrev;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.SpinEdit txt_MarginTop;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.SpinEdit txt_MarginLeft;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.SpinEdit txt_MarginRight;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.SpinEdit txt_MarginBottom;
        private DevExpress.XtraEditors.SpinEdit txtRowsCount;
        private DevExpress.XtraEditors.SpinEdit txtColumnsCount;
        private System.Windows.Forms.GroupBox groupBox5;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.FontEdit cb_Fonts;
        private DevExpress.XtraEditors.TextEdit txt_QtyPrefix;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.TextEdit txt_BatchPrefix;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.CheckEdit chk_ShowBacrodeText;
        private DevExpress.XtraEditors.CheckEdit chk_PrintBatchNo;
    }
}