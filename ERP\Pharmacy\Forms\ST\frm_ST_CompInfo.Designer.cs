﻿namespace Pharmacy.Forms
{
    partial class frm_ST_CompInfo
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_ST_CompInfo));
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtCompanyNameAr = new DevExpress.XtraEditors.TextEdit();
            this.txtCompanyNameEn = new DevExpress.XtraEditors.TextEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtAddress = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtCity = new DevExpress.XtraEditors.TextEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtCountry = new DevExpress.XtraEditors.TextEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.txtTel = new DevExpress.XtraEditors.TextEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.txtMobile = new DevExpress.XtraEditors.TextEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.txtCommercialBook = new DevExpress.XtraEditors.TextEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.txtTaxCard = new DevExpress.XtraEditors.TextEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.txtMngrAddress = new DevExpress.XtraEditors.TextEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.txtMngrName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.txtMngrMobile = new DevExpress.XtraEditors.TextEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.txtMngrTel = new DevExpress.XtraEditors.TextEdit();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Save = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.btnLoadLogo = new DevExpress.XtraEditors.SimpleButton();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.btnRemovePic = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.dtFiscalYearStartDate = new DevExpress.XtraEditors.DateEdit();
            this.dtFiscalYearEndDate = new DevExpress.XtraEditors.DateEdit();
            this.txtIban = new DevExpress.XtraEditors.TextEdit();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.txtActivityType = new DevExpress.XtraEditors.TextEdit();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameAr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameEn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAddress.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCity.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCountry.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMobile.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCommercialBook.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxCard.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMngrAddress.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMngrName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMngrMobile.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMngrTel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFiscalYearStartDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFiscalYearStartDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFiscalYearEndDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFiscalYearEndDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtIban.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtActivityType.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // txtCompanyNameAr
            // 
            resources.ApplyResources(this.txtCompanyNameAr, "txtCompanyNameAr");
            this.txtCompanyNameAr.EnterMoveNextControl = true;
            this.txtCompanyNameAr.Name = "txtCompanyNameAr";
            this.txtCompanyNameAr.Properties.AccessibleDescription = resources.GetString("txtCompanyNameAr.Properties.AccessibleDescription");
            this.txtCompanyNameAr.Properties.AccessibleName = resources.GetString("txtCompanyNameAr.Properties.AccessibleName");
            this.txtCompanyNameAr.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.FontSizeDelta")));
            this.txtCompanyNameAr.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.FontStyleDelta")));
            this.txtCompanyNameAr.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.GradientMode")));
            this.txtCompanyNameAr.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCompanyNameAr.Properties.Appearance.Image")));
            this.txtCompanyNameAr.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCompanyNameAr.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCompanyNameAr.Properties.AutoHeight = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.AutoHeight")));
            this.txtCompanyNameAr.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCompanyNameAr.Properties.Mask.AutoComplete")));
            this.txtCompanyNameAr.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.BeepOnError")));
            this.txtCompanyNameAr.Properties.Mask.EditMask = resources.GetString("txtCompanyNameAr.Properties.Mask.EditMask");
            this.txtCompanyNameAr.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.IgnoreMaskBlank")));
            this.txtCompanyNameAr.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCompanyNameAr.Properties.Mask.MaskType")));
            this.txtCompanyNameAr.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCompanyNameAr.Properties.Mask.PlaceHolder")));
            this.txtCompanyNameAr.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.SaveLiteral")));
            this.txtCompanyNameAr.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.ShowPlaceHolders")));
            this.txtCompanyNameAr.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCompanyNameAr.Properties.MaxLength = 50;
            this.txtCompanyNameAr.Properties.NullValuePrompt = resources.GetString("txtCompanyNameAr.Properties.NullValuePrompt");
            this.txtCompanyNameAr.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCompanyNameAr.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCompanyNameAr.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // txtCompanyNameEn
            // 
            resources.ApplyResources(this.txtCompanyNameEn, "txtCompanyNameEn");
            this.txtCompanyNameEn.EnterMoveNextControl = true;
            this.txtCompanyNameEn.Name = "txtCompanyNameEn";
            this.txtCompanyNameEn.Properties.AccessibleDescription = resources.GetString("txtCompanyNameEn.Properties.AccessibleDescription");
            this.txtCompanyNameEn.Properties.AccessibleName = resources.GetString("txtCompanyNameEn.Properties.AccessibleName");
            this.txtCompanyNameEn.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.FontSizeDelta")));
            this.txtCompanyNameEn.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.FontStyleDelta")));
            this.txtCompanyNameEn.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.GradientMode")));
            this.txtCompanyNameEn.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCompanyNameEn.Properties.Appearance.Image")));
            this.txtCompanyNameEn.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCompanyNameEn.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCompanyNameEn.Properties.AutoHeight = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.AutoHeight")));
            this.txtCompanyNameEn.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCompanyNameEn.Properties.Mask.AutoComplete")));
            this.txtCompanyNameEn.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.BeepOnError")));
            this.txtCompanyNameEn.Properties.Mask.EditMask = resources.GetString("txtCompanyNameEn.Properties.Mask.EditMask");
            this.txtCompanyNameEn.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.IgnoreMaskBlank")));
            this.txtCompanyNameEn.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCompanyNameEn.Properties.Mask.MaskType")));
            this.txtCompanyNameEn.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCompanyNameEn.Properties.Mask.PlaceHolder")));
            this.txtCompanyNameEn.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.SaveLiteral")));
            this.txtCompanyNameEn.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.ShowPlaceHolders")));
            this.txtCompanyNameEn.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCompanyNameEn.Properties.MaxLength = 50;
            this.txtCompanyNameEn.Properties.NullValuePrompt = resources.GetString("txtCompanyNameEn.Properties.NullValuePrompt");
            this.txtCompanyNameEn.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCompanyNameEn.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCompanyNameEn.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // txtAddress
            // 
            resources.ApplyResources(this.txtAddress, "txtAddress");
            this.txtAddress.EnterMoveNextControl = true;
            this.txtAddress.Name = "txtAddress";
            this.txtAddress.Properties.AccessibleDescription = resources.GetString("txtAddress.Properties.AccessibleDescription");
            this.txtAddress.Properties.AccessibleName = resources.GetString("txtAddress.Properties.AccessibleName");
            this.txtAddress.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtAddress.Properties.Appearance.FontSizeDelta")));
            this.txtAddress.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtAddress.Properties.Appearance.FontStyleDelta")));
            this.txtAddress.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtAddress.Properties.Appearance.GradientMode")));
            this.txtAddress.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtAddress.Properties.Appearance.Image")));
            this.txtAddress.Properties.Appearance.Options.UseTextOptions = true;
            this.txtAddress.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtAddress.Properties.AutoHeight = ((bool)(resources.GetObject("txtAddress.Properties.AutoHeight")));
            this.txtAddress.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtAddress.Properties.Mask.AutoComplete")));
            this.txtAddress.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtAddress.Properties.Mask.BeepOnError")));
            this.txtAddress.Properties.Mask.EditMask = resources.GetString("txtAddress.Properties.Mask.EditMask");
            this.txtAddress.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtAddress.Properties.Mask.IgnoreMaskBlank")));
            this.txtAddress.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtAddress.Properties.Mask.MaskType")));
            this.txtAddress.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtAddress.Properties.Mask.PlaceHolder")));
            this.txtAddress.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtAddress.Properties.Mask.SaveLiteral")));
            this.txtAddress.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtAddress.Properties.Mask.ShowPlaceHolders")));
            this.txtAddress.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtAddress.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtAddress.Properties.MaxLength = 50;
            this.txtAddress.Properties.NullValuePrompt = resources.GetString("txtAddress.Properties.NullValuePrompt");
            this.txtAddress.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtAddress.Properties.NullValuePromptShowForEmptyValue")));
            this.txtAddress.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // txtCity
            // 
            resources.ApplyResources(this.txtCity, "txtCity");
            this.txtCity.EnterMoveNextControl = true;
            this.txtCity.Name = "txtCity";
            this.txtCity.Properties.AccessibleDescription = resources.GetString("txtCity.Properties.AccessibleDescription");
            this.txtCity.Properties.AccessibleName = resources.GetString("txtCity.Properties.AccessibleName");
            this.txtCity.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCity.Properties.Appearance.FontSizeDelta")));
            this.txtCity.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCity.Properties.Appearance.FontStyleDelta")));
            this.txtCity.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCity.Properties.Appearance.GradientMode")));
            this.txtCity.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCity.Properties.Appearance.Image")));
            this.txtCity.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCity.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCity.Properties.AutoHeight = ((bool)(resources.GetObject("txtCity.Properties.AutoHeight")));
            this.txtCity.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCity.Properties.Mask.AutoComplete")));
            this.txtCity.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCity.Properties.Mask.BeepOnError")));
            this.txtCity.Properties.Mask.EditMask = resources.GetString("txtCity.Properties.Mask.EditMask");
            this.txtCity.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCity.Properties.Mask.IgnoreMaskBlank")));
            this.txtCity.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCity.Properties.Mask.MaskType")));
            this.txtCity.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCity.Properties.Mask.PlaceHolder")));
            this.txtCity.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCity.Properties.Mask.SaveLiteral")));
            this.txtCity.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCity.Properties.Mask.ShowPlaceHolders")));
            this.txtCity.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCity.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCity.Properties.MaxLength = 50;
            this.txtCity.Properties.NullValuePrompt = resources.GetString("txtCity.Properties.NullValuePrompt");
            this.txtCity.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCity.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCity.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // txtCountry
            // 
            resources.ApplyResources(this.txtCountry, "txtCountry");
            this.txtCountry.EnterMoveNextControl = true;
            this.txtCountry.Name = "txtCountry";
            this.txtCountry.Properties.AccessibleDescription = resources.GetString("txtCountry.Properties.AccessibleDescription");
            this.txtCountry.Properties.AccessibleName = resources.GetString("txtCountry.Properties.AccessibleName");
            this.txtCountry.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCountry.Properties.Appearance.FontSizeDelta")));
            this.txtCountry.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCountry.Properties.Appearance.FontStyleDelta")));
            this.txtCountry.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCountry.Properties.Appearance.GradientMode")));
            this.txtCountry.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCountry.Properties.Appearance.Image")));
            this.txtCountry.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCountry.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCountry.Properties.AutoHeight = ((bool)(resources.GetObject("txtCountry.Properties.AutoHeight")));
            this.txtCountry.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCountry.Properties.Mask.AutoComplete")));
            this.txtCountry.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCountry.Properties.Mask.BeepOnError")));
            this.txtCountry.Properties.Mask.EditMask = resources.GetString("txtCountry.Properties.Mask.EditMask");
            this.txtCountry.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCountry.Properties.Mask.IgnoreMaskBlank")));
            this.txtCountry.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCountry.Properties.Mask.MaskType")));
            this.txtCountry.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCountry.Properties.Mask.PlaceHolder")));
            this.txtCountry.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCountry.Properties.Mask.SaveLiteral")));
            this.txtCountry.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCountry.Properties.Mask.ShowPlaceHolders")));
            this.txtCountry.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCountry.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCountry.Properties.MaxLength = 50;
            this.txtCountry.Properties.NullValuePrompt = resources.GetString("txtCountry.Properties.NullValuePrompt");
            this.txtCountry.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCountry.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCountry.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // txtTel
            // 
            resources.ApplyResources(this.txtTel, "txtTel");
            this.txtTel.EnterMoveNextControl = true;
            this.txtTel.Name = "txtTel";
            this.txtTel.Properties.AccessibleDescription = resources.GetString("txtTel.Properties.AccessibleDescription");
            this.txtTel.Properties.AccessibleName = resources.GetString("txtTel.Properties.AccessibleName");
            this.txtTel.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtTel.Properties.Appearance.FontSizeDelta")));
            this.txtTel.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtTel.Properties.Appearance.FontStyleDelta")));
            this.txtTel.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtTel.Properties.Appearance.GradientMode")));
            this.txtTel.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtTel.Properties.Appearance.Image")));
            this.txtTel.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTel.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTel.Properties.AutoHeight = ((bool)(resources.GetObject("txtTel.Properties.AutoHeight")));
            this.txtTel.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTel.Properties.Mask.AutoComplete")));
            this.txtTel.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTel.Properties.Mask.BeepOnError")));
            this.txtTel.Properties.Mask.EditMask = resources.GetString("txtTel.Properties.Mask.EditMask");
            this.txtTel.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTel.Properties.Mask.IgnoreMaskBlank")));
            this.txtTel.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTel.Properties.Mask.MaskType")));
            this.txtTel.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTel.Properties.Mask.PlaceHolder")));
            this.txtTel.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTel.Properties.Mask.SaveLiteral")));
            this.txtTel.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTel.Properties.Mask.ShowPlaceHolders")));
            this.txtTel.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTel.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTel.Properties.MaxLength = 50;
            this.txtTel.Properties.NullValuePrompt = resources.GetString("txtTel.Properties.NullValuePrompt");
            this.txtTel.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTel.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTel.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // txtMobile
            // 
            resources.ApplyResources(this.txtMobile, "txtMobile");
            this.txtMobile.EnterMoveNextControl = true;
            this.txtMobile.Name = "txtMobile";
            this.txtMobile.Properties.AccessibleDescription = resources.GetString("txtMobile.Properties.AccessibleDescription");
            this.txtMobile.Properties.AccessibleName = resources.GetString("txtMobile.Properties.AccessibleName");
            this.txtMobile.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMobile.Properties.Appearance.FontSizeDelta")));
            this.txtMobile.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMobile.Properties.Appearance.FontStyleDelta")));
            this.txtMobile.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMobile.Properties.Appearance.GradientMode")));
            this.txtMobile.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMobile.Properties.Appearance.Image")));
            this.txtMobile.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMobile.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMobile.Properties.AutoHeight = ((bool)(resources.GetObject("txtMobile.Properties.AutoHeight")));
            this.txtMobile.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMobile.Properties.Mask.AutoComplete")));
            this.txtMobile.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMobile.Properties.Mask.BeepOnError")));
            this.txtMobile.Properties.Mask.EditMask = resources.GetString("txtMobile.Properties.Mask.EditMask");
            this.txtMobile.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMobile.Properties.Mask.IgnoreMaskBlank")));
            this.txtMobile.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMobile.Properties.Mask.MaskType")));
            this.txtMobile.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMobile.Properties.Mask.PlaceHolder")));
            this.txtMobile.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMobile.Properties.Mask.SaveLiteral")));
            this.txtMobile.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMobile.Properties.Mask.ShowPlaceHolders")));
            this.txtMobile.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMobile.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMobile.Properties.MaxLength = 50;
            this.txtMobile.Properties.NullValuePrompt = resources.GetString("txtMobile.Properties.NullValuePrompt");
            this.txtMobile.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMobile.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMobile.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Name = "labelControl7";
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // txtCommercialBook
            // 
            resources.ApplyResources(this.txtCommercialBook, "txtCommercialBook");
            this.txtCommercialBook.EnterMoveNextControl = true;
            this.txtCommercialBook.Name = "txtCommercialBook";
            this.txtCommercialBook.Properties.AccessibleDescription = resources.GetString("txtCommercialBook.Properties.AccessibleDescription");
            this.txtCommercialBook.Properties.AccessibleName = resources.GetString("txtCommercialBook.Properties.AccessibleName");
            this.txtCommercialBook.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCommercialBook.Properties.Appearance.FontSizeDelta")));
            this.txtCommercialBook.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCommercialBook.Properties.Appearance.FontStyleDelta")));
            this.txtCommercialBook.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCommercialBook.Properties.Appearance.GradientMode")));
            this.txtCommercialBook.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCommercialBook.Properties.Appearance.Image")));
            this.txtCommercialBook.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCommercialBook.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCommercialBook.Properties.AutoHeight = ((bool)(resources.GetObject("txtCommercialBook.Properties.AutoHeight")));
            this.txtCommercialBook.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCommercialBook.Properties.Mask.AutoComplete")));
            this.txtCommercialBook.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.BeepOnError")));
            this.txtCommercialBook.Properties.Mask.EditMask = resources.GetString("txtCommercialBook.Properties.Mask.EditMask");
            this.txtCommercialBook.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.IgnoreMaskBlank")));
            this.txtCommercialBook.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCommercialBook.Properties.Mask.MaskType")));
            this.txtCommercialBook.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCommercialBook.Properties.Mask.PlaceHolder")));
            this.txtCommercialBook.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.SaveLiteral")));
            this.txtCommercialBook.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.ShowPlaceHolders")));
            this.txtCommercialBook.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCommercialBook.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCommercialBook.Properties.MaxLength = 50;
            this.txtCommercialBook.Properties.NullValuePrompt = resources.GetString("txtCommercialBook.Properties.NullValuePrompt");
            this.txtCommercialBook.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCommercialBook.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCommercialBook.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Name = "labelControl12";
            // 
            // txtTaxCard
            // 
            resources.ApplyResources(this.txtTaxCard, "txtTaxCard");
            this.txtTaxCard.EnterMoveNextControl = true;
            this.txtTaxCard.Name = "txtTaxCard";
            this.txtTaxCard.Properties.AccessibleDescription = resources.GetString("txtTaxCard.Properties.AccessibleDescription");
            this.txtTaxCard.Properties.AccessibleName = resources.GetString("txtTaxCard.Properties.AccessibleName");
            this.txtTaxCard.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtTaxCard.Properties.Appearance.FontSizeDelta")));
            this.txtTaxCard.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtTaxCard.Properties.Appearance.FontStyleDelta")));
            this.txtTaxCard.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtTaxCard.Properties.Appearance.GradientMode")));
            this.txtTaxCard.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtTaxCard.Properties.Appearance.Image")));
            this.txtTaxCard.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTaxCard.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTaxCard.Properties.AutoHeight = ((bool)(resources.GetObject("txtTaxCard.Properties.AutoHeight")));
            this.txtTaxCard.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTaxCard.Properties.Mask.AutoComplete")));
            this.txtTaxCard.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.BeepOnError")));
            this.txtTaxCard.Properties.Mask.EditMask = resources.GetString("txtTaxCard.Properties.Mask.EditMask");
            this.txtTaxCard.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.IgnoreMaskBlank")));
            this.txtTaxCard.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTaxCard.Properties.Mask.MaskType")));
            this.txtTaxCard.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTaxCard.Properties.Mask.PlaceHolder")));
            this.txtTaxCard.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.SaveLiteral")));
            this.txtTaxCard.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.ShowPlaceHolders")));
            this.txtTaxCard.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTaxCard.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTaxCard.Properties.MaxLength = 50;
            this.txtTaxCard.Properties.NullValuePrompt = resources.GetString("txtTaxCard.Properties.NullValuePrompt");
            this.txtTaxCard.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTaxCard.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTaxCard.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Name = "labelControl13";
            // 
            // txtMngrAddress
            // 
            resources.ApplyResources(this.txtMngrAddress, "txtMngrAddress");
            this.txtMngrAddress.EnterMoveNextControl = true;
            this.txtMngrAddress.Name = "txtMngrAddress";
            this.txtMngrAddress.Properties.AccessibleDescription = resources.GetString("txtMngrAddress.Properties.AccessibleDescription");
            this.txtMngrAddress.Properties.AccessibleName = resources.GetString("txtMngrAddress.Properties.AccessibleName");
            this.txtMngrAddress.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMngrAddress.Properties.Appearance.FontSizeDelta")));
            this.txtMngrAddress.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMngrAddress.Properties.Appearance.FontStyleDelta")));
            this.txtMngrAddress.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMngrAddress.Properties.Appearance.GradientMode")));
            this.txtMngrAddress.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMngrAddress.Properties.Appearance.Image")));
            this.txtMngrAddress.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMngrAddress.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMngrAddress.Properties.AutoHeight = ((bool)(resources.GetObject("txtMngrAddress.Properties.AutoHeight")));
            this.txtMngrAddress.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMngrAddress.Properties.Mask.AutoComplete")));
            this.txtMngrAddress.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMngrAddress.Properties.Mask.BeepOnError")));
            this.txtMngrAddress.Properties.Mask.EditMask = resources.GetString("txtMngrAddress.Properties.Mask.EditMask");
            this.txtMngrAddress.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMngrAddress.Properties.Mask.IgnoreMaskBlank")));
            this.txtMngrAddress.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMngrAddress.Properties.Mask.MaskType")));
            this.txtMngrAddress.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMngrAddress.Properties.Mask.PlaceHolder")));
            this.txtMngrAddress.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMngrAddress.Properties.Mask.SaveLiteral")));
            this.txtMngrAddress.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMngrAddress.Properties.Mask.ShowPlaceHolders")));
            this.txtMngrAddress.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMngrAddress.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMngrAddress.Properties.MaxLength = 50;
            this.txtMngrAddress.Properties.NullValuePrompt = resources.GetString("txtMngrAddress.Properties.NullValuePrompt");
            this.txtMngrAddress.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMngrAddress.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMngrAddress.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Name = "labelControl14";
            // 
            // txtMngrName
            // 
            resources.ApplyResources(this.txtMngrName, "txtMngrName");
            this.txtMngrName.EnterMoveNextControl = true;
            this.txtMngrName.Name = "txtMngrName";
            this.txtMngrName.Properties.AccessibleDescription = resources.GetString("txtMngrName.Properties.AccessibleDescription");
            this.txtMngrName.Properties.AccessibleName = resources.GetString("txtMngrName.Properties.AccessibleName");
            this.txtMngrName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMngrName.Properties.Appearance.FontSizeDelta")));
            this.txtMngrName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMngrName.Properties.Appearance.FontStyleDelta")));
            this.txtMngrName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMngrName.Properties.Appearance.GradientMode")));
            this.txtMngrName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMngrName.Properties.Appearance.Image")));
            this.txtMngrName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMngrName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMngrName.Properties.AutoHeight = ((bool)(resources.GetObject("txtMngrName.Properties.AutoHeight")));
            this.txtMngrName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMngrName.Properties.Mask.AutoComplete")));
            this.txtMngrName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMngrName.Properties.Mask.BeepOnError")));
            this.txtMngrName.Properties.Mask.EditMask = resources.GetString("txtMngrName.Properties.Mask.EditMask");
            this.txtMngrName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMngrName.Properties.Mask.IgnoreMaskBlank")));
            this.txtMngrName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMngrName.Properties.Mask.MaskType")));
            this.txtMngrName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMngrName.Properties.Mask.PlaceHolder")));
            this.txtMngrName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMngrName.Properties.Mask.SaveLiteral")));
            this.txtMngrName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMngrName.Properties.Mask.ShowPlaceHolders")));
            this.txtMngrName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMngrName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMngrName.Properties.MaxLength = 50;
            this.txtMngrName.Properties.NullValuePrompt = resources.GetString("txtMngrName.Properties.NullValuePrompt");
            this.txtMngrName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMngrName.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMngrName.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Name = "labelControl16";
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Name = "labelControl17";
            // 
            // txtMngrMobile
            // 
            resources.ApplyResources(this.txtMngrMobile, "txtMngrMobile");
            this.txtMngrMobile.EnterMoveNextControl = true;
            this.txtMngrMobile.Name = "txtMngrMobile";
            this.txtMngrMobile.Properties.AccessibleDescription = resources.GetString("txtMngrMobile.Properties.AccessibleDescription");
            this.txtMngrMobile.Properties.AccessibleName = resources.GetString("txtMngrMobile.Properties.AccessibleName");
            this.txtMngrMobile.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMngrMobile.Properties.Appearance.FontSizeDelta")));
            this.txtMngrMobile.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMngrMobile.Properties.Appearance.FontStyleDelta")));
            this.txtMngrMobile.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMngrMobile.Properties.Appearance.GradientMode")));
            this.txtMngrMobile.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMngrMobile.Properties.Appearance.Image")));
            this.txtMngrMobile.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMngrMobile.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMngrMobile.Properties.AutoHeight = ((bool)(resources.GetObject("txtMngrMobile.Properties.AutoHeight")));
            this.txtMngrMobile.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMngrMobile.Properties.Mask.AutoComplete")));
            this.txtMngrMobile.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMngrMobile.Properties.Mask.BeepOnError")));
            this.txtMngrMobile.Properties.Mask.EditMask = resources.GetString("txtMngrMobile.Properties.Mask.EditMask");
            this.txtMngrMobile.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMngrMobile.Properties.Mask.IgnoreMaskBlank")));
            this.txtMngrMobile.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMngrMobile.Properties.Mask.MaskType")));
            this.txtMngrMobile.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMngrMobile.Properties.Mask.PlaceHolder")));
            this.txtMngrMobile.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMngrMobile.Properties.Mask.SaveLiteral")));
            this.txtMngrMobile.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMngrMobile.Properties.Mask.ShowPlaceHolders")));
            this.txtMngrMobile.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMngrMobile.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMngrMobile.Properties.MaxLength = 50;
            this.txtMngrMobile.Properties.NullValuePrompt = resources.GetString("txtMngrMobile.Properties.NullValuePrompt");
            this.txtMngrMobile.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMngrMobile.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMngrMobile.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl18
            // 
            resources.ApplyResources(this.labelControl18, "labelControl18");
            this.labelControl18.Name = "labelControl18";
            // 
            // txtMngrTel
            // 
            resources.ApplyResources(this.txtMngrTel, "txtMngrTel");
            this.txtMngrTel.EnterMoveNextControl = true;
            this.txtMngrTel.Name = "txtMngrTel";
            this.txtMngrTel.Properties.AccessibleDescription = resources.GetString("txtMngrTel.Properties.AccessibleDescription");
            this.txtMngrTel.Properties.AccessibleName = resources.GetString("txtMngrTel.Properties.AccessibleName");
            this.txtMngrTel.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMngrTel.Properties.Appearance.FontSizeDelta")));
            this.txtMngrTel.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMngrTel.Properties.Appearance.FontStyleDelta")));
            this.txtMngrTel.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMngrTel.Properties.Appearance.GradientMode")));
            this.txtMngrTel.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMngrTel.Properties.Appearance.Image")));
            this.txtMngrTel.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMngrTel.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMngrTel.Properties.AutoHeight = ((bool)(resources.GetObject("txtMngrTel.Properties.AutoHeight")));
            this.txtMngrTel.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMngrTel.Properties.Mask.AutoComplete")));
            this.txtMngrTel.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMngrTel.Properties.Mask.BeepOnError")));
            this.txtMngrTel.Properties.Mask.EditMask = resources.GetString("txtMngrTel.Properties.Mask.EditMask");
            this.txtMngrTel.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMngrTel.Properties.Mask.IgnoreMaskBlank")));
            this.txtMngrTel.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMngrTel.Properties.Mask.MaskType")));
            this.txtMngrTel.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMngrTel.Properties.Mask.PlaceHolder")));
            this.txtMngrTel.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMngrTel.Properties.Mask.SaveLiteral")));
            this.txtMngrTel.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMngrTel.Properties.Mask.ShowPlaceHolders")));
            this.txtMngrTel.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMngrTel.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMngrTel.Properties.MaxLength = 50;
            this.txtMngrTel.Properties.NullValuePrompt = resources.GetString("txtMngrTel.Properties.NullValuePrompt");
            this.txtMngrTel.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMngrTel.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMngrTel.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Name = "labelControl19";
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtn_Save,
            this.barBtn_Help,
            this.barBtnClose});
            this.barManager1.MaxItemId = 25;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(328, 161);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Save),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtn_Save
            // 
            resources.ApplyResources(this.barBtn_Save, "barBtn_Save");
            this.barBtn_Save.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Save.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtn_Save.Id = 0;
            this.barBtn_Save.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtn_Save.Name = "barBtn_Save";
            this.barBtn_Save.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Save.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 24;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.GradientMode")));
            this.barAndDockingController1.AppearancesBar.Dock.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.Image")));
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDe" +
        "lta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMod" +
        "e")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelt" +
        "a")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.ActiveTab.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.ActiveTab.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.ActiveTab.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.ActiveTab.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.ActiveTab.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.ActiveTab.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.ActiveTab.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.ActiveTab.Image")));
            this.barAndDockingController1.AppearancesDocking.ActiveTab.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.ActiveTab.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.FloatFormCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.FloatFormCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.FloatFormCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.FloatFormCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.FloatFormCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.FloatFormCaption.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.FloatFormCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.FloatFormCaption.Image")));
            this.barAndDockingController1.AppearancesDocking.FloatFormCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.FloatFormCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.Image")));
            this.barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.FloatFormCaptionActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.HideContainer.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.HideContainer.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.HideContainer.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.HideContainer.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.HideContainer.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.HideContainer.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.HideContainer.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.HideContainer.Image")));
            this.barAndDockingController1.AppearancesDocking.HideContainer.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.HideContainer.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.Panel.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.Image")));
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.Image")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaptionActive.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaptionActive.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaptionActive.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaptionActive.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaptionActive.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaptionActive.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.PanelCaptionActive.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaptionActive.Image")));
            this.barAndDockingController1.AppearancesDocking.PanelCaptionActive.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaptionActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Tabs.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.Tabs.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.Tabs.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.Tabs.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.Tabs.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.Tabs.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.Tabs.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.Tabs.Image")));
            this.barAndDockingController1.AppearancesDocking.Tabs.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Tabs.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // btnLoadLogo
            // 
            resources.ApplyResources(this.btnLoadLogo, "btnLoadLogo");
            this.btnLoadLogo.Name = "btnLoadLogo";
            this.btnLoadLogo.Click += new System.EventHandler(this.btnLoadLogo_Click);
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.MenuManager = this.barManager1;
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.AccessibleDescription = resources.GetString("picLogo.Properties.AccessibleDescription");
            this.picLogo.Properties.AccessibleName = resources.GetString("picLogo.Properties.AccessibleName");
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom;
            this.picLogo.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Name = "labelControl20";
            // 
            // btnRemovePic
            // 
            resources.ApplyResources(this.btnRemovePic, "btnRemovePic");
            this.btnRemovePic.Name = "btnRemovePic";
            this.btnRemovePic.Click += new System.EventHandler(this.btnRemovePic_Click);
            // 
            // labelControl21
            // 
            resources.ApplyResources(this.labelControl21, "labelControl21");
            this.labelControl21.Name = "labelControl21";
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Name = "labelControl22";
            // 
            // labelControl23
            // 
            resources.ApplyResources(this.labelControl23, "labelControl23");
            this.labelControl23.Name = "labelControl23";
            // 
            // labelControl24
            // 
            resources.ApplyResources(this.labelControl24, "labelControl24");
            this.labelControl24.Name = "labelControl24";
            // 
            // dtFiscalYearStartDate
            // 
            resources.ApplyResources(this.dtFiscalYearStartDate, "dtFiscalYearStartDate");
            this.dtFiscalYearStartDate.MenuManager = this.barManager1;
            this.dtFiscalYearStartDate.Name = "dtFiscalYearStartDate";
            this.dtFiscalYearStartDate.Properties.AccessibleDescription = resources.GetString("dtFiscalYearStartDate.Properties.AccessibleDescription");
            this.dtFiscalYearStartDate.Properties.AccessibleName = resources.GetString("dtFiscalYearStartDate.Properties.AccessibleName");
            this.dtFiscalYearStartDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.AutoHeight")));
            this.dtFiscalYearStartDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtFiscalYearStartDate.Properties.Buttons"))))});
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtFiscalYearStartDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtFiscalYearStartDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayForm" +
        "at")));
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtFiscalYearStartDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtFiscalYearStartDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmp" +
        "tyValue")));
            this.dtFiscalYearStartDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtFiscalYearStartDate.Properties.Mask.AutoComplete")));
            this.dtFiscalYearStartDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.Mask.BeepOnError")));
            this.dtFiscalYearStartDate.Properties.Mask.EditMask = resources.GetString("dtFiscalYearStartDate.Properties.Mask.EditMask");
            this.dtFiscalYearStartDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtFiscalYearStartDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtFiscalYearStartDate.Properties.Mask.MaskType")));
            this.dtFiscalYearStartDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtFiscalYearStartDate.Properties.Mask.PlaceHolder")));
            this.dtFiscalYearStartDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.Mask.SaveLiteral")));
            this.dtFiscalYearStartDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.Mask.ShowPlaceHolders")));
            this.dtFiscalYearStartDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtFiscalYearStartDate.Properties.NullValuePrompt = resources.GetString("dtFiscalYearStartDate.Properties.NullValuePrompt");
            this.dtFiscalYearStartDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtFiscalYearStartDate.Properties.NullValuePromptShowForEmptyValue")));
            this.dtFiscalYearStartDate.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // dtFiscalYearEndDate
            // 
            resources.ApplyResources(this.dtFiscalYearEndDate, "dtFiscalYearEndDate");
            this.dtFiscalYearEndDate.MenuManager = this.barManager1;
            this.dtFiscalYearEndDate.Name = "dtFiscalYearEndDate";
            this.dtFiscalYearEndDate.Properties.AccessibleDescription = resources.GetString("dtFiscalYearEndDate.Properties.AccessibleDescription");
            this.dtFiscalYearEndDate.Properties.AccessibleName = resources.GetString("dtFiscalYearEndDate.Properties.AccessibleName");
            this.dtFiscalYearEndDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.AutoHeight")));
            this.dtFiscalYearEndDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtFiscalYearEndDate.Properties.Buttons"))))});
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtFiscalYearEndDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtFiscalYearEndDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" +
        "")));
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtFiscalYearEndDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtFiscalYearEndDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmpty" +
        "Value")));
            this.dtFiscalYearEndDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtFiscalYearEndDate.Properties.Mask.AutoComplete")));
            this.dtFiscalYearEndDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.Mask.BeepOnError")));
            this.dtFiscalYearEndDate.Properties.Mask.EditMask = resources.GetString("dtFiscalYearEndDate.Properties.Mask.EditMask");
            this.dtFiscalYearEndDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtFiscalYearEndDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtFiscalYearEndDate.Properties.Mask.MaskType")));
            this.dtFiscalYearEndDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtFiscalYearEndDate.Properties.Mask.PlaceHolder")));
            this.dtFiscalYearEndDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.Mask.SaveLiteral")));
            this.dtFiscalYearEndDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.Mask.ShowPlaceHolders")));
            this.dtFiscalYearEndDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtFiscalYearEndDate.Properties.NullValuePrompt = resources.GetString("dtFiscalYearEndDate.Properties.NullValuePrompt");
            this.dtFiscalYearEndDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtFiscalYearEndDate.Properties.NullValuePromptShowForEmptyValue")));
            this.dtFiscalYearEndDate.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // txtIban
            // 
            resources.ApplyResources(this.txtIban, "txtIban");
            this.txtIban.EnterMoveNextControl = true;
            this.txtIban.Name = "txtIban";
            this.txtIban.Properties.AccessibleDescription = resources.GetString("txtIban.Properties.AccessibleDescription");
            this.txtIban.Properties.AccessibleName = resources.GetString("txtIban.Properties.AccessibleName");
            this.txtIban.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtIban.Properties.Appearance.FontSizeDelta")));
            this.txtIban.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtIban.Properties.Appearance.FontStyleDelta")));
            this.txtIban.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtIban.Properties.Appearance.GradientMode")));
            this.txtIban.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtIban.Properties.Appearance.Image")));
            this.txtIban.Properties.Appearance.Options.UseTextOptions = true;
            this.txtIban.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtIban.Properties.AutoHeight = ((bool)(resources.GetObject("txtIban.Properties.AutoHeight")));
            this.txtIban.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtIban.Properties.Mask.AutoComplete")));
            this.txtIban.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtIban.Properties.Mask.BeepOnError")));
            this.txtIban.Properties.Mask.EditMask = resources.GetString("txtIban.Properties.Mask.EditMask");
            this.txtIban.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtIban.Properties.Mask.IgnoreMaskBlank")));
            this.txtIban.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtIban.Properties.Mask.MaskType")));
            this.txtIban.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtIban.Properties.Mask.PlaceHolder")));
            this.txtIban.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtIban.Properties.Mask.SaveLiteral")));
            this.txtIban.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtIban.Properties.Mask.ShowPlaceHolders")));
            this.txtIban.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtIban.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtIban.Properties.MaxLength = 50;
            this.txtIban.Properties.NullValuePrompt = resources.GetString("txtIban.Properties.NullValuePrompt");
            this.txtIban.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtIban.Properties.NullValuePromptShowForEmptyValue")));
            this.txtIban.Modified += new System.EventHandler(this.txtCompanyNameAr_Modified);
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Name = "labelControl25";
            // 
            // txtActivityType
            // 
            resources.ApplyResources(this.txtActivityType, "txtActivityType");
            this.txtActivityType.EnterMoveNextControl = true;
            this.txtActivityType.Name = "txtActivityType";
            this.txtActivityType.Properties.AccessibleDescription = resources.GetString("txtActivityType.Properties.AccessibleDescription");
            this.txtActivityType.Properties.AccessibleName = resources.GetString("txtActivityType.Properties.AccessibleName");
            this.txtActivityType.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtActivityType.Properties.Appearance.FontSizeDelta")));
            this.txtActivityType.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtActivityType.Properties.Appearance.FontStyleDelta")));
            this.txtActivityType.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtActivityType.Properties.Appearance.GradientMode")));
            this.txtActivityType.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtActivityType.Properties.Appearance.Image")));
            this.txtActivityType.Properties.Appearance.Options.UseTextOptions = true;
            this.txtActivityType.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtActivityType.Properties.AutoHeight = ((bool)(resources.GetObject("txtActivityType.Properties.AutoHeight")));
            this.txtActivityType.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtActivityType.Properties.Mask.AutoComplete")));
            this.txtActivityType.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.BeepOnError")));
            this.txtActivityType.Properties.Mask.EditMask = resources.GetString("txtActivityType.Properties.Mask.EditMask");
            this.txtActivityType.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.IgnoreMaskBlank")));
            this.txtActivityType.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtActivityType.Properties.Mask.MaskType")));
            this.txtActivityType.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtActivityType.Properties.Mask.PlaceHolder")));
            this.txtActivityType.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.SaveLiteral")));
            this.txtActivityType.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.ShowPlaceHolders")));
            this.txtActivityType.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtActivityType.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtActivityType.Properties.MaxLength = 50;
            this.txtActivityType.Properties.NullValuePrompt = resources.GetString("txtActivityType.Properties.NullValuePrompt");
            this.txtActivityType.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtActivityType.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl26
            // 
            resources.ApplyResources(this.labelControl26, "labelControl26");
            this.labelControl26.Name = "labelControl26";
            // 
            // frm_ST_CompInfo
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.txtActivityType);
            this.Controls.Add(this.labelControl26);
            this.Controls.Add(this.txtIban);
            this.Controls.Add(this.labelControl25);
            this.Controls.Add(this.dtFiscalYearEndDate);
            this.Controls.Add(this.dtFiscalYearStartDate);
            this.Controls.Add(this.labelControl24);
            this.Controls.Add(this.labelControl23);
            this.Controls.Add(this.labelControl21);
            this.Controls.Add(this.labelControl22);
            this.Controls.Add(this.btnRemovePic);
            this.Controls.Add(this.labelControl20);
            this.Controls.Add(this.btnLoadLogo);
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.txtMngrMobile);
            this.Controls.Add(this.labelControl18);
            this.Controls.Add(this.txtMngrTel);
            this.Controls.Add(this.labelControl19);
            this.Controls.Add(this.txtMngrAddress);
            this.Controls.Add(this.labelControl14);
            this.Controls.Add(this.txtMngrName);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.labelControl16);
            this.Controls.Add(this.labelControl17);
            this.Controls.Add(this.txtTaxCard);
            this.Controls.Add(this.labelControl13);
            this.Controls.Add(this.txtCommercialBook);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.txtMobile);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.txtTel);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.txtCountry);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.txtCity);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.txtAddress);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.txtCompanyNameEn);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.txtCompanyNameAr);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_ST_CompInfo";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_ST_PharmacyInfo_FormClosing);
            this.Load += new System.EventHandler(this.frm_ST_PharmacyInfo_Load);
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameAr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCompanyNameEn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAddress.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCity.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCountry.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMobile.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCommercialBook.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxCard.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMngrAddress.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMngrName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMngrMobile.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMngrTel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFiscalYearStartDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFiscalYearStartDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFiscalYearEndDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFiscalYearEndDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtIban.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtActivityType.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtCompanyNameAr;
        private DevExpress.XtraEditors.TextEdit txtCompanyNameEn;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.TextEdit txtAddress;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtCity;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit txtCountry;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.TextEdit txtTel;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.TextEdit txtMobile;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.TextEdit txtCommercialBook;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.TextEdit txtTaxCard;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.TextEdit txtMngrAddress;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.TextEdit txtMngrName;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.TextEdit txtMngrMobile;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.TextEdit txtMngrTel;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtn_Save;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.SimpleButton btnLoadLogo;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.SimpleButton btnRemovePic;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraEditors.DateEdit dtFiscalYearEndDate;
        private DevExpress.XtraEditors.DateEdit dtFiscalYearStartDate;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.TextEdit txtIban;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.TextEdit txtActivityType;
        private DevExpress.XtraEditors.LabelControl labelControl26;
    }
}