namespace Reports
{
    partial class rpt_SL_Invoice
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(rpt_SL_Invoice));
            DevExpress.XtraPrinting.BarCode.Code128Generator code128Generator1 = new DevExpress.XtraPrinting.BarCode.Code128Generator();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Total = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Disc = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_Paied = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Net = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Remains = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountR = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Total = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ExpensesV = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel17 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel24 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.txtDelivery = new DevExpress.XtraReports.UI.XRLabel();
            this.xrBarCode_Voucher = new DevExpress.XtraReports.UI.XRBarCode();
            this.lblDue = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DueDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ProcessName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_SourceCode = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.lbl_PurchaseOrderNo = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel22 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Shipping = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel18 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_SalesEmp = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Paymethod = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Drawer = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_store = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Serial = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Customer = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DeliverDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Updated = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_BalanceBefore = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.lbl_TotalQty = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TaxR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_ItemDescriptionEn = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemDescription = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_PicPath = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Serial = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_AudiencePrice = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Location = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_salesEmp_Job = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ExpensesR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Pack = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_MUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_MUOM_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable5 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Weight_KG = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_PiecesCount = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_SalesTax = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_SalesTaxRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ManufactureDate = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_DeductTaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DriverName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_VehicleNumber = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Destination = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ScaleWeightSerial = new DevExpress.XtraReports.UI.XRLabel();
            this.pic_ItemPic = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lbl_BalanceAfter = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrSubreport3 = new DevExpress.XtraReports.UI.XRSubreport();
            this.lblShift = new DevExpress.XtraReports.UI.XRLabel();
            this.xrSubreport2 = new DevExpress.XtraReports.UI.XRSubreport();
            this.lbl_totalPieces = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TaxFileNumber = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TaxCardNumber = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel26 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Handing = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_trans = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel29 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel21 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Cust_Address = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Cust_Tel = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Cust_Mobile = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_retention = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_advancepayment = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel25 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_CusTaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AddTaxR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblSubTotal = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AddTaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotalWords = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountV = new DevExpress.XtraReports.UI.XRLabel();
            this.winControlContainer1 = new DevExpress.XtraReports.UI.WinControlContainer();
            this.grd = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.directoryEntry1 = new System.DirectoryServices.DirectoryEntry();
            this.xr_Totals = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Qtys = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Category = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail1 = new DevExpress.XtraReports.UI.DetailBand();
            this.ReportHeader = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.DetailReport_TotalQtyPerCategory = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail2 = new DevExpress.XtraReports.UI.DetailBand();
            this.ReportHeader1 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xr_Totals)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            resources.ApplyResources(this.Detail, "Detail");
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable2, "xrTable2");
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow2
            // 
            resources.ApplyResources(this.xrTableRow2, "xrTableRow2");
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Total,
            this.cell_DiscountRatio,
            this.cell_Disc,
            this.cell_Price,
            this.cell_Qty,
            this.cell_Batch,
            this.cell_UOM,
            this.cell_ItemName,
            this.cell_code});
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            // 
            // cell_Total
            // 
            this.cell_Total.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.cell_Total, "cell_Total");
            this.cell_Total.Name = "cell_Total";
            this.cell_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Total.StylePriority.UseBorders = false;
            this.cell_Total.StylePriority.UseFont = false;
            this.cell_Total.StylePriority.UsePadding = false;
            this.cell_Total.StylePriority.UseTextAlignment = false;
            // 
            // cell_DiscountRatio
            // 
            this.cell_DiscountRatio.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.cell_DiscountRatio, "cell_DiscountRatio");
            this.cell_DiscountRatio.Name = "cell_DiscountRatio";
            this.cell_DiscountRatio.StylePriority.UseBorders = false;
            this.cell_DiscountRatio.StylePriority.UseFont = false;
            this.cell_DiscountRatio.StylePriority.UsePadding = false;
            this.cell_DiscountRatio.StylePriority.UseTextAlignment = false;
            // 
            // cell_Disc
            // 
            this.cell_Disc.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.cell_Disc, "cell_Disc");
            this.cell_Disc.Name = "cell_Disc";
            this.cell_Disc.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Disc.StylePriority.UseBorders = false;
            this.cell_Disc.StylePriority.UseFont = false;
            this.cell_Disc.StylePriority.UsePadding = false;
            this.cell_Disc.StylePriority.UseTextAlignment = false;
            // 
            // cell_Price
            // 
            this.cell_Price.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.cell_Price, "cell_Price");
            this.cell_Price.Name = "cell_Price";
            this.cell_Price.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Price.StylePriority.UseBorders = false;
            this.cell_Price.StylePriority.UseFont = false;
            this.cell_Price.StylePriority.UsePadding = false;
            this.cell_Price.StylePriority.UseTextAlignment = false;
            // 
            // cell_Qty
            // 
            this.cell_Qty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.cell_Qty, "cell_Qty");
            this.cell_Qty.Name = "cell_Qty";
            this.cell_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Qty.StylePriority.UseBorders = false;
            this.cell_Qty.StylePriority.UseFont = false;
            this.cell_Qty.StylePriority.UsePadding = false;
            this.cell_Qty.StylePriority.UseTextAlignment = false;
            // 
            // cell_Batch
            // 
            this.cell_Batch.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.cell_Batch, "cell_Batch");
            this.cell_Batch.Name = "cell_Batch";
            this.cell_Batch.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Batch.StylePriority.UseBorders = false;
            this.cell_Batch.StylePriority.UseFont = false;
            this.cell_Batch.StylePriority.UsePadding = false;
            this.cell_Batch.StylePriority.UseTextAlignment = false;
            // 
            // cell_UOM
            // 
            this.cell_UOM.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.cell_UOM, "cell_UOM");
            this.cell_UOM.Name = "cell_UOM";
            this.cell_UOM.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_UOM.StylePriority.UseBorders = false;
            this.cell_UOM.StylePriority.UseFont = false;
            this.cell_UOM.StylePriority.UsePadding = false;
            this.cell_UOM.StylePriority.UseTextAlignment = false;
            // 
            // cell_ItemName
            // 
            this.cell_ItemName.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.cell_ItemName, "cell_ItemName");
            this.cell_ItemName.Name = "cell_ItemName";
            this.cell_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_ItemName.StylePriority.UseBorders = false;
            this.cell_ItemName.StylePriority.UseFont = false;
            this.cell_ItemName.StylePriority.UsePadding = false;
            this.cell_ItemName.StylePriority.UseTextAlignment = false;
            // 
            // cell_code
            // 
            this.cell_code.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.cell_code, "cell_code");
            this.cell_code.Name = "cell_code";
            this.cell_code.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_code.StylePriority.UseBorders = false;
            this.cell_code.StylePriority.UseFont = false;
            this.cell_code.StylePriority.UsePadding = false;
            this.cell_code.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Paied
            // 
            this.lbl_Paied.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Paied.CanGrow = false;
            resources.ApplyResources(this.lbl_Paied, "lbl_Paied");
            this.lbl_Paied.Name = "lbl_Paied";
            this.lbl_Paied.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Paied.StylePriority.UseBorders = false;
            this.lbl_Paied.StylePriority.UseFont = false;
            this.lbl_Paied.StylePriority.UsePadding = false;
            this.lbl_Paied.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Net
            // 
            this.lbl_Net.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Net.CanGrow = false;
            resources.ApplyResources(this.lbl_Net, "lbl_Net");
            this.lbl_Net.Name = "lbl_Net";
            this.lbl_Net.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Net.StylePriority.UseBorders = false;
            this.lbl_Net.StylePriority.UseFont = false;
            this.lbl_Net.StylePriority.UsePadding = false;
            this.lbl_Net.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Remains
            // 
            this.lbl_Remains.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Remains.CanGrow = false;
            resources.ApplyResources(this.lbl_Remains, "lbl_Remains");
            this.lbl_Remains.Name = "lbl_Remains";
            this.lbl_Remains.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Remains.StylePriority.UseBorders = false;
            this.lbl_Remains.StylePriority.UseFont = false;
            this.lbl_Remains.StylePriority.UsePadding = false;
            this.lbl_Remains.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DiscountR
            // 
            this.lbl_DiscountR.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_DiscountR.CanGrow = false;
            resources.ApplyResources(this.lbl_DiscountR, "lbl_DiscountR");
            this.lbl_DiscountR.Name = "lbl_DiscountR";
            this.lbl_DiscountR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DiscountR.StylePriority.UseBorders = false;
            this.lbl_DiscountR.StylePriority.UseFont = false;
            this.lbl_DiscountR.StylePriority.UseTextAlignment = false;
            // 
            // lbl_TaxV
            // 
            this.lbl_TaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_TaxV.CanGrow = false;
            resources.ApplyResources(this.lbl_TaxV, "lbl_TaxV");
            this.lbl_TaxV.Name = "lbl_TaxV";
            this.lbl_TaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_TaxV.StylePriority.UseBorders = false;
            this.lbl_TaxV.StylePriority.UseFont = false;
            this.lbl_TaxV.StylePriority.UsePadding = false;
            this.lbl_TaxV.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Total
            // 
            this.lbl_Total.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Total.CanGrow = false;
            resources.ApplyResources(this.lbl_Total, "lbl_Total");
            this.lbl_Total.Name = "lbl_Total";
            this.lbl_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Total.StylePriority.UseBorders = false;
            this.lbl_Total.StylePriority.UseFont = false;
            this.lbl_Total.StylePriority.UsePadding = false;
            this.lbl_Total.StylePriority.UseTextAlignment = false;
            // 
            // lbl_ExpensesV
            // 
            this.lbl_ExpensesV.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_ExpensesV.CanGrow = false;
            resources.ApplyResources(this.lbl_ExpensesV, "lbl_ExpensesV");
            this.lbl_ExpensesV.Name = "lbl_ExpensesV";
            this.lbl_ExpensesV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_ExpensesV.StylePriority.UseBorders = false;
            this.lbl_ExpensesV.StylePriority.UseFont = false;
            this.lbl_ExpensesV.StylePriority.UsePadding = false;
            this.lbl_ExpensesV.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel17
            // 
            this.xrLabel17.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel17.CanGrow = false;
            resources.ApplyResources(this.xrLabel17, "xrLabel17");
            this.xrLabel17.Name = "xrLabel17";
            this.xrLabel17.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel17.StylePriority.UseBorders = false;
            this.xrLabel17.StylePriority.UseFont = false;
            this.xrLabel17.StylePriority.UsePadding = false;
            this.xrLabel17.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel23
            // 
            this.xrLabel23.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel23.CanGrow = false;
            resources.ApplyResources(this.xrLabel23, "xrLabel23");
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel23.StylePriority.UseBorders = false;
            this.xrLabel23.StylePriority.UseFont = false;
            this.xrLabel23.StylePriority.UsePadding = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel24
            // 
            this.xrLabel24.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel24.CanGrow = false;
            resources.ApplyResources(this.xrLabel24, "xrLabel24");
            this.xrLabel24.Name = "xrLabel24";
            this.xrLabel24.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel24.StylePriority.UseBorders = false;
            this.xrLabel24.StylePriority.UseFont = false;
            this.xrLabel24.StylePriority.UsePadding = false;
            this.xrLabel24.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel19.CanGrow = false;
            resources.ApplyResources(this.xrLabel19, "xrLabel19");
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.StylePriority.UseBorders = false;
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel20
            // 
            this.xrLabel20.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel20.CanGrow = false;
            resources.ApplyResources(this.xrLabel20, "xrLabel20");
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel20.StylePriority.UseBorders = false;
            this.xrLabel20.StylePriority.UseFont = false;
            this.xrLabel20.StylePriority.UsePadding = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel15.CanGrow = false;
            resources.ApplyResources(this.xrLabel15, "xrLabel15");
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel15.StylePriority.UseBorders = false;
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UsePadding = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel16.CanGrow = false;
            resources.ApplyResources(this.xrLabel16, "xrLabel16");
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel16.StylePriority.UseBorders = false;
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UsePadding = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.txtDelivery,
            this.xrBarCode_Voucher,
            this.lblDue,
            this.lbl_DueDate,
            this.lbl_ProcessName,
            this.lbl_SourceCode,
            this.xrLine1,
            this.lbl_PurchaseOrderNo,
            this.xrLabel22,
            this.lbl_Shipping,
            this.xrLabel18,
            this.xrLabel13,
            this.lbl_SalesEmp,
            this.xrLabel10,
            this.lbl_User,
            this.xrLabel6,
            this.xrLabel4,
            this.lbl_Paymethod,
            this.lbl_Drawer,
            this.xrLabel12,
            this.lbl_store,
            this.xrLabel7,
            this.xrLabel8,
            this.lbl_Serial,
            this.lbl_Customer,
            this.xrLabel1,
            this.lblReportName,
            this.picLogo,
            this.lbl_date,
            this.lbl_Number,
            this.lblCompName,
            this.lbl_notes,
            this.lbl_DeliverDate,
            this.lbl_Updated});
            resources.ApplyResources(this.TopMargin, "TopMargin");
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // txtDelivery
            // 
            resources.ApplyResources(this.txtDelivery, "txtDelivery");
            this.txtDelivery.Name = "txtDelivery";
            this.txtDelivery.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.txtDelivery.StylePriority.UseFont = false;
            this.txtDelivery.StylePriority.UseTextAlignment = false;
            // 
            // xrBarCode_Voucher
            // 
            resources.ApplyResources(this.xrBarCode_Voucher, "xrBarCode_Voucher");
            this.xrBarCode_Voucher.Name = "xrBarCode_Voucher";
            this.xrBarCode_Voucher.Padding = new DevExpress.XtraPrinting.PaddingInfo(10, 10, 0, 0, 100F);
            this.xrBarCode_Voucher.Symbology = code128Generator1;
            // 
            // lblDue
            // 
            resources.ApplyResources(this.lblDue, "lblDue");
            this.lblDue.Multiline = true;
            this.lblDue.Name = "lblDue";
            this.lblDue.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblDue.StylePriority.UseFont = false;
            this.lblDue.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DueDate
            // 
            resources.ApplyResources(this.lbl_DueDate, "lbl_DueDate");
            this.lbl_DueDate.Name = "lbl_DueDate";
            this.lbl_DueDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DueDate.StylePriority.UseFont = false;
            this.lbl_DueDate.StylePriority.UseTextAlignment = false;
            // 
            // lbl_ProcessName
            // 
            resources.ApplyResources(this.lbl_ProcessName, "lbl_ProcessName");
            this.lbl_ProcessName.Name = "lbl_ProcessName";
            this.lbl_ProcessName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ProcessName.StylePriority.UseFont = false;
            this.lbl_ProcessName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_SourceCode
            // 
            resources.ApplyResources(this.lbl_SourceCode, "lbl_SourceCode");
            this.lbl_SourceCode.Name = "lbl_SourceCode";
            this.lbl_SourceCode.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_SourceCode.StylePriority.UseFont = false;
            this.lbl_SourceCode.StylePriority.UseTextAlignment = false;
            // 
            // xrLine1
            // 
            resources.ApplyResources(this.xrLine1, "xrLine1");
            this.xrLine1.BorderWidth = 0F;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // lbl_PurchaseOrderNo
            // 
            resources.ApplyResources(this.lbl_PurchaseOrderNo, "lbl_PurchaseOrderNo");
            this.lbl_PurchaseOrderNo.Name = "lbl_PurchaseOrderNo";
            this.lbl_PurchaseOrderNo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_PurchaseOrderNo.StylePriority.UseFont = false;
            this.lbl_PurchaseOrderNo.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel22
            // 
            resources.ApplyResources(this.xrLabel22, "xrLabel22");
            this.xrLabel22.Name = "xrLabel22";
            this.xrLabel22.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel22.StylePriority.UseFont = false;
            this.xrLabel22.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Shipping
            // 
            resources.ApplyResources(this.lbl_Shipping, "lbl_Shipping");
            this.lbl_Shipping.Multiline = true;
            this.lbl_Shipping.Name = "lbl_Shipping";
            this.lbl_Shipping.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Shipping.StylePriority.UseFont = false;
            this.lbl_Shipping.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel18
            // 
            resources.ApplyResources(this.xrLabel18, "xrLabel18");
            this.xrLabel18.Name = "xrLabel18";
            this.xrLabel18.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel18.StylePriority.UseFont = false;
            this.xrLabel18.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel13
            // 
            resources.ApplyResources(this.xrLabel13, "xrLabel13");
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            // 
            // lbl_SalesEmp
            // 
            resources.ApplyResources(this.lbl_SalesEmp, "lbl_SalesEmp");
            this.lbl_SalesEmp.Name = "lbl_SalesEmp";
            this.lbl_SalesEmp.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_SalesEmp.StylePriority.UseFont = false;
            this.lbl_SalesEmp.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel10
            // 
            resources.ApplyResources(this.xrLabel10, "xrLabel10");
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_User, "lbl_User");
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.xrLabel6, "xrLabel6");
            this.xrLabel6.Multiline = true;
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel4
            // 
            resources.ApplyResources(this.xrLabel4, "xrLabel4");
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Paymethod
            // 
            resources.ApplyResources(this.lbl_Paymethod, "lbl_Paymethod");
            this.lbl_Paymethod.Name = "lbl_Paymethod";
            this.lbl_Paymethod.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Paymethod.StylePriority.UseFont = false;
            this.lbl_Paymethod.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Drawer
            // 
            resources.ApplyResources(this.lbl_Drawer, "lbl_Drawer");
            this.lbl_Drawer.Name = "lbl_Drawer";
            this.lbl_Drawer.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Drawer.StylePriority.UseFont = false;
            this.lbl_Drawer.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel12
            // 
            resources.ApplyResources(this.xrLabel12, "xrLabel12");
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            // 
            // lbl_store
            // 
            resources.ApplyResources(this.lbl_store, "lbl_store");
            this.lbl_store.Name = "lbl_store";
            this.lbl_store.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_store.StylePriority.UseFont = false;
            this.lbl_store.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel7
            // 
            resources.ApplyResources(this.xrLabel7, "xrLabel7");
            this.xrLabel7.Multiline = true;
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel8
            // 
            resources.ApplyResources(this.xrLabel8, "xrLabel8");
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Serial
            // 
            this.lbl_Serial.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_Serial, "lbl_Serial");
            this.lbl_Serial.Name = "lbl_Serial";
            this.lbl_Serial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Serial.StylePriority.UseBorders = false;
            this.lbl_Serial.StylePriority.UseFont = false;
            this.lbl_Serial.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Customer
            // 
            resources.ApplyResources(this.lbl_Customer, "lbl_Customer");
            this.lbl_Customer.Name = "lbl_Customer";
            this.lbl_Customer.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Customer.StylePriority.UseFont = false;
            this.lbl_Customer.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel1
            // 
            resources.ApplyResources(this.xrLabel1, "xrLabel1");
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lbl_date
            // 
            resources.ApplyResources(this.lbl_date, "lbl_date");
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Number
            // 
            resources.ApplyResources(this.lbl_Number, "lbl_Number");
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            // 
            // lblCompName
            // 
            resources.ApplyResources(this.lblCompName, "lblCompName");
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_notes
            // 
            resources.ApplyResources(this.lbl_notes, "lbl_notes");
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DeliverDate
            // 
            resources.ApplyResources(this.lbl_DeliverDate, "lbl_DeliverDate");
            this.lbl_DeliverDate.Name = "lbl_DeliverDate";
            this.lbl_DeliverDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DeliverDate.StylePriority.UseFont = false;
            this.lbl_DeliverDate.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Updated
            // 
            this.lbl_Updated.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_Updated, "lbl_Updated");
            this.lbl_Updated.Name = "lbl_Updated";
            this.lbl_Updated.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Updated.StylePriority.UseBorders = false;
            this.lbl_Updated.StylePriority.UseFont = false;
            this.lbl_Updated.StylePriority.UseTextAlignment = false;
            // 
            // lbl_BalanceBefore
            // 
            resources.ApplyResources(this.lbl_BalanceBefore, "lbl_BalanceBefore");
            this.lbl_BalanceBefore.Name = "lbl_BalanceBefore";
            this.lbl_BalanceBefore.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_BalanceBefore.StylePriority.UseFont = false;
            this.lbl_BalanceBefore.StylePriority.UseTextAlignment = false;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_TotalQty,
            this.lbl_TaxR,
            this.xrTable3,
            this.lbl_salesEmp_Job,
            this.lbl_ExpensesR,
            this.xrTable4,
            this.xrTable5,
            this.lbl_DeductTaxV,
            this.lbl_DriverName,
            this.lbl_VehicleNumber,
            this.lbl_Destination,
            this.lbl_ScaleWeightSerial,
            this.pic_ItemPic});
            resources.ApplyResources(this.BottomMargin, "BottomMargin");
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // lbl_TotalQty
            // 
            resources.ApplyResources(this.lbl_TotalQty, "lbl_TotalQty");
            this.lbl_TotalQty.Name = "lbl_TotalQty";
            this.lbl_TotalQty.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_TotalQty.StylePriority.UseFont = false;
            this.lbl_TotalQty.StylePriority.UseTextAlignment = false;
            // 
            // lbl_TaxR
            // 
            resources.ApplyResources(this.lbl_TaxR, "lbl_TaxR");
            this.lbl_TaxR.Name = "lbl_TaxR";
            this.lbl_TaxR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // xrTable3
            // 
            resources.ApplyResources(this.xrTable3, "xrTable3");
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_ItemDescriptionEn,
            this.cell_ItemDescription,
            this.cell_PicPath,
            this.cell_Height,
            this.cell_Width,
            this.cell_Length,
            this.cell_TotalQty,
            this.cell_Serial,
            this.cell_AudiencePrice,
            this.cell_Location});
            resources.ApplyResources(this.xrTableRow3, "xrTableRow3");
            this.xrTableRow3.Name = "xrTableRow3";
            // 
            // cell_ItemDescriptionEn
            // 
            resources.ApplyResources(this.cell_ItemDescriptionEn, "cell_ItemDescriptionEn");
            this.cell_ItemDescriptionEn.Name = "cell_ItemDescriptionEn";
            // 
            // cell_ItemDescription
            // 
            resources.ApplyResources(this.cell_ItemDescription, "cell_ItemDescription");
            this.cell_ItemDescription.Name = "cell_ItemDescription";
            // 
            // cell_PicPath
            // 
            resources.ApplyResources(this.cell_PicPath, "cell_PicPath");
            this.cell_PicPath.Name = "cell_PicPath";
            // 
            // cell_Height
            // 
            resources.ApplyResources(this.cell_Height, "cell_Height");
            this.cell_Height.Name = "cell_Height";
            // 
            // cell_Width
            // 
            resources.ApplyResources(this.cell_Width, "cell_Width");
            this.cell_Width.Name = "cell_Width";
            // 
            // cell_Length
            // 
            resources.ApplyResources(this.cell_Length, "cell_Length");
            this.cell_Length.Name = "cell_Length";
            // 
            // cell_TotalQty
            // 
            resources.ApplyResources(this.cell_TotalQty, "cell_TotalQty");
            this.cell_TotalQty.Name = "cell_TotalQty";
            // 
            // cell_Serial
            // 
            resources.ApplyResources(this.cell_Serial, "cell_Serial");
            this.cell_Serial.Name = "cell_Serial";
            // 
            // cell_AudiencePrice
            // 
            resources.ApplyResources(this.cell_AudiencePrice, "cell_AudiencePrice");
            this.cell_AudiencePrice.Name = "cell_AudiencePrice";
            // 
            // cell_Location
            // 
            resources.ApplyResources(this.cell_Location, "cell_Location");
            this.cell_Location.Name = "cell_Location";
            // 
            // lbl_salesEmp_Job
            // 
            resources.ApplyResources(this.lbl_salesEmp_Job, "lbl_salesEmp_Job");
            this.lbl_salesEmp_Job.Name = "lbl_salesEmp_Job";
            this.lbl_salesEmp_Job.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // lbl_ExpensesR
            // 
            resources.ApplyResources(this.lbl_ExpensesR, "lbl_ExpensesR");
            this.lbl_ExpensesR.Name = "lbl_ExpensesR";
            this.lbl_ExpensesR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // xrTable4
            // 
            resources.ApplyResources(this.xrTable4, "xrTable4");
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Pack,
            this.Cell_MUOM,
            this.Cell_MUOM_Factor,
            this.cell_Factor});
            resources.ApplyResources(this.xrTableRow4, "xrTableRow4");
            this.xrTableRow4.Name = "xrTableRow4";
            // 
            // cell_Pack
            // 
            resources.ApplyResources(this.cell_Pack, "cell_Pack");
            this.cell_Pack.Name = "cell_Pack";
            // 
            // Cell_MUOM
            // 
            resources.ApplyResources(this.Cell_MUOM, "Cell_MUOM");
            this.Cell_MUOM.Name = "Cell_MUOM";
            // 
            // Cell_MUOM_Factor
            // 
            resources.ApplyResources(this.Cell_MUOM_Factor, "Cell_MUOM_Factor");
            this.Cell_MUOM_Factor.Name = "Cell_MUOM_Factor";
            // 
            // cell_Factor
            // 
            resources.ApplyResources(this.cell_Factor, "cell_Factor");
            this.cell_Factor.Name = "cell_Factor";
            // 
            // xrTable5
            // 
            resources.ApplyResources(this.xrTable5, "xrTable5");
            this.xrTable5.Name = "xrTable5";
            this.xrTable5.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5});
            this.xrTable5.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Weight_KG,
            this.cell_PiecesCount,
            this.cell_SalesTax,
            this.cell_code2,
            this.cell_Expire,
            this.cell_DiscountRatio2,
            this.cell_DiscountRatio3,
            this.cell_SalesTaxRatio,
            this.cell_ManufactureDate});
            resources.ApplyResources(this.xrTableRow5, "xrTableRow5");
            this.xrTableRow5.Name = "xrTableRow5";
            // 
            // cell_Weight_KG
            // 
            resources.ApplyResources(this.cell_Weight_KG, "cell_Weight_KG");
            this.cell_Weight_KG.Name = "cell_Weight_KG";
            // 
            // cell_PiecesCount
            // 
            resources.ApplyResources(this.cell_PiecesCount, "cell_PiecesCount");
            this.cell_PiecesCount.Name = "cell_PiecesCount";
            // 
            // cell_SalesTax
            // 
            resources.ApplyResources(this.cell_SalesTax, "cell_SalesTax");
            this.cell_SalesTax.Name = "cell_SalesTax";
            // 
            // cell_code2
            // 
            resources.ApplyResources(this.cell_code2, "cell_code2");
            this.cell_code2.Name = "cell_code2";
            // 
            // cell_Expire
            // 
            resources.ApplyResources(this.cell_Expire, "cell_Expire");
            this.cell_Expire.Name = "cell_Expire";
            // 
            // cell_DiscountRatio2
            // 
            resources.ApplyResources(this.cell_DiscountRatio2, "cell_DiscountRatio2");
            this.cell_DiscountRatio2.Name = "cell_DiscountRatio2";
            // 
            // cell_DiscountRatio3
            // 
            resources.ApplyResources(this.cell_DiscountRatio3, "cell_DiscountRatio3");
            this.cell_DiscountRatio3.Name = "cell_DiscountRatio3";
            // 
            // cell_SalesTaxRatio
            // 
            resources.ApplyResources(this.cell_SalesTaxRatio, "cell_SalesTaxRatio");
            this.cell_SalesTaxRatio.Name = "cell_SalesTaxRatio";
            // 
            // cell_ManufactureDate
            // 
            resources.ApplyResources(this.cell_ManufactureDate, "cell_ManufactureDate");
            this.cell_ManufactureDate.Name = "cell_ManufactureDate";
            // 
            // lbl_DeductTaxV
            // 
            this.lbl_DeductTaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_DeductTaxV, "lbl_DeductTaxV");
            this.lbl_DeductTaxV.Name = "lbl_DeductTaxV";
            this.lbl_DeductTaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DeductTaxV.StylePriority.UseBorders = false;
            this.lbl_DeductTaxV.StylePriority.UseFont = false;
            this.lbl_DeductTaxV.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DriverName
            // 
            this.lbl_DriverName.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_DriverName, "lbl_DriverName");
            this.lbl_DriverName.Name = "lbl_DriverName";
            this.lbl_DriverName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DriverName.StylePriority.UseBorders = false;
            this.lbl_DriverName.StylePriority.UseFont = false;
            this.lbl_DriverName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_VehicleNumber
            // 
            this.lbl_VehicleNumber.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_VehicleNumber, "lbl_VehicleNumber");
            this.lbl_VehicleNumber.Name = "lbl_VehicleNumber";
            this.lbl_VehicleNumber.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_VehicleNumber.StylePriority.UseBorders = false;
            this.lbl_VehicleNumber.StylePriority.UseFont = false;
            this.lbl_VehicleNumber.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Destination
            // 
            this.lbl_Destination.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_Destination, "lbl_Destination");
            this.lbl_Destination.Name = "lbl_Destination";
            this.lbl_Destination.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Destination.StylePriority.UseBorders = false;
            this.lbl_Destination.StylePriority.UseFont = false;
            this.lbl_Destination.StylePriority.UseTextAlignment = false;
            // 
            // lbl_ScaleWeightSerial
            // 
            this.lbl_ScaleWeightSerial.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_ScaleWeightSerial, "lbl_ScaleWeightSerial");
            this.lbl_ScaleWeightSerial.Name = "lbl_ScaleWeightSerial";
            this.lbl_ScaleWeightSerial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ScaleWeightSerial.StylePriority.UseBorders = false;
            this.lbl_ScaleWeightSerial.StylePriority.UseFont = false;
            this.lbl_ScaleWeightSerial.StylePriority.UseTextAlignment = false;
            // 
            // pic_ItemPic
            // 
            resources.ApplyResources(this.pic_ItemPic, "pic_ItemPic");
            this.pic_ItemPic.Name = "pic_ItemPic";
            // 
            // lbl_BalanceAfter
            // 
            resources.ApplyResources(this.lbl_BalanceAfter, "lbl_BalanceAfter");
            this.lbl_BalanceAfter.Name = "lbl_BalanceAfter";
            this.lbl_BalanceAfter.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_BalanceAfter.StylePriority.UseFont = false;
            this.lbl_BalanceAfter.StylePriority.UseTextAlignment = false;
            // 
            // xrPageInfo1
            // 
            resources.ApplyResources(this.xrPageInfo1, "xrPageInfo1");
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            // 
            // PageHeader
            // 
            resources.ApplyResources(this.PageHeader, "PageHeader");
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable1, "xrTable1");
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow1
            // 
            resources.ApplyResources(this.xrTableRow1, "xrTableRow1");
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1,
            this.xrTableCell9,
            this.xrTableCell4,
            this.xrTableCell7,
            this.xrTableCell6,
            this.xrTableCell2,
            this.xrTableCell5,
            this.xrTableCell3,
            this.xrTableCell8});
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            // 
            // xrTableCell1
            // 
            resources.ApplyResources(this.xrTableCell1, "xrTableCell1");
            this.xrTableCell1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.StylePriority.UseBackColor = false;
            this.xrTableCell1.StylePriority.UseBorders = false;
            // 
            // xrTableCell9
            // 
            resources.ApplyResources(this.xrTableCell9, "xrTableCell9");
            this.xrTableCell9.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.StylePriority.UseBackColor = false;
            this.xrTableCell9.StylePriority.UseBorders = false;
            // 
            // xrTableCell4
            // 
            resources.ApplyResources(this.xrTableCell4, "xrTableCell4");
            this.xrTableCell4.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.StylePriority.UseBackColor = false;
            this.xrTableCell4.StylePriority.UseBorders = false;
            // 
            // xrTableCell7
            // 
            resources.ApplyResources(this.xrTableCell7, "xrTableCell7");
            this.xrTableCell7.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.StylePriority.UseBackColor = false;
            this.xrTableCell7.StylePriority.UseBorders = false;
            // 
            // xrTableCell6
            // 
            resources.ApplyResources(this.xrTableCell6, "xrTableCell6");
            this.xrTableCell6.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.StylePriority.UseBorders = false;
            // 
            // xrTableCell2
            // 
            resources.ApplyResources(this.xrTableCell2, "xrTableCell2");
            this.xrTableCell2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.StylePriority.UseBackColor = false;
            this.xrTableCell2.StylePriority.UseBorders = false;
            // 
            // xrTableCell5
            // 
            resources.ApplyResources(this.xrTableCell5, "xrTableCell5");
            this.xrTableCell5.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseBackColor = false;
            this.xrTableCell5.StylePriority.UseBorders = false;
            // 
            // xrTableCell3
            // 
            resources.ApplyResources(this.xrTableCell3, "xrTableCell3");
            this.xrTableCell3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            // 
            // xrTableCell8
            // 
            resources.ApplyResources(this.xrTableCell8, "xrTableCell8");
            this.xrTableCell8.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseBackColor = false;
            this.xrTableCell8.StylePriority.UseBorders = false;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrSubreport3,
            this.lblShift,
            this.xrSubreport2,
            this.lbl_totalPieces,
            this.lbl_TaxFileNumber,
            this.lbl_TaxCardNumber,
            this.xrLabel26,
            this.lbl_Handing,
            this.lbl_trans,
            this.xrLabel29,
            this.xrLabel14,
            this.xrLabel21,
            this.lbl_Cust_Address,
            this.lbl_Cust_Tel,
            this.lbl_Cust_Mobile,
            this.xrLabel11,
            this.txt_retention,
            this.txt_advancepayment,
            this.xrLabel25,
            this.xrLabel9,
            this.lbl_CusTaxV,
            this.lbl_AddTaxR,
            this.xrLabel3,
            this.xrLabel2,
            this.lblSubTotal,
            this.lbl_AddTaxV,
            this.lblTotalWords,
            this.xrLabel5,
            this.lbl_ExpensesV,
            this.lbl_Net,
            this.lbl_Remains,
            this.lbl_DiscountR,
            this.xrLabel16,
            this.lbl_Total,
            this.lbl_Paied,
            this.xrLabel17,
            this.xrLabel23,
            this.xrLabel24,
            this.xrLabel19,
            this.xrLabel20,
            this.xrLabel15,
            this.xrPageInfo1,
            this.lbl_DiscountV,
            this.lbl_TaxV,
            this.lbl_BalanceAfter,
            this.lbl_BalanceBefore,
            this.winControlContainer1});
            resources.ApplyResources(this.ReportFooter, "ReportFooter");
            this.ReportFooter.Name = "ReportFooter";
            this.ReportFooter.PrintAtBottom = true;
            // 
            // xrSubreport3
            // 
            resources.ApplyResources(this.xrSubreport3, "xrSubreport3");
            this.xrSubreport3.Name = "xrSubreport3";
            // 
            // lblShift
            // 
            this.lblShift.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lblShift, "lblShift");
            this.lblShift.Name = "lblShift";
            this.lblShift.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblShift.StylePriority.UseBorders = false;
            this.lblShift.StylePriority.UseFont = false;
            this.lblShift.StylePriority.UseTextAlignment = false;
            // 
            // xrSubreport2
            // 
            resources.ApplyResources(this.xrSubreport2, "xrSubreport2");
            this.xrSubreport2.Name = "xrSubreport2";
            // 
            // lbl_totalPieces
            // 
            this.lbl_totalPieces.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_totalPieces, "lbl_totalPieces");
            this.lbl_totalPieces.Name = "lbl_totalPieces";
            this.lbl_totalPieces.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_totalPieces.StylePriority.UseBorders = false;
            this.lbl_totalPieces.StylePriority.UseFont = false;
            this.lbl_totalPieces.StylePriority.UseTextAlignment = false;
            // 
            // lbl_TaxFileNumber
            // 
            this.lbl_TaxFileNumber.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_TaxFileNumber, "lbl_TaxFileNumber");
            this.lbl_TaxFileNumber.Name = "lbl_TaxFileNumber";
            this.lbl_TaxFileNumber.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_TaxFileNumber.StylePriority.UseBorders = false;
            this.lbl_TaxFileNumber.StylePriority.UseFont = false;
            this.lbl_TaxFileNumber.StylePriority.UseTextAlignment = false;
            // 
            // lbl_TaxCardNumber
            // 
            this.lbl_TaxCardNumber.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_TaxCardNumber, "lbl_TaxCardNumber");
            this.lbl_TaxCardNumber.Name = "lbl_TaxCardNumber";
            this.lbl_TaxCardNumber.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_TaxCardNumber.StylePriority.UseBorders = false;
            this.lbl_TaxCardNumber.StylePriority.UseFont = false;
            this.lbl_TaxCardNumber.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel26
            // 
            this.xrLabel26.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel26.CanGrow = false;
            resources.ApplyResources(this.xrLabel26, "xrLabel26");
            this.xrLabel26.Name = "xrLabel26";
            this.xrLabel26.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel26.StylePriority.UseBorders = false;
            this.xrLabel26.StylePriority.UseFont = false;
            this.xrLabel26.StylePriority.UsePadding = false;
            this.xrLabel26.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Handing
            // 
            this.lbl_Handing.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Handing.CanGrow = false;
            resources.ApplyResources(this.lbl_Handing, "lbl_Handing");
            this.lbl_Handing.Name = "lbl_Handing";
            this.lbl_Handing.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Handing.StylePriority.UseBorders = false;
            this.lbl_Handing.StylePriority.UseFont = false;
            this.lbl_Handing.StylePriority.UsePadding = false;
            this.lbl_Handing.StylePriority.UseTextAlignment = false;
            // 
            // lbl_trans
            // 
            this.lbl_trans.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_trans.CanGrow = false;
            resources.ApplyResources(this.lbl_trans, "lbl_trans");
            this.lbl_trans.Name = "lbl_trans";
            this.lbl_trans.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_trans.StylePriority.UseBorders = false;
            this.lbl_trans.StylePriority.UseFont = false;
            this.lbl_trans.StylePriority.UsePadding = false;
            this.lbl_trans.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel29
            // 
            this.xrLabel29.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel29.CanGrow = false;
            resources.ApplyResources(this.xrLabel29, "xrLabel29");
            this.xrLabel29.Name = "xrLabel29";
            this.xrLabel29.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel29.StylePriority.UseBorders = false;
            this.xrLabel29.StylePriority.UseFont = false;
            this.xrLabel29.StylePriority.UsePadding = false;
            this.xrLabel29.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel14
            // 
            this.xrLabel14.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel14.CanGrow = false;
            resources.ApplyResources(this.xrLabel14, "xrLabel14");
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel14.StylePriority.UseBorders = false;
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.StylePriority.UsePadding = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel21
            // 
            this.xrLabel21.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel21.CanGrow = false;
            resources.ApplyResources(this.xrLabel21, "xrLabel21");
            this.xrLabel21.Name = "xrLabel21";
            this.xrLabel21.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel21.StylePriority.UseBorders = false;
            this.xrLabel21.StylePriority.UseFont = false;
            this.xrLabel21.StylePriority.UsePadding = false;
            this.xrLabel21.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Cust_Address
            // 
            this.lbl_Cust_Address.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_Cust_Address, "lbl_Cust_Address");
            this.lbl_Cust_Address.Name = "lbl_Cust_Address";
            this.lbl_Cust_Address.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Cust_Address.StylePriority.UseBorders = false;
            this.lbl_Cust_Address.StylePriority.UseFont = false;
            this.lbl_Cust_Address.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Cust_Tel
            // 
            this.lbl_Cust_Tel.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_Cust_Tel, "lbl_Cust_Tel");
            this.lbl_Cust_Tel.Name = "lbl_Cust_Tel";
            this.lbl_Cust_Tel.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Cust_Tel.StylePriority.UseBorders = false;
            this.lbl_Cust_Tel.StylePriority.UseFont = false;
            this.lbl_Cust_Tel.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Cust_Mobile
            // 
            this.lbl_Cust_Mobile.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_Cust_Mobile, "lbl_Cust_Mobile");
            this.lbl_Cust_Mobile.Name = "lbl_Cust_Mobile";
            this.lbl_Cust_Mobile.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Cust_Mobile.StylePriority.UseBorders = false;
            this.lbl_Cust_Mobile.StylePriority.UseFont = false;
            this.lbl_Cust_Mobile.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel11
            // 
            this.xrLabel11.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel11.CanGrow = false;
            resources.ApplyResources(this.xrLabel11, "xrLabel11");
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel11.StylePriority.UseBorders = false;
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.StylePriority.UsePadding = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            // 
            // txt_retention
            // 
            this.txt_retention.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.txt_retention.CanGrow = false;
            resources.ApplyResources(this.txt_retention, "txt_retention");
            this.txt_retention.Name = "txt_retention";
            this.txt_retention.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.txt_retention.StylePriority.UseBorders = false;
            this.txt_retention.StylePriority.UseFont = false;
            this.txt_retention.StylePriority.UsePadding = false;
            this.txt_retention.StylePriority.UseTextAlignment = false;
            // 
            // txt_advancepayment
            // 
            this.txt_advancepayment.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.txt_advancepayment.CanGrow = false;
            resources.ApplyResources(this.txt_advancepayment, "txt_advancepayment");
            this.txt_advancepayment.Name = "txt_advancepayment";
            this.txt_advancepayment.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.txt_advancepayment.StylePriority.UseBorders = false;
            this.txt_advancepayment.StylePriority.UseFont = false;
            this.txt_advancepayment.StylePriority.UsePadding = false;
            this.txt_advancepayment.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel25
            // 
            this.xrLabel25.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel25.CanGrow = false;
            resources.ApplyResources(this.xrLabel25, "xrLabel25");
            this.xrLabel25.Name = "xrLabel25";
            this.xrLabel25.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel25.StylePriority.UseBorders = false;
            this.xrLabel25.StylePriority.UseFont = false;
            this.xrLabel25.StylePriority.UsePadding = false;
            this.xrLabel25.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel9.CanGrow = false;
            resources.ApplyResources(this.xrLabel9, "xrLabel9");
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel9.StylePriority.UseBorders = false;
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UsePadding = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            // 
            // lbl_CusTaxV
            // 
            this.lbl_CusTaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_CusTaxV.CanGrow = false;
            resources.ApplyResources(this.lbl_CusTaxV, "lbl_CusTaxV");
            this.lbl_CusTaxV.Name = "lbl_CusTaxV";
            this.lbl_CusTaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_CusTaxV.StylePriority.UseBorders = false;
            this.lbl_CusTaxV.StylePriority.UseFont = false;
            this.lbl_CusTaxV.StylePriority.UsePadding = false;
            this.lbl_CusTaxV.StylePriority.UseTextAlignment = false;
            // 
            // lbl_AddTaxR
            // 
            this.lbl_AddTaxR.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_AddTaxR.CanGrow = false;
            resources.ApplyResources(this.lbl_AddTaxR, "lbl_AddTaxR");
            this.lbl_AddTaxR.Name = "lbl_AddTaxR";
            this.lbl_AddTaxR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AddTaxR.StylePriority.UseBorders = false;
            this.lbl_AddTaxR.StylePriority.UseFont = false;
            this.lbl_AddTaxR.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel3.CanGrow = false;
            resources.ApplyResources(this.xrLabel3, "xrLabel3");
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UsePadding = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel2.CanGrow = false;
            resources.ApplyResources(this.xrLabel2, "xrLabel2");
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel2.StylePriority.UseBorders = false;
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UsePadding = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            // 
            // lblSubTotal
            // 
            this.lblSubTotal.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lblSubTotal.CanGrow = false;
            resources.ApplyResources(this.lblSubTotal, "lblSubTotal");
            this.lblSubTotal.Name = "lblSubTotal";
            this.lblSubTotal.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lblSubTotal.StylePriority.UseBorders = false;
            this.lblSubTotal.StylePriority.UseFont = false;
            this.lblSubTotal.StylePriority.UsePadding = false;
            this.lblSubTotal.StylePriority.UseTextAlignment = false;
            // 
            // lbl_AddTaxV
            // 
            this.lbl_AddTaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_AddTaxV, "lbl_AddTaxV");
            this.lbl_AddTaxV.Name = "lbl_AddTaxV";
            this.lbl_AddTaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AddTaxV.StylePriority.UseBorders = false;
            this.lbl_AddTaxV.StylePriority.UseFont = false;
            this.lbl_AddTaxV.StylePriority.UseTextAlignment = false;
            // 
            // lblTotalWords
            // 
            resources.ApplyResources(this.lblTotalWords, "lblTotalWords");
            this.lblTotalWords.Name = "lblTotalWords";
            this.lblTotalWords.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotalWords.StylePriority.UseBackColor = false;
            this.lblTotalWords.StylePriority.UseFont = false;
            this.lblTotalWords.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel5
            // 
            resources.ApplyResources(this.xrLabel5, "xrLabel5");
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.StylePriority.UseBackColor = false;
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DiscountV
            // 
            this.lbl_DiscountV.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_DiscountV.CanGrow = false;
            resources.ApplyResources(this.lbl_DiscountV, "lbl_DiscountV");
            this.lbl_DiscountV.Name = "lbl_DiscountV";
            this.lbl_DiscountV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_DiscountV.StylePriority.UseBorders = false;
            this.lbl_DiscountV.StylePriority.UseFont = false;
            this.lbl_DiscountV.StylePriority.UsePadding = false;
            this.lbl_DiscountV.StylePriority.UseTextAlignment = false;
            // 
            // winControlContainer1
            // 
            resources.ApplyResources(this.winControlContainer1, "winControlContainer1");
            this.winControlContainer1.Name = "winControlContainer1";
            this.winControlContainer1.WinControl = this.grd;
            // 
            // grd
            // 
            resources.ApplyResources(this.grd, "grd");
            this.grd.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd.EmbeddedNavigator.AccessibleDescription");
            this.grd.EmbeddedNavigator.AccessibleName = resources.GetString("grd.EmbeddedNavigator.AccessibleName");
            this.grd.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd.EmbeddedNavigator.Anchor")));
            this.grd.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd.EmbeddedNavigator.BackgroundImage")));
            this.grd.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd.EmbeddedNavigator.ImeMode")));
            this.grd.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd.EmbeddedNavigator.MaximumSize")));
            this.grd.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd.EmbeddedNavigator.TextLocation")));
            this.grd.EmbeddedNavigator.ToolTip = resources.GetString("grd.EmbeddedNavigator.ToolTip");
            this.grd.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd.EmbeddedNavigator.ToolTipIconType")));
            this.grd.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd.EmbeddedNavigator.ToolTipTitle");
            this.grd.MainView = this.gridView1;
            this.grd.Name = "grd";
            this.grd.ShowOnlyPredefinedDetails = true;
            this.grd.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.gridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Image")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.GradientMode")));
            this.gridView1.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.Image")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupRow.GradientMode")));
            this.gridView1.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupRow.Image")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.gridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Image")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Lines.FontSizeDelta")));
            this.gridView1.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Lines.FontStyleDelta")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Lines.GradientMode")));
            this.gridView1.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Lines.Image")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Row.FontSizeDelta")));
            this.gridView1.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Row.FontStyleDelta")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Row.GradientMode")));
            this.gridView1.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Row.Image")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.ColumnPanelRowHeight = 35;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn1});
            this.gridView1.GridControl = this.grd;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsPrint.AutoWidth = false;
            this.gridView1.OptionsPrint.UsePrintStyles = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.ColumnAutoWidth = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.FieldName = "Qtys";
            this.gridColumn2.MaxWidth = 100;
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.FixedWidth = true;
            this.gridColumn2.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "Category";
            this.gridColumn1.MaxWidth = 100;
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.FixedWidth = true;
            this.gridColumn1.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // xr_Totals
            // 
            resources.ApplyResources(this.xr_Totals, "xr_Totals");
            this.xr_Totals.Name = "xr_Totals";
            this.xr_Totals.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow6});
            this.xr_Totals.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow6
            // 
            this.xrTableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Qtys,
            this.cell_Category});
            resources.ApplyResources(this.xrTableRow6, "xrTableRow6");
            this.xrTableRow6.Name = "xrTableRow6";
            // 
            // cell_Qtys
            // 
            resources.ApplyResources(this.cell_Qtys, "cell_Qtys");
            this.cell_Qtys.Name = "cell_Qtys";
            // 
            // cell_Category
            // 
            resources.ApplyResources(this.cell_Category, "cell_Category");
            this.cell_Category.Name = "cell_Category";
            // 
            // DetailReport
            // 
            this.DetailReport.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail1,
            this.ReportHeader});
            resources.ApplyResources(this.DetailReport, "DetailReport");
            this.DetailReport.Level = 0;
            this.DetailReport.Name = "DetailReport";
            // 
            // Detail1
            // 
            this.Detail1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            resources.ApplyResources(this.Detail1, "Detail1");
            this.Detail1.Name = "Detail1";
            // 
            // ReportHeader
            // 
            this.ReportHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            resources.ApplyResources(this.ReportHeader, "ReportHeader");
            this.ReportHeader.Name = "ReportHeader";
            // 
            // DetailReport_TotalQtyPerCategory
            // 
            this.DetailReport_TotalQtyPerCategory.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail2,
            this.ReportHeader1});
            resources.ApplyResources(this.DetailReport_TotalQtyPerCategory, "DetailReport_TotalQtyPerCategory");
            this.DetailReport_TotalQtyPerCategory.Level = 1;
            this.DetailReport_TotalQtyPerCategory.Name = "DetailReport_TotalQtyPerCategory";
            // 
            // Detail2
            // 
            this.Detail2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xr_Totals});
            resources.ApplyResources(this.Detail2, "Detail2");
            this.Detail2.Name = "Detail2";
            // 
            // ReportHeader1
            // 
            resources.ApplyResources(this.ReportHeader1, "ReportHeader1");
            this.ReportHeader1.Name = "ReportHeader1";
            // 
            // rpt_SL_Invoice
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter,
            this.DetailReport,
            this.DetailReport_TotalQtyPerCategory});
            resources.ApplyResources(this, "$this");
            this.ShowPrintMarginsWarning = false;
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xr_Totals)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel lbl_store;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_Serial;
        private DevExpress.XtraReports.UI.XRLabel lbl_Customer;
        private DevExpress.XtraReports.UI.XRLabel lbl_Paymethod;
        private DevExpress.XtraReports.UI.XRLabel lbl_Drawer;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRLabel xrLabel24;
        private DevExpress.XtraReports.UI.XRLabel lbl_Paied;
        private DevExpress.XtraReports.UI.XRLabel lbl_Net;
        private DevExpress.XtraReports.UI.XRLabel lbl_Remains;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountR;
        private DevExpress.XtraReports.UI.XRLabel lbl_TaxV;
        private DevExpress.XtraReports.UI.XRLabel lbl_Total;
        private DevExpress.XtraReports.UI.XRLabel lbl_ExpensesV;
        private DevExpress.XtraReports.UI.XRLabel xrLabel17;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Total;
        private DevExpress.XtraReports.UI.XRTableCell cell_Disc;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio;
        private DevExpress.XtraReports.UI.XRTableCell cell_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_code;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLabel lblTotalWords;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountV;
        private DevExpress.XtraReports.UI.XRLabel lbl_SalesEmp;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel lbl_Shipping;
        private DevExpress.XtraReports.UI.XRLabel xrLabel18;
        private DevExpress.XtraReports.UI.XRLabel lbl_DeliverDate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
        private DevExpress.XtraReports.UI.XRLabel lbl_PurchaseOrderNo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel22;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell cell_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_Length;
        private DevExpress.XtraReports.UI.XRTableCell cell_TotalQty;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemDescription;
        private DevExpress.XtraReports.UI.XRLabel lbl_salesEmp_Job;
        private DevExpress.XtraReports.UI.XRLabel lbl_TaxR;
        private DevExpress.XtraReports.UI.XRLabel lbl_ExpensesR;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM_Factor;
        private DevExpress.XtraReports.UI.XRTableCell cell_Factor;
        private DevExpress.XtraReports.UI.XRTable xrTable5;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow5;
        private DevExpress.XtraReports.UI.XRTableCell cell_Expire;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio2;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio3;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesTaxRatio;
        private DevExpress.XtraReports.UI.XRLabel lbl_DeductTaxV;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesTax;
        private DevExpress.XtraReports.UI.XRLabel lbl_AddTaxV;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRTableCell cell_code2;
        private DevExpress.XtraReports.UI.XRLabel lbl_BalanceBefore;
        private DevExpress.XtraReports.UI.XRLabel lbl_BalanceAfter;
        private DevExpress.XtraReports.UI.XRLabel lbl_Destination;
        private DevExpress.XtraReports.UI.XRLabel lbl_VehicleNumber;
        private DevExpress.XtraReports.UI.XRLabel lbl_DriverName;
        private DevExpress.XtraReports.UI.XRTableCell cell_PicPath;
        private DevExpress.XtraReports.UI.XRPictureBox pic_ItemPic;
        private DevExpress.XtraReports.UI.XRLabel lbl_ProcessName;
        private DevExpress.XtraReports.UI.XRLabel lbl_SourceCode;
        private DevExpress.XtraReports.UI.XRLabel lbl_ScaleWeightSerial;
        private DevExpress.XtraReports.UI.XRTableCell cell_Serial;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lblSubTotal;
        private DevExpress.XtraReports.UI.XRLabel lbl_AddTaxR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRTableCell cell_Batch;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell cell_ManufactureDate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel lbl_CusTaxV;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel txt_retention;
        private DevExpress.XtraReports.UI.XRLabel txt_advancepayment;
        private DevExpress.XtraReports.UI.XRLabel xrLabel25;
        private DevExpress.XtraReports.UI.XRLabel lblDue;
        private DevExpress.XtraReports.UI.XRLabel lbl_DueDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_TotalQty;
        private DevExpress.XtraReports.UI.XRTableCell cell_AudiencePrice;
        private DevExpress.XtraReports.UI.XRBarCode xrBarCode_Voucher;
        private DevExpress.XtraReports.UI.XRLabel lbl_Cust_Address;
        private DevExpress.XtraReports.UI.XRLabel lbl_Cust_Tel;
        private DevExpress.XtraReports.UI.XRLabel lbl_Cust_Mobile;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel xrLabel21;
        private DevExpress.XtraReports.UI.XRLabel xrLabel26;
        private DevExpress.XtraReports.UI.XRLabel lbl_Handing;
        private DevExpress.XtraReports.UI.XRLabel lbl_trans;
        private DevExpress.XtraReports.UI.XRLabel xrLabel29;
        private DevExpress.XtraReports.UI.XRTableCell cell_Weight_KG;
        private DevExpress.XtraReports.UI.XRTableCell cell_PiecesCount;
        private DevExpress.XtraReports.UI.XRLabel lbl_TaxFileNumber;
        private DevExpress.XtraReports.UI.XRLabel lbl_TaxCardNumber;
        private DevExpress.XtraReports.UI.XRLabel lbl_totalPieces;
        private DevExpress.XtraReports.UI.XRSubreport xrSubreport2;
        private DevExpress.XtraReports.UI.XRLabel lblShift;
        private DevExpress.XtraReports.UI.XRLabel lbl_Updated;
        private DevExpress.XtraReports.UI.XRSubreport xrSubreport3;
        private DevExpress.XtraReports.UI.XRTableCell cell_Location;
        private DevExpress.XtraReports.UI.XRLabel txtDelivery;
        private DevExpress.XtraReports.UI.XRTableCell cell_Pack;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemDescriptionEn;
        private DevExpress.XtraReports.UI.WinControlContainer winControlContainer1;
        private DevExpress.XtraGrid.GridControl grd;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private System.DirectoryServices.DirectoryEntry directoryEntry1;
        private DevExpress.XtraReports.UI.XRTable xr_Totals;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow6;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qtys;
        private DevExpress.XtraReports.UI.XRTableCell cell_Category;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport;
        private DevExpress.XtraReports.UI.DetailBand Detail1;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_TotalQtyPerCategory;
        private DevExpress.XtraReports.UI.DetailBand Detail2;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader1;
    }
}
