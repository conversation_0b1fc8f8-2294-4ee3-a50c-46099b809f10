﻿using System;
using System.Drawing;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraNavBar;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraReports.UI;
using System.Threading;
using System.Globalization;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraEditors.Repository;
using DAL;

namespace Reports
{
    public static class ReportsRTL
    {
        #region RTL & Culture Methods
        //changed by login screen only

        public static void EnCulture(bool isEnglish)
        {
            if (isEnglish)
            {
                Thread.CurrentThread.CurrentCulture = new CultureInfo("en-US");
                Thread.CurrentThread.CurrentUICulture = new CultureInfo("en-US");
            }
            else
            {
                //string defaultLanguage = Thread.CurrentThread.CurrentUICulture.ToString();
                Thread.CurrentThread.CurrentCulture = new CultureInfo("ar-EG");
                Thread.CurrentThread.CurrentUICulture = new CultureInfo("ar-EG");
            }
        }

        public static void RTLLayout(XtraReport rpt)
        {
            foreach (DevExpress.XtraReports.UI.XRControl x in rpt.Bands[BandKind.Detail].Controls)
            {
                if (x is DevExpress.XtraReports.UI.WinControlContainer)
                {
                    DevExpress.XtraReports.UI.WinControlContainer y = x as DevExpress.XtraReports.UI.WinControlContainer;
                    MirrorControls(y.WinControl, x.Location, x.Width, null);
                }
            }
        }

        public static void LTRLayout(Control ctrl)
        {
            ctrl.RightToLeft = RightToLeft.No;
            foreach (Control c in ctrl.Controls)
            {
                MirrorControls(c, ctrl.Location, ctrl.Width, ctrl);
            }
        }

        /// <summary>
        /// mirror all types of controls 
        /// </summary>
        /// <param name="ctrl"></param>
        /// <param name="c"></param>
        private static void MirrorControls(Control c, Point ContainerLocation, int ContainerWidth, Control Container)
        {
            if (Container != null)
            {
                if (Container is XtraForm)
                    c.Location = new System.Drawing.Point
                    (ContainerWidth - c.Location.X - c.Width - 14, c.Location.Y);
                else if ((c is System.Windows.Forms.SplitContainer) == false && (c is System.Windows.Forms.SplitterPanel) == false)
                    c.Location = new System.Drawing.Point
                    (ContainerWidth - c.Location.X - c.Width, c.Location.Y);
            }

            #region Anchor
            if (c.Dock != DockStyle.None)
            { }
            else if (c.Anchor == ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right))))
                c.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            else if (c.Anchor == ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right))))
                c.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            else if (c.Anchor == ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left))))
                c.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            else if (c.Anchor == ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right))))
                c.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)));
            else if (c.Anchor == ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left))))
                c.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            else if (c.Anchor == ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right))))
                c.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            else if (c.Anchor == ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left))))
                c.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            #endregion

            if (c is BaseEdit)
            {
                ((BaseEdit)c).Properties.Appearance.TextOptions.HAlignment =
                    DevExpress.Utils.HorzAlignment.Near;
                ((BaseEdit)c).RightToLeft = RightToLeft.No;

                if (c is TextEdit)
                {
                    if (((TextEdit)c).Properties.DisplayFormat.FormatString.Contains("n"))
                    {
                        ((TextEdit)c).Properties.Appearance.TextOptions.HAlignment =
                            DevExpress.Utils.HorzAlignment.Far;
                        ((TextEdit)c).Properties.AppearanceFocused.TextOptions.HAlignment =
                            DevExpress.Utils.HorzAlignment.Far;
                    }
                    else
                    {
                        ((TextEdit)c).Properties.Appearance.TextOptions.HAlignment =
                            DevExpress.Utils.HorzAlignment.Near;
                        ((TextEdit)c).Properties.AppearanceFocused.TextOptions.HAlignment =
                            DevExpress.Utils.HorzAlignment.Near;
                    }
                }
                if (c is SpinEdit)
                {
                        ((TextEdit)c).Properties.Appearance.TextOptions.HAlignment =
                            DevExpress.Utils.HorzAlignment.Far;
                        ((TextEdit)c).Properties.AppearanceFocused.TextOptions.HAlignment =
                            DevExpress.Utils.HorzAlignment.Far;                    
                }
                if (c is DateEdit)
                {
                    ((TextEdit)c).Properties.Appearance.TextOptions.HAlignment =
                        DevExpress.Utils.HorzAlignment.Near;
                    ((TextEdit)c).Properties.AppearanceFocused.TextOptions.HAlignment =
                        DevExpress.Utils.HorzAlignment.Near;
                }
                if (c is MemoEdit)
                {
                    ((MemoEdit)c).Properties.Appearance.TextOptions.HAlignment =
                        DevExpress.Utils.HorzAlignment.Near;
                    ((MemoEdit)c).Properties.AppearanceFocused.TextOptions.HAlignment =
                        DevExpress.Utils.HorzAlignment.Near;
                }
                else if (c is CheckEdit)
                {
                    CheckEdit chk = c as CheckEdit;
                    chk.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Near;
                }
                else if (c is RadioGroup)
                {
                    RadioGroup chk = c as RadioGroup;
                    chk.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Near;
                }
                else if (c is LookUpEdit)
                {
                    #region LookUpEdit
                    LookUpEdit lkp = c as LookUpEdit;

                    ReorderLkpEditColumns(lkp);
                    #endregion
                }
                else if (c is GridLookUpEdit)
                {
                    GridLookUpEdit lkp = c as GridLookUpEdit;
                    lkp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                    GridView gv = ((GridLookUpEdit)c).Properties.View as GridView;
                    ReorderGridViewColumns(gv, null);
                }
            }
            else if (c is GridControl)
            {
                GridView gv = null;
                DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bnGv = null;

                if (((GridControl)c).FocusedView is DevExpress.XtraGrid.Views.BandedGrid.BandedGridView)
                {
                    bnGv = ((GridControl)c).FocusedView as DevExpress.XtraGrid.Views.BandedGrid.BandedGridView;
                    ReorderGridViewColumns(gv, bnGv);

                }
                else if (((GridControl)c).FocusedView is GridView)
                {
                    gv = ((GridControl)c).FocusedView as GridView;
                    ReorderGridViewColumns(gv, bnGv);
                }
            }
            else if (c is NavBarControl)
            {
                #region NavBar
                NavBarControl nb = c as NavBarControl;
                nb.Appearance.Background.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                nb.Appearance.Button.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.ButtonDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.ButtonHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.ButtonPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                nb.Appearance.GroupBackground.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.GroupHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.GroupHeaderPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.GroupHeaderActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.GroupHeaderHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                nb.Appearance.Hint.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                nb.Appearance.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.ItemActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.ItemDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.ItemPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.ItemHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                nb.Appearance.LinkDropTarget.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                nb.Appearance.NavigationPaneHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                #endregion
            }
            else if (c is ImageListBoxControl)
            {
                ImageListBoxControl lb = c as ImageListBoxControl;
                lb.RightToLeft = RightToLeft.No;
                lb.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            }
            else if (c is CheckedListBoxControl)
            {
                CheckedListBoxControl lb = c as CheckedListBoxControl;
                lb.RightToLeft = RightToLeft.No;
                lb.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            }
            else if (c is ImageComboBoxEdit)
            {
                ImageComboBoxEdit lb = c as ImageComboBoxEdit;
                lb.RightToLeft = RightToLeft.No;
            }
            else if (c is TreeView)
            {
                TreeView tr = c as TreeView;
                tr.RightToLeft = RightToLeft.No;
            }
            else if (c is DevExpress.XtraLayout.LayoutControl)
            {
                DevExpress.XtraLayout.LayoutControl ly = c as DevExpress.XtraLayout.LayoutControl;
                ly.RightToLeft = RightToLeft.Yes;
                LTRLayout(ly);
                //foreach (DevExpress.XtraLayout.LayoutControlItem i in ly.Root.Items)
                //{
                //    ly.controls
                //}
            }
            else if (c is DevExpress.XtraTab.XtraTabControl)
            {
                DevExpress.XtraTab.XtraTabControl x = c as DevExpress.XtraTab.XtraTabControl;

                if (x.HeaderLocation == DevExpress.XtraTab.TabHeaderLocation.Right)
                    x.HeaderLocation = DevExpress.XtraTab.TabHeaderLocation.Left;
                else
                {
                    List<DevExpress.XtraTab.XtraTabPage> lst_bar_links = new List<DevExpress.XtraTab.XtraTabPage>();
                    foreach (DevExpress.XtraTab.XtraTabPage link in x.TabPages.AsEnumerable())
                        lst_bar_links.Add(link);

                    x.TabPages.Clear();
                    x.TabPages.AddRange(lst_bar_links.ToArray().Reverse().ToArray());
                }

                foreach (DevExpress.XtraTab.XtraTabPage p in x.TabPages)
                {
                    LTRLayout(p);
                }
            }
            else if (c is TabControl)
            {
                TabControl x = c as TabControl;
                x.RightToLeft = RightToLeft.No;

                foreach (TabPage p in x.TabPages)
                {
                    LTRLayout(p);
                }
            }
            else if (c is DevExpress.XtraBars.Docking.DockPanel)
            {
                DevExpress.XtraBars.Docking.DockPanel x = c as DevExpress.XtraBars.Docking.DockPanel;
                x.Dock = DevExpress.XtraBars.Docking.DockingStyle.Right;

                LTRLayout(x);
            }
            else if (c is ScrollableControl || c is IContainerControl)
            {
                if (c is GroupControl)
                {
                    ((GroupControl)c).AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                    if (((GroupControl)c).CaptionLocation == DevExpress.Utils.Locations.Right)
                        ((GroupControl)c).CaptionLocation = DevExpress.Utils.Locations.Left;
                    else if (((GroupControl)c).CaptionLocation == DevExpress.Utils.Locations.Right)
                        ((GroupControl)c).CaptionLocation = DevExpress.Utils.Locations.Left;
                }
                else if (c is FlowLayoutPanel)
                {
                    if (((FlowLayoutPanel)c).FlowDirection == FlowDirection.RightToLeft)
                        ((FlowLayoutPanel)c).FlowDirection = FlowDirection.LeftToRight;
                }
                LTRLayout(c);
            }
            else if (c is GroupBox)
            {
                ((GroupBox)c).RightToLeft = RightToLeft.No;
                LTRLayout(c);
            }
            else if (c is UserControl)
            {
                LTRLayout(c);
            }
        }

        private static void ReorderLkpEditColumns(LookUpEdit lkp)
        {
            lkp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

            List<DevExpress.XtraEditors.Controls.LookUpColumnInfo> cols =
                new List<DevExpress.XtraEditors.Controls.LookUpColumnInfo>();

            foreach (DevExpress.XtraEditors.Controls.LookUpColumnInfo col in lkp.Properties.Columns)
            {
                cols.Add(col);
            }
            lkp.Properties.Columns.Clear();

            cols.Reverse(0, cols.Count);
            foreach (DevExpress.XtraEditors.Controls.LookUpColumnInfo col in cols)
            {
                col.Alignment = DevExpress.Utils.HorzAlignment.Near;
                lkp.Properties.Columns.Add(col);
            }
        }
        private static void ReorderLkpEditColumns(RepositoryItemLookUpEdit lkp)
        {
            lkp.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

            List<DevExpress.XtraEditors.Controls.LookUpColumnInfo> cols =
                new List<DevExpress.XtraEditors.Controls.LookUpColumnInfo>();

            foreach (DevExpress.XtraEditors.Controls.LookUpColumnInfo col in lkp.Columns)
            {
                cols.Add(col);
            }
            lkp.Columns.Clear();

            cols.Reverse(0, cols.Count);
            foreach (DevExpress.XtraEditors.Controls.LookUpColumnInfo col in cols)
            {
                col.Alignment = DevExpress.Utils.HorzAlignment.Near;
                lkp.Columns.Add(col);
            }
        }

        public static void MirrorGridControl(GridControl g)
        {
            GridView gv = null;
            DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bnGv = null;

            if (((GridControl)g).FocusedView is DevExpress.XtraGrid.Views.BandedGrid.BandedGridView)
            {
                bnGv = ((GridControl)g).FocusedView as DevExpress.XtraGrid.Views.BandedGrid.BandedGridView;
                ReorderGridViewColumns(gv, bnGv);

            }
            else if (((GridControl)g).FocusedView is GridView)
            {
                gv = ((GridControl)g).FocusedView as GridView;
                ReorderGridViewColumns(gv, bnGv);
            }
        }

        /// <summary>
        /// used to reorder dataGrid or gridLookup columns for RTL purpose
        /// </summary>
        /// <param name="gv">GridView</param>
        private static void ReorderGridViewColumns(GridView gv, DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bnGv)
        {
            if (gv != null)
            {
                #region GridView
                gv.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                gv.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                gv.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                gv.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                int maxvi = gv.Columns.Select(x => x.VisibleIndex).ToList().DefaultIfEmpty(0).Max();

                foreach (GridColumn col in gv.Columns)
                {
                    if (col.Visible)
                        col.VisibleIndex = maxvi - col.VisibleIndex;

                    if (col.DisplayFormat.FormatString.Contains("n") == false)
                        col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                    else
                        col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;

                    if (col.AppearanceHeader.TextOptions.HAlignment == DevExpress.Utils.HorzAlignment.Far)
                        col.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;


                    //reorder repository columns
                    if (col.ColumnEdit != null && col.ColumnEdit is RepositoryItemGridLookUpEdit)
                    {
                        RepositoryItemGridLookUpEdit r = col.ColumnEdit as RepositoryItemGridLookUpEdit;
                        ReorderGridViewColumns(r.View, null);
                    }
                    if (col.ColumnEdit != null && col.ColumnEdit is RepositoryItemLookUpEdit)
                    {
                        RepositoryItemLookUpEdit r = col.ColumnEdit as RepositoryItemLookUpEdit;
                        ReorderLkpEditColumns(r);
                    }
                }
                #endregion
            }
            else //advBanded
            {
                #region BandedGrid
                bnGv.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                bnGv.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                bnGv.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                List<DevExpress.XtraGrid.Views.BandedGrid.GridBand> bands = new List<DevExpress.XtraGrid.Views.BandedGrid.GridBand>();
                List<DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn> cols = new List<DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn>();
                for (int x = 0; x < bnGv.Bands.Count; x++)
                {
                    bands.Add(bnGv.Bands[x]);
                }
                bands = bands.OrderByDescending(l => l.VisibleIndex).ToList<DevExpress.XtraGrid.Views.BandedGrid.GridBand>();
                bnGv.Bands.Clear();

                foreach (DevExpress.XtraGrid.Views.BandedGrid.GridBand band in bands)
                {
                    if (band.AppearanceHeader.TextOptions.HAlignment == DevExpress.Utils.HorzAlignment.Far)
                        band.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                    List<GridColumn> bCols = new List<GridColumn>();
                    for (int x = 0; x < band.Columns.Count; x++)
                    {
                        bCols.Add(band.Columns[x]);
                    }
                    bCols.Reverse(0, band.Columns.Count);
                    band.Columns.Clear();

                    int counter = 0;
                    foreach (DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col in bCols)
                    {
                        if (col.DisplayFormat.FormatString.Contains("n") == false)
                            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                        else
                            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;

                        if (col.AppearanceHeader.TextOptions.HAlignment == DevExpress.Utils.HorzAlignment.Far)
                            col.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;

                        if (col.VisibleIndex == -1)
                            band.Columns.Add(col);
                        else
                        {
                            col.VisibleIndex = counter;
                            band.Columns.Add(col);
                            counter++;
                        }

                        //reorder repository columns
                        if (col.ColumnEdit != null && col.ColumnEdit is RepositoryItemGridLookUpEdit)
                        {
                            RepositoryItemGridLookUpEdit r = col.ColumnEdit as RepositoryItemGridLookUpEdit;
                            ReorderGridViewColumns(r.View, null);
                        }
                        if (col.ColumnEdit != null && col.ColumnEdit is RepositoryItemLookUpEdit)
                        {
                            RepositoryItemLookUpEdit r = col.ColumnEdit as RepositoryItemLookUpEdit;
                            ReorderLkpEditColumns(r);
                        }
                    }
                    bnGv.Bands.Add(band);
                }
            }
                #endregion
        }

        public static void RTL_BarManager(DevExpress.XtraBars.BarManager barManager1)
        {
            if (Shared.IsEnglish == false)
                return;

            foreach (DevExpress.XtraBars.BarItem item in barManager1.Items)
                item.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Left;

            foreach (DevExpress.XtraBars.Bar bar in barManager1.Bars)
            {
                List<DevExpress.XtraBars.LinkPersistInfo> lst_bar_links =
                new List<DevExpress.XtraBars.LinkPersistInfo>();
                foreach (DevExpress.XtraBars.LinkPersistInfo link in bar.LinksPersistInfo)
                    lst_bar_links.Add(link);

                bar.LinksPersistInfo.Clear();// = new DevExpress.XtraBars.LinksInfo();
                bar.LinksPersistInfo.AddRange(lst_bar_links.ToArray().Reverse().ToArray());
            }

        }


        #endregion
    }
}
