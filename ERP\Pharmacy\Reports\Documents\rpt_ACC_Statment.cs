using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_ACC_Statment : DevExpress.XtraReports.UI.XtraReport
    {
        string accountType, accountName, AcNumber,
            FromDate, ToDate,
            userName, totalDebit, totalCredit, Total,
            P0, P1, P2, P3, P4, P5,
            _P0caption, _P1caption, _P2caption, _P3caption, _P4caption, _P5caption, CurrenyName;
        DataTable dt_inv_details;

        public rpt_ACC_Statment()
        {
            InitializeComponent();
        }

        public rpt_ACC_Statment(string _accountType, string _accountName,
            string _FromDate, string _ToDate,
            DataTable dt, string userName, string _totalDebit, string _totalCredit, string _Total,
            string _P0, string _P1, string _P2, string _P3, string _P4, string _P5,
            string _P0caption, string _P1caption, string _P2caption, string _P3caption, string _P4caption, string _P5caption,
            string CurrenyName, string _AcNumber = "")
        {
            InitializeComponent();

            accountType = _accountType;
            accountName = _accountName;
            FromDate = _FromDate;
            ToDate = _ToDate;

            this.userName = userName;
            totalDebit = _totalDebit;
            totalCredit = _totalCredit;
            Total = _Total;

            P0 = _P0;
            P1 = _P1;
            P2 = _P2;
            P3 = _P3;
            P4 = _P4;
            P5 = _P5;

            this._P0caption = _P0caption;
            this._P1caption = _P1caption;
            this._P2caption = _P2caption;
            this._P3caption = _P3caption;
            this._P4caption = _P4caption;
            this._P5caption = _P5caption;
            this.CurrenyName = CurrenyName;
            this.AcNumber = _AcNumber;
            dt_inv_details = dt;
            this.DataSource = dt_inv_details;
            getReportHeader();
            //LoadData();
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = Shared.IsEnglish ? comp.CmpNameEn : comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_accountType.Text = accountType;
            lbl_accountName.Text = accountName;
            lbl_fromDate.Text = FromDate;
            lbl_toDate.Text = ToDate;
            lbl_User.Text = userName;

            lbl_TotalDebit.Text = totalDebit;
            lbl_TotalCredit.Text = totalCredit;

            lblTotal.Text = Total;
            lbl_CurrencyName.Text = CurrenyName;

            cell_P0.Text = P0;
            cell_P1.Text = P1;
            cell_P2.Text = P2;
            cell_P3.Text = P3;
            cell_P4.Text = P4;
            cell_P5.Text = P5;

            cell_header_P0.Text = _P0caption;
            cell_header_P1.Text = _P1caption;
            cell_header_P2.Text = _P2caption;
            cell_header_P3.Text = _P3caption;
            cell_header_P4.Text = _P4caption;
            cell_header_P5.Text = _P5caption;

            this.DataSource = dt_inv_details;

            cell_JCode.DataBindings.Add("Text", this.DataSource, "JCode");
            cell_process.DataBindings.Add("Text", this.DataSource, "ProcessName");
            cell_insertDate.DataBindings.Add("Text", this.DataSource, "InsertDate");
            cell_debit.DataBindings.Add("Text", this.DataSource, "Debit");
            cell_credit.DataBindings.Add("Text", this.DataSource, "Credit");
            cell_notes.DataBindings.Add("Text", this.DataSource, "Notes");
            cell_balance.DataBindings.Add("Text", this.DataSource, "Balance");

            cell_JNumber.DataBindings.Add("Text", this.DataSource, "JNumber");
            cell_CrncId.DataBindings.Add("Text", this.DataSource, "CrncId");
            cell_CrncRate.DataBindings.Add("Text", this.DataSource, "CrncRate");
            cell_fDebit.DataBindings.Add("Text", this.DataSource, "fDebit");
            cell_fCredit.DataBindings.Add("Text", this.DataSource, "fCredit");
            lblAcNumber.Text = AcNumber;
        }

        int count = 1;
        private void cell_index_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
            cell_index.Text = (count).ToString();
            count++;
        }
    }
}
