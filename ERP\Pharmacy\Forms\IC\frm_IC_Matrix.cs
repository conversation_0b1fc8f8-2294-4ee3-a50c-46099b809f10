﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Columns;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_IC_Matrix : DevExpress.XtraEditors.XtraForm
    {
        int Id;
        DataTable dtMtDetails = new DataTable();
        FormAction action = FormAction.None;        
        UserPriv prvlg;
        bool DataModified;

        public frm_IC_Matrix(int Id, FormAction action)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(barManager1);

            this.Id = Id;
            this.action = action;
        }

        private void frm_ACC_CustomAccList_Load(object sender, EventArgs e)
        {            
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            LoadPrivilege();            
            BindDataSources();

            LoadMatrix();
            ErpUtils.ColumnChooser(grdMtDetails);
        }

        private void frm_ACC_CustomAccList_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }

        private void frm_ACC_CustomAccList_KeyUp(object sender, KeyEventArgs e)
        {      
            if (e.KeyCode == Keys.Insert)
                grdMtDetails.Focus();

            if (e.KeyCode == Keys.PageUp)
            {
                btnPrevious.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                txtName.Focus();
            }
        }


        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();

            int lastInvId = (from x in DB.IC_Matrixes
                             where x.MatrixId > Id                             
                             orderby x.MatrixId ascending
                             select x.MatrixId).FirstOrDefault();

            if (lastInvId != 0)
            {
                Id = lastInvId;
                LoadMatrix();
            }
            else
            {
                lastInvId = (from x in DB.IC_Matrixes                             
                             orderby x.MatrixId ascending
                             select x.MatrixId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    Id = lastInvId;
                    LoadMatrix();
                }
            }     
        }

        private void btnPrevious_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();

            int lastInvId = (from x in DB.IC_Matrixes
                             where x.MatrixId < Id
                             orderby x.MatrixId descending
                             select x.MatrixId).FirstOrDefault();

            if (lastInvId != 0)
            {
                Id = lastInvId;
                LoadMatrix();
            }
            else
            {
                lastInvId = (from x in DB.IC_Matrixes
                             orderby x.MatrixId ascending
                             select x.MatrixId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    Id = lastInvId;
                    LoadMatrix();
                }
            }     
        }


        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }        

        private void barBtn_Delete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (action == FormAction.Add)
                return;

            if (Id > 0)
            {

                DAL.ERPDataContext DB = new DAL.ERPDataContext();
                if (XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgAskDel : ResICAr.MsgAskDel,
                    Shared.IsEnglish == true ? ResICEn.MsgTWarn : ResICAr.MsgTWarn,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
                    == DialogResult.Yes)
                {
                    if (DB.IC_Items.Where(x => x.mtrxId1 == Id || x.mtrxId2 == Id || x.mtrxId3 == Id).Count() > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish == true ? ResICEn.MsgDelMtrx : ResICAr.MsgDelMtrx,
                           "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                    var j = (from a in DB.IC_Matrixes
                             where a.MatrixId == Id
                             select a).Single();
                    DB.IC_Matrixes.DeleteOnSubmit(j);

                    var jd = (from a in DB.IC_MatrixDetails
                              where a.MatrixId == Id
                              select a);
                    DB.IC_MatrixDetails.DeleteAllOnSubmit(jd);

                    MyHelper.UpdateST_UserLog(DB, txtCode.Text, txtName.Text,
                        (int)FormAction.Delete, (int)FormsNames.frm_IC_MatrixList);

                    DB.SubmitChanges();
                    XtraMessageBox.Show(Shared.IsEnglish == true ? ResICEn.MsgDel : ResICAr.MsgDel,// "تم الحذف بنجاح", 
                        "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    NewMatrix();
                }
            }
        }
                
        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            NewMatrix();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if(!ValidData())
                return;

            SaveData();
            
        }


        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            GridView gridView = grdMtDetails.FocusedView as GridView;
            ColumnView view = sender as ColumnView;

            if (view.GetRowCellValue(e.RowHandle, colMDName) == null
                ||view.GetRowCellValue(e.RowHandle, colMDName).ToString() == string.Empty
                || view.GetRowCellValue(e.RowHandle, colMDCode) == null
                || view.GetRowCellValue(e.RowHandle, colMDCode).ToString() == string.Empty)
            {
                e.Valid = false;
                view.SetColumnError(colMDName,
                    Shared.IsEnglish == true ? ResICEn.MsgData : ResICAr.MsgData);
            }
        }

        private void gridView1_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            if ((e.Row as DataRowView).Row["MDCode"] == DBNull.Value ||
                (e.Row as DataRowView).Row["MDName"] == DBNull.Value)
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore;
            else
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;            
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;
            if (e.RowHandle() < 0)
                return;
            e.Value = e.RowHandle() + 1;
        }

        private void gridView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            grd_FocusOnColumn("MDCode");
        }

        private void gridView1_InitNewRow(object sender, InitNewRowEventArgs e)
        {
            GridView view = grdMtDetails.FocusedView as GridView;
            DataRow row = view.GetFocusedDataRow();
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
   //         if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
   //         {
   //             ERPDataContext DB = new ERPDataContext();

   //             if (MessageBox.Show(
   //                 Shared.IsEnglish == true ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, //"حذف صف ؟"
   //                 Shared.IsEnglish == true ? ResICEn.MsgTQues : ResICAr.MsgTQues,
   //                 MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
   //               DialogResult.Yes)
   //                 return;

   //             if (Id> 0 && DB.IC_Items.Where(x => x.mtrxId1 == Id || x.mtrxId2 == Id || x.mtrxId3 == Id).Count() > 0)
   //             {
   //                 XtraMessageBox.Show(Shared.IsEnglish == true ? ResICEn.MsgDelMtrx : ResICAr.MsgDelMtrx,
   //                    "", MessageBoxButtons.OK, MessageBoxIcon.Information);
   //                 return;
   //             }

   //             GridView view = sender as GridView;
   //             if (view.GetFocusedRowCellValue(colMatrixDetailId) == DBNull.Value)
   //             {
   //                 view.DeleteRow(view.FocusedRowHandle);
   //                 return;
   //             }
   //             else
   //             {
   //                 XtraMessageBox.Show(Shared.IsEnglish == true ? ResICEn.MsgDelMtrx : ResICAr.MsgDelMtrx,
   //"", MessageBoxButtons.OK, MessageBoxIcon.Information);
   //                 return;
   //             }

                //int MatrixDetailId = Convert.ToInt32(view.GetFocusedRowCellValue(colMatrixDetailId));

                //if (DB.IC_Items.Where(x => x.mtrxAttribute1 == MatrixDetailId ||
                //    x.mtrxAttribute2 == MatrixDetailId || x.mtrxAttribute3 == MatrixDetailId).Count() > 0)
                //{
                //    XtraMessageBox.Show(Shared.IsEnglish == true ? ResICEn.MsgDelMtrx : ResICAr.MsgDelMtrx,
                //       "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                //    return;
                //}

                //view.DeleteRow(view.FocusedRowHandle);
            //}            
        }

        private void grdJDetails_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                var view = grdMtDetails.FocusedView as GridView;
                var focused_column = view.FocusedColumn;

                int focused_row_handle = (grdMtDetails.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;
                if (e.KeyCode == Keys.Enter)
                {
                    if (view.FocusedColumn == colMDCode)
                    {
                        view.FocusedColumn = colMDName;
                        return;
                    }
                    if (view.FocusedColumn == colMDName)
                    {
                        grdJDetails_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                    }

                    if (view.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                    {
                        view.AddNewRow();
                        view.FocusedColumn = colMDCode;
                    }
                    else
                    {
                        view.FocusedRowHandle = focused_row_handle + 1;
                        view.FocusedColumn = focused_column;
                    }

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
            }
            catch
            { }
        }                         
                
 
        private void controls_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }


        void grd_FocusOnColumn(string columnName)
        {
            GridView view = grdMtDetails.FocusedView as GridView;
            view.FocusedColumn = view.Columns[columnName];
        }

        private void BindDataSources()
        {
            dtMtDetails.Columns.Clear();
            dtMtDetails.Columns.Add("MatrixDetailId");
            dtMtDetails.Columns.Add("MatrixId");
            dtMtDetails.Columns.Add("MDCode");
            dtMtDetails.Columns.Add("MDName");
            
            grdMtDetails.DataSource = dtMtDetails;
        }
                           
        void Reset()
        {
            txtCode.Text = string.Empty;
            txtName.Text = string.Empty;                        
            dtMtDetails.Rows.Clear();            
            grdMtDetails.RefreshDataSource();
        }
                
        void LoadMatrix()
        {
            ERPDataContext DB = new ERPDataContext();
            var m = DB.IC_Matrixes.Where(x => x.MatrixId == Id).SingleOrDefault();
            if (m == null)
            {
                NewMatrix();
            }
            else
            {
                Id = m.MatrixId;
                txtCode.Text = m.MatrixCode.ToString();
                txtName.Text = m.MatrixName.ToString();                
                
                FillMatrixDetails(DB);

                DoValidate();                
                action = FormAction.Edit;
            }
        }

        void FillMatrixDetails(ERPDataContext DB)
        {
            dtMtDetails.Rows.Clear();
            var jDetails = (from j in DB.IC_MatrixDetails
                            where j.MatrixId == Id
                            select new
                            {                                
                                j.MatrixDetailId,
                                j.MatrixId,
                                j.MDCode,
                                j.MDName
                            }).ToList();

            foreach (var j in jDetails)
            {
                DataRow dr = dtMtDetails.NewRow();
                dr["MatrixDetailId"] = j.MatrixDetailId;
                dr["MatrixId"] = j.MatrixId;
                dr["MDCode"] = j.MDCode;
                dr["MDName"] = j.MDName;
                dtMtDetails.Rows.Add(dr);
            }
            dtMtDetails.AcceptChanges();
        }

        private void NewMatrix()
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            Reset();
            action = FormAction.Add;
            txtCode.Focus();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            Id = (DB.IC_Matrixes.Select(a => a.MatrixId).ToList().DefaultIfEmpty(0).Max() + 1);            

            DoValidate();            
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_IC_MatrixList).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
            }
        }

        DialogResult ChangesMade()
        {
            if (Id > 0 &&
            (DataModified ||
            dtMtDetails.GetChanges(DataRowState.Added)!= null ||
            dtMtDetails.GetChanges(DataRowState.Modified)!= null ||
            dtMtDetails.GetChanges(DataRowState.Deleted)!= null                        
            ))
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDataModified : ResICAr.MsgDataModified, 
                    "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        private void SaveData()
        {
            ERPDataContext DB = new ERPDataContext();
            if (action == FormAction.Add)
            {
                IC_Matrix m = new IC_Matrix();
                m.MatrixCode = txtCode.Text;
                m.MatrixName = txtName.Text;                

                DB.IC_Matrixes.InsertOnSubmit(m);
                DB.SubmitChanges();
                Id = m.MatrixId;

                MyHelper.UpdateST_UserLog(DB, txtCode.Text, txtName.Text,
    (int)FormAction.Add, (int)FormsNames.frm_IC_MatrixList);
            }
            else if (action == FormAction.Edit)
            {
                if (Id > 0)
                {                   
                    var m = (from i in DB.IC_Matrixes
                             where i.MatrixId == Id
                             select i).SingleOrDefault();
                    m.MatrixCode = txtCode.Text;
                    m.MatrixName = txtName.Text;

                    MyHelper.UpdateST_UserLog(DB, txtCode.Text, txtName.Text,
    (int)FormAction.Edit, (int)FormsNames.frm_IC_MatrixList);

                    DB.SubmitChanges();
                }
            }
            
            for (int x = 0; x < dtMtDetails.Rows.Count; x++)
            {
                if (dtMtDetails.Rows[x].RowState == DataRowState.Added)
                {
                    IC_MatrixDetail d = new IC_MatrixDetail();
                    d.MatrixId = Id;
                    d.MDCode = dtMtDetails.Rows[x]["MDCode"].ToString().Trim();
                    d.MDName = dtMtDetails.Rows[x]["MDName"].ToString().Trim();
                    DB.IC_MatrixDetails.InsertOnSubmit(d);
                }
                else if (dtMtDetails.Rows[x].RowState == DataRowState.Modified)
                {
                    var row = DB.IC_MatrixDetails.Where(i => i.MatrixDetailId == Convert.ToInt32(dtMtDetails.Rows[x]["MatrixDetailId"].ToString())).First();
                    row.MDCode = dtMtDetails.Rows[x]["MDCode"].ToString().Trim();
                    row.MDName = dtMtDetails.Rows[x]["MDName"].ToString().Trim();
                }
            }
            DB.SubmitChanges();

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgSave : ResICAr.MsgSave,//"تم الحفظ بنجاح"
                "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            DoValidate();
            action = FormAction.Edit;
        }

        private bool ValidData()
        {
            if (action == FormAction.Add)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    XtraMessageBox.Show(Shared.IsEnglish == true ? ResICEn.MsgPrvNew : ResICAr.MsgPrvNew,
                        "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (action == FormAction.Edit)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvEdit : ResICAr.MsgPrvEdit,
                        "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            gridView2.CloseEditor();
            gridView2.UpdateCurrentRow();

            if (dtMtDetails.Rows.Count < 1)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgMtRows : ResICAr.MsgMtRows,
                    "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }

            if (txtCode.Text.Trim() == string.Empty)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgMtCode : ResICAr.MsgMtCode,
                    "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                txtCode.Focus();
                return false;
            }
            if (txtName.Text.Trim() == string.Empty)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgMtName : ResICAr.MsgMtName,
                    "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                txtName.Focus();
                return false;
            }
            
            return true;
        }

        void DoValidate()
        {
            txtCode.DoValidate();
            txtName.DoValidate();            
            dtMtDetails.AcceptChanges();
            DataModified = false;
        }
      

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdMtDetails.MinimumSize = grdMtDetails.Size;
            new Reports.rpt_Template(this.Text, "", txtCode.Text + "-" + txtName.Text, "", grdMtDetails, false).ShowPreview();
            grdMtDetails.MinimumSize = new Size(0, 0);
        }
                                    
    }
}