using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_PR_PriceLevel: DevExpress.XtraReports.UI.XtraReport
    {
        string pricelevelName;

        DataTable dt_PriceLevelDetail;

        public rpt_PR_PriceLevel()
        {
            InitializeComponent();
        }
        public rpt_PR_PriceLevel(string _PriceLevelName, DataTable dt)
        {
            InitializeComponent();
            pricelevelName = _PriceLevelName;
            dt_PriceLevelDetail= dt;
            this.DataSource = dt_PriceLevelDetail;
            getReportHeader();
            //LoadData();            
        }        

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;                
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }            
        }

        public void LoadData()
        {            
            lbl_PriceLevelName.Text = pricelevelName;            
            this.DataSource = dt_PriceLevelDetail;

            cell_code.DataBindings.Add("Text", this.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", this.DataSource, "ItemCode2");            
            cell_ItemName.DataBindings.Add("Text", this.DataSource, "ItemName");
            cell_ItemNameEn.DataBindings.Add("Text", this.DataSource, "ItemNameEn");
            cell_S_Unit.DataBindings.Add("Text", this.DataSource, "S_Unit");
            cell_S_Price.DataBindings.Add("Text", this.DataSource, "S_Price");
            cell_M_Unit.DataBindings.Add("Text", this.DataSource, "M_Unit");            
            cell_M_Price.DataBindings.Add("Text", this.DataSource, "M_Price");
            cell_L_Unit.DataBindings.Add("Text", this.DataSource, "L_Unit");
            cell_L_Price.DataBindings.Add("Text", this.DataSource, "L_Price");
            Cell_MinOrder.DataBindings.Add("Text", this.DataSource, "MinOrder");
            Cell_MUOM_Factor.DataBindings.Add("Text", this.DataSource, "MUOM_Factor");
        }
        
    }
}
