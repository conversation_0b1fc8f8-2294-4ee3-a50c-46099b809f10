﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="labelControl5.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn28.Caption" xml:space="preserve">
    <value>MediumUOMFactor</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barAndDockingController1.AppearancesRibbon.Item.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnAddCustomer.Name" xml:space="preserve">
    <value>btnAddCustomer</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="rep_expireDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_DiscountRatio3.Width" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tabExtraData.Text" xml:space="preserve">
    <value>Extra Data</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="textEdit7.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn29.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmdProcess.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 22</value>
  </data>
  <data name="gridColumn14.Caption" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="txt_MaxCredit.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn40.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Name" xml:space="preserve">
    <value>barBtnDelete</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnSourceId.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="textEdit6.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn18.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txt_AddTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCurrency.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>61, 365</value>
  </data>
  <data name="textEdit8.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit8.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl13.TabIndex" type="System.Int32, mscorlib">
    <value>167</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmdProcess.Properties.AppearanceReadOnly.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDestination.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkpStore.Properties.Columns38" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns39" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns36" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns37" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns34" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns35" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsInTrns.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lkpStore.Properties.Columns30" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns31" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="barBtnDelete.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="textEdit3.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;importFromExcelSheetToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn5.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Name" xml:space="preserve">
    <value>txtDiscountValue</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="repLocation.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;labelControl28.Name" xml:space="preserve">
    <value>labelControl28</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;labelControl14.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns" xml:space="preserve">
    <value>AccountName</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repExpireDate.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="txtExpenses.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl40.Location" type="System.Drawing.Point, System.Drawing">
    <value>1088, 337</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtScaleSerial.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>105, 18</value>
  </data>
  <data name="txtCurrency.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repSpin.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbPayMethod.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Name" xml:space="preserve">
    <value>gridColumn33</value>
  </data>
  <data name="&gt;&gt;txtCurrency.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lbl_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="txt_DeductTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>94, 406</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns8" xml:space="preserve">
    <value>Account Id</value>
  </data>
  <data name="&gt;&gt;panelControl1.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="btnSourceId.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDriverName.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.ZOrder" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="&gt;&gt;labelControl26.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="barSubItem1.MenuAppearance.HeaderItemAppearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="page_AccInfo.Text" xml:space="preserve">
    <value>Account Information</value>
  </data>
  <data name="txtCurrency.EditValue" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txtSourceCode.Properties.AppearanceReadOnly.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="batBtnList.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="&gt;&gt;labelControl14.Name" xml:space="preserve">
    <value>labelControl14</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;repLocation.Name" xml:space="preserve">
    <value>repLocation</value>
  </data>
  <data name="pnlCrncy.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="txtCurrency.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Deduct Tax</value>
  </data>
  <data name="txt_DeductTaxR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>Sales Return Invoices</value>
  </data>
  <data name="&gt;&gt;txtScaleSerial.Name" xml:space="preserve">
    <value>txtScaleSerial</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Balance_After.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lkpStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 20</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemDescription.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>710, 1</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn12.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>422, 11</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="lblDestination.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="panelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="txt_DeductTaxR.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="&gt;&gt;groupControl1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtExpenses.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 450</value>
  </data>
  <data name="lkp_Drawers2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repLocation.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;chk_IsInTrns.Name" xml:space="preserve">
    <value>chk_IsInTrns</value>
  </data>
  <data name="txtExpenses.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 20</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn1.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="frm_SL_ReturnArchive.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit5.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="col_PiecesCount.Width" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="repDiscountRatio.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;colLocationId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit6.TabIndex" type="System.Int32, mscorlib">
    <value>257</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxR.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblDriverName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_TaxValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl20.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;labelControl13.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit6.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn2.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_SalesTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmdProcess.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="textEdit3.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsInTrns.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="gridColumn28.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn35.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Name" xml:space="preserve">
    <value>txt_PayAcc1_Paid</value>
  </data>
  <data name="cmdProcess.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Near</value>
  </data>
  <data name="txt_Balance_After.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridColumn23.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnPrevious.ToolTip" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="&gt;&gt;lkpStore.Name" xml:space="preserve">
    <value>lkpStore</value>
  </data>
  <data name="btnSourceId.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblVehicleNumber.Name" xml:space="preserve">
    <value>lblVehicleNumber</value>
  </data>
  <data name="cmdProcess.Properties.AppearanceReadOnly.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="&gt;&gt;col_Length.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DeductTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="col_Batch.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn43.Width" type="System.Int32, mscorlib">
    <value>86</value>
  </data>
  <data name="btnAddCustomer.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridView3.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="flowLayoutPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>316, 7</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>Branch Code</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl14.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Name" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="txtCurrency.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="txt_MaxCredit.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 13</value>
  </data>
  <data name="textEdit8.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="col_Width.Width" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="pnlSrcPrc.Size" type="System.Drawing.Size, System.Drawing">
    <value>97, 44</value>
  </data>
  <data name="textEdit6.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>Past Balance</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl35.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn24.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl22.TabIndex" type="System.Int32, mscorlib">
    <value>252</value>
  </data>
  <data name="txt_DeductTaxV.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl22.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barSubItem1.MenuAppearance.HeaderItemAppearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl20.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Length.Width" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn14.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl7.Name" xml:space="preserve">
    <value>labelControl7</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_MaxCredit.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns10" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="dtInvoiceDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl35.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="chk_IsInTrns.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 19</value>
  </data>
  <data name="&gt;&gt;col_CategoryNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_TaxValue.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit8.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 21</value>
  </data>
  <data name="&gt;&gt;labelControl40.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="dtInvoiceDate.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Total.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="textEdit8.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="cmbPayMethod.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn3.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="textEdit7.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="pnlCrncy.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="cmdProcess.Properties.Items1" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="col_Batch.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn43.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit3.Name" xml:space="preserve">
    <value>textEdit3</value>
  </data>
  <data name="cmbPayMethod.Properties.Items3" xml:space="preserve">
    <value>Cash</value>
  </data>
  <data name="txtCurrency.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnPrevious.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridColumn6.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;col_ItemDescription.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmdProcess.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpenses.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtInvoiceCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtExpenses.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_paid.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>247, 245, 241</value>
  </data>
  <data name="&gt;&gt;panelControl2.Name" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="textEdit3.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>122, 44</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>342, 10</value>
  </data>
  <data name="&gt;&gt;txtDestination.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_InvoiceBook.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="txtCurrency.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="importFromExcelSheetToolStripMenuItem.Text" xml:space="preserve">
    <value>Import From Excel sheet</value>
  </data>
  <data name="&gt;&gt;cmdProcess.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl7.TabIndex" type="System.Int32, mscorlib">
    <value>161</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Name" xml:space="preserve">
    <value>gridColumn12</value>
  </data>
  <data name="col_Expire.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="contextMenuStrip1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtCurrency.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_AddTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="lblDestination.TabIndex" type="System.Int32, mscorlib">
    <value>248</value>
  </data>
  <data name="dtInvoiceDate.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Name" xml:space="preserve">
    <value>btnPrevious</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;textEdit1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn41.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;btnSourceId.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ButtonEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn24.Caption" xml:space="preserve">
    <value>Sell Price</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="repDiscountRatio.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Name" xml:space="preserve">
    <value>txtDiscountRatio</value>
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_TaxValue.Name" xml:space="preserve">
    <value>txt_TaxValue</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="gridColumn35.Width" type="System.Int32, mscorlib">
    <value>57</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn10.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="colPurchasePrice.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="col_Expire.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn43.Caption" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="cmdProcess.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit1.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.Name" xml:space="preserve">
    <value>txt_Balance_Before</value>
  </data>
  <data name="txtDriverName.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="pnlBranch.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Customers.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn35.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn5.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;labelControl40.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn36.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn5.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>696, 30</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn2.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;xtraTabControl1.Name" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="repExpireDate_txt.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;col_Length.Name" xml:space="preserve">
    <value>col_Length</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtDiscountRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_TaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 384</value>
  </data>
  <data name="txt_Total.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit1.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="textEdit6.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 467</value>
  </data>
  <data name="col_Batch.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="panelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1158, 102</value>
  </data>
  <data name="&gt;&gt;pnlBook.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_Remains.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txtDestination.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtCurrency.Name" xml:space="preserve">
    <value>txtCurrency</value>
  </data>
  <data name="btnAddCustomer.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="mi_PasteRows.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="btnSourceId.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="col_Serial.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barBtnSave.Name" xml:space="preserve">
    <value>barBtnSave</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDisc.Name" xml:space="preserve">
    <value>mi_InvoiceStaticDisc</value>
  </data>
  <data name="&gt;&gt;txtNotes.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;panelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtSourceCode.Name" xml:space="preserve">
    <value>txtSourceCode</value>
  </data>
  <data name="&gt;&gt;col_CompanyNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtNet.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl8.TabIndex" type="System.Int32, mscorlib">
    <value>169</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl16.TabIndex" type="System.Int32, mscorlib">
    <value>172</value>
  </data>
  <data name="barBtnPrint.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.Caption" xml:space="preserve">
    <value>Added to Store</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="&gt;&gt;textEdit3.Parent" xml:space="preserve">
    <value>pnlSalesEmp</value>
  </data>
  <data name="textEdit6.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="col_ItemDescription.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_TotalQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Name" xml:space="preserve">
    <value>txtExpenses</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn7.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl16.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lkpStore.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridColumn11.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Drawers.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_DiscountRatio2.Caption" xml:space="preserve">
    <value>Disc R2</value>
  </data>
  <data name="gridColumn13.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txt_Remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>178, 47</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtNet.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;labelControl24.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="gridColumn8.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lblDestination.Name" xml:space="preserve">
    <value>lblDestination</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="txtDriverName.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit7.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc1_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 3</value>
  </data>
  <data name="textEdit7.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn4.Width" type="System.Int32, mscorlib">
    <value>165</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Name" xml:space="preserve">
    <value>gridColumn22</value>
  </data>
  <data name="txt_Remains.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn4.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;col_Height.Name" xml:space="preserve">
    <value>col_Height</value>
  </data>
  <data name="lkpCostCenter.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Items4" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl17.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="repExpireDate_txt.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_paid.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit6.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 22</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit1.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btnNext.Name" xml:space="preserve">
    <value>btnNext</value>
  </data>
  <data name="lkpCostCenter.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn9.Caption" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="txt_AddTaxV.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;xtraTabControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;repSpin.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_PiecesCount.Caption" xml:space="preserve">
    <value>Pieces Count</value>
  </data>
  <data name="txtDestination.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn11.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btn_AddMatrixItems.ToolTip" xml:space="preserve">
    <value>Add Matrix Items</value>
  </data>
  <data name="&gt;&gt;textEdit6.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textEdit3.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmdProcess.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn3.Caption" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textEdit6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit8.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repExpireDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl24.TabIndex" type="System.Int32, mscorlib">
    <value>214</value>
  </data>
  <data name="&gt;&gt;labelControl15.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;gridColumn38.Name" xml:space="preserve">
    <value>gridColumn38</value>
  </data>
  <data name="rep_expireDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_Batch.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl28.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;col_CategoryNameAr.Name" xml:space="preserve">
    <value>col_CategoryNameAr</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="txt_AddTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 20</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtSourceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn41.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl40.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 13</value>
  </data>
  <data name="&gt;&gt;gridView5.Name" xml:space="preserve">
    <value>gridView5</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn7.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtVehicleNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>244, 54</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_IsCredit_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="txtDestination.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 431</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtNet.Name" xml:space="preserve">
    <value>txtNet</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="textEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 22</value>
  </data>
  <data name="&gt;&gt;labelControl7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>434, 69</value>
  </data>
  <data name="col_Batch.Width" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="textEdit5.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Name" xml:space="preserve">
    <value>txt_PayAcc2_Paid</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;repManufactureDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemDateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repExpireDate_txt.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lkpStore.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;pnlCrncy.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 498</value>
  </data>
  <data name="txtNotes.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl5.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;rep_vendors.Name" xml:space="preserve">
    <value>rep_vendors</value>
  </data>
  <data name="lkpStore.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="col_TotalSellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lbl_IsCredit_Before.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_DeductTaxR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit7.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;tabExtraData.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmdProcess.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Name" xml:space="preserve">
    <value>gridColumn10</value>
  </data>
  <data name="textEdit7.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="cmdProcess.Properties.AppearanceReadOnly.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_AddTaxV.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblBlncAftr.Text" xml:space="preserve">
    <value>Balance After Invoice</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpenses.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="batBtnList.Caption" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 33</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 22</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxR.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1182, 498</value>
  </data>
  <data name="labelControl40.TabIndex" type="System.Int32, mscorlib">
    <value>239</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="txtDestination.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="barBtnSave.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_Total.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lbl_IsCredit_After.TabIndex" type="System.Int32, mscorlib">
    <value>217</value>
  </data>
  <data name="textEdit6.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_vendors.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Batch.Caption" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="&gt;&gt;repManufactureDate.Name" xml:space="preserve">
    <value>repManufactureDate</value>
  </data>
  <data name="txt_Remains.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="textEdit1.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSourceCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lbl_Paid.Name" xml:space="preserve">
    <value>lbl_Paid</value>
  </data>
  <data name="txtDestination.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn20.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn20.Width" type="System.Int32, mscorlib">
    <value>172</value>
  </data>
  <data name="&gt;&gt;col_Batch.Name" xml:space="preserve">
    <value>col_Batch</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txt_paid.Name" xml:space="preserve">
    <value>txt_paid</value>
  </data>
  <data name="txtSourceCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 20</value>
  </data>
  <data name="lkp_Drawers.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>502, 134</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns1" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="col_Expire.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit5.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="uc_Currency1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.Name" xml:space="preserve">
    <value>uc_Currency1</value>
  </data>
  <data name="&gt;&gt;gridColumn40.Name" xml:space="preserve">
    <value>gridColumn40</value>
  </data>
  <data name="xtraTabControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="textEdit3.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Parent" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="txtDestination.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDriverName.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;repItems.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn2.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate_txt.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="btnSourceId.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkpStore.Properties.Columns7" xml:space="preserve">
    <value>StoreCode</value>
  </data>
  <data name="txt_Balance_After.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="btnSourceId.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkpStore.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="txt_MaxCredit.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="gridColumn43.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxR.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Expire.Width" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="textEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 22</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Balance_Before.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="col_Expire.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Name" xml:space="preserve">
    <value>txt_AddTaxR</value>
  </data>
  <data name="col_DiscountRatio2.Width" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="rep_expireDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Name" xml:space="preserve">
    <value>gridColumn3</value>
  </data>
  <data name="textEdit5.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 387</value>
  </data>
  <data name="txtDriverName.TabIndex" type="System.Int32, mscorlib">
    <value>243</value>
  </data>
  <data name="&gt;&gt;cmdProcess.Parent" xml:space="preserve">
    <value>pnlSrcPrc</value>
  </data>
  <data name="txt_Balance_Before.TabIndex" type="System.Int32, mscorlib">
    <value>219</value>
  </data>
  <data name="&gt;&gt;btnNext.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="panelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rep_expireDate.Mask.EditMask" xml:space="preserve">
    <value>M-yyyy</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barSubItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarSubItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="repItems.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns7" xml:space="preserve">
    <value>CostCenterName</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lbl_remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>341, 50</value>
  </data>
  <data name="txtNotes.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;textEdit5.Parent" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="textEdit7.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit7.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDiscountValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="lkpStore.Properties.Columns28" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns29" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns26" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns27" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns24" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns25" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns22" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns23" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns21" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="&gt;&gt;txt_Total.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 13</value>
  </data>
  <data name="&gt;&gt;labelControl19.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtInvoiceCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txtNotes.Size" type="System.Drawing.Size, System.Drawing">
    <value>231, 90</value>
  </data>
  <data name="labelControl10.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtNet.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Name" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Name" xml:space="preserve">
    <value>gridColumn1</value>
  </data>
  <data name="labelControl12.TabIndex" type="System.Int32, mscorlib">
    <value>161</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="importFromExcelSheetToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit3.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit3.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="textEdit7.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn29.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit6.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="pnlCrncy.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 1</value>
  </data>
  <data name="&gt;&gt;labelControl12.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="labelControl40.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="gridColumn31.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="pnlInvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 44</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Total.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="panelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 55</value>
  </data>
  <data name="groupControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="txt_Balance_After.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="pnlSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="textEdit6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelControl18.Name" xml:space="preserve">
    <value>labelControl18</value>
  </data>
  <data name="rep_expireDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_SalesEmp.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.Name" xml:space="preserve">
    <value>txtVehicleNumber</value>
  </data>
  <data name="&gt;&gt;pnlDate.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="textEdit5.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn7.Name" xml:space="preserve">
    <value>gridColumn7</value>
  </data>
  <data name="lkpStore.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit3.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit8.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="mi_frm_IC_Item.Text" xml:space="preserve">
    <value>Item Info</value>
  </data>
  <data name="labelControl28.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="&gt;&gt;gridColumn24.Name" xml:space="preserve">
    <value>gridColumn24</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 31</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtDestination.Name" xml:space="preserve">
    <value>txtDestination</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsInTrns.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;rep_vendors.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="repExpireDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="&gt;&gt;txtNotes.Name" xml:space="preserve">
    <value>txtNotes</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>Pay Account 2</value>
  </data>
  <data name="textEdit3.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="txtCurrency.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_MaxCredit.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;batBtnList.Name" xml:space="preserve">
    <value>batBtnList</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="cmbPayMethod.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;txtDriverName.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit8.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtInvoiceCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>219, 421</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Name" xml:space="preserve">
    <value>gridColumn5</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDriverName.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn31.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl13.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtSourceCode.Properties.AppearanceReadOnly.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Remains.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnSourceId.Name" xml:space="preserve">
    <value>btnSourceId</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn12.Width" type="System.Int32, mscorlib">
    <value>310</value>
  </data>
  <data name="txt_paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="textEdit3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="gridColumn42.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtNotes.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="txt_Remains.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_SalesTax.Width" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Parent" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="txt_PayAcc1_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Name" xml:space="preserve">
    <value>txt_DeductTaxR</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Name" xml:space="preserve">
    <value>lkpCostCenter</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Name" xml:space="preserve">
    <value>gridColumn2</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelControl36.Name" xml:space="preserve">
    <value>labelControl36</value>
  </data>
  <data name="&gt;&gt;col_PiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lbl_IsCredit_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>339, 32</value>
  </data>
  <data name="col_Expire.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio3.Name" xml:space="preserve">
    <value>col_DiscountRatio3</value>
  </data>
  <data name="lblVehicleNumber.TabIndex" type="System.Int32, mscorlib">
    <value>246</value>
  </data>
  <data name="cmdProcess.Properties.Items4" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="barSubItem1.MenuAppearance.HeaderItemAppearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Width.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl25.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txt_Total.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lbl_IsCredit_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 431</value>
  </data>
  <data name="gridColumn33.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_PayAcc2_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 25</value>
  </data>
  <data name="lkpCostCenter.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.ZOrder" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="labelControl36.TabIndex" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="cmdProcess.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="lkpStore.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lbl_remains.Text" xml:space="preserve">
    <value>Remains</value>
  </data>
  <data name="gridView4.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="&gt;&gt;pnlSrcPrc.Name" xml:space="preserve">
    <value>pnlSrcPrc</value>
  </data>
  <data name="textEdit8.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 22</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDimensions.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="textEdit8.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="lkpStore.Properties.Columns" xml:space="preserve">
    <value>StoreNameAr</value>
  </data>
  <data name="lkp_Drawers2.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="textEdit3.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="&gt;&gt;txtNotes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_InvoiceBook.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="textEdit5.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>807, 33</value>
  </data>
  <data name="&gt;&gt;gridColumn39.Name" xml:space="preserve">
    <value>gridColumn39</value>
  </data>
  <data name="&gt;&gt;mi_frm_IC_Item.Name" xml:space="preserve">
    <value>mi_frm_IC_Item</value>
  </data>
  <data name="textEdit1.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="pnlInvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 1</value>
  </data>
  <data name="txt_PayAcc2_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="lkp_Drawers2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;gridColumn31.Name" xml:space="preserve">
    <value>gridColumn31</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn36.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txt_Total.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="labelControl21.TabIndex" type="System.Int32, mscorlib">
    <value>213</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDriverName.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmdProcess.TabIndex" type="System.Int32, mscorlib">
    <value>262</value>
  </data>
  <data name="textEdit1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;repLocation.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="txtDestination.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtExpenses.ZOrder" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Name" xml:space="preserve">
    <value>lkp_Drawers</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Name" xml:space="preserve">
    <value>gridColumn6</value>
  </data>
  <data name="panelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkpCostCenter.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_SL_ReturnArchive</value>
  </data>
  <data name="txt_MaxCredit.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;textEdit6.Parent" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;col_CompanyNameAr.Name" xml:space="preserve">
    <value>col_CompanyNameAr</value>
  </data>
  <data name="textEdit1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Name" xml:space="preserve">
    <value>gridColumn19</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="&gt;&gt;panelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Name" xml:space="preserve">
    <value>gridColumn11</value>
  </data>
  <data name="txtDriverName.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn36.Caption" xml:space="preserve">
    <value>TotalMainPrice</value>
  </data>
  <data name="gridColumn11.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>162</value>
  </data>
  <data name="dtInvoiceDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridColumn29.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtCurrency.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="&gt;&gt;textEdit8.Parent" xml:space="preserve">
    <value>pnlBook</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Height.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="repExpireDate_txt.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Regular</value>
  </data>
  <data name="textEdit1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDriverName.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtExpenses.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txtNet.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;txtDestination.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;labelControl14.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView5.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit5.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn21.Width" type="System.Int32, mscorlib">
    <value>383</value>
  </data>
  <data name="btnAddCustomer.Location" type="System.Drawing.Point, System.Drawing">
    <value>875, 30</value>
  </data>
  <data name="textEdit5.EditValue" xml:space="preserve">
    <value>Branch</value>
  </data>
  <data name="txtExpenses.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtVehicleNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="gridColumn40.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="pnlSalesEmp.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="&gt;&gt;mi_PasteRows.Name" xml:space="preserve">
    <value>mi_PasteRows</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>Other charge</value>
  </data>
  <data name="btnSourceId.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_Drawers2.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtVehicleNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn23.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_DiscountRatio3.Caption" xml:space="preserve">
    <value>Disc R3</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbPayMethod.Properties.Items7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit3.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>Disc R</value>
  </data>
  <data name="txtCurrency.TabIndex" type="System.Int32, mscorlib">
    <value>277</value>
  </data>
  <data name="txtCurrency.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit7.Name" xml:space="preserve">
    <value>textEdit7</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;txtDriverName.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;groupControl1.Name" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="gridColumn17.Caption" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Name" xml:space="preserve">
    <value>gridColumn41</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="flowLayoutPanel1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;gridColumn24.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn23.Caption" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="txt_Total.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscountRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 13</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl22.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl35.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;cmdProcess.Name" xml:space="preserve">
    <value>cmdProcess</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Name" xml:space="preserve">
    <value>gridColumn15</value>
  </data>
  <data name="txt_paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 47</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl14.TabIndex" type="System.Int32, mscorlib">
    <value>160</value>
  </data>
  <data name="btnSourceId.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 23</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="dtInvoiceDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 20</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn20.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lbl_IsCredit_After.Text" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="pnlBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>126, 44</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repExpireDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="gridColumn8.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn8.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn29.Name" xml:space="preserve">
    <value>gridColumn29</value>
  </data>
  <data name="gridColumn5.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn16.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl18.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="pnlSrcPrc.Location" type="System.Drawing.Point, System.Drawing">
    <value>615, 47</value>
  </data>
  <data name="page_AccInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>496, 106</value>
  </data>
  <data name="barBtnHelp.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_Total.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DeductTaxV.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.FlowLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNet.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repSpin.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="col_Serial.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_AddTaxR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;gridColumn20.Name" xml:space="preserve">
    <value>gridColumn20</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnSourceId.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btn_AddMatrixItems.Text" xml:space="preserve">
    <value>Add Matrix Items</value>
  </data>
  <data name="panelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="textEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>256</value>
  </data>
  <data name="txtVehicleNumber.TabIndex" type="System.Int32, mscorlib">
    <value>245</value>
  </data>
  <data name="textEdit6.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Balance_Before.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="btnSourceId.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCurrency.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="uc_Currency1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="gridColumn21.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lblDestination.Location" type="System.Drawing.Point, System.Drawing">
    <value>422, 80</value>
  </data>
  <data name="txt_Remains.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit3.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSourceCode.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Bold</value>
  </data>
  <data name="txt_TaxValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 20</value>
  </data>
  <data name="txtNet.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn3.Width" type="System.Int32, mscorlib">
    <value>184</value>
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="&gt;&gt;gridView5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl17.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="rep_expireDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.Parent" xml:space="preserve">
    <value>pnlBook</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>Sell P</value>
  </data>
  <data name="textEdit3.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;textEdit6.Name" xml:space="preserve">
    <value>textEdit6</value>
  </data>
  <data name="frm_SL_ReturnArchive.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="pnlBook.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_TotalSellPrice.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="lkp_InvoiceBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 20</value>
  </data>
  <data name="lbl_remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn10.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl5.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Underline</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1182, 0</value>
  </data>
  <data name="lbl_Paid.Text" xml:space="preserve">
    <value>Total Paid</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="textEdit5.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit5.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn8.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn40.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;labelControl40.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Name" xml:space="preserve">
    <value>gridColumn25</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnSourceId.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblDriverName.Text" xml:space="preserve">
    <value>Driver Name</value>
  </data>
  <data name="txtCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 22</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn10.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="btnAddCustomer.ToolTip" xml:space="preserve">
    <value>Add Customer</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="txt_paid.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="btnAddCustomer.TabIndex" type="System.Int32, mscorlib">
    <value>177</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="xtraTabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>276</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.Name" xml:space="preserve">
    <value>lkp_InvoiceBook</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>S. Tax</value>
  </data>
  <data name="&gt;&gt;repExpireDate_txt.Name" xml:space="preserve">
    <value>repExpireDate_txt</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_PayAcc2_Paid.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn21.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit6.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_Total.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;textEdit5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="col_Expire.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpStore.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtVehicleNumber.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;col_Serial.Name" xml:space="preserve">
    <value>col_Serial</value>
  </data>
  <data name="&gt;&gt;pnlCrncy.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="txtNet.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="barSubItem1.Caption" xml:space="preserve">
    <value>Load</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Serial.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit5.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repUOM.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl25.Name" xml:space="preserve">
    <value>labelControl25</value>
  </data>
  <data name="pnlCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>714, 47</value>
  </data>
  <data name="&gt;&gt;labelControl21.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="cmbPayMethod.Properties.Items" xml:space="preserve">
    <value>On Credit</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;pnlSalesEmp.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtDiscountValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lbl_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>164</value>
  </data>
  <data name="txt_Remains.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="gridColumn3.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="pnlSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>51, 1</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="txtCurrency.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns1" xml:space="preserve">
    <value>Employee Name</value>
  </data>
  <data name="txt_Total.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpStore.Properties.Columns32" xml:space="preserve">
    <value />
  </data>
  <data name="col_Batch.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.ZOrder" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelControl21.Name" xml:space="preserve">
    <value>labelControl21</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns14" xml:space="preserve">
    <value>PrintFileName</value>
  </data>
  <data name="txtScaleSerial.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblDriverName.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repManufactureDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_Remains.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;btnSourceId.Parent" xml:space="preserve">
    <value>pnlSrcPrc</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>Pay Method</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="pnlDate.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="cmbPayMethod.EditValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="pnlCrncy.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 44</value>
  </data>
  <data name="gridColumn11.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;panelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn2.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txtNet.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 20</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="pnlSrcPrc.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Name" xml:space="preserve">
    <value>txt_AddTaxV</value>
  </data>
  <data name="&gt;&gt;labelControl26.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtNet.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkpCostCenter.Parent" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="tabExtraData.Size" type="System.Drawing.Size, System.Drawing">
    <value>496, 106</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Name" xml:space="preserve">
    <value>txtInvoiceCode</value>
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmdProcess.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barbtnLoadSellInvoice.Caption" xml:space="preserve">
    <value>Load Sell Invoice</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtSourceCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="textEdit5.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="gridColumn6.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="repExpireDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="&gt;&gt;col_ItemDescription.Name" xml:space="preserve">
    <value>col_ItemDescription</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns33" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Batch.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn14.Width" type="System.Int32, mscorlib">
    <value>187</value>
  </data>
  <data name="&gt;&gt;gridColumn39.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn10.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView2.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>259</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>Unit Of Measure</value>
  </data>
  <data name="txtInvoiceCode.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Balance_After.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl35.TabIndex" type="System.Int32, mscorlib">
    <value>85</value>
  </data>
  <data name="&gt;&gt;colLocationId.Name" xml:space="preserve">
    <value>colLocationId</value>
  </data>
  <data name="txtSourceCode.Properties.AppearanceReadOnly.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;gridColumn42.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl28.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>Disc V</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit1.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;repUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.Name" xml:space="preserve">
    <value>txt_MaxCredit</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl18.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="cmdProcess.EditValue" xml:space="preserve">
    <value>Qoute code</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Text" xml:space="preserve">
    <value>Static Invoice Discounts</value>
  </data>
  <data name="textEdit5.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn18.Width" type="System.Int32, mscorlib">
    <value>184</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn18.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtInvoiceCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 20</value>
  </data>
  <data name="&gt;&gt;txtDriverName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsInTrns.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDriverName.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtVehicleNumber.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;txtSourceCode.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;repExpireDate.Name" xml:space="preserve">
    <value>repExpireDate</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblBlncAftr.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 55</value>
  </data>
  <data name="lkpStore.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="repDiscountRatio.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;colPurchasePrice.Name" xml:space="preserve">
    <value>colPurchasePrice</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn11.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn43.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridColumn13.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="txt_Balance_After.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlSrcPrc.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Name" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="repDiscountRatio.Mask.EditMask" xml:space="preserve">
    <value>p2</value>
  </data>
  <data name="gridColumn1.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Remains.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit8.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;textEdit8.Name" xml:space="preserve">
    <value>textEdit8</value>
  </data>
  <data name="gridColumn33.Caption" xml:space="preserve">
    <value>Producer Company</value>
  </data>
  <data name="txt_Total.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>94, 428</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>Disc</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn11.Width" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="repManufactureDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="gridColumn36.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl26.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Customers.Location" type="System.Drawing.Point, System.Drawing">
    <value>906, 30</value>
  </data>
  <data name="gridColumn8.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="lkpStore.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Name" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="lkpStore.Properties.Columns15" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="lkpStore.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpStore.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="txtVehicleNumber.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lbl_remains.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="txtNotes.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lblVehicleNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>422, 57</value>
  </data>
  <data name="&gt;&gt;labelControl17.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_Balance_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 13</value>
  </data>
  <data name="txtDiscountValue.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Serial.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Serial.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="txtDiscountRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Remains.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl11.TabIndex" type="System.Int32, mscorlib">
    <value>169</value>
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridColumn20.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn23.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_Total.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn35.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnSourceId.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grdPrInvoice.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;labelControl16.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>Add Tax</value>
  </data>
  <data name="textEdit6.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 2</value>
  </data>
  <data name="dtInvoiceDate.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl15.Name" xml:space="preserve">
    <value>labelControl15</value>
  </data>
  <data name="&gt;&gt;labelControl8.Name" xml:space="preserve">
    <value>labelControl8</value>
  </data>
  <data name="labelControl13.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="gridColumn16.Caption" xml:space="preserve">
    <value>Factor</value>
  </data>
  <data name="cmdProcess.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl26.Location" type="System.Drawing.Point, System.Drawing">
    <value>113, 7</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.Name" xml:space="preserve">
    <value>pnlSalesEmp</value>
  </data>
  <data name="cmbPayMethod.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Near</value>
  </data>
  <data name="&gt;&gt;pnlDate.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="gridColumn23.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="lkpStore.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;gridColumn29.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmdProcess.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Customers.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc1_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_TaxValue.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsInTrns.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblBlncAftr.TabIndex" type="System.Int32, mscorlib">
    <value>216</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn7.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn40.Caption" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="txt_Total.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl11.Name" xml:space="preserve">
    <value>labelControl11</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtNotes.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="&gt;&gt;colDescription.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="gridColumn10.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barBtnPrint.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="xtraTabControl1.HeaderAutoFill" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDiscountValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 20</value>
  </data>
  <data name="lkpStore.Properties.Columns48" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="repExpireDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpStore.Properties.Columns46" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns47" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns44" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns45" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns42" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns43" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns40" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns41" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtDriverName.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repSpin.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn31.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Name" xml:space="preserve">
    <value>repositoryItemGridLookUpEdit1View</value>
  </data>
  <data name="col_Width.Caption" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="col_TotalSellPrice.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="txtDestination.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Name" xml:space="preserve">
    <value>lbl_remains</value>
  </data>
  <data name="textEdit6.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="txtDriverName.Location" type="System.Drawing.Point, System.Drawing">
    <value>244, 31</value>
  </data>
  <data name="repUOM.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Width.Name" xml:space="preserve">
    <value>col_Width</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;pnlSrcPrc.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="cmbPayMethod.Properties.Items6" xml:space="preserve">
    <value>Cash / On Credit</value>
  </data>
  <data name="lkp_SalesEmp.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repExpireDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_AddTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 428</value>
  </data>
  <data name="&gt;&gt;importFromExcelSheetToolStripMenuItem.Name" xml:space="preserve">
    <value>importFromExcelSheetToolStripMenuItem</value>
  </data>
  <data name="textEdit1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit3.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDriverName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="repDiscountRatio.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridView3.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Down</value>
  </data>
  <data name="lkpCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 20</value>
  </data>
  <data name="labelControl26.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="textEdit1.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>77, 409</value>
  </data>
  <data name="lkpStore.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl18.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="gridColumn31.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtExpenses.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn31.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;lblDestination.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn7.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_AddMatrixItems.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="col_Expire.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="flowLayoutPanel1.FlowDirection" type="System.Windows.Forms.FlowDirection, System.Windows.Forms">
    <value>RightToLeft</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn22.Width" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="repManufactureDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtNet.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_IsInTrns.Location" type="System.Drawing.Point, System.Drawing">
    <value>537, 30</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="labelControl12.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Parent" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;gridView4.Name" xml:space="preserve">
    <value>gridView4</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txt_Total.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="groupControl1.AppearanceCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_ItemDescriptionEn.Name" xml:space="preserve">
    <value>col_ItemDescriptionEn</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lbl_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>113, 50</value>
  </data>
  <data name="gridColumn16.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl22.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtDiscountValue.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barBtnPrint.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txt_paid.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns1" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtExpenses.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lblDriverName.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 13</value>
  </data>
  <data name="txt_Total.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns8" xml:space="preserve">
    <value>كود الحساب</value>
  </data>
  <data name="textEdit5.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="&gt;&gt;repDiscountRatio.Name" xml:space="preserve">
    <value>repDiscountRatio</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>42, 30</value>
  </data>
  <data name="textEdit5.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl16.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="gridColumn2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="cmdProcess.Properties.Items3" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Balance_Before.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl22.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtCurrency.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;tabExtraData.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_Total.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_expireDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemDateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl19.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repManufactureDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>F Name</value>
  </data>
  <data name="repExpireDate.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblDriverName.TabIndex" type="System.Int32, mscorlib">
    <value>244</value>
  </data>
  <data name="&gt;&gt;colPurchasePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Balance_Before.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn23.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtNet.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn11.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="groupControl1.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;gridColumn35.Name" xml:space="preserve">
    <value>gridColumn35</value>
  </data>
  <data name="gridColumn24.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns14" xml:space="preserve">
    <value>CostCenterCode</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="txtNet.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 471</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="gridColumn1.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Drawers2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblVehicleNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit5.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Serial.Caption" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="&gt;&gt;labelControl36.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtCurrency.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;textEdit7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_AddTaxV.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="pnlDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>495, 1</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="&gt;&gt;labelControl17.Name" xml:space="preserve">
    <value>labelControl17</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.Name" xml:space="preserve">
    <value>lbl_IsCredit_After</value>
  </data>
  <data name="btnSourceId.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn23.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn8.Width" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDestination.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="cmdProcess.Properties.AppearanceReadOnly.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repLocation.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtVehicleNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_MaxCredit.TabIndex" type="System.Int32, mscorlib">
    <value>218</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>113, 28</value>
  </data>
  <data name="textEdit1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl28.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cmbPayMethod.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="labelControl40.Text" xml:space="preserve">
    <value>Add Matrix Items</value>
  </data>
  <data name="&gt;&gt;txtNet.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Remains.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="pnlBranch.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 44</value>
  </data>
  <data name="cmbPayMethod.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtNet.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmdProcess.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="col_TotalSellPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl13.Name" xml:space="preserve">
    <value>labelControl13</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 13</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpenses.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_PayAcc2_Paid.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="repExpireDate_txt.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>61, 409</value>
  </data>
  <data name="pnlInvCode.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;labelControl13.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl5.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_Expire.Name" xml:space="preserve">
    <value>col_Expire</value>
  </data>
  <data name="gridColumn41.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="textEdit7.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 467</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn7.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn4.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="txt_Remains.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.Columns" xml:space="preserve">
    <value>AccountName</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="gridColumn23.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNet.TabIndex" type="System.Int32, mscorlib">
    <value>168</value>
  </data>
  <data name="txtExpenses.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl11.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl25.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lbl_IsCredit_Before.Text" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="lkp_Drawers2.Location" type="System.Drawing.Point, System.Drawing">
    <value>178, 25</value>
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit3.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit7.EditValue" xml:space="preserve">
    <value>Invoice Code</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Name" xml:space="preserve">
    <value>gridColumn9</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;textEdit8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridView1.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmdProcess.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Balance_After.TabIndex" type="System.Int32, mscorlib">
    <value>220</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="gridColumn10.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSourceCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="txt_paid.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txtNotes.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lbl_IsCredit_Before.TabIndex" type="System.Int32, mscorlib">
    <value>215</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDestination.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtDestination.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns2" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="col_ItemDescription.Caption" xml:space="preserve">
    <value>Item Description</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtNet.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns9" xml:space="preserve">
    <value>EmpCode</value>
  </data>
  <data name="txt_AddTaxR.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtScaleSerial.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;textEdit1.Name" xml:space="preserve">
    <value>textEdit1</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="textEdit8.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkp_Customers.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Balance_Before.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>77, 365</value>
  </data>
  <data name="pnlCostCenter.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="colDescription.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 454</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Name" xml:space="preserve">
    <value>grdPrInvoice</value>
  </data>
  <data name="textEdit6.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;col_Expire.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtNet.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_ItemDescription.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="txt_MaxCredit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit5.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="textEdit1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmdProcess.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtScaleSerial.Location" type="System.Drawing.Point, System.Drawing">
    <value>244, 8</value>
  </data>
  <data name="&gt;&gt;pnlDate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblDriverName.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="txtDiscountRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.Type" xml:space="preserve">
    <value>Pharmacy.Forms.uc_Currency, LinkIT ERP System, Version=1.1.24.2, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="textEdit8.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barBtnPrint.Name" xml:space="preserve">
    <value>barBtnPrint</value>
  </data>
  <data name="txt_paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="btn_AddMatrixItems.TabIndex" type="System.Int32, mscorlib">
    <value>238</value>
  </data>
  <data name="chk_IsInTrns.TabIndex" type="System.Int32, mscorlib">
    <value>271</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtExpenses.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtNotes.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repSpin.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>77, 431</value>
  </data>
  <data name="&gt;&gt;labelControl18.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl20.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;panelControl1.Name" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="frm_SL_ReturnArchive.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;textEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btnPrevious.ZOrder" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="txtScaleSerial.TabIndex" type="System.Int32, mscorlib">
    <value>251</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 13</value>
  </data>
  <data name="txt_TaxValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbPayMethod.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Name" xml:space="preserve">
    <value>lkp_SalesEmp</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;grdPrInvoice.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="repSpin.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl24.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repSpin.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Parent" xml:space="preserve">
    <value>pnlCrncy</value>
  </data>
  <data name="cmbPayMethod.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Serial.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Name" xml:space="preserve">
    <value>gridColumn4</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Text" xml:space="preserve">
    <value>Static Invoice Dimenstions</value>
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="&gt;&gt;barSubItem1.Name" xml:space="preserve">
    <value>barSubItem1</value>
  </data>
  <data name="&gt;&gt;gridColumn36.Name" xml:space="preserve">
    <value>gridColumn36</value>
  </data>
  <data name="gridColumn1.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl19.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit8.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SalesEmp.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>668, 359</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns15" xml:space="preserve">
    <value>PrintFileName</value>
  </data>
  <data name="gridColumn11.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repExpireDate_txt.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnSourceId.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 20</value>
  </data>
  <data name="textEdit6.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;pnlSrcPrc.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="col_Serial.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtDriverName.Name" xml:space="preserve">
    <value>txtDriverName</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Name" xml:space="preserve">
    <value>dtInvoiceDate</value>
  </data>
  <data name="lkp_SalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit8.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView5.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtVehicleNumber.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtDriverName.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SalesEmp.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtScaleSerial.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridColumn28.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;pnlBook.Name" xml:space="preserve">
    <value>pnlBook</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Name" xml:space="preserve">
    <value>barBtnHelp</value>
  </data>
  <data name="gridColumn12.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtNet.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtNet.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn29.Width" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="&gt;&gt;txt_Remains.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="lkpCostCenter.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Customers.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridColumn1.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_paid.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit7.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSourceCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Customers.Size" type="System.Drawing.Size, System.Drawing">
    <value>205, 20</value>
  </data>
  <data name="groupControl1.AppearanceCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn38.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtNet.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repExpireDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="txtDiscountValue.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="labelControl25.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_IsInTrns.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit8.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="textEdit3.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Remains.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl7.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="txtScaleSerial.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="repDiscountRatio.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate_txt.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl24.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="gridView3.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtSourceCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;textEdit5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_IsInTrns.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Name" xml:space="preserve">
    <value>gridColumn17</value>
  </data>
  <data name="lkpStore.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView4.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnNext.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="col_TotalQty.Width" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="repDiscountRatio.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridColumn31.Width" type="System.Int32, mscorlib">
    <value>126</value>
  </data>
  <data name="txtSourceCode.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="panelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1158, 169</value>
  </data>
  <data name="gridView4.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView5.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Total.TabIndex" type="System.Int32, mscorlib">
    <value>171</value>
  </data>
  <data name="lkp_Drawers.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl20.TabIndex" type="System.Int32, mscorlib">
    <value>202</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn39.Caption" xml:space="preserve">
    <value>VendorId</value>
  </data>
  <data name="textEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="&gt;&gt;labelControl25.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="btnPrevious.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="&gt;&gt;col_TotalSellPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="panelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 162</value>
  </data>
  <data name="btnSourceId.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlBook.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_Height.Width" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="lkpCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 24</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn7.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn24.Width" type="System.Int32, mscorlib">
    <value>125</value>
  </data>
  <data name="&gt;&gt;colLocationNameAr.Name" xml:space="preserve">
    <value>colLocationNameAr</value>
  </data>
  <data name="lkp_Customers.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_SalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 20</value>
  </data>
  <data name="cmbPayMethod.Properties.Items1" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn19.Caption" xml:space="preserve">
    <value>CustomerId</value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="gridColumn33.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="colLocationId.Caption" xml:space="preserve">
    <value>gridColumn29</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="&gt;&gt;labelControl11.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repSpin.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit7.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 22</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn41.Width" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="grdPrInvoice.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>270</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio2.Name" xml:space="preserve">
    <value>col_DiscountRatio2</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridColumn1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colDescription.Caption" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="&gt;&gt;panelControl2.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Name" xml:space="preserve">
    <value>txt_DeductTaxV</value>
  </data>
  <data name="btn_AddMatrixItems.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="lbl_IsCredit_After.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtDriverName.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Name" xml:space="preserve">
    <value>txt_Remains</value>
  </data>
  <data name="gridColumn28.Width" type="System.Int32, mscorlib">
    <value>102</value>
  </data>
  <data name="&gt;&gt;gridView2.Name" xml:space="preserve">
    <value>gridView2</value>
  </data>
  <data name="txtDestination.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DeductTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 20</value>
  </data>
  <data name="gridColumn35.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repDiscountRatio.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1182, 31</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 9</value>
  </data>
  <data name="dtInvoiceDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="&gt;&gt;txt_paid.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txt_DeductTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 406</value>
  </data>
  <data name="cmdProcess.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl19.Name" xml:space="preserve">
    <value>labelControl19</value>
  </data>
  <data name="txt_MaxCredit.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCurrency.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn42.Name" xml:space="preserve">
    <value>gridColumn42</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn33.Width" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="&gt;&gt;repUOM.Name" xml:space="preserve">
    <value>repUOM</value>
  </data>
  <data name="textEdit3.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>160</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Sales Return Invoice</value>
  </data>
  <data name="mi_frm_IC_Item.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 22</value>
  </data>
  <data name="uc_Currency1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="gridColumn5.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Name" xml:space="preserve">
    <value>gridColumn16</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_Serial.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns1" xml:space="preserve">
    <value>Book Name</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns1" xml:space="preserve">
    <value>Branch Name</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="gridColumn17.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtVehicleNumber.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 342</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.ZOrder" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repSpin.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Balance_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>183, 32</value>
  </data>
  <data name="txt_PayAcc1_Paid.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn5.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsInTrns.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtScaleSerial.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1182, 31</value>
  </data>
  <data name="txt_DeductTaxR.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="gridColumn42.Caption" xml:space="preserve">
    <value>F Name</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtExpenses.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Total.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit8.EditValue" xml:space="preserve">
    <value>Book</value>
  </data>
  <data name="&gt;&gt;txtDestination.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.EditMask" xml:space="preserve">
    <value>g</value>
  </data>
  <data name="gridColumn11.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_Balance_After.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCostCenter.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn28.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Drawers2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Total.Name" xml:space="preserve">
    <value>txt_Total</value>
  </data>
  <data name="gridColumn17.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;btn_AddMatrixItems.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.TabIndex" type="System.Int32, mscorlib">
    <value>258</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.Parent" xml:space="preserve">
    <value>pnlCrncy</value>
  </data>
  <data name="&gt;&gt;repItems.Name" xml:space="preserve">
    <value>repItems</value>
  </data>
  <data name="gridColumn1.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns15" xml:space="preserve">
    <value>Cost Center Code</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtExpenses.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="gridColumn2.Width" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Length.Caption" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="lkp_Customers.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>Pay Account 1</value>
  </data>
  <data name="gridColumn40.Width" type="System.Int32, mscorlib">
    <value>80</value>
  </data>
  <data name="&gt;&gt;barbtnLoadSellInvoice.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItemDescriptionEn.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnPrevious.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 30</value>
  </data>
  <data name="&gt;&gt;labelControl24.Name" xml:space="preserve">
    <value>labelControl24</value>
  </data>
  <data name="lblVehicleNumber.Text" xml:space="preserve">
    <value>Vehicle Number</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_SalesEmp.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl40.Name" xml:space="preserve">
    <value>labelControl40</value>
  </data>
  <data name="&gt;&gt;textEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Balance_Before.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn33.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl8.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="labelControl5.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Name" xml:space="preserve">
    <value>lkp_Drawers2</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl12.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="labelControl19.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn8.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnNext.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 23</value>
  </data>
  <data name="cmdProcess.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridView3.Name" xml:space="preserve">
    <value>gridView3</value>
  </data>
  <data name="lkpStore.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn21.Name" xml:space="preserve">
    <value>gridColumn21</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Drawers.Location" type="System.Drawing.Point, System.Drawing">
    <value>178, 3</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_TotalSellPrice.Name" xml:space="preserve">
    <value>col_TotalSellPrice</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 409</value>
  </data>
  <data name="gridView2.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl20.Name" xml:space="preserve">
    <value>labelControl20</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Name" xml:space="preserve">
    <value>gridColumn8</value>
  </data>
  <data name="txt_AddTaxR.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="colLocationNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Balance_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 13</value>
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDiscountRatio.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="gridColumn31.Caption" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl11.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="textEdit8.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;pnlBook.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="lkpStore.Properties.Columns14" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="&gt;&gt;textEdit7.Parent" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="lkp_Drawers.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl17.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="txtExpenses.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridView4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="lkpStore.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="labelControl19.TabIndex" type="System.Int32, mscorlib">
    <value>172</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;colLocationNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DeductTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repDiscountRatio.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="&gt;&gt;panelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Total.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 20</value>
  </data>
  <data name="groupControl1.AppearanceCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Balance_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>183, 55</value>
  </data>
  <data name="gridView1.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 32</value>
  </data>
  <data name="&gt;&gt;lkpStore.Parent" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="txtDriverName.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.EditValue" xml:space="preserve">
    <value>Cost Center</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns8" xml:space="preserve">
    <value>Taxable</value>
  </data>
  <data name="btnSourceId.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>160</value>
  </data>
  <data name="textEdit8.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_CategoryNameAr.Caption" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridView2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colPurchasePrice.Caption" xml:space="preserve">
    <value>Purchase P</value>
  </data>
  <data name="col_TotalQty.Caption" xml:space="preserve">
    <value>Total Qty</value>
  </data>
  <data name="&gt;&gt;labelControl36.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>161</value>
  </data>
  <data name="labelControl15.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Total.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="gridView2.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmdProcess.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="btn_AddMatrixItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>1063, 334</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDimensions.Name" xml:space="preserve">
    <value>mi_InvoiceStaticDimensions</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;gridColumn36.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblDestination.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 13</value>
  </data>
  <data name="txtDiscountRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colPurchasePrice.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtNotes.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 7</value>
  </data>
  <data name="cmdProcess.Properties.Items" xml:space="preserve">
    <value>Scale</value>
  </data>
  <data name="gridColumn23.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn23.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="flowLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>837, 94</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 474</value>
  </data>
  <data name="txt_TaxValue.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn29.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Customers.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.ZOrder" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="&gt;&gt;btn_AddMatrixItems.Name" xml:space="preserve">
    <value>btn_AddMatrixItems</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;barbtnLoadSellInvoice.Name" xml:space="preserve">
    <value>barbtnLoadSellInvoice</value>
  </data>
  <data name="&gt;&gt;rep_expireDate.Name" xml:space="preserve">
    <value>rep_expireDate</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.TabIndex" type="System.Int32, mscorlib">
    <value>259</value>
  </data>
  <data name="lblBlncAftr.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 13</value>
  </data>
  <data name="gridColumn36.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl16.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="repManufactureDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_InvoiceBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="txtDiscountValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 362</value>
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl11.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Name" xml:space="preserve">
    <value>lkp_Customers</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn16.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="pnlDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>122, 44</value>
  </data>
  <data name="&gt;&gt;batBtnList.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lbl_IsCredit_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>339, 55</value>
  </data>
  <data name="flowLayoutPanel1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl21.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="textEdit3.EditValue" xml:space="preserve">
    <value>Sales Empolyee</value>
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_Serial.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns6" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn35.Caption" xml:space="preserve">
    <value>MainPrice</value>
  </data>
  <data name="txt_Total.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Customers.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txtScaleSerial.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn11.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl26.Name" xml:space="preserve">
    <value>labelControl26</value>
  </data>
  <data name="gridColumn29.Caption" xml:space="preserve">
    <value>LargeUOMFactor</value>
  </data>
  <data name="txtScaleSerial.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.TabIndex" type="System.Int32, mscorlib">
    <value>272</value>
  </data>
  <data name="labelControl9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="textEdit7.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkp_Drawers.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Bold, Underline</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.ZOrder" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="rep_expireDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Name" xml:space="preserve">
    <value>gridColumn13</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtDestination.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>1119, 33</value>
  </data>
  <data name="labelControl8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_IsInTrns.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txt_PayAcc1_Paid.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;textEdit5.Name" xml:space="preserve">
    <value>textEdit5</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="txtScaleSerial.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.Name" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="btnPrevious.Text" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="&gt;&gt;labelControl22.Name" xml:space="preserve">
    <value>labelControl22</value>
  </data>
  <data name="gridColumn15.Caption" xml:space="preserve">
    <value>itemId</value>
  </data>
  <data name="&gt;&gt;pnlCrncy.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;lblDriverName.Name" xml:space="preserve">
    <value>lblDriverName</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Drawers.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="col_TotalSellPrice.Caption" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="pnlSrcPrc.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="pnlInvCode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.ZOrder" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="col_Height.Caption" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="rep_vendors.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>Max Credit</value>
  </data>
  <data name="btnSourceId.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate_txt.Mask.EditMask" xml:space="preserve">
    <value>\d?\d?/20\d\d</value>
  </data>
  <data name="&gt;&gt;pnlBranch.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtScaleSerial.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDestination.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 365</value>
  </data>
  <data name="&gt;&gt;labelControl35.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl19.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="gridColumn13.Width" type="System.Int32, mscorlib">
    <value>320</value>
  </data>
  <data name="txtNet.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridColumn1.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn38.Caption" xml:space="preserve">
    <value>UomIndex</value>
  </data>
  <data name="labelControl24.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;pnlBook.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="rep_expireDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>247, 245, 241</value>
  </data>
  <data name="&gt;&gt;gridColumn43.Name" xml:space="preserve">
    <value>gridColumn43</value>
  </data>
  <data name="pnlSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 44</value>
  </data>
  <data name="&gt;&gt;labelControl35.Name" xml:space="preserve">
    <value>labelControl35</value>
  </data>
  <data name="textEdit7.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtInvoiceCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtDiscountValue.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn13.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns3" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="&gt;&gt;labelControl15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.Name" xml:space="preserve">
    <value>lbl_IsCredit_Before</value>
  </data>
  <data name="textEdit8.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit1.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="gridColumn18.Caption" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="chk_IsInTrns.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl21.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="col_Batch.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rep_expireDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl15.TabIndex" type="System.Int32, mscorlib">
    <value>169</value>
  </data>
  <data name="gridView2.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 13</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 409</value>
  </data>
  <data name="rep_vendors.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;repExpireDate_txt.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit3.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Parent" xml:space="preserve">
    <value>pnlSalesEmp</value>
  </data>
  <data name="gridColumn35.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_PiecesCount.Name" xml:space="preserve">
    <value>col_PiecesCount</value>
  </data>
  <data name="txt_DeductTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="lblDestination.Text" xml:space="preserve">
    <value>Destination</value>
  </data>
  <data name="col_ItemDescriptionEn.Caption" xml:space="preserve">
    <value>Item Description En</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Name" xml:space="preserve">
    <value>gridColumn23</value>
  </data>
  <data name="&gt;&gt;txtSourceCode.Parent" xml:space="preserve">
    <value>pnlSrcPrc</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="col_ItemDescription.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_paid.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdPrInvoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>1154, 165</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;groupControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>135, 365</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn28.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="groupControl1.AppearanceCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtDestination.TabIndex" type="System.Int32, mscorlib">
    <value>247</value>
  </data>
  <data name="txtCurrency.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>172</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Name" xml:space="preserve">
    <value>contextMenuStrip1</value>
  </data>
  <data name="btn_AddMatrixItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="cmbPayMethod.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_SalesTax.Caption" xml:space="preserve">
    <value>Sales Tax</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 387</value>
  </data>
  <data name="uc_Currency1.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 19</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Name" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="gridView5.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Name" xml:space="preserve">
    <value>gridColumn18</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="frm_SL_ReturnArchive.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_IsInTrns.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridView1.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lblBlncAftr.Name" xml:space="preserve">
    <value>lblBlncAftr</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="col_Batch.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Drawers.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_MaxCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>183, 9</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn11.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Total.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkp_Customers.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl21.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridColumn5.Width" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="textEdit8.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl19.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtExpenses.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn31.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn33.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnNext.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="pnlBranch.Location" type="System.Drawing.Point, System.Drawing">
    <value>349, 1</value>
  </data>
  <data name="gridColumn42.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="textEdit7.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDisc.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txt_AddTaxR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="repSpin.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtNet.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn1.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtVehicleNumber.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lblDestination.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="repDiscountRatio.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_paid.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>265</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpCostCenter.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="gridColumn7.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl20.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;pnlCrncy.Name" xml:space="preserve">
    <value>pnlCrncy</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtScaleSerial.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;cmbPayMethod.Name" xml:space="preserve">
    <value>cmbPayMethod</value>
  </data>
  <data name="&gt;&gt;txtNet.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="txt_paid.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repItems.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn8.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView4.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;pnlSrcPrc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl16.Name" xml:space="preserve">
    <value>labelControl16</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns8" xml:space="preserve">
    <value>Cost Center Name</value>
  </data>
  <data name="textEdit7.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colLocationNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;mi_frm_IC_Item.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Name" xml:space="preserve">
    <value>gridColumn28</value>
  </data>
  <data name="cmdProcess.Properties.AppearanceReadOnly.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>203</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="txtDiscountValue.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl15.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="txt_AddTaxR.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn7.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnSourceId.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Expire.Caption" xml:space="preserve">
    <value>Expire Date</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;repDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn36.Width" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="txt_AddTaxR.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Balance_Before.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lkp_SalesEmp.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lbl_remains.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txt_TaxValue.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="gridColumn10.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl36.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barSubItem1.MenuAppearance.HeaderItemAppearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDestination.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txt_paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl12.Name" xml:space="preserve">
    <value>labelControl12</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn42.Width" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn10.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="labelControl7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;mi_PasteRows.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="textEdit6.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtDiscountRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>94, 362</value>
  </data>
  <data name="gridColumn33.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn7.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;textEdit1.Parent" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="txtDestination.Location" type="System.Drawing.Point, System.Drawing">
    <value>244, 77</value>
  </data>
  <data name="pnlBranch.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="&gt;&gt;pnlDate.Name" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="col_Expire.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn31.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtSourceCode.Properties.AppearanceReadOnly.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns7" xml:space="preserve">
    <value>IsTaxable</value>
  </data>
  <data name="&gt;&gt;col_SalesTax.Name" xml:space="preserve">
    <value>col_SalesTax</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit6.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdPrInvoice.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="btnAddCustomer.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Batch.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repExpireDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemDateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_paid.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>61, 431</value>
  </data>
  <data name="lbl_remains.TabIndex" type="System.Int32, mscorlib">
    <value>166</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="lkpStore.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn14.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;textEdit8.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="col_CompanyNameAr.Caption" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="btnSourceId.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lblVehicleNumber.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="txt_Total.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 339</value>
  </data>
  <data name="txtDriverName.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;textEdit7.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textEdit8.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_paid.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn6.Width" type="System.Int32, mscorlib">
    <value>184</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns1" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>214, 114</value>
  </data>
  <data name="cmbPayMethod.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers2.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtExpenses.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtVehicleNumber.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="&gt;&gt;repSpin.Name" xml:space="preserve">
    <value>repSpin</value>
  </data>
  <data name="colLocationNameAr.Caption" xml:space="preserve">
    <value>Location Name</value>
  </data>
  <data name="textEdit6.EditValue" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="repManufactureDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.Name" xml:space="preserve">
    <value>txt_Balance_After</value>
  </data>
  <data name="txtSourceCode.Properties.AppearanceReadOnly.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="&gt;&gt;col_TotalQty.Name" xml:space="preserve">
    <value>col_TotalQty</value>
  </data>
  <data name="gridView2.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtScaleSerial.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="mi_PasteRows.Text" xml:space="preserve">
    <value>Paste Rows</value>
  </data>
  <data name="gridView3.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpCostCenter.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit5.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit8.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblDriverName.Location" type="System.Drawing.Point, System.Drawing">
    <value>422, 34</value>
  </data>
  <data name="txtDriverName.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="textEdit5.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="repSpin.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="btnNext.Text" xml:space="preserve">
    <value>=&gt;</value>
  </data>
  <data name="cmbPayMethod.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="pnlDate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="txtInvoiceCode.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btn_AddMatrixItems.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Name" xml:space="preserve">
    <value>gridColumn14</value>
  </data>
  <data name="&gt;&gt;lblDriverName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmdProcess.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;colDescription.Name" xml:space="preserve">
    <value>colDescription</value>
  </data>
  <data name="labelControl18.TabIndex" type="System.Int32, mscorlib">
    <value>170</value>
  </data>
  <data name="lblVehicleNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 13</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>342, 29</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="gridView4.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns" xml:space="preserve">
    <value>InvoiceBookName</value>
  </data>
  <data name="txt_TaxValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="repExpireDate_txt.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colDescription.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblDestination.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_TaxValue.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="rep_expireDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>35</value>
  </metadata>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>518, 17</value>
  </metadata>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>