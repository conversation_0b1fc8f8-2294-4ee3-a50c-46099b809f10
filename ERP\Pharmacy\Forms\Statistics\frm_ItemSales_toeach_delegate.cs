﻿using DAL;
using DAL.Res;
using DevExpress.XtraCharts;
using DevExpress.XtraPrinting;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy.Forms
{
    public partial class frm_ItemSales_toeach_delegate : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new DAL.ERPDataContext();
        UserPriv prvlg;
        public frm_ItemSales_toeach_delegate()
        {
            RTL.EnCulture(Shared.IsEnglish);

            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
        }

        private void frm_ItemSales_toeach_delegate_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
            LoadPrivilege();
            dtFromDate.EditValue = MyHelper.Get_Server_DateTime();
            dtToDate.EditValue = MyHelper.Get_Server_DateTime();

            var lstItm = (from i in DB.IC_Items
                          where i.ItemType != (int)DAL.ItemType.MatrixParent &&
                          i.ItemType != (int)DAL.ItemType.Subtotal
                          select new { i.ItemNameAr, i.ItemNameEn, i.ItemId }).ToList();



            chkLst.Properties.DisplayMember = "ItemNameAr";
            chkLst.Properties.ValueMember = "ItemId";
            chkLst.Properties.DataSource = lstItm;
        }

        private void barBtnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                var ItemList = chkLst.Properties.GetItems().GetCheckedValues();
                DateTime From = Convert.ToDateTime(dtFromDate.EditValue);
                DateTime To = Convert.ToDateTime(dtToDate.EditValue);

                var data = (from d in DB.SL_InvoiceDetails
                            join t in DB.IC_Items.DefaultIfEmpty()
                            on d.ItemId equals t.ItemId
                            where ItemList.Count() > 0 ? ItemList.Contains(d.ItemId) : true
                            join i in DB.SL_Invoices
                            on d.SL_InvoiceId equals i.SL_InvoiceId
                            where dtFromDate.EditValue == null ? true : (i.InvoiceDate.Date >= From.Date)
                            where dtToDate.EditValue == null ? true : (i.InvoiceDate.Date <= To.Date)


                            // where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId
                            join m in DB.HR_Employees
                               on i.SalesEmpId equals m.EmpId

                            group new { t, i, d, m } by new { i.SalesEmpId, d.ItemId } into igroup

                            //where ItemList.Contains(igroup.Select(a=>a.d.ItemId).FirstOrDefault())
                            select new
                            {
                                ItemName = igroup.Select(g => g.t.ItemNameAr).FirstOrDefault(),
                                Qty = igroup.Sum(g => g.d.UOMIndex == 0 ? g.d.Qty : g.d.UOMIndex == 1 ? (g.d.Qty * Convert.ToDecimal(g.t.MediumUOMFactor)) : (g.d.Qty * Convert.ToDecimal(g.t.LargeUOMFactor))),
                                Delegate = igroup.Select(g => g.m.EmpName).FirstOrDefault(),
                                Delegateid = igroup.Select(g => g.m.EmpId).FirstOrDefault(),
                                ItemId = igroup.Select(g => g.t.ItemId).FirstOrDefault(),
                                // DelegateId = igroup.Select(g => g.m.EmpId).FirstOrDefault(),
                                TotalSellPrice = igroup.Sum(g => g.d.TotalSellPrice)
                            }).Distinct().OrderBy(a=>a.ItemId).ToList();

                // chartControl1.DataSource = data;

                var emp = DB.HR_Employees.Where(m => m.SalesRep == true);
                chartControl1.DataSource = null;
                chartControl1.Series.Clear();
                var counter = 0;
                foreach (var item in data.Select(a => new { a.Delegate, a.Delegateid }).Distinct())
                {
                    Series newSeries = new Series(item.Delegate.ToString(), ViewType.Bar);
                    chartControl1.Series.Add(newSeries);
                    //newSeries.ChangeView(ViewType.Bar);

                    chartControl1.Series[counter].DataSource = data.Where(x => x.Delegate == item.Delegate);

                    chartControl1.Series[counter].ArgumentDataMember = "ItemName";
                    chartControl1.Series[counter].ValueDataMembers[0] = "TotalSellPrice"/*+(count+1)*/;
                    counter++;
                }

                //counter = 0;
                //foreach (var item2 in data.Select(a => new { a.Delegate, a.Delegateid }).Distinct())
                //{
                //    Series newSeries = new Series(item2.Delegate.ToString(), ViewType.Bar);
                //    chartControl2.Series.Add(newSeries);
                //    //newSeries.ChangeView(ViewType.Bar);

                //    chartControl2.Series[counter].DataSource = data.Where(x => x.Delegate == item2.Delegate);

                //    //chartControl1.Series[counter].ArgumentDataMember = "ItemName";
                //    //chartControl1.Series[counter].ValueDataMembers[0] = "Qty"/*+(count+1)*/;

                //    // chartControl1.Series[counter+1].DataSource = data.Where(x => x.Delegate == item.Delegate);

                //    chartControl2.Series[counter].ArgumentDataMember = "ItemName";
                //    chartControl2.Series[counter].ValueDataMembers[0] = "TotalSellPrice"/*+(count+1)*/;
                //    counter++;
                //}
            }
            catch (Exception ex) { }

        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        private void printableComponentLink1_CreateReportHeaderArea(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            string ReportName = this.Text;
            string dateFilters = string.Empty;
            string otherFilters = string.Empty;

            //create filters line
            if (dtFromDate.EditValue != null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtFrom : ResAccAr.txtFrom) +
                    dtFromDate.DateTime.ToShortDateString() +
                    (Shared.IsEnglish == true ? ResAccEn.txtTo : ResAccAr.txtTo) +
                    dtToDate.DateTime.ToShortDateString();

            else if (dtFromDate.EditValue != null && dtToDate.EditValue == null)
                dateFilters =
                    (Shared.IsEnglish == true ? ResAccEn.txtFromDate : ResAccAr.txtFromDate) +
                    dtFromDate.DateTime.ToShortDateString();
            else if (dtFromDate.EditValue == null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtToDate : ResAccAr.txtToDate) +
                    dtToDate.DateTime.ToShortDateString();
            else
                dateFilters = "";


            ErpUtils.CreateReportHeader(e, ReportName, dateFilters, otherFilters);
        }
        private void printableComponentLink1_CreateReportFooter(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            RectangleF recTotal = new RectangleF((float)10, (float)17, 740, (float)25);

            e.Graph.StringFormat = Shared.IsEnglish ? new BrickStringFormat(StringAlignment.Near) : new BrickStringFormat(StringAlignment.Far);
            e.Graph.Font = new Font("Times New Roman", 13, FontStyle.Regular);
            e.Graph.ForeColor = Color.Black;
            e.Graph.DefaultBrickStyle.BorderColor = Color.Transparent;
            e.Graph.BackColor = Color.Snow;


            //string total = txtTotal.Text;

            //e.Graph.DrawString(total, recTotal);            
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (this.Width == 1376)
                chartControl1.Width = this.Width - 300;
            //chartControl1.Height = this.Height - 130;
            PrintingSystem printSystem = new PrintingSystem(this.components);
            PrintableComponentLink printLink;
            if (this.components == null)
                printLink = new PrintableComponentLink();
            else
                printLink = new PrintableComponentLink(this.components);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).BeginInit();

            printSystem.Links.AddRange(new object[] {
            printLink});

            printLink.Component = this.chartControl1;

            printLink.PaperKind = System.Drawing.Printing.PaperKind.A4;
            printLink.Landscape = true;
            printLink.Margins = new System.Drawing.Printing.Margins(5, 5, 135, 50);
            printLink.PrintingSystem = printSystem;
            printLink.PrintingSystemBase = printSystem;

            printLink.CreateMarginalHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportHeaderArea);
            printLink.CreateReportFooterArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportFooter);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).EndInit();

            printLink.CreateDocument();
            printLink.ShowPreview();
            chartControl1.Width = this.Width - 51;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_ItemSales_toeach_delegate).FirstOrDefault();

                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnOk.Enabled = false;
            }
        }
    }
}
