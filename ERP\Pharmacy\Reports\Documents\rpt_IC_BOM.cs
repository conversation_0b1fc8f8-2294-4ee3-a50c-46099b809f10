using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_IC_BOM : DevExpress.XtraReports.UI.XtraReport
    {
        string BOM_Name, BOM_Description, ProductItem, Uom, WorkedHours, Qty;

        DataTable dt_Supposed_Raws;
        DataTable dt_Supposed_expenses;

        public rpt_IC_BOM()
        {
            InitializeComponent();
        }

        public rpt_IC_BOM(string _BOM_Name, string _BOM_Description, string _ProductItem, string _Uom, string _WorkedHours, string _Qty,
            DataTable dt_Supposed_Raws,
            DataTable dt_Supposed_expenses)
        {
            InitializeComponent();
            BOM_Name = _BOM_Name;
            BOM_Description = _BOM_Description;
            ProductItem = _ProductItem;
            Uom = _Uom;
            WorkedHours = _WorkedHours;
            Qty = _Qty;

            this.dt_Supposed_Raws = dt_Supposed_Raws;
            this.dt_Supposed_expenses = dt_Supposed_expenses;

            getReportHeader();
        }

        public void LoadData()
        {
            lbl_BOM_Name.Text = BOM_Name;
            lbl_BOM_Description.Text = BOM_Description;
            lbl_ProductItem.Text = ProductItem;
            lbl_Uom.Text = Uom;
            lbl_WorkedHours.Text = WorkedHours;
            lbl_Qty.Text = Qty;

            DetailReport_SupposedRaws.DataSource = dt_Supposed_Raws;
            cell_S_ItemCode1.DataBindings.Add("Text", DetailReport_SupposedRaws.DataSource, "S_ItemCode1");
            cell_S_ItemCode1.DataBindings.Add("Text", DetailReport_SupposedRaws.DataSource, "S_ItemCode2");
            cell_S_ItemName.DataBindings.Add("Text", DetailReport_SupposedRaws.DataSource, "S_ItemName");
            cell_S_UOM.DataBindings.Add("Text", DetailReport_SupposedRaws.DataSource, "S_UOM");
            cell_S_Qty.DataBindings.Add("Text", DetailReport_SupposedRaws.DataSource, "S_Qty");


            DetailReport_SupposedExpenses.DataSource = dt_Supposed_expenses;
            cell_S_ExpenseName.DataBindings.Add("Text", DetailReport_SupposedExpenses.DataSource, "S_ExpenseName");
            cell_S_ExpenseValue.DataBindings.Add("Text", DetailReport_SupposedExpenses.DataSource, "S_ExpenseValue");
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

    }
}
