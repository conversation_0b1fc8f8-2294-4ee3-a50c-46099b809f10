﻿namespace Pharmacy.Forms
{
    partial class frm_SL_InvoiceArchive
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_InvoiceArchive));
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_ConvertTo = new DevExpress.XtraBars.BarSubItem();
            this.barSubItemPrint = new DevExpress.XtraBars.BarSubItem();
            this.barbtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barbtnPrintF = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.batBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barBtnNotesReceivable = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_Sl_Qoute = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_SalesOrder = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_IC_OutTrns = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_JO = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_PR_Invoice = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_IC_Transfer = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_OutTrns = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_CashNote = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.txtInvoiceCode = new DevExpress.XtraEditors.TextEdit();
            this.btnPrevious = new DevExpress.XtraEditors.SimpleButton();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.dtInvoiceDate = new DevExpress.XtraEditors.DateEdit();
            this.labelControl35 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.pnlBook = new System.Windows.Forms.Panel();
            this.txtTserial = new DevExpress.XtraEditors.TextEdit();
            this.lkp_InvoiceBook = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlInvCode = new System.Windows.Forms.Panel();
            this.txtTinvCode = new DevExpress.XtraEditors.TextEdit();
            this.pnlDate = new System.Windows.Forms.Panel();
            this.txtTdate = new DevExpress.XtraEditors.TextEdit();
            this.pnlBranch = new System.Windows.Forms.Panel();
            this.txtTstore = new DevExpress.XtraEditors.TextEdit();
            this.lkpStore = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlAgeDate = new System.Windows.Forms.Panel();
            this.txtTdueDate = new DevExpress.XtraEditors.TextEdit();
            this.txt_DueDays = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DueDate = new DevExpress.XtraEditors.DateEdit();
            this.pnlDeliveryDate = new System.Windows.Forms.Panel();
            this.txtTdeliverDate = new DevExpress.XtraEditors.TextEdit();
            this.dtDeliverDate = new DevExpress.XtraEditors.DateEdit();
            this.pnlCurrency = new System.Windows.Forms.Panel();
            this.txtCurrency = new DevExpress.XtraEditors.TextEdit();
            this.pnlPostStore = new System.Windows.Forms.Panel();
            this.txt_Post_Date = new DevExpress.XtraEditors.DateEdit();
            this.chk_IsPosted = new DevExpress.XtraEditors.CheckEdit();
            this.pnlPO = new System.Windows.Forms.Panel();
            this.txtTpo = new DevExpress.XtraEditors.TextEdit();
            this.txt_PO_No = new DevExpress.XtraEditors.TextEdit();
            this.pnlSalesEmp = new System.Windows.Forms.Panel();
            this.txtTSalesEmp = new DevExpress.XtraEditors.TextEdit();
            this.lkp_SalesEmp = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlCostCenter = new System.Windows.Forms.Panel();
            this.lkpCostCenter = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit9 = new DevExpress.XtraEditors.TextEdit();
            this.pnlSrcPrc = new System.Windows.Forms.Panel();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.lblShipTo = new DevExpress.XtraEditors.LabelControl();
            this.txtNotes = new DevExpress.XtraEditors.MemoEdit();
            this.txt_Shipping = new DevExpress.XtraEditors.MemoEdit();
            this.txtSourceCode = new DevExpress.XtraEditors.TextEdit();
            this.btnSourceId = new DevExpress.XtraEditors.ButtonEdit();
            this.cmdProcess = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.chk_IsOutTrns = new DevExpress.XtraEditors.CheckEdit();
            this.cmbPayMethod = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.lkp_Drawers = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.lbl_remains = new DevExpress.XtraEditors.LabelControl();
            this.txt_Remains = new DevExpress.XtraEditors.TextEdit();
            this.lbl_Paid = new DevExpress.XtraEditors.LabelControl();
            this.txt_paid = new DevExpress.XtraEditors.TextEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Customers = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.grdPrInvoice = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_frm_IC_Item = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_CustLastPrices = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_LastPrices = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_PasteRows = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_ExportData = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_InvoiceStaticDisc = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_InvoiceStaticDimensions = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_ImportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repSpin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repDiscountRatio = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.col_SellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repUOM = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repItems = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CompanyNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CategoryNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grdcol_branch = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkp_storee = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Expire = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_expireDate = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Batch = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Batch = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Length = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Width = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Height = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PiecesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemDescriptionEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SalesTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountRatio2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountRatio3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Serial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repManufactureDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.col_CusTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkp_store = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.txtNet = new DevExpress.XtraEditors.TextEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Total = new DevExpress.XtraEditors.TextEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.btnAddCustomer = new DevExpress.XtraEditors.SimpleButton();
            this.grdLastPrices = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colTotalPurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colUOM = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCustNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colInvoiceDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colInvoiceCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.txtExpenses = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Balance_After = new DevExpress.XtraEditors.LabelControl();
            this.txt_Balance_Before = new DevExpress.XtraEditors.LabelControl();
            this.txt_MaxCredit = new DevExpress.XtraEditors.LabelControl();
            this.lbl_IsCredit_After = new DevExpress.XtraEditors.LabelControl();
            this.lblBlncAftr = new DevExpress.XtraEditors.LabelControl();
            this.lbl_IsCredit_Before = new DevExpress.XtraEditors.LabelControl();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.lkp_Drawers2 = new DevExpress.XtraEditors.LookUpEdit();
            this.txt_PayAcc1_Paid = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl30 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl31 = new DevExpress.XtraEditors.LabelControl();
            this.txt_PayAcc2_Paid = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl33 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.page_AccInfo = new DevExpress.XtraTab.XtraTabPage();
            this.lbl_Validate_MaxLimit = new DevExpress.XtraEditors.LabelControl();
            this.page_JobOrder = new DevExpress.XtraTab.XtraTabPage();
            this.textEdit3 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_JOStatus = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_JOPriority = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit5 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_JOSalesEmp = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit8 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_JODept = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit4 = new DevExpress.XtraEditors.TextEdit();
            this.txt_JOJob = new DevExpress.XtraEditors.TextEdit();
            this.textEdit2 = new DevExpress.XtraEditors.TextEdit();
            this.txt_JODeliveryDate = new DevExpress.XtraEditors.DateEdit();
            this.textEdit7 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit6 = new DevExpress.XtraEditors.TextEdit();
            this.txt_JORegDate = new DevExpress.XtraEditors.DateEdit();
            this.txt_JOCode = new DevExpress.XtraEditors.TextEdit();
            this.Page_LastPrices = new DevExpress.XtraTab.XtraTabPage();
            this.tabExtraData = new DevExpress.XtraTab.XtraTabPage();
            this.txtScaleSerial = new DevExpress.XtraEditors.TextEdit();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.txtDestination = new DevExpress.XtraEditors.TextEdit();
            this.lblDestination = new DevExpress.XtraEditors.LabelControl();
            this.txtVehicleNumber = new DevExpress.XtraEditors.TextEdit();
            this.lblVehicleNumber = new DevExpress.XtraEditors.LabelControl();
            this.txtDriverName = new DevExpress.XtraEditors.TextEdit();
            this.lblDriverName = new DevExpress.XtraEditors.LabelControl();
            this.txt_AttnMr = new DevExpress.XtraEditors.TextEdit();
            this.labelControl41 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.txtDiscountRatio = new DevExpress.XtraEditors.SpinEdit();
            this.txtDiscountValue = new DevExpress.XtraEditors.SpinEdit();
            this.txt_TaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DeductTaxR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DeductTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AddTaxR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AddTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.txtExpensesR = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl37 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl38 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl39 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl42 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl43 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl44 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl45 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl46 = new DevExpress.XtraEditors.LabelControl();
            this.txt_retentionR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_RetentionV = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AdvancePayR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AdvancePayV = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.txt_CusTaxV = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            this.flowLayoutPanel1.SuspendLayout();
            this.pnlBook.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTserial.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_InvoiceBook.Properties)).BeginInit();
            this.pnlInvCode.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTinvCode.Properties)).BeginInit();
            this.pnlDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTdate.Properties)).BeginInit();
            this.pnlBranch.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTstore.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).BeginInit();
            this.pnlAgeDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTdueDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDays.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDate.Properties)).BeginInit();
            this.pnlDeliveryDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTdeliverDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties)).BeginInit();
            this.pnlCurrency.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).BeginInit();
            this.pnlPostStore.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Post_Date.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Post_Date.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsPosted.Properties)).BeginInit();
            this.pnlPO.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTpo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PO_No.Properties)).BeginInit();
            this.pnlSalesEmp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTSalesEmp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).BeginInit();
            this.pnlCostCenter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCostCenter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit9.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Shipping.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSourceCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnSourceId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmdProcess.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsOutTrns.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPayMethod.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Remains.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDiscountRatio)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_storee)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Batch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_store)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Total.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdLastPrices)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc1_Paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc2_Paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.page_AccInfo.SuspendLayout();
            this.page_JobOrder.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOStatus.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOPriority.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOSalesEmp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JODept.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit4.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JOJob.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JODeliveryDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JODeliveryDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JORegDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JORegDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JOCode.Properties)).BeginInit();
            this.Page_LastPrices.SuspendLayout();
            this.tabExtraData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtScaleSerial.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDestination.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVehicleNumber.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDriverName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AttnMr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpensesR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_retentionR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_RetentionV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AdvancePayR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AdvancePayV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CusTaxV.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnClose,
            this.barBtnHelp,
            this.batBtnList,
            this.barBtnNew,
            this.barBtnDelete,
            this.barBtnNotesReceivable,
            this.barSubItemPrint,
            this.barbtnPrint,
            this.barbtnPrintF,
            this.barBtnLoad_Sl_Qoute,
            this.barBtnLoad_SalesOrder,
            this.barBtnLoad_IC_OutTrns,
            this.barBtnLoad_JO,
            this.barBtnLoad_PR_Invoice,
            this.barBtnLoad_IC_Transfer,
            this.barBtn_ConvertTo,
            this.barBtn_OutTrns,
            this.barBtn_CashNote});
            this.barManager1.MaxItemId = 56;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_ConvertTo),
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItemPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnDelete),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.batBtnList),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtn_ConvertTo
            // 
            this.barBtn_ConvertTo.Id = 53;
            this.barBtn_ConvertTo.Name = "barBtn_ConvertTo";
            // 
            // barSubItemPrint
            // 
            this.barSubItemPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barSubItemPrint, "barSubItemPrint");
            this.barSubItemPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barSubItemPrint.Id = 32;
            this.barSubItemPrint.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnPrintF)});
            this.barSubItemPrint.Name = "barSubItemPrint";
            this.barSubItemPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barbtnPrint
            // 
            resources.ApplyResources(this.barbtnPrint, "barbtnPrint");
            this.barbtnPrint.Id = 33;
            this.barbtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barbtnPrint.Name = "barbtnPrint";
            this.barbtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barbtnPrintF
            // 
            resources.ApplyResources(this.barbtnPrintF, "barbtnPrintF");
            this.barbtnPrintF.Id = 34;
            this.barbtnPrintF.Name = "barbtnPrintF";
            this.barbtnPrintF.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_F_ItemClick);
            // 
            // barBtnNew
            // 
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Enabled = false;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 26;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnDelete
            // 
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Enabled = false;
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 28;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnDelete_ItemClick);
            // 
            // barBtnSave
            // 
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Enabled = false;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // batBtnList
            // 
            this.batBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.batBtnList, "batBtnList");
            this.batBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.batBtnList.Id = 25;
            this.batBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.batBtnList.Name = "batBtnList";
            this.batBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.batBtnList.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.batBtnList_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 1;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.Item.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // barBtnNotesReceivable
            // 
            this.barBtnNotesReceivable.Id = 46;
            this.barBtnNotesReceivable.Name = "barBtnNotesReceivable";
            // 
            // barBtnLoad_Sl_Qoute
            // 
            this.barBtnLoad_Sl_Qoute.Id = 47;
            this.barBtnLoad_Sl_Qoute.Name = "barBtnLoad_Sl_Qoute";
            // 
            // barBtnLoad_SalesOrder
            // 
            this.barBtnLoad_SalesOrder.Id = 48;
            this.barBtnLoad_SalesOrder.Name = "barBtnLoad_SalesOrder";
            // 
            // barBtnLoad_IC_OutTrns
            // 
            this.barBtnLoad_IC_OutTrns.Id = 49;
            this.barBtnLoad_IC_OutTrns.Name = "barBtnLoad_IC_OutTrns";
            // 
            // barBtnLoad_JO
            // 
            this.barBtnLoad_JO.Id = 50;
            this.barBtnLoad_JO.Name = "barBtnLoad_JO";
            // 
            // barBtnLoad_PR_Invoice
            // 
            this.barBtnLoad_PR_Invoice.Id = 51;
            this.barBtnLoad_PR_Invoice.Name = "barBtnLoad_PR_Invoice";
            // 
            // barBtnLoad_IC_Transfer
            // 
            this.barBtnLoad_IC_Transfer.Id = 52;
            this.barBtnLoad_IC_Transfer.Name = "barBtnLoad_IC_Transfer";
            // 
            // barBtn_OutTrns
            // 
            this.barBtn_OutTrns.Id = 54;
            this.barBtn_OutTrns.Name = "barBtn_OutTrns";
            // 
            // barBtn_CashNote
            // 
            this.barBtn_CashNote.Id = 55;
            this.barBtn_CashNote.Name = "barBtn_CashNote";
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // txtInvoiceCode
            // 
            resources.ApplyResources(this.txtInvoiceCode, "txtInvoiceCode");
            this.txtInvoiceCode.EnterMoveNextControl = true;
            this.txtInvoiceCode.Name = "txtInvoiceCode";
            this.txtInvoiceCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtInvoiceCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtInvoiceCode.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // btnPrevious
            // 
            this.btnPrevious.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrevious.Image = global::Pharmacy.Properties.Resources.prev32;
            resources.ApplyResources(this.btnPrevious, "btnPrevious");
            this.btnPrevious.Name = "btnPrevious";
            this.btnPrevious.TabStop = false;
            this.btnPrevious.Click += new System.EventHandler(this.btnPrevious_Click);
            // 
            // btnNext
            // 
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.Name = "btnNext";
            this.btnNext.TabStop = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // dtInvoiceDate
            // 
            resources.ApplyResources(this.dtInvoiceDate, "dtInvoiceDate");
            this.dtInvoiceDate.EnterMoveNextControl = true;
            this.dtInvoiceDate.MenuManager = this.barManager1;
            this.dtInvoiceDate.Name = "dtInvoiceDate";
            this.dtInvoiceDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.dtInvoiceDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtInvoiceDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtInvoiceDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtInvoiceDate.Properties.Buttons"))))});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtInvoiceDate.Properties.DisplayFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.EditFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.Mask.EditMask");
            this.dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtInvoiceDate.EditValueChanged += new System.EventHandler(this.dtInvoiceDate_EditValueChanged);
            this.dtInvoiceDate.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl35
            // 
            resources.ApplyResources(this.labelControl35, "labelControl35");
            this.labelControl35.Name = "labelControl35";
            // 
            // labelControl36
            // 
            resources.ApplyResources(this.labelControl36, "labelControl36");
            this.labelControl36.Name = "labelControl36";
            // 
            // panelControl1
            // 
            resources.ApplyResources(this.panelControl1, "panelControl1");
            this.panelControl1.Controls.Add(this.flowLayoutPanel1);
            this.panelControl1.Controls.Add(this.labelControl4);
            this.panelControl1.Controls.Add(this.lblShipTo);
            this.panelControl1.Controls.Add(this.txtNotes);
            this.panelControl1.Controls.Add(this.txt_Shipping);
            this.panelControl1.Name = "panelControl1";
            // 
            // flowLayoutPanel1
            // 
            resources.ApplyResources(this.flowLayoutPanel1, "flowLayoutPanel1");
            this.flowLayoutPanel1.Controls.Add(this.pnlBook);
            this.flowLayoutPanel1.Controls.Add(this.pnlInvCode);
            this.flowLayoutPanel1.Controls.Add(this.pnlDate);
            this.flowLayoutPanel1.Controls.Add(this.pnlBranch);
            this.flowLayoutPanel1.Controls.Add(this.pnlAgeDate);
            this.flowLayoutPanel1.Controls.Add(this.pnlDeliveryDate);
            this.flowLayoutPanel1.Controls.Add(this.pnlCurrency);
            this.flowLayoutPanel1.Controls.Add(this.pnlPostStore);
            this.flowLayoutPanel1.Controls.Add(this.pnlPO);
            this.flowLayoutPanel1.Controls.Add(this.pnlSalesEmp);
            this.flowLayoutPanel1.Controls.Add(this.pnlCostCenter);
            this.flowLayoutPanel1.Controls.Add(this.pnlSrcPrc);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            // 
            // pnlBook
            // 
            this.pnlBook.Controls.Add(this.txtTserial);
            this.pnlBook.Controls.Add(this.lkp_InvoiceBook);
            resources.ApplyResources(this.pnlBook, "pnlBook");
            this.pnlBook.Name = "pnlBook";
            // 
            // txtTserial
            // 
            resources.ApplyResources(this.txtTserial, "txtTserial");
            this.txtTserial.EnterMoveNextControl = true;
            this.txtTserial.MenuManager = this.barManager1;
            this.txtTserial.Name = "txtTserial";
            this.txtTserial.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTserial.Properties.Appearance.BackColor")));
            this.txtTserial.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTserial.Properties.Appearance.ForeColor")));
            this.txtTserial.Properties.Appearance.Options.UseBackColor = true;
            this.txtTserial.Properties.Appearance.Options.UseForeColor = true;
            this.txtTserial.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTserial.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTserial.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTserial.Properties.MaxLength = 190;
            this.txtTserial.TabStop = false;
            // 
            // lkp_InvoiceBook
            // 
            resources.ApplyResources(this.lkp_InvoiceBook, "lkp_InvoiceBook");
            this.lkp_InvoiceBook.EnterMoveNextControl = true;
            this.lkp_InvoiceBook.Name = "lkp_InvoiceBook";
            this.lkp_InvoiceBook.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_InvoiceBook.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_InvoiceBook.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_InvoiceBook.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_InvoiceBook.Properties.Buttons"))))});
            this.lkp_InvoiceBook.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_InvoiceBook.Properties.Columns"), resources.GetString("lkp_InvoiceBook.Properties.Columns1"), ((int)(resources.GetObject("lkp_InvoiceBook.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_InvoiceBook.Properties.Columns3"))), resources.GetString("lkp_InvoiceBook.Properties.Columns4"), ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_InvoiceBook.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_InvoiceBook.Properties.Columns7"), resources.GetString("lkp_InvoiceBook.Properties.Columns8"), ((int)(resources.GetObject("lkp_InvoiceBook.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_InvoiceBook.Properties.Columns10"))), resources.GetString("lkp_InvoiceBook.Properties.Columns11"), ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_InvoiceBook.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_InvoiceBook.Properties.Columns14"), resources.GetString("lkp_InvoiceBook.Properties.Columns15"), ((int)(resources.GetObject("lkp_InvoiceBook.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_InvoiceBook.Properties.Columns17"))), resources.GetString("lkp_InvoiceBook.Properties.Columns18"), ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_InvoiceBook.Properties.Columns20"))))});
            this.lkp_InvoiceBook.Properties.NullText = resources.GetString("lkp_InvoiceBook.Properties.NullText");
            this.lkp_InvoiceBook.EditValueChanged += new System.EventHandler(this.lkpStore_EditValueChanged);
            this.lkp_InvoiceBook.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlInvCode
            // 
            this.pnlInvCode.Controls.Add(this.txtTinvCode);
            this.pnlInvCode.Controls.Add(this.txtInvoiceCode);
            resources.ApplyResources(this.pnlInvCode, "pnlInvCode");
            this.pnlInvCode.Name = "pnlInvCode";
            // 
            // txtTinvCode
            // 
            resources.ApplyResources(this.txtTinvCode, "txtTinvCode");
            this.txtTinvCode.EnterMoveNextControl = true;
            this.txtTinvCode.MenuManager = this.barManager1;
            this.txtTinvCode.Name = "txtTinvCode";
            this.txtTinvCode.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTinvCode.Properties.Appearance.BackColor")));
            this.txtTinvCode.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTinvCode.Properties.Appearance.ForeColor")));
            this.txtTinvCode.Properties.Appearance.Options.UseBackColor = true;
            this.txtTinvCode.Properties.Appearance.Options.UseForeColor = true;
            this.txtTinvCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTinvCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTinvCode.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTinvCode.Properties.MaxLength = 190;
            this.txtTinvCode.TabStop = false;
            // 
            // pnlDate
            // 
            this.pnlDate.Controls.Add(this.txtTdate);
            this.pnlDate.Controls.Add(this.dtInvoiceDate);
            resources.ApplyResources(this.pnlDate, "pnlDate");
            this.pnlDate.Name = "pnlDate";
            // 
            // txtTdate
            // 
            resources.ApplyResources(this.txtTdate, "txtTdate");
            this.txtTdate.EnterMoveNextControl = true;
            this.txtTdate.MenuManager = this.barManager1;
            this.txtTdate.Name = "txtTdate";
            this.txtTdate.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTdate.Properties.Appearance.BackColor")));
            this.txtTdate.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTdate.Properties.Appearance.ForeColor")));
            this.txtTdate.Properties.Appearance.Options.UseBackColor = true;
            this.txtTdate.Properties.Appearance.Options.UseForeColor = true;
            this.txtTdate.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTdate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTdate.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTdate.Properties.MaxLength = 190;
            this.txtTdate.TabStop = false;
            // 
            // pnlBranch
            // 
            this.pnlBranch.Controls.Add(this.txtTstore);
            this.pnlBranch.Controls.Add(this.lkpStore);
            resources.ApplyResources(this.pnlBranch, "pnlBranch");
            this.pnlBranch.Name = "pnlBranch";
            // 
            // txtTstore
            // 
            resources.ApplyResources(this.txtTstore, "txtTstore");
            this.txtTstore.EnterMoveNextControl = true;
            this.txtTstore.MenuManager = this.barManager1;
            this.txtTstore.Name = "txtTstore";
            this.txtTstore.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTstore.Properties.Appearance.BackColor")));
            this.txtTstore.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTstore.Properties.Appearance.ForeColor")));
            this.txtTstore.Properties.Appearance.Options.UseBackColor = true;
            this.txtTstore.Properties.Appearance.Options.UseForeColor = true;
            this.txtTstore.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTstore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTstore.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTstore.Properties.MaxLength = 190;
            this.txtTstore.TabStop = false;
            // 
            // lkpStore
            // 
            resources.ApplyResources(this.lkpStore, "lkpStore");
            this.lkpStore.EnterMoveNextControl = true;
            this.lkpStore.MenuManager = this.barManager1;
            this.lkpStore.Name = "lkpStore";
            this.lkpStore.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpStore.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpStore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpStore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpStore.Properties.Buttons"))))});
            this.lkpStore.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns"), resources.GetString("lkpStore.Properties.Columns1"), ((int)(resources.GetObject("lkpStore.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns3"))), resources.GetString("lkpStore.Properties.Columns4"), ((bool)(resources.GetObject("lkpStore.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns7"), resources.GetString("lkpStore.Properties.Columns8"), ((int)(resources.GetObject("lkpStore.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns10"))), resources.GetString("lkpStore.Properties.Columns11"), ((bool)(resources.GetObject("lkpStore.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns14"), resources.GetString("lkpStore.Properties.Columns15"), ((int)(resources.GetObject("lkpStore.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns17"))), resources.GetString("lkpStore.Properties.Columns18"), ((bool)(resources.GetObject("lkpStore.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns21"), resources.GetString("lkpStore.Properties.Columns22"), ((int)(resources.GetObject("lkpStore.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns24"))), resources.GetString("lkpStore.Properties.Columns25"), ((bool)(resources.GetObject("lkpStore.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns27")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns28"), resources.GetString("lkpStore.Properties.Columns29"), ((int)(resources.GetObject("lkpStore.Properties.Columns30"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns31"))), resources.GetString("lkpStore.Properties.Columns32"), ((bool)(resources.GetObject("lkpStore.Properties.Columns33"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns34")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns35"), resources.GetString("lkpStore.Properties.Columns36"), ((int)(resources.GetObject("lkpStore.Properties.Columns37"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns38"))), resources.GetString("lkpStore.Properties.Columns39"), ((bool)(resources.GetObject("lkpStore.Properties.Columns40"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns41")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns42"), resources.GetString("lkpStore.Properties.Columns43"), ((int)(resources.GetObject("lkpStore.Properties.Columns44"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns45"))), resources.GetString("lkpStore.Properties.Columns46"), ((bool)(resources.GetObject("lkpStore.Properties.Columns47"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns48"))))});
            this.lkpStore.Properties.NullText = resources.GetString("lkpStore.Properties.NullText");
            this.lkpStore.EditValueChanged += new System.EventHandler(this.lkpStore_EditValueChanged);
            this.lkpStore.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlAgeDate
            // 
            this.pnlAgeDate.Controls.Add(this.txtTdueDate);
            this.pnlAgeDate.Controls.Add(this.txt_DueDays);
            this.pnlAgeDate.Controls.Add(this.txt_DueDate);
            resources.ApplyResources(this.pnlAgeDate, "pnlAgeDate");
            this.pnlAgeDate.Name = "pnlAgeDate";
            // 
            // txtTdueDate
            // 
            resources.ApplyResources(this.txtTdueDate, "txtTdueDate");
            this.txtTdueDate.EnterMoveNextControl = true;
            this.txtTdueDate.MenuManager = this.barManager1;
            this.txtTdueDate.Name = "txtTdueDate";
            this.txtTdueDate.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTdueDate.Properties.Appearance.BackColor")));
            this.txtTdueDate.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTdueDate.Properties.Appearance.ForeColor")));
            this.txtTdueDate.Properties.Appearance.Options.UseBackColor = true;
            this.txtTdueDate.Properties.Appearance.Options.UseForeColor = true;
            this.txtTdueDate.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTdueDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTdueDate.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTdueDate.Properties.MaxLength = 190;
            this.txtTdueDate.TabStop = false;
            // 
            // txt_DueDays
            // 
            resources.ApplyResources(this.txt_DueDays, "txt_DueDays");
            this.txt_DueDays.EnterMoveNextControl = true;
            this.txt_DueDays.MenuManager = this.barManager1;
            this.txt_DueDays.Name = "txt_DueDays";
            this.txt_DueDays.Properties.IsFloatValue = false;
            this.txt_DueDays.Properties.Mask.EditMask = resources.GetString("txt_DueDays.Properties.Mask.EditMask");
            this.txt_DueDays.Leave += new System.EventHandler(this.txt_DueDays_Leave);
            // 
            // txt_DueDate
            // 
            resources.ApplyResources(this.txt_DueDate, "txt_DueDate");
            this.txt_DueDate.EnterMoveNextControl = true;
            this.txt_DueDate.MenuManager = this.barManager1;
            this.txt_DueDate.Name = "txt_DueDate";
            this.txt_DueDate.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DueDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DueDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_DueDate.Properties.Buttons"))))});
            this.txt_DueDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_DueDate.EditValueChanged += new System.EventHandler(this.txt_DueDate_EditValueChanged);
            // 
            // pnlDeliveryDate
            // 
            this.pnlDeliveryDate.Controls.Add(this.txtTdeliverDate);
            this.pnlDeliveryDate.Controls.Add(this.dtDeliverDate);
            resources.ApplyResources(this.pnlDeliveryDate, "pnlDeliveryDate");
            this.pnlDeliveryDate.Name = "pnlDeliveryDate";
            // 
            // txtTdeliverDate
            // 
            resources.ApplyResources(this.txtTdeliverDate, "txtTdeliverDate");
            this.txtTdeliverDate.EnterMoveNextControl = true;
            this.txtTdeliverDate.MenuManager = this.barManager1;
            this.txtTdeliverDate.Name = "txtTdeliverDate";
            this.txtTdeliverDate.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTdeliverDate.Properties.Appearance.BackColor")));
            this.txtTdeliverDate.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTdeliverDate.Properties.Appearance.ForeColor")));
            this.txtTdeliverDate.Properties.Appearance.Options.UseBackColor = true;
            this.txtTdeliverDate.Properties.Appearance.Options.UseForeColor = true;
            this.txtTdeliverDate.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTdeliverDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTdeliverDate.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTdeliverDate.Properties.MaxLength = 190;
            this.txtTdeliverDate.TabStop = false;
            // 
            // dtDeliverDate
            // 
            resources.ApplyResources(this.dtDeliverDate, "dtDeliverDate");
            this.dtDeliverDate.EnterMoveNextControl = true;
            this.dtDeliverDate.MenuManager = this.barManager1;
            this.dtDeliverDate.Name = "dtDeliverDate";
            this.dtDeliverDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dtDeliverDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtDeliverDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtDeliverDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtDeliverDate.Properties.Buttons"))))});
            this.dtDeliverDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtDeliverDate.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlCurrency
            // 
            this.pnlCurrency.Controls.Add(this.txtCurrency);
            resources.ApplyResources(this.pnlCurrency, "pnlCurrency");
            this.pnlCurrency.Name = "pnlCurrency";
            // 
            // txtCurrency
            // 
            resources.ApplyResources(this.txtCurrency, "txtCurrency");
            this.txtCurrency.EnterMoveNextControl = true;
            this.txtCurrency.MenuManager = this.barManager1;
            this.txtCurrency.Name = "txtCurrency";
            this.txtCurrency.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtCurrency.Properties.Appearance.BackColor")));
            this.txtCurrency.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtCurrency.Properties.Appearance.ForeColor")));
            this.txtCurrency.Properties.Appearance.Options.UseBackColor = true;
            this.txtCurrency.Properties.Appearance.Options.UseForeColor = true;
            this.txtCurrency.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCurrency.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCurrency.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtCurrency.Properties.MaxLength = 190;
            this.txtCurrency.TabStop = false;
            // 
            // pnlPostStore
            // 
            this.pnlPostStore.Controls.Add(this.txt_Post_Date);
            this.pnlPostStore.Controls.Add(this.chk_IsPosted);
            resources.ApplyResources(this.pnlPostStore, "pnlPostStore");
            this.pnlPostStore.Name = "pnlPostStore";
            // 
            // txt_Post_Date
            // 
            resources.ApplyResources(this.txt_Post_Date, "txt_Post_Date");
            this.txt_Post_Date.EnterMoveNextControl = true;
            this.txt_Post_Date.MenuManager = this.barManager1;
            this.txt_Post_Date.Name = "txt_Post_Date";
            this.txt_Post_Date.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txt_Post_Date.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Post_Date.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Post_Date.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_Post_Date.Properties.Buttons"))))});
            this.txt_Post_Date.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Post_Date.Properties.DisplayFormat.FormatString = "g";
            this.txt_Post_Date.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.txt_Post_Date.Properties.EditFormat.FormatString = "g";
            this.txt_Post_Date.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.txt_Post_Date.Properties.Mask.EditMask = resources.GetString("txt_Post_Date.Properties.Mask.EditMask");
            this.txt_Post_Date.TabStop = false;
            // 
            // chk_IsPosted
            // 
            resources.ApplyResources(this.chk_IsPosted, "chk_IsPosted");
            this.chk_IsPosted.MenuManager = this.barManager1;
            this.chk_IsPosted.Name = "chk_IsPosted";
            this.chk_IsPosted.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsPosted.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsPosted.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.chk_IsPosted.Properties.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.chk_IsPosted.Properties.Caption = resources.GetString("chk_IsPosted.Properties.Caption");
            this.chk_IsPosted.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsPosted.Properties.GlyphAlignment")));
            this.chk_IsPosted.TabStop = false;
            this.chk_IsPosted.EditValueChanged += new System.EventHandler(this.chk_IsPosted_EditValueChanged);
            // 
            // pnlPO
            // 
            this.pnlPO.Controls.Add(this.txtTpo);
            this.pnlPO.Controls.Add(this.txt_PO_No);
            resources.ApplyResources(this.pnlPO, "pnlPO");
            this.pnlPO.Name = "pnlPO";
            // 
            // txtTpo
            // 
            resources.ApplyResources(this.txtTpo, "txtTpo");
            this.txtTpo.EnterMoveNextControl = true;
            this.txtTpo.MenuManager = this.barManager1;
            this.txtTpo.Name = "txtTpo";
            this.txtTpo.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTpo.Properties.Appearance.BackColor")));
            this.txtTpo.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTpo.Properties.Appearance.ForeColor")));
            this.txtTpo.Properties.Appearance.Options.UseBackColor = true;
            this.txtTpo.Properties.Appearance.Options.UseForeColor = true;
            this.txtTpo.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTpo.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTpo.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTpo.Properties.MaxLength = 190;
            this.txtTpo.TabStop = false;
            // 
            // txt_PO_No
            // 
            resources.ApplyResources(this.txt_PO_No, "txt_PO_No");
            this.txt_PO_No.EnterMoveNextControl = true;
            this.txt_PO_No.Name = "txt_PO_No";
            this.txt_PO_No.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_PO_No.Properties.Appearance.BackColor")));
            this.txt_PO_No.Properties.Appearance.Options.UseBackColor = true;
            this.txt_PO_No.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_PO_No.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_PO_No.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlSalesEmp
            // 
            this.pnlSalesEmp.Controls.Add(this.txtTSalesEmp);
            this.pnlSalesEmp.Controls.Add(this.lkp_SalesEmp);
            resources.ApplyResources(this.pnlSalesEmp, "pnlSalesEmp");
            this.pnlSalesEmp.Name = "pnlSalesEmp";
            // 
            // txtTSalesEmp
            // 
            resources.ApplyResources(this.txtTSalesEmp, "txtTSalesEmp");
            this.txtTSalesEmp.EnterMoveNextControl = true;
            this.txtTSalesEmp.MenuManager = this.barManager1;
            this.txtTSalesEmp.Name = "txtTSalesEmp";
            this.txtTSalesEmp.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTSalesEmp.Properties.Appearance.BackColor")));
            this.txtTSalesEmp.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTSalesEmp.Properties.Appearance.ForeColor")));
            this.txtTSalesEmp.Properties.Appearance.Options.UseBackColor = true;
            this.txtTSalesEmp.Properties.Appearance.Options.UseForeColor = true;
            this.txtTSalesEmp.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTSalesEmp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTSalesEmp.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTSalesEmp.Properties.MaxLength = 190;
            this.txtTSalesEmp.TabStop = false;
            // 
            // lkp_SalesEmp
            // 
            resources.ApplyResources(this.lkp_SalesEmp, "lkp_SalesEmp");
            this.lkp_SalesEmp.EnterMoveNextControl = true;
            this.lkp_SalesEmp.MenuManager = this.barManager1;
            this.lkp_SalesEmp.Name = "lkp_SalesEmp";
            this.lkp_SalesEmp.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_SalesEmp.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_SalesEmp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_SalesEmp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_SalesEmp.Properties.Buttons"))))});
            this.lkp_SalesEmp.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns"), resources.GetString("lkp_SalesEmp.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns2"), resources.GetString("lkp_SalesEmp.Properties.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns4"), resources.GetString("lkp_SalesEmp.Properties.Columns5"), ((int)(resources.GetObject("lkp_SalesEmp.Properties.Columns6"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SalesEmp.Properties.Columns7"))), resources.GetString("lkp_SalesEmp.Properties.Columns8"), ((bool)(resources.GetObject("lkp_SalesEmp.Properties.Columns9"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SalesEmp.Properties.Columns10"))))});
            this.lkp_SalesEmp.Properties.DisplayMember = "EmpName";
            this.lkp_SalesEmp.Properties.NullText = resources.GetString("lkp_SalesEmp.Properties.NullText");
            this.lkp_SalesEmp.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkp_SalesEmp.Properties.ValueMember = "EmpId";
            this.lkp_SalesEmp.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlCostCenter
            // 
            this.pnlCostCenter.Controls.Add(this.lkpCostCenter);
            this.pnlCostCenter.Controls.Add(this.textEdit9);
            resources.ApplyResources(this.pnlCostCenter, "pnlCostCenter");
            this.pnlCostCenter.Name = "pnlCostCenter";
            // 
            // lkpCostCenter
            // 
            resources.ApplyResources(this.lkpCostCenter, "lkpCostCenter");
            this.lkpCostCenter.EnterMoveNextControl = true;
            this.lkpCostCenter.MenuManager = this.barManager1;
            this.lkpCostCenter.Name = "lkpCostCenter";
            this.lkpCostCenter.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpCostCenter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCostCenter.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpCostCenter.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpCostCenter.Properties.Buttons"))))});
            this.lkpCostCenter.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCostCenter.Properties.Columns"), resources.GetString("lkpCostCenter.Properties.Columns1"), ((int)(resources.GetObject("lkpCostCenter.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCostCenter.Properties.Columns3"))), resources.GetString("lkpCostCenter.Properties.Columns4"), ((bool)(resources.GetObject("lkpCostCenter.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCostCenter.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCostCenter.Properties.Columns7"), resources.GetString("lkpCostCenter.Properties.Columns8"), ((int)(resources.GetObject("lkpCostCenter.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCostCenter.Properties.Columns10"))), resources.GetString("lkpCostCenter.Properties.Columns11"), ((bool)(resources.GetObject("lkpCostCenter.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCostCenter.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCostCenter.Properties.Columns14"), resources.GetString("lkpCostCenter.Properties.Columns15"), ((int)(resources.GetObject("lkpCostCenter.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCostCenter.Properties.Columns17"))), resources.GetString("lkpCostCenter.Properties.Columns18"), ((bool)(resources.GetObject("lkpCostCenter.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCostCenter.Properties.Columns20"))))});
            this.lkpCostCenter.Properties.NullText = resources.GetString("lkpCostCenter.Properties.NullText");
            // 
            // textEdit9
            // 
            resources.ApplyResources(this.textEdit9, "textEdit9");
            this.textEdit9.EnterMoveNextControl = true;
            this.textEdit9.MenuManager = this.barManager1;
            this.textEdit9.Name = "textEdit9";
            this.textEdit9.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit9.Properties.Appearance.BackColor")));
            this.textEdit9.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit9.Properties.Appearance.ForeColor")));
            this.textEdit9.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit9.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit9.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit9.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit9.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit9.Properties.MaxLength = 190;
            this.textEdit9.TabStop = false;
            // 
            // pnlSrcPrc
            // 
            resources.ApplyResources(this.pnlSrcPrc, "pnlSrcPrc");
            this.pnlSrcPrc.Name = "pnlSrcPrc";
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // lblShipTo
            // 
            resources.ApplyResources(this.lblShipTo, "lblShipTo");
            this.lblShipTo.Name = "lblShipTo";
            // 
            // txtNotes
            // 
            resources.ApplyResources(this.txtNotes, "txtNotes");
            this.txtNotes.MenuManager = this.barManager1;
            this.txtNotes.Name = "txtNotes";
            this.txtNotes.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtNotes.Properties.Appearance.BackColor")));
            this.txtNotes.Properties.Appearance.Options.UseBackColor = true;
            this.txtNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.txtNotes.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txt_Shipping
            // 
            resources.ApplyResources(this.txt_Shipping, "txt_Shipping");
            this.txt_Shipping.Name = "txt_Shipping";
            this.txt_Shipping.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_Shipping.Properties.Appearance.BackColor")));
            this.txt_Shipping.Properties.Appearance.Options.UseBackColor = true;
            this.txt_Shipping.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txtSourceCode
            // 
            resources.ApplyResources(this.txtSourceCode, "txtSourceCode");
            this.txtSourceCode.Name = "txtSourceCode";
            // 
            // btnSourceId
            // 
            resources.ApplyResources(this.btnSourceId, "btnSourceId");
            this.btnSourceId.Name = "btnSourceId";
            // 
            // cmdProcess
            // 
            resources.ApplyResources(this.cmdProcess, "cmdProcess");
            this.cmdProcess.Name = "cmdProcess";
            // 
            // chk_IsOutTrns
            // 
            resources.ApplyResources(this.chk_IsOutTrns, "chk_IsOutTrns");
            this.chk_IsOutTrns.Name = "chk_IsOutTrns";
            this.chk_IsOutTrns.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsOutTrns.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsOutTrns.Properties.AppearanceReadOnly.ForeColor = ((System.Drawing.Color)(resources.GetObject("chk_IsOutTrns.Properties.AppearanceReadOnly.ForeColor")));
            this.chk_IsOutTrns.Properties.AppearanceReadOnly.Options.UseForeColor = true;
            this.chk_IsOutTrns.Properties.Caption = resources.GetString("chk_IsOutTrns.Properties.Caption");
            this.chk_IsOutTrns.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsOutTrns.Properties.GlyphAlignment")));
            this.chk_IsOutTrns.Properties.ReadOnly = true;
            this.chk_IsOutTrns.TabStop = false;
            // 
            // cmbPayMethod
            // 
            resources.ApplyResources(this.cmbPayMethod, "cmbPayMethod");
            this.cmbPayMethod.EnterMoveNextControl = true;
            this.cmbPayMethod.MenuManager = this.barManager1;
            this.cmbPayMethod.Name = "cmbPayMethod";
            this.cmbPayMethod.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbPayMethod.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.Appearance.BackColor")));
            this.cmbPayMethod.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("cmbPayMethod.Properties.Appearance.Font")));
            this.cmbPayMethod.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.Appearance.ForeColor")));
            this.cmbPayMethod.Properties.Appearance.Options.UseBackColor = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseFont = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseForeColor = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbPayMethod.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.cmbPayMethod.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.BackColor")));
            this.cmbPayMethod.Properties.AppearanceDisabled.Font = ((System.Drawing.Font)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.Font")));
            this.cmbPayMethod.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.ForeColor")));
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseFont = true;
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.cmbPayMethod.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.cmbPayMethod.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbPayMethod.Properties.Buttons"))), resources.GetString("cmbPayMethod.Properties.Buttons1"), ((int)(resources.GetObject("cmbPayMethod.Properties.Buttons2"))), ((bool)(resources.GetObject("cmbPayMethod.Properties.Buttons3"))), ((bool)(resources.GetObject("cmbPayMethod.Properties.Buttons4"))), ((bool)(resources.GetObject("cmbPayMethod.Properties.Buttons5"))), ((DevExpress.XtraEditors.ImageLocation)(resources.GetObject("cmbPayMethod.Properties.Buttons6"))), ((System.Drawing.Image)(resources.GetObject("cmbPayMethod.Properties.Buttons7"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, resources.GetString("cmbPayMethod.Properties.Buttons8"), ((object)(resources.GetObject("cmbPayMethod.Properties.Buttons9"))), ((DevExpress.Utils.SuperToolTip)(resources.GetObject("cmbPayMethod.Properties.Buttons10"))), ((bool)(resources.GetObject("cmbPayMethod.Properties.Buttons11"))))});
            this.cmbPayMethod.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items1"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items3"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items4"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items6"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items7"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items8"))))});
            // 
            // lkp_Drawers
            // 
            this.lkp_Drawers.EnterMoveNextControl = true;
            resources.ApplyResources(this.lkp_Drawers, "lkp_Drawers");
            this.lkp_Drawers.MenuManager = this.barManager1;
            this.lkp_Drawers.Name = "lkp_Drawers";
            this.lkp_Drawers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Drawers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Drawers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Drawers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Drawers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Drawers.Properties.Buttons"))))});
            this.lkp_Drawers.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns"), resources.GetString("lkp_Drawers.Properties.Columns1"), ((int)(resources.GetObject("lkp_Drawers.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Drawers.Properties.Columns3"))), resources.GetString("lkp_Drawers.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Drawers.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Drawers.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns7"), resources.GetString("lkp_Drawers.Properties.Columns8"))});
            this.lkp_Drawers.Properties.DisplayMember = "AccountName";
            this.lkp_Drawers.Properties.NullText = resources.GetString("lkp_Drawers.Properties.NullText");
            this.lkp_Drawers.Properties.ValueMember = "AccountId";
            this.lkp_Drawers.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Name = "labelControl17";
            // 
            // lbl_remains
            // 
            resources.ApplyResources(this.lbl_remains, "lbl_remains");
            this.lbl_remains.Name = "lbl_remains";
            // 
            // txt_Remains
            // 
            resources.ApplyResources(this.txt_Remains, "txt_Remains");
            this.txt_Remains.EnterMoveNextControl = true;
            this.txt_Remains.Name = "txt_Remains";
            this.txt_Remains.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_Remains.Properties.Appearance.BackColor")));
            this.txt_Remains.Properties.Appearance.Options.UseBackColor = true;
            this.txt_Remains.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Remains.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Remains.Properties.Mask.EditMask = resources.GetString("txt_Remains.Properties.Mask.EditMask");
            this.txt_Remains.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Remains.Properties.Mask.MaskType")));
            this.txt_Remains.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_Remains.EditValueChanged += new System.EventHandler(this.txt_paid_EditValueChanged);
            this.txt_Remains.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_Remains.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // lbl_Paid
            // 
            resources.ApplyResources(this.lbl_Paid, "lbl_Paid");
            this.lbl_Paid.Name = "lbl_Paid";
            // 
            // txt_paid
            // 
            resources.ApplyResources(this.txt_paid, "txt_paid");
            this.txt_paid.EnterMoveNextControl = true;
            this.txt_paid.Name = "txt_paid";
            this.txt_paid.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_paid.Properties.Appearance.BackColor")));
            this.txt_paid.Properties.Appearance.Options.UseBackColor = true;
            this.txt_paid.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_paid.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_paid.Properties.Mask.EditMask = resources.GetString("txt_paid.Properties.Mask.EditMask");
            this.txt_paid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_paid.Properties.Mask.MaskType")));
            this.txt_paid.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_paid.EditValueChanged += new System.EventHandler(this.txt_paid_EditValueChanged);
            this.txt_paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_paid.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Name = "labelControl7";
            // 
            // lkp_Customers
            // 
            resources.ApplyResources(this.lkp_Customers, "lkp_Customers");
            this.lkp_Customers.EnterMoveNextControl = true;
            this.lkp_Customers.MenuManager = this.barManager1;
            this.lkp_Customers.Name = "lkp_Customers";
            this.lkp_Customers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Customers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Customers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Customers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Customers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Customers.Properties.Buttons"))))});
            this.lkp_Customers.Properties.ImmediatePopup = true;
            this.lkp_Customers.Properties.NullText = resources.GetString("lkp_Customers.Properties.NullText");
            this.lkp_Customers.Properties.View = this.gridView1;
            this.lkp_Customers.EditValueChanged += new System.EventHandler(this.lkp_Customers_EditValueChanged);
            this.lkp_Customers.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn30});
            this.gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.EnableAppearanceOddRow = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // gridColumn19
            // 
            resources.ApplyResources(this.gridColumn19, "gridColumn19");
            this.gridColumn19.FieldName = "CustomerId";
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            resources.ApplyResources(this.gridColumn20, "gridColumn20");
            this.gridColumn20.FieldName = "CusCode";
            this.gridColumn20.Name = "gridColumn20";
            // 
            // gridColumn21
            // 
            resources.ApplyResources(this.gridColumn21, "gridColumn21");
            this.gridColumn21.FieldName = "CusNameAr";
            this.gridColumn21.Name = "gridColumn21";
            // 
            // gridColumn22
            // 
            resources.ApplyResources(this.gridColumn22, "gridColumn22");
            this.gridColumn22.FieldName = "CusNameEn";
            this.gridColumn22.Name = "gridColumn22";
            // 
            // gridColumn26
            // 
            resources.ApplyResources(this.gridColumn26, "gridColumn26");
            this.gridColumn26.FieldName = "GroupId";
            this.gridColumn26.Name = "gridColumn26";
            // 
            // gridColumn27
            // 
            resources.ApplyResources(this.gridColumn27, "gridColumn27");
            this.gridColumn27.FieldName = "City";
            this.gridColumn27.Name = "gridColumn27";
            // 
            // gridColumn30
            // 
            resources.ApplyResources(this.gridColumn30, "gridColumn30");
            this.gridColumn30.FieldName = "Mobile";
            this.gridColumn30.Name = "gridColumn30";
            // 
            // panelControl2
            // 
            resources.ApplyResources(this.panelControl2, "panelControl2");
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl2.Controls.Add(this.grdPrInvoice);
            this.panelControl2.Name = "panelControl2";
            // 
            // grdPrInvoice
            // 
            this.grdPrInvoice.ContextMenuStrip = this.contextMenuStrip1;
            this.grdPrInvoice.Cursor = System.Windows.Forms.Cursors.Default;
            resources.ApplyResources(this.grdPrInvoice, "grdPrInvoice");
            this.grdPrInvoice.MainView = this.gridView2;
            this.grdPrInvoice.Name = "grdPrInvoice";
            this.grdPrInvoice.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpin,
            this.repItems,
            this.repUOM,
            this.repDiscountRatio,
            this.rep_expireDate,
            this.rep_Batch,
            this.lkp_store,
            this.lkp_storee,
            this.repManufactureDate});
            this.grdPrInvoice.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            this.grdPrInvoice.Click += new System.EventHandler(this.grdPrInvoice_Click);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_frm_IC_Item,
            this.mi_CustLastPrices,
            this.mi_LastPrices,
            this.mi_PasteRows,
            this.mi_ExportData,
            this.mi_InvoiceStaticDisc,
            this.mi_InvoiceStaticDimensions,
            this.mi_ImportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            this.contextMenuStrip1.Opened += new System.EventHandler(this.contextMenuStrip1_Opened);
            // 
            // mi_frm_IC_Item
            // 
            this.mi_frm_IC_Item.Name = "mi_frm_IC_Item";
            resources.ApplyResources(this.mi_frm_IC_Item, "mi_frm_IC_Item");
            this.mi_frm_IC_Item.Click += new System.EventHandler(this.mi_frm_IC_Item_Click);
            // 
            // mi_CustLastPrices
            // 
            this.mi_CustLastPrices.Name = "mi_CustLastPrices";
            resources.ApplyResources(this.mi_CustLastPrices, "mi_CustLastPrices");
            this.mi_CustLastPrices.Click += new System.EventHandler(this.mi_CustLastPrices_Click);
            // 
            // mi_LastPrices
            // 
            this.mi_LastPrices.Name = "mi_LastPrices";
            resources.ApplyResources(this.mi_LastPrices, "mi_LastPrices");
            this.mi_LastPrices.Click += new System.EventHandler(this.mi_LastPrices_Click);
            // 
            // mi_PasteRows
            // 
            this.mi_PasteRows.Name = "mi_PasteRows";
            resources.ApplyResources(this.mi_PasteRows, "mi_PasteRows");
            this.mi_PasteRows.Click += new System.EventHandler(this.mi_PasteRows_Click);
            // 
            // mi_ExportData
            // 
            this.mi_ExportData.Name = "mi_ExportData";
            resources.ApplyResources(this.mi_ExportData, "mi_ExportData");
            this.mi_ExportData.Click += new System.EventHandler(this.mi_ExportData_Click);
            // 
            // mi_InvoiceStaticDisc
            // 
            this.mi_InvoiceStaticDisc.Name = "mi_InvoiceStaticDisc";
            resources.ApplyResources(this.mi_InvoiceStaticDisc, "mi_InvoiceStaticDisc");
            this.mi_InvoiceStaticDisc.Click += new System.EventHandler(this.mi_InvoiceStaticDisc_Click);
            // 
            // mi_InvoiceStaticDimensions
            // 
            this.mi_InvoiceStaticDimensions.Name = "mi_InvoiceStaticDimensions";
            resources.ApplyResources(this.mi_InvoiceStaticDimensions, "mi_InvoiceStaticDimensions");
            this.mi_InvoiceStaticDimensions.Click += new System.EventHandler(this.mi_InvoiceStaticDimensions_Click);
            // 
            // mi_ImportExcel
            // 
            this.mi_ImportExcel.Name = "mi_ImportExcel";
            resources.ApplyResources(this.mi_ImportExcel, "mi_ImportExcel");
            this.mi_ImportExcel.Click += new System.EventHandler(this.mi_ImportExcel_Click);
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView2.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView2.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView2.ColumnPanelRowHeight = 33;
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn29,
            this.gridColumn28,
            this.col_TotalSellPrice,
            this.gridColumn2,
            this.gridColumn1,
            this.col_SellPrice,
            this.colPurchasePrice,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn10,
            this.grdcol_branch,
            this.gridColumn11,
            this.gridColumn31,
            this.gridColumn41,
            this.col_Expire,
            this.col_Batch,
            this.col_Length,
            this.col_Width,
            this.col_Height,
            this.col_TotalQty,
            this.col_PiecesCount,
            this.col_ItemDescription,
            this.col_ItemDescriptionEn,
            this.col_SalesTax,
            this.col_DiscountRatio2,
            this.col_DiscountRatio3,
            this.col_Serial,
            this.gridColumn32,
            this.col_CusTax});
            this.gridView2.CustomizationFormBounds = new System.Drawing.Rectangle(982, 107, 216, 388);
            this.gridView2.GridControl = this.grdPrInvoice;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView2.OptionsView.EnableAppearanceOddRow = true;
            this.gridView2.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView2.OptionsView.RowAutoHeight = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.RowStyle += new DevExpress.XtraGrid.Views.Grid.RowStyleEventHandler(this.gridView2_RowStyle);
            this.gridView2.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.gridView2_ShowingEditor);
            this.gridView2.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridView1_FocusedRowChanged);
            this.gridView2.FocusedColumnChanged += new DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventHandler(this.gridView1_FocusedColumnChanged);
            this.gridView2.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gridView1_CellValueChanged);
            this.gridView2.InvalidRowException += new DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventHandler(this.gridView1_InvalidRowException);
            this.gridView2.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gridView1_ValidateRow);
            this.gridView2.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView2_CustomUnboundColumnData);
            this.gridView2.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView1_CustomColumnDisplayText);
            this.gridView2.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gridView1_KeyDown);
            // 
            // gridColumn29
            // 
            this.gridColumn29.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn29.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn29, "gridColumn29");
            this.gridColumn29.FieldName = "LargeUOMFactor";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.OptionsColumn.AllowEdit = false;
            this.gridColumn29.OptionsColumn.AllowFocus = false;
            this.gridColumn29.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn29.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn29.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn28
            // 
            this.gridColumn28.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn28.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn28, "gridColumn28");
            this.gridColumn28.FieldName = "MediumUOMFactor";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.OptionsColumn.AllowEdit = false;
            this.gridColumn28.OptionsColumn.AllowFocus = false;
            this.gridColumn28.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn28.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn28.OptionsFilter.AllowFilter = false;
            // 
            // col_TotalSellPrice
            // 
            this.col_TotalSellPrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_TotalSellPrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_TotalSellPrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_TotalSellPrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_TotalSellPrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_TotalSellPrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_TotalSellPrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_TotalSellPrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_TotalSellPrice, "col_TotalSellPrice");
            this.col_TotalSellPrice.FieldName = "TotalSellPrice";
            this.col_TotalSellPrice.Name = "col_TotalSellPrice";
            this.col_TotalSellPrice.OptionsColumn.AllowEdit = false;
            this.col_TotalSellPrice.OptionsColumn.AllowFocus = false;
            this.col_TotalSellPrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalSellPrice.OptionsColumn.ReadOnly = true;
            this.col_TotalSellPrice.OptionsFilter.AllowFilter = false;
            this.col_TotalSellPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSellPrice.Summary"))))});
            // 
            // gridColumn2
            // 
            this.gridColumn2.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.ColumnEdit = this.repSpin;
            this.gridColumn2.FieldName = "DiscountValue";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn2.OptionsFilter.AllowFilter = false;
            // 
            // repSpin
            // 
            resources.ApplyResources(this.repSpin, "repSpin");
            this.repSpin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repSpin.Name = "repSpin";
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.ColumnEdit = this.repDiscountRatio;
            this.gridColumn1.FieldName = "DiscountRatio";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn1.OptionsFilter.AllowFilter = false;
            // 
            // repDiscountRatio
            // 
            resources.ApplyResources(this.repDiscountRatio, "repDiscountRatio");
            this.repDiscountRatio.Mask.EditMask = resources.GetString("repDiscountRatio.Mask.EditMask");
            this.repDiscountRatio.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repDiscountRatio.Mask.MaskType")));
            this.repDiscountRatio.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repDiscountRatio.Mask.UseMaskAsDisplayFormat")));
            this.repDiscountRatio.Name = "repDiscountRatio";
            // 
            // col_SellPrice
            // 
            this.col_SellPrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_SellPrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SellPrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SellPrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SellPrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_SellPrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SellPrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SellPrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SellPrice.ColumnEdit = this.repSpin;
            this.col_SellPrice.FieldName = "SellPrice";
            this.col_SellPrice.Name = "col_SellPrice";
            this.col_SellPrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_SellPrice.OptionsFilter.AllowFilter = false;
            resources.ApplyResources(this.col_SellPrice, "col_SellPrice");
            // 
            // colPurchasePrice
            // 
            this.colPurchasePrice.AppearanceCell.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colPurchasePrice.AppearanceHeader.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colPurchasePrice, "colPurchasePrice");
            this.colPurchasePrice.ColumnEdit = this.repSpin;
            this.colPurchasePrice.FieldName = "PurchasePrice";
            this.colPurchasePrice.Name = "colPurchasePrice";
            this.colPurchasePrice.OptionsColumn.AllowEdit = false;
            this.colPurchasePrice.OptionsColumn.AllowFocus = false;
            this.colPurchasePrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colPurchasePrice.OptionsColumn.ReadOnly = true;
            this.colPurchasePrice.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.ColumnEdit = this.repSpin;
            this.gridColumn7.FieldName = "Qty";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn7.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.ColumnEdit = this.repUOM;
            this.gridColumn8.FieldName = "UOM";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn8.OptionsFilter.AllowFilter = false;
            // 
            // repUOM
            // 
            this.repUOM.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repUOM.Buttons"))))});
            this.repUOM.Name = "repUOM";
            resources.ApplyResources(this.repUOM, "repUOM");
            this.repUOM.View = this.gridView4;
            this.repUOM.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.repUOM_CustomDisplayText);
            // 
            // gridView4
            // 
            this.gridView4.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView4.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView4.Appearance.Row.Options.UseTextOptions = true;
            this.gridView4.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn3});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowDetailButtons = false;
            this.gridView4.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            this.gridView4.OptionsView.ShowIndicator = false;
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "Index";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn16
            // 
            resources.ApplyResources(this.gridColumn16, "gridColumn16");
            this.gridColumn16.FieldName = "Factor";
            this.gridColumn16.Name = "gridColumn16";
            // 
            // gridColumn17
            // 
            resources.ApplyResources(this.gridColumn17, "gridColumn17");
            this.gridColumn17.FieldName = "Uom";
            this.gridColumn17.Name = "gridColumn17";
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "UomId";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.ColumnEdit = this.repItems;
            this.gridColumn10.FieldName = "ItemId";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn10.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn10.OptionsFilter.AllowFilter = false;
            // 
            // repItems
            // 
            this.repItems.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repItems.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.repItems.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repItems.Buttons"))))});
            this.repItems.DisplayMember = "ItemNameAr";
            this.repItems.ImmediatePopup = true;
            this.repItems.Name = "repItems";
            resources.ApplyResources(this.repItems, "repItems");
            this.repItems.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.repItems.ValueMember = "ItemId";
            this.repItems.View = this.repositoryItemGridLookUpEdit1View;
            this.repItems.Popup += new System.EventHandler(this.repItems_Popup);
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn5,
            this.gridColumn4,
            this.gridColumn25,
            this.col_CompanyNameAr,
            this.col_CategoryNameAr});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoSelectAllInEditor = false;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.UseIndicatorForSelection = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.BestFitMaxRowCount = 10;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // gridColumn12
            // 
            resources.ApplyResources(this.gridColumn12, "gridColumn12");
            this.gridColumn12.FieldName = "ItemNameEn";
            this.gridColumn12.Name = "gridColumn12";
            // 
            // gridColumn13
            // 
            resources.ApplyResources(this.gridColumn13, "gridColumn13");
            this.gridColumn13.FieldName = "ItemNameAr";
            this.gridColumn13.Name = "gridColumn13";
            // 
            // gridColumn14
            // 
            resources.ApplyResources(this.gridColumn14, "gridColumn14");
            this.gridColumn14.FieldName = "ItemCode1";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.FieldName = "ItemId";
            this.gridColumn15.Name = "gridColumn15";
            // 
            // gridColumn5
            // 
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "ItemCode2";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "Description";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // gridColumn25
            // 
            resources.ApplyResources(this.gridColumn25, "gridColumn25");
            this.gridColumn25.DisplayFormat.FormatString = "n2";
            this.gridColumn25.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn25.FieldName = "SellPrice";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_CompanyNameAr
            // 
            resources.ApplyResources(this.col_CompanyNameAr, "col_CompanyNameAr");
            this.col_CompanyNameAr.FieldName = "CompanyNameAr";
            this.col_CompanyNameAr.Name = "col_CompanyNameAr";
            // 
            // col_CategoryNameAr
            // 
            resources.ApplyResources(this.col_CategoryNameAr, "col_CategoryNameAr");
            this.col_CategoryNameAr.FieldName = "CategoryNameAr";
            this.col_CategoryNameAr.Name = "col_CategoryNameAr";
            // 
            // grdcol_branch
            // 
            resources.ApplyResources(this.grdcol_branch, "grdcol_branch");
            this.grdcol_branch.ColumnEdit = this.lkp_storee;
            this.grdcol_branch.FieldName = "StoreId";
            this.grdcol_branch.Name = "grdcol_branch";
            // 
            // lkp_storee
            // 
            this.lkp_storee.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_storee.Buttons"))))});
            this.lkp_storee.DisplayMember = "StoreNameAr";
            this.lkp_storee.Name = "lkp_storee";
            resources.ApplyResources(this.lkp_storee, "lkp_storee");
            this.lkp_storee.ValueMember = "StoreId";
            this.lkp_storee.View = this.gridView7;
            // 
            // gridView7
            // 
            this.gridView7.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView7.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView7.OptionsBehavior.AutoSelectAllInEditor = false;
            this.gridView7.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.gridView7.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn11
            // 
            this.gridColumn11.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.FieldName = "ItemCode2";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn11.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn31
            // 
            this.gridColumn31.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn31, "gridColumn31");
            this.gridColumn31.FieldName = "ItemCode1";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn31.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn41
            // 
            resources.ApplyResources(this.gridColumn41, "gridColumn41");
            this.gridColumn41.FieldName = "UomIndex";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.OptionsColumn.AllowEdit = false;
            this.gridColumn41.OptionsColumn.AllowFocus = false;
            this.gridColumn41.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn41.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn41.OptionsFilter.AllowFilter = false;
            // 
            // col_Expire
            // 
            this.col_Expire.AppearanceCell.Options.UseTextOptions = true;
            this.col_Expire.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Expire.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expire.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Expire.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Expire.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Expire.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expire.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Expire, "col_Expire");
            this.col_Expire.ColumnEdit = this.rep_expireDate;
            this.col_Expire.FieldName = "Expire";
            this.col_Expire.Name = "col_Expire";
            this.col_Expire.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Expire.OptionsFilter.AllowFilter = false;
            // 
            // rep_expireDate
            // 
            resources.ApplyResources(this.rep_expireDate, "rep_expireDate");
            this.rep_expireDate.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.rep_expireDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_expireDate.Buttons"))))});
            this.rep_expireDate.Name = "rep_expireDate";
            this.rep_expireDate.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_expireDate.View = this.gridView5;
            this.rep_expireDate.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.rep_expireDate_CustomDisplayText);
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn18,
            this.gridColumn23,
            this.gridColumn24});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView5_CustomColumnDisplayText);
            // 
            // gridColumn18
            // 
            resources.ApplyResources(this.gridColumn18, "gridColumn18");
            this.gridColumn18.FieldName = "Expire";
            this.gridColumn18.Name = "gridColumn18";
            // 
            // gridColumn23
            // 
            resources.ApplyResources(this.gridColumn23, "gridColumn23");
            this.gridColumn23.FieldName = "Batch";
            this.gridColumn23.Name = "gridColumn23";
            // 
            // gridColumn24
            // 
            resources.ApplyResources(this.gridColumn24, "gridColumn24");
            this.gridColumn24.FieldName = "Qty";
            this.gridColumn24.Name = "gridColumn24";
            // 
            // col_Batch
            // 
            this.col_Batch.AppearanceCell.Options.UseTextOptions = true;
            this.col_Batch.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Batch.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Batch.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Batch.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Batch.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Batch.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Batch.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Batch, "col_Batch");
            this.col_Batch.ColumnEdit = this.rep_Batch;
            this.col_Batch.FieldName = "Batch";
            this.col_Batch.Name = "col_Batch";
            this.col_Batch.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Batch.OptionsFilter.AllowFilter = false;
            // 
            // rep_Batch
            // 
            resources.ApplyResources(this.rep_Batch, "rep_Batch");
            this.rep_Batch.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Batch.Buttons"))))});
            this.rep_Batch.Name = "rep_Batch";
            this.rep_Batch.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Batch.View = this.gridView6;
            // 
            // gridView6
            // 
            this.gridView6.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn33,
            this.gridColumn34});
            this.gridView6.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn33
            // 
            resources.ApplyResources(this.gridColumn33, "gridColumn33");
            this.gridColumn33.FieldName = "Batch";
            this.gridColumn33.Name = "gridColumn33";
            // 
            // gridColumn34
            // 
            resources.ApplyResources(this.gridColumn34, "gridColumn34");
            this.gridColumn34.FieldName = "Qty";
            this.gridColumn34.Name = "gridColumn34";
            // 
            // col_Length
            // 
            resources.ApplyResources(this.col_Length, "col_Length");
            this.col_Length.ColumnEdit = this.repSpin;
            this.col_Length.FieldName = "Length";
            this.col_Length.Name = "col_Length";
            this.col_Length.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Length.OptionsFilter.AllowFilter = false;
            // 
            // col_Width
            // 
            resources.ApplyResources(this.col_Width, "col_Width");
            this.col_Width.ColumnEdit = this.repSpin;
            this.col_Width.FieldName = "Width";
            this.col_Width.Name = "col_Width";
            this.col_Width.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Width.OptionsFilter.AllowFilter = false;
            // 
            // col_Height
            // 
            resources.ApplyResources(this.col_Height, "col_Height");
            this.col_Height.ColumnEdit = this.repSpin;
            this.col_Height.FieldName = "Height";
            this.col_Height.Name = "col_Height";
            this.col_Height.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Height.OptionsFilter.AllowFilter = false;
            // 
            // col_TotalQty
            // 
            resources.ApplyResources(this.col_TotalQty, "col_TotalQty");
            this.col_TotalQty.FieldName = "TotalQty";
            this.col_TotalQty.Name = "col_TotalQty";
            this.col_TotalQty.OptionsColumn.AllowEdit = false;
            this.col_TotalQty.OptionsColumn.AllowFocus = false;
            this.col_TotalQty.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalQty.OptionsFilter.AllowFilter = false;
            this.col_TotalQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_PiecesCount
            // 
            this.col_PiecesCount.AppearanceCell.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_PiecesCount.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PiecesCount.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_PiecesCount.AppearanceHeader.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_PiecesCount.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PiecesCount.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_PiecesCount, "col_PiecesCount");
            this.col_PiecesCount.ColumnEdit = this.repSpin;
            this.col_PiecesCount.FieldName = "PiecesCount";
            this.col_PiecesCount.Name = "col_PiecesCount";
            this.col_PiecesCount.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_PiecesCount.OptionsFilter.AllowFilter = false;
            // 
            // col_ItemDescription
            // 
            this.col_ItemDescription.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemDescription.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescription.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescription.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemDescription.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemDescription.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescription.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescription.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemDescription, "col_ItemDescription");
            this.col_ItemDescription.FieldName = "ItemDescription";
            this.col_ItemDescription.Name = "col_ItemDescription";
            this.col_ItemDescription.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemDescription.OptionsFilter.AllowFilter = false;
            // 
            // col_ItemDescriptionEn
            // 
            this.col_ItemDescriptionEn.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemDescriptionEn.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemDescriptionEn, "col_ItemDescriptionEn");
            this.col_ItemDescriptionEn.FieldName = "ItemDescriptionEn";
            this.col_ItemDescriptionEn.Name = "col_ItemDescriptionEn";
            this.col_ItemDescriptionEn.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemDescriptionEn.OptionsFilter.AllowFilter = false;
            // 
            // col_SalesTax
            // 
            resources.ApplyResources(this.col_SalesTax, "col_SalesTax");
            this.col_SalesTax.ColumnEdit = this.repSpin;
            this.col_SalesTax.FieldName = "SalesTax";
            this.col_SalesTax.Name = "col_SalesTax";
            this.col_SalesTax.OptionsColumn.AllowEdit = false;
            this.col_SalesTax.OptionsColumn.AllowFocus = false;
            this.col_SalesTax.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_SalesTax.OptionsFilter.AllowFilter = false;
            // 
            // col_DiscountRatio2
            // 
            resources.ApplyResources(this.col_DiscountRatio2, "col_DiscountRatio2");
            this.col_DiscountRatio2.ColumnEdit = this.repDiscountRatio;
            this.col_DiscountRatio2.FieldName = "DiscountRatio2";
            this.col_DiscountRatio2.Name = "col_DiscountRatio2";
            this.col_DiscountRatio2.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_DiscountRatio2.OptionsFilter.AllowFilter = false;
            // 
            // col_DiscountRatio3
            // 
            resources.ApplyResources(this.col_DiscountRatio3, "col_DiscountRatio3");
            this.col_DiscountRatio3.ColumnEdit = this.repDiscountRatio;
            this.col_DiscountRatio3.FieldName = "DiscountRatio3";
            this.col_DiscountRatio3.Name = "col_DiscountRatio3";
            this.col_DiscountRatio3.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_DiscountRatio3.OptionsFilter.AllowFilter = false;
            // 
            // col_Serial
            // 
            this.col_Serial.AppearanceCell.Options.UseTextOptions = true;
            this.col_Serial.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Serial.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Serial.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Serial, "col_Serial");
            this.col_Serial.FieldName = "Serial";
            this.col_Serial.Name = "col_Serial";
            this.col_Serial.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            // 
            // gridColumn32
            // 
            this.gridColumn32.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn32.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn32.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn32.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn32.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn32.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn32.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn32.ColumnEdit = this.repManufactureDate;
            this.gridColumn32.FieldName = "ManufactureDate";
            this.gridColumn32.Name = "gridColumn32";
            // 
            // repManufactureDate
            // 
            this.repManufactureDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repManufactureDate.Buttons"))))});
            this.repManufactureDate.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Buttons"))))});
            this.repManufactureDate.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repManufactureDate.Mask.UseMaskAsDisplayFormat")));
            this.repManufactureDate.Name = "repManufactureDate";
            // 
            // col_CusTax
            // 
            resources.ApplyResources(this.col_CusTax, "col_CusTax");
            this.col_CusTax.FieldName = "CustomTax";
            this.col_CusTax.Name = "col_CusTax";
            // 
            // lkp_store
            // 
            this.lkp_store.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_store.Buttons"))))});
            this.lkp_store.DisplayMember = "StoreNameAr";
            this.lkp_store.Name = "lkp_store";
            resources.ApplyResources(this.lkp_store, "lkp_store");
            this.lkp_store.ValueMember = "StoreId";
            // 
            // txtNet
            // 
            resources.ApplyResources(this.txtNet, "txtNet");
            this.txtNet.EnterMoveNextControl = true;
            this.txtNet.Name = "txtNet";
            this.txtNet.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNet.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNet.Properties.ReadOnly = true;
            this.txtNet.TabStop = false;
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Name = "labelControl13";
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // txt_Total
            // 
            resources.ApplyResources(this.txt_Total, "txt_Total");
            this.txt_Total.EnterMoveNextControl = true;
            this.txt_Total.Name = "txt_Total";
            this.txt_Total.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Total.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Total.Properties.ReadOnly = true;
            this.txt_Total.TabStop = false;
            // 
            // labelControl18
            // 
            resources.ApplyResources(this.labelControl18, "labelControl18");
            this.labelControl18.Name = "labelControl18";
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Name = "labelControl19";
            // 
            // btnAddCustomer
            // 
            resources.ApplyResources(this.btnAddCustomer, "btnAddCustomer");
            this.btnAddCustomer.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnAddCustomer.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddCustomer.Name = "btnAddCustomer";
            this.btnAddCustomer.TabStop = false;
            this.btnAddCustomer.Click += new System.EventHandler(this.btnAddCustomer_Click);
            // 
            // grdLastPrices
            // 
            this.grdLastPrices.ContextMenuStrip = this.contextMenuStrip1;
            resources.ApplyResources(this.grdLastPrices, "grdLastPrices");
            this.grdLastPrices.MainView = this.gridView3;
            this.grdLastPrices.Name = "grdLastPrices";
            this.grdLastPrices.TabStop = false;
            this.grdLastPrices.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView3.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView3.Appearance.Row.Options.UseTextOptions = true;
            this.gridView3.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colTotalPurchasePrice,
            this.gridColumn6,
            this.colUOM,
            this.colQty,
            this.colCustNameAr,
            this.colInvoiceDate,
            this.colInvoiceCode});
            this.gridView3.GridControl = this.grdLastPrices;
            this.gridView3.HorzScrollStep = 2;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView3.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsCustomization.AllowColumnMoving = false;
            this.gridView3.OptionsCustomization.AllowFilter = false;
            this.gridView3.OptionsMenu.EnableColumnMenu = false;
            this.gridView3.OptionsMenu.EnableFooterMenu = false;
            this.gridView3.OptionsMenu.EnableGroupPanelMenu = false;
            this.gridView3.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gridView3.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gridView3.OptionsNavigation.EnterMoveNextColumn = true;
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView3.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gridView3.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView3.OptionsView.ShowDetailButtons = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.OptionsView.ShowIndicator = false;
            this.gridView3.PaintStyleName = "UltraFlat";
            // 
            // colTotalPurchasePrice
            // 
            resources.ApplyResources(this.colTotalPurchasePrice, "colTotalPurchasePrice");
            this.colTotalPurchasePrice.FieldName = "TotalSellPrice";
            this.colTotalPurchasePrice.Name = "colTotalPurchasePrice";
            // 
            // gridColumn6
            // 
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "SellPrice";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // colUOM
            // 
            resources.ApplyResources(this.colUOM, "colUOM");
            this.colUOM.FieldName = "UOM";
            this.colUOM.Name = "colUOM";
            // 
            // colQty
            // 
            resources.ApplyResources(this.colQty, "colQty");
            this.colQty.FieldName = "Qty";
            this.colQty.Name = "colQty";
            // 
            // colCustNameAr
            // 
            resources.ApplyResources(this.colCustNameAr, "colCustNameAr");
            this.colCustNameAr.FieldName = "CusNameAr";
            this.colCustNameAr.Name = "colCustNameAr";
            // 
            // colInvoiceDate
            // 
            resources.ApplyResources(this.colInvoiceDate, "colInvoiceDate");
            this.colInvoiceDate.FieldName = "InvoiceDate";
            this.colInvoiceDate.Name = "colInvoiceDate";
            // 
            // colInvoiceCode
            // 
            resources.ApplyResources(this.colInvoiceCode, "colInvoiceCode");
            this.colInvoiceCode.FieldName = "InvoiceCode";
            this.colInvoiceCode.Name = "colInvoiceCode";
            // 
            // txtExpenses
            // 
            resources.ApplyResources(this.txtExpenses, "txtExpenses");
            this.txtExpenses.EnterMoveNextControl = true;
            this.txtExpenses.Name = "txtExpenses";
            this.txtExpenses.Properties.Appearance.Options.UseTextOptions = true;
            this.txtExpenses.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtExpenses.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtExpenses.Properties.Mask.EditMask = resources.GetString("txtExpenses.Properties.Mask.EditMask");
            this.txtExpenses.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtExpenses.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtExpenses.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Name = "labelControl20";
            // 
            // txt_Balance_After
            // 
            resources.ApplyResources(this.txt_Balance_After, "txt_Balance_After");
            this.txt_Balance_After.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Balance_After.Name = "txt_Balance_After";
            // 
            // txt_Balance_Before
            // 
            resources.ApplyResources(this.txt_Balance_Before, "txt_Balance_Before");
            this.txt_Balance_Before.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Balance_Before.Name = "txt_Balance_Before";
            // 
            // txt_MaxCredit
            // 
            resources.ApplyResources(this.txt_MaxCredit, "txt_MaxCredit");
            this.txt_MaxCredit.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_MaxCredit.Name = "txt_MaxCredit";
            // 
            // lbl_IsCredit_After
            // 
            resources.ApplyResources(this.lbl_IsCredit_After, "lbl_IsCredit_After");
            this.lbl_IsCredit_After.Name = "lbl_IsCredit_After";
            // 
            // lblBlncAftr
            // 
            resources.ApplyResources(this.lblBlncAftr, "lblBlncAftr");
            this.lblBlncAftr.Name = "lblBlncAftr";
            // 
            // lbl_IsCredit_Before
            // 
            resources.ApplyResources(this.lbl_IsCredit_Before, "lbl_IsCredit_Before");
            this.lbl_IsCredit_Before.Name = "lbl_IsCredit_Before";
            // 
            // labelControl24
            // 
            resources.ApplyResources(this.labelControl24, "labelControl24");
            this.labelControl24.Name = "labelControl24";
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // groupControl1
            // 
            resources.ApplyResources(this.groupControl1, "groupControl1");
            this.groupControl1.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl1.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControl1.CaptionLocation = DevExpress.Utils.Locations.Right;
            this.groupControl1.Controls.Add(this.lkp_Drawers2);
            this.groupControl1.Controls.Add(this.txt_PayAcc1_Paid);
            this.groupControl1.Controls.Add(this.labelControl30);
            this.groupControl1.Controls.Add(this.labelControl31);
            this.groupControl1.Controls.Add(this.lkp_Drawers);
            this.groupControl1.Controls.Add(this.labelControl17);
            this.groupControl1.Controls.Add(this.txt_PayAcc2_Paid);
            this.groupControl1.Controls.Add(this.labelControl33);
            this.groupControl1.Controls.Add(this.txt_paid);
            this.groupControl1.Controls.Add(this.lbl_Paid);
            this.groupControl1.Controls.Add(this.lbl_remains);
            this.groupControl1.Controls.Add(this.txt_Remains);
            this.groupControl1.Name = "groupControl1";
            // 
            // lkp_Drawers2
            // 
            this.lkp_Drawers2.EnterMoveNextControl = true;
            resources.ApplyResources(this.lkp_Drawers2, "lkp_Drawers2");
            this.lkp_Drawers2.MenuManager = this.barManager1;
            this.lkp_Drawers2.Name = "lkp_Drawers2";
            this.lkp_Drawers2.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_Drawers2.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Drawers2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Drawers2.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Drawers2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Drawers2.Properties.Buttons"))))});
            this.lkp_Drawers2.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers2.Properties.Columns"), resources.GetString("lkp_Drawers2.Properties.Columns1"), ((int)(resources.GetObject("lkp_Drawers2.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Drawers2.Properties.Columns3"))), resources.GetString("lkp_Drawers2.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Drawers2.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Drawers2.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers2.Properties.Columns7"), resources.GetString("lkp_Drawers2.Properties.Columns8"))});
            this.lkp_Drawers2.Properties.DisplayMember = "AccountName";
            this.lkp_Drawers2.Properties.NullText = resources.GetString("lkp_Drawers2.Properties.NullText");
            this.lkp_Drawers2.Properties.ValueMember = "AccountId";
            this.lkp_Drawers2.EditValueChanged += new System.EventHandler(this.lkp_Drawers2_EditValueChanged);
            this.lkp_Drawers2.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txt_PayAcc1_Paid
            // 
            resources.ApplyResources(this.txt_PayAcc1_Paid, "txt_PayAcc1_Paid");
            this.txt_PayAcc1_Paid.EnterMoveNextControl = true;
            this.txt_PayAcc1_Paid.MenuManager = this.barManager1;
            this.txt_PayAcc1_Paid.Name = "txt_PayAcc1_Paid";
            this.txt_PayAcc1_Paid.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_PayAcc1_Paid.EditValueChanged += new System.EventHandler(this.txt_PayAcc1_Paid_EditValueChanged);
            this.txt_PayAcc1_Paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl30
            // 
            resources.ApplyResources(this.labelControl30, "labelControl30");
            this.labelControl30.Name = "labelControl30";
            // 
            // labelControl31
            // 
            resources.ApplyResources(this.labelControl31, "labelControl31");
            this.labelControl31.Name = "labelControl31";
            // 
            // txt_PayAcc2_Paid
            // 
            resources.ApplyResources(this.txt_PayAcc2_Paid, "txt_PayAcc2_Paid");
            this.txt_PayAcc2_Paid.EnterMoveNextControl = true;
            this.txt_PayAcc2_Paid.Name = "txt_PayAcc2_Paid";
            this.txt_PayAcc2_Paid.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_PayAcc2_Paid.EditValueChanged += new System.EventHandler(this.txt_PayAcc1_Paid_EditValueChanged);
            this.txt_PayAcc2_Paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl33
            // 
            resources.ApplyResources(this.labelControl33, "labelControl33");
            this.labelControl33.Name = "labelControl33";
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.AppearancePage.Header.Options.UseTextOptions = true;
            this.xtraTabControl1.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.page_AccInfo;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.page_JobOrder,
            this.Page_LastPrices,
            this.tabExtraData,
            this.page_AccInfo});
            // 
            // page_AccInfo
            // 
            this.page_AccInfo.Controls.Add(this.txt_Balance_After);
            this.page_AccInfo.Controls.Add(this.txt_Balance_Before);
            this.page_AccInfo.Controls.Add(this.lbl_Validate_MaxLimit);
            this.page_AccInfo.Controls.Add(this.txt_MaxCredit);
            this.page_AccInfo.Controls.Add(this.lbl_IsCredit_After);
            this.page_AccInfo.Controls.Add(this.lbl_IsCredit_Before);
            this.page_AccInfo.Controls.Add(this.lblBlncAftr);
            this.page_AccInfo.Controls.Add(this.labelControl10);
            this.page_AccInfo.Controls.Add(this.labelControl24);
            this.page_AccInfo.Name = "page_AccInfo";
            resources.ApplyResources(this.page_AccInfo, "page_AccInfo");
            // 
            // lbl_Validate_MaxLimit
            // 
            resources.ApplyResources(this.lbl_Validate_MaxLimit, "lbl_Validate_MaxLimit");
            this.lbl_Validate_MaxLimit.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lbl_Validate_MaxLimit.Appearance.Font")));
            this.lbl_Validate_MaxLimit.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lbl_Validate_MaxLimit.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lbl_Validate_MaxLimit.Name = "lbl_Validate_MaxLimit";
            // 
            // page_JobOrder
            // 
            this.page_JobOrder.Controls.Add(this.textEdit3);
            this.page_JobOrder.Controls.Add(this.lkp_JOStatus);
            this.page_JobOrder.Controls.Add(this.textEdit1);
            this.page_JobOrder.Controls.Add(this.lkp_JOPriority);
            this.page_JobOrder.Controls.Add(this.textEdit5);
            this.page_JobOrder.Controls.Add(this.lkp_JOSalesEmp);
            this.page_JobOrder.Controls.Add(this.textEdit8);
            this.page_JobOrder.Controls.Add(this.lkp_JODept);
            this.page_JobOrder.Controls.Add(this.textEdit4);
            this.page_JobOrder.Controls.Add(this.txt_JOJob);
            this.page_JobOrder.Controls.Add(this.textEdit2);
            this.page_JobOrder.Controls.Add(this.txt_JODeliveryDate);
            this.page_JobOrder.Controls.Add(this.textEdit7);
            this.page_JobOrder.Controls.Add(this.textEdit6);
            this.page_JobOrder.Controls.Add(this.txt_JORegDate);
            this.page_JobOrder.Controls.Add(this.txt_JOCode);
            this.page_JobOrder.Name = "page_JobOrder";
            resources.ApplyResources(this.page_JobOrder, "page_JobOrder");
            // 
            // textEdit3
            // 
            resources.ApplyResources(this.textEdit3, "textEdit3");
            this.textEdit3.EnterMoveNextControl = true;
            this.textEdit3.MenuManager = this.barManager1;
            this.textEdit3.Name = "textEdit3";
            this.textEdit3.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit3.Properties.Appearance.BackColor")));
            this.textEdit3.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit3.Properties.Appearance.ForeColor")));
            this.textEdit3.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit3.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit3.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit3.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit3.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit3.Properties.MaxLength = 190;
            this.textEdit3.TabStop = false;
            // 
            // lkp_JOStatus
            // 
            resources.ApplyResources(this.lkp_JOStatus, "lkp_JOStatus");
            this.lkp_JOStatus.EnterMoveNextControl = true;
            this.lkp_JOStatus.MenuManager = this.barManager1;
            this.lkp_JOStatus.Name = "lkp_JOStatus";
            this.lkp_JOStatus.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_JOStatus.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_JOStatus.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_JOStatus.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_JOStatus.Properties.Buttons"))))});
            this.lkp_JOStatus.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_JOStatus.Properties.Columns"), resources.GetString("lkp_JOStatus.Properties.Columns1"), ((int)(resources.GetObject("lkp_JOStatus.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_JOStatus.Properties.Columns3"))), resources.GetString("lkp_JOStatus.Properties.Columns4"), ((bool)(resources.GetObject("lkp_JOStatus.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_JOStatus.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_JOStatus.Properties.Columns7"), resources.GetString("lkp_JOStatus.Properties.Columns8"), ((int)(resources.GetObject("lkp_JOStatus.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_JOStatus.Properties.Columns10"))), resources.GetString("lkp_JOStatus.Properties.Columns11"), ((bool)(resources.GetObject("lkp_JOStatus.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_JOStatus.Properties.Columns13"))))});
            this.lkp_JOStatus.Properties.NullText = resources.GetString("lkp_JOStatus.Properties.NullText");
            // 
            // textEdit1
            // 
            resources.ApplyResources(this.textEdit1, "textEdit1");
            this.textEdit1.EnterMoveNextControl = true;
            this.textEdit1.MenuManager = this.barManager1;
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit1.Properties.Appearance.BackColor")));
            this.textEdit1.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit1.Properties.Appearance.ForeColor")));
            this.textEdit1.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit1.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit1.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit1.Properties.MaxLength = 190;
            this.textEdit1.TabStop = false;
            // 
            // lkp_JOPriority
            // 
            resources.ApplyResources(this.lkp_JOPriority, "lkp_JOPriority");
            this.lkp_JOPriority.EnterMoveNextControl = true;
            this.lkp_JOPriority.MenuManager = this.barManager1;
            this.lkp_JOPriority.Name = "lkp_JOPriority";
            this.lkp_JOPriority.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_JOPriority.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_JOPriority.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_JOPriority.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_JOPriority.Properties.Buttons"))))});
            this.lkp_JOPriority.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_JOPriority.Properties.Columns"), resources.GetString("lkp_JOPriority.Properties.Columns1"), ((int)(resources.GetObject("lkp_JOPriority.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_JOPriority.Properties.Columns3"))), resources.GetString("lkp_JOPriority.Properties.Columns4"), ((bool)(resources.GetObject("lkp_JOPriority.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_JOPriority.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_JOPriority.Properties.Columns7"), resources.GetString("lkp_JOPriority.Properties.Columns8"), ((int)(resources.GetObject("lkp_JOPriority.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_JOPriority.Properties.Columns10"))), resources.GetString("lkp_JOPriority.Properties.Columns11"), ((bool)(resources.GetObject("lkp_JOPriority.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_JOPriority.Properties.Columns13"))))});
            this.lkp_JOPriority.Properties.NullText = resources.GetString("lkp_JOPriority.Properties.NullText");
            // 
            // textEdit5
            // 
            resources.ApplyResources(this.textEdit5, "textEdit5");
            this.textEdit5.EnterMoveNextControl = true;
            this.textEdit5.MenuManager = this.barManager1;
            this.textEdit5.Name = "textEdit5";
            this.textEdit5.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit5.Properties.Appearance.BackColor")));
            this.textEdit5.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit5.Properties.Appearance.ForeColor")));
            this.textEdit5.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit5.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit5.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit5.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit5.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit5.Properties.MaxLength = 190;
            this.textEdit5.TabStop = false;
            // 
            // lkp_JOSalesEmp
            // 
            resources.ApplyResources(this.lkp_JOSalesEmp, "lkp_JOSalesEmp");
            this.lkp_JOSalesEmp.EnterMoveNextControl = true;
            this.lkp_JOSalesEmp.MenuManager = this.barManager1;
            this.lkp_JOSalesEmp.Name = "lkp_JOSalesEmp";
            this.lkp_JOSalesEmp.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_JOSalesEmp.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_JOSalesEmp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_JOSalesEmp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_JOSalesEmp.Properties.Buttons"))))});
            this.lkp_JOSalesEmp.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_JOSalesEmp.Properties.Columns"), resources.GetString("lkp_JOSalesEmp.Properties.Columns1"))});
            this.lkp_JOSalesEmp.Properties.DisplayMember = "EmpName";
            this.lkp_JOSalesEmp.Properties.NullText = resources.GetString("lkp_JOSalesEmp.Properties.NullText");
            this.lkp_JOSalesEmp.Properties.ValueMember = "EmpId";
            // 
            // textEdit8
            // 
            resources.ApplyResources(this.textEdit8, "textEdit8");
            this.textEdit8.EnterMoveNextControl = true;
            this.textEdit8.MenuManager = this.barManager1;
            this.textEdit8.Name = "textEdit8";
            this.textEdit8.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit8.Properties.Appearance.BackColor")));
            this.textEdit8.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit8.Properties.Appearance.ForeColor")));
            this.textEdit8.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit8.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit8.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit8.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit8.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit8.Properties.MaxLength = 190;
            this.textEdit8.TabStop = false;
            // 
            // lkp_JODept
            // 
            resources.ApplyResources(this.lkp_JODept, "lkp_JODept");
            this.lkp_JODept.EnterMoveNextControl = true;
            this.lkp_JODept.MenuManager = this.barManager1;
            this.lkp_JODept.Name = "lkp_JODept";
            this.lkp_JODept.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_JODept.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_JODept.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_JODept.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_JODept.Properties.Buttons"))))});
            this.lkp_JODept.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_JODept.Properties.Columns"), resources.GetString("lkp_JODept.Properties.Columns1"), ((int)(resources.GetObject("lkp_JODept.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_JODept.Properties.Columns3"))), resources.GetString("lkp_JODept.Properties.Columns4"), ((bool)(resources.GetObject("lkp_JODept.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_JODept.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_JODept.Properties.Columns7"), resources.GetString("lkp_JODept.Properties.Columns8"), ((int)(resources.GetObject("lkp_JODept.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_JODept.Properties.Columns10"))), resources.GetString("lkp_JODept.Properties.Columns11"), ((bool)(resources.GetObject("lkp_JODept.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_JODept.Properties.Columns13"))))});
            this.lkp_JODept.Properties.NullText = resources.GetString("lkp_JODept.Properties.NullText");
            // 
            // textEdit4
            // 
            resources.ApplyResources(this.textEdit4, "textEdit4");
            this.textEdit4.EnterMoveNextControl = true;
            this.textEdit4.MenuManager = this.barManager1;
            this.textEdit4.Name = "textEdit4";
            this.textEdit4.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit4.Properties.Appearance.BackColor")));
            this.textEdit4.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit4.Properties.Appearance.ForeColor")));
            this.textEdit4.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit4.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit4.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit4.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit4.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit4.Properties.MaxLength = 190;
            this.textEdit4.TabStop = false;
            // 
            // txt_JOJob
            // 
            resources.ApplyResources(this.txt_JOJob, "txt_JOJob");
            this.txt_JOJob.EnterMoveNextControl = true;
            this.txt_JOJob.Name = "txt_JOJob";
            this.txt_JOJob.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_JOJob.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            // 
            // textEdit2
            // 
            resources.ApplyResources(this.textEdit2, "textEdit2");
            this.textEdit2.EnterMoveNextControl = true;
            this.textEdit2.MenuManager = this.barManager1;
            this.textEdit2.Name = "textEdit2";
            this.textEdit2.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit2.Properties.Appearance.BackColor")));
            this.textEdit2.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit2.Properties.Appearance.ForeColor")));
            this.textEdit2.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit2.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit2.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit2.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit2.Properties.MaxLength = 190;
            this.textEdit2.TabStop = false;
            // 
            // txt_JODeliveryDate
            // 
            resources.ApplyResources(this.txt_JODeliveryDate, "txt_JODeliveryDate");
            this.txt_JODeliveryDate.EnterMoveNextControl = true;
            this.txt_JODeliveryDate.Name = "txt_JODeliveryDate";
            this.txt_JODeliveryDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txt_JODeliveryDate.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_JODeliveryDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_JODeliveryDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_JODeliveryDate.Properties.Buttons"))))});
            this.txt_JODeliveryDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            // 
            // textEdit7
            // 
            resources.ApplyResources(this.textEdit7, "textEdit7");
            this.textEdit7.EnterMoveNextControl = true;
            this.textEdit7.MenuManager = this.barManager1;
            this.textEdit7.Name = "textEdit7";
            this.textEdit7.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit7.Properties.Appearance.BackColor")));
            this.textEdit7.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit7.Properties.Appearance.ForeColor")));
            this.textEdit7.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit7.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit7.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit7.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit7.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit7.Properties.MaxLength = 190;
            this.textEdit7.TabStop = false;
            // 
            // textEdit6
            // 
            resources.ApplyResources(this.textEdit6, "textEdit6");
            this.textEdit6.EnterMoveNextControl = true;
            this.textEdit6.MenuManager = this.barManager1;
            this.textEdit6.Name = "textEdit6";
            this.textEdit6.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit6.Properties.Appearance.BackColor")));
            this.textEdit6.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit6.Properties.Appearance.ForeColor")));
            this.textEdit6.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit6.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit6.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit6.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit6.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit6.Properties.MaxLength = 190;
            this.textEdit6.TabStop = false;
            // 
            // txt_JORegDate
            // 
            resources.ApplyResources(this.txt_JORegDate, "txt_JORegDate");
            this.txt_JORegDate.EnterMoveNextControl = true;
            this.txt_JORegDate.MenuManager = this.barManager1;
            this.txt_JORegDate.Name = "txt_JORegDate";
            this.txt_JORegDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txt_JORegDate.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_JORegDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_JORegDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_JORegDate.Properties.Buttons"))))});
            this.txt_JORegDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            // 
            // txt_JOCode
            // 
            resources.ApplyResources(this.txt_JOCode, "txt_JOCode");
            this.txt_JOCode.EnterMoveNextControl = true;
            this.txt_JOCode.Name = "txt_JOCode";
            this.txt_JOCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_JOCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            // 
            // Page_LastPrices
            // 
            this.Page_LastPrices.Controls.Add(this.grdLastPrices);
            this.Page_LastPrices.Name = "Page_LastPrices";
            resources.ApplyResources(this.Page_LastPrices, "Page_LastPrices");
            // 
            // tabExtraData
            // 
            this.tabExtraData.Controls.Add(this.txtScaleSerial);
            this.tabExtraData.Controls.Add(this.labelControl26);
            this.tabExtraData.Controls.Add(this.txtDestination);
            this.tabExtraData.Controls.Add(this.lblDestination);
            this.tabExtraData.Controls.Add(this.txtVehicleNumber);
            this.tabExtraData.Controls.Add(this.lblVehicleNumber);
            this.tabExtraData.Controls.Add(this.txtDriverName);
            this.tabExtraData.Controls.Add(this.lblDriverName);
            this.tabExtraData.Name = "tabExtraData";
            resources.ApplyResources(this.tabExtraData, "tabExtraData");
            // 
            // txtScaleSerial
            // 
            resources.ApplyResources(this.txtScaleSerial, "txtScaleSerial");
            this.txtScaleSerial.EnterMoveNextControl = true;
            this.txtScaleSerial.MenuManager = this.barManager1;
            this.txtScaleSerial.Name = "txtScaleSerial";
            this.txtScaleSerial.Properties.MaxLength = 190;
            // 
            // labelControl26
            // 
            resources.ApplyResources(this.labelControl26, "labelControl26");
            this.labelControl26.Name = "labelControl26";
            // 
            // txtDestination
            // 
            resources.ApplyResources(this.txtDestination, "txtDestination");
            this.txtDestination.EnterMoveNextControl = true;
            this.txtDestination.MenuManager = this.barManager1;
            this.txtDestination.Name = "txtDestination";
            this.txtDestination.Properties.MaxLength = 190;
            // 
            // lblDestination
            // 
            resources.ApplyResources(this.lblDestination, "lblDestination");
            this.lblDestination.Name = "lblDestination";
            // 
            // txtVehicleNumber
            // 
            resources.ApplyResources(this.txtVehicleNumber, "txtVehicleNumber");
            this.txtVehicleNumber.EnterMoveNextControl = true;
            this.txtVehicleNumber.MenuManager = this.barManager1;
            this.txtVehicleNumber.Name = "txtVehicleNumber";
            this.txtVehicleNumber.Properties.MaxLength = 190;
            // 
            // lblVehicleNumber
            // 
            resources.ApplyResources(this.lblVehicleNumber, "lblVehicleNumber");
            this.lblVehicleNumber.Name = "lblVehicleNumber";
            // 
            // txtDriverName
            // 
            resources.ApplyResources(this.txtDriverName, "txtDriverName");
            this.txtDriverName.EnterMoveNextControl = true;
            this.txtDriverName.MenuManager = this.barManager1;
            this.txtDriverName.Name = "txtDriverName";
            this.txtDriverName.Properties.MaxLength = 190;
            // 
            // lblDriverName
            // 
            resources.ApplyResources(this.lblDriverName, "lblDriverName");
            this.lblDriverName.Name = "lblDriverName";
            // 
            // txt_AttnMr
            // 
            resources.ApplyResources(this.txt_AttnMr, "txt_AttnMr");
            this.txt_AttnMr.EnterMoveNextControl = true;
            this.txt_AttnMr.MenuManager = this.barManager1;
            this.txt_AttnMr.Name = "txt_AttnMr";
            this.txt_AttnMr.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_AttnMr.Properties.Appearance.BackColor")));
            this.txt_AttnMr.Properties.Appearance.BackColor2 = ((System.Drawing.Color)(resources.GetObject("txt_AttnMr.Properties.Appearance.BackColor2")));
            this.txt_AttnMr.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txt_AttnMr.Properties.Appearance.Font")));
            this.txt_AttnMr.Properties.Appearance.Options.UseBackColor = true;
            this.txt_AttnMr.Properties.Appearance.Options.UseFont = true;
            this.txt_AttnMr.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AttnMr.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.txt_AttnMr.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_AttnMr.Properties.AppearanceDisabled.BackColor")));
            this.txt_AttnMr.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.txt_AttnMr.Properties.AppearanceFocused.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_AttnMr.Properties.AppearanceFocused.BackColor")));
            this.txt_AttnMr.Properties.AppearanceFocused.Options.UseBackColor = true;
            this.txt_AttnMr.Properties.AppearanceReadOnly.Options.UseTextOptions = true;
            this.txt_AttnMr.Properties.AppearanceReadOnly.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AttnMr.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.UltraFlat;
            this.txt_AttnMr.Properties.MaxLength = 190;
            // 
            // labelControl41
            // 
            resources.ApplyResources(this.labelControl41, "labelControl41");
            this.labelControl41.Name = "labelControl41";
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl3.Appearance.Font")));
            this.labelControl3.Name = "labelControl3";
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Name = "labelControl12";
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Name = "labelControl14";
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Name = "labelControl16";
            // 
            // labelControl21
            // 
            resources.ApplyResources(this.labelControl21, "labelControl21");
            this.labelControl21.Name = "labelControl21";
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Name = "labelControl22";
            // 
            // labelControl23
            // 
            resources.ApplyResources(this.labelControl23, "labelControl23");
            this.labelControl23.Name = "labelControl23";
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Name = "labelControl25";
            // 
            // txtDiscountRatio
            // 
            resources.ApplyResources(this.txtDiscountRatio, "txtDiscountRatio");
            this.txtDiscountRatio.EnterMoveNextControl = true;
            this.txtDiscountRatio.Name = "txtDiscountRatio";
            this.txtDiscountRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtDiscountRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDiscountRatio.Properties.Mask.MaskType")));
            this.txtDiscountRatio.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtDiscountRatio.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txtDiscountRatio.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtDiscountRatio.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // txtDiscountValue
            // 
            resources.ApplyResources(this.txtDiscountValue, "txtDiscountValue");
            this.txtDiscountValue.EnterMoveNextControl = true;
            this.txtDiscountValue.Name = "txtDiscountValue";
            this.txtDiscountValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtDiscountValue.Properties.Mask.EditMask = resources.GetString("txtDiscountValue.Properties.Mask.EditMask");
            this.txtDiscountValue.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtDiscountValue.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtDiscountValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            this.txtDiscountValue.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txt_TaxValue
            // 
            resources.ApplyResources(this.txt_TaxValue, "txt_TaxValue");
            this.txt_TaxValue.EnterMoveNextControl = true;
            this.txt_TaxValue.Name = "txt_TaxValue";
            this.txt_TaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_TaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_TaxValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_TaxValue.Properties.Mask.EditMask = resources.GetString("txt_TaxValue.Properties.Mask.EditMask");
            this.txt_TaxValue.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_TaxValue.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txt_DeductTaxR
            // 
            resources.ApplyResources(this.txt_DeductTaxR, "txt_DeductTaxR");
            this.txt_DeductTaxR.EnterMoveNextControl = true;
            this.txt_DeductTaxR.Name = "txt_DeductTaxR";
            this.txt_DeductTaxR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DeductTaxR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DeductTaxR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_DeductTaxR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DeductTaxR.Properties.Mask.MaskType")));
            this.txt_DeductTaxR.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_DeductTaxR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txt_DeductTaxR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_DeductTaxR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // txt_DeductTaxV
            // 
            resources.ApplyResources(this.txt_DeductTaxV, "txt_DeductTaxV");
            this.txt_DeductTaxV.EnterMoveNextControl = true;
            this.txt_DeductTaxV.Name = "txt_DeductTaxV";
            this.txt_DeductTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DeductTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DeductTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_DeductTaxV.Properties.Mask.EditMask = resources.GetString("txt_DeductTaxV.Properties.Mask.EditMask");
            this.txt_DeductTaxV.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_DeductTaxV.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_DeductTaxV.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            this.txt_DeductTaxV.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txt_AddTaxR
            // 
            resources.ApplyResources(this.txt_AddTaxR, "txt_AddTaxR");
            this.txt_AddTaxR.EnterMoveNextControl = true;
            this.txt_AddTaxR.Name = "txt_AddTaxR";
            this.txt_AddTaxR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AddTaxR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AddTaxR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AddTaxR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_AddTaxR.Properties.Mask.MaskType")));
            this.txt_AddTaxR.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_AddTaxR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txt_AddTaxR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_AddTaxR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // txt_AddTaxV
            // 
            resources.ApplyResources(this.txt_AddTaxV, "txt_AddTaxV");
            this.txt_AddTaxV.EnterMoveNextControl = true;
            this.txt_AddTaxV.Name = "txt_AddTaxV";
            this.txt_AddTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AddTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AddTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AddTaxV.Properties.Mask.EditMask = resources.GetString("txt_AddTaxV.Properties.Mask.EditMask");
            this.txt_AddTaxV.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_AddTaxV.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_AddTaxV.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            this.txt_AddTaxV.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txtExpensesR
            // 
            resources.ApplyResources(this.txtExpensesR, "txtExpensesR");
            this.txtExpensesR.EnterMoveNextControl = true;
            this.txtExpensesR.Name = "txtExpensesR";
            this.txtExpensesR.Properties.Appearance.Options.UseTextOptions = true;
            this.txtExpensesR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtExpensesR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtExpensesR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtExpensesR.Properties.Mask.MaskType")));
            this.txtExpensesR.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtExpensesR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txtExpensesR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtExpensesR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // labelControl37
            // 
            resources.ApplyResources(this.labelControl37, "labelControl37");
            this.labelControl37.Name = "labelControl37";
            // 
            // labelControl38
            // 
            resources.ApplyResources(this.labelControl38, "labelControl38");
            this.labelControl38.Name = "labelControl38";
            // 
            // labelControl39
            // 
            resources.ApplyResources(this.labelControl39, "labelControl39");
            this.labelControl39.Name = "labelControl39";
            // 
            // labelControl42
            // 
            resources.ApplyResources(this.labelControl42, "labelControl42");
            this.labelControl42.Name = "labelControl42";
            // 
            // labelControl43
            // 
            resources.ApplyResources(this.labelControl43, "labelControl43");
            this.labelControl43.Name = "labelControl43";
            // 
            // labelControl44
            // 
            resources.ApplyResources(this.labelControl44, "labelControl44");
            this.labelControl44.Name = "labelControl44";
            // 
            // labelControl45
            // 
            resources.ApplyResources(this.labelControl45, "labelControl45");
            this.labelControl45.Name = "labelControl45";
            // 
            // labelControl46
            // 
            resources.ApplyResources(this.labelControl46, "labelControl46");
            this.labelControl46.Name = "labelControl46";
            // 
            // txt_retentionR
            // 
            resources.ApplyResources(this.txt_retentionR, "txt_retentionR");
            this.txt_retentionR.EnterMoveNextControl = true;
            this.txt_retentionR.Name = "txt_retentionR";
            this.txt_retentionR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_retentionR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_retentionR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_retentionR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_retentionR.Properties.Mask.MaskType")));
            // 
            // txt_RetentionV
            // 
            resources.ApplyResources(this.txt_RetentionV, "txt_RetentionV");
            this.txt_RetentionV.EnterMoveNextControl = true;
            this.txt_RetentionV.Name = "txt_RetentionV";
            this.txt_RetentionV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_RetentionV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_RetentionV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_RetentionV.Properties.Mask.EditMask = resources.GetString("txt_RetentionV.Properties.Mask.EditMask");
            // 
            // txt_AdvancePayR
            // 
            resources.ApplyResources(this.txt_AdvancePayR, "txt_AdvancePayR");
            this.txt_AdvancePayR.EnterMoveNextControl = true;
            this.txt_AdvancePayR.Name = "txt_AdvancePayR";
            this.txt_AdvancePayR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AdvancePayR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AdvancePayR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AdvancePayR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_AdvancePayR.Properties.Mask.MaskType")));
            // 
            // txt_AdvancePayV
            // 
            resources.ApplyResources(this.txt_AdvancePayV, "txt_AdvancePayV");
            this.txt_AdvancePayV.EnterMoveNextControl = true;
            this.txt_AdvancePayV.Name = "txt_AdvancePayV";
            this.txt_AdvancePayV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AdvancePayV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AdvancePayV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AdvancePayV.Properties.Mask.EditMask = resources.GetString("txt_AdvancePayV.Properties.Mask.EditMask");
            // 
            // labelControl27
            // 
            resources.ApplyResources(this.labelControl27, "labelControl27");
            this.labelControl27.Name = "labelControl27";
            // 
            // labelControl28
            // 
            resources.ApplyResources(this.labelControl28, "labelControl28");
            this.labelControl28.Name = "labelControl28";
            // 
            // txt_CusTaxV
            // 
            resources.ApplyResources(this.txt_CusTaxV, "txt_CusTaxV");
            this.txt_CusTaxV.EnterMoveNextControl = true;
            this.txt_CusTaxV.Name = "txt_CusTaxV";
            this.txt_CusTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_CusTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_CusTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_CusTaxV.Properties.Mask.EditMask = resources.GetString("txt_CusTaxV.Properties.Mask.EditMask");
            // 
            // frm_SL_InvoiceArchive
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.labelControl27);
            this.Controls.Add(this.labelControl28);
            this.Controls.Add(this.txt_CusTaxV);
            this.Controls.Add(this.labelControl37);
            this.Controls.Add(this.labelControl38);
            this.Controls.Add(this.labelControl39);
            this.Controls.Add(this.labelControl42);
            this.Controls.Add(this.labelControl43);
            this.Controls.Add(this.labelControl44);
            this.Controls.Add(this.labelControl45);
            this.Controls.Add(this.labelControl46);
            this.Controls.Add(this.txt_retentionR);
            this.Controls.Add(this.txt_RetentionV);
            this.Controls.Add(this.txt_AdvancePayR);
            this.Controls.Add(this.txt_AdvancePayV);
            this.Controls.Add(this.chk_IsOutTrns);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.txt_AttnMr);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.xtraTabControl1);
            this.Controls.Add(this.labelControl41);
            this.Controls.Add(this.cmbPayMethod);
            this.Controls.Add(this.btnAddCustomer);
            this.Controls.Add(this.labelControl16);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.labelControl23);
            this.Controls.Add(this.labelControl25);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.labelControl20);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.labelControl14);
            this.Controls.Add(this.labelControl22);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl21);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.labelControl19);
            this.Controls.Add(this.txt_Total);
            this.Controls.Add(this.labelControl18);
            this.Controls.Add(this.txtNet);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.labelControl13);
            this.Controls.Add(this.labelControl36);
            this.Controls.Add(this.lkp_Customers);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.labelControl35);
            this.Controls.Add(this.btnPrevious);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.txtExpenses);
            this.Controls.Add(this.txtDiscountRatio);
            this.Controls.Add(this.txtDiscountValue);
            this.Controls.Add(this.txt_TaxValue);
            this.Controls.Add(this.txt_DeductTaxR);
            this.Controls.Add(this.txt_DeductTaxV);
            this.Controls.Add(this.txt_AddTaxR);
            this.Controls.Add(this.txt_AddTaxV);
            this.Controls.Add(this.txtExpensesR);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_InvoiceArchive";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_Invoice_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_Invoice_Load);
            this.Shown += new System.EventHandler(this.frm_SL_Invoice_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_SL_Invoice_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            this.flowLayoutPanel1.ResumeLayout(false);
            this.pnlBook.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTserial.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_InvoiceBook.Properties)).EndInit();
            this.pnlInvCode.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTinvCode.Properties)).EndInit();
            this.pnlDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTdate.Properties)).EndInit();
            this.pnlBranch.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTstore.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).EndInit();
            this.pnlAgeDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTdueDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDays.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDate.Properties)).EndInit();
            this.pnlDeliveryDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTdeliverDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties)).EndInit();
            this.pnlCurrency.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).EndInit();
            this.pnlPostStore.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txt_Post_Date.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Post_Date.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsPosted.Properties)).EndInit();
            this.pnlPO.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTpo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PO_No.Properties)).EndInit();
            this.pnlSalesEmp.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTSalesEmp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).EndInit();
            this.pnlCostCenter.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.lkpCostCenter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit9.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Shipping.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSourceCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.btnSourceId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmdProcess.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsOutTrns.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPayMethod.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Remains.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDiscountRatio)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_storee)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Batch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_store)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Total.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdLastPrices)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc1_Paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc2_Paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.page_AccInfo.ResumeLayout(false);
            this.page_AccInfo.PerformLayout();
            this.page_JobOrder.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOStatus.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOPriority.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOSalesEmp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JODept.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit4.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JOJob.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JODeliveryDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JODeliveryDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JORegDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JORegDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JOCode.Properties)).EndInit();
            this.Page_LastPrices.ResumeLayout(false);
            this.tabExtraData.ResumeLayout(false);
            this.tabExtraData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtScaleSerial.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDestination.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVehicleNumber.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDriverName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AttnMr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpensesR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_retentionR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_RetentionV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AdvancePayR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AdvancePayV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CusTaxV.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.TextEdit txtInvoiceCode;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrevious;
        private DevExpress.XtraBars.BarButtonItem batBtnList;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraEditors.LabelControl labelControl35;
        private DevExpress.XtraEditors.DateEdit dtInvoiceDate;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraEditors.MemoEdit txtNotes;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbPayMethod;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        
        private DevExpress.XtraEditors.GridLookUpEdit lkp_Customers;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraGrid.GridControl grdPrInvoice;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpin;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn col_SellPrice;
        private DevExpress.XtraGrid.Columns.GridColumn colPurchasePrice;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repUOM;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repItems;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSellPrice;
        private DevExpress.XtraEditors.TextEdit txtNet;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl lbl_Paid;
        private DevExpress.XtraEditors.TextEdit txt_paid;
        private DevExpress.XtraEditors.LabelControl lbl_remains;
        private DevExpress.XtraEditors.TextEdit txt_Remains;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LookUpEdit lkp_Drawers;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraEditors.TextEdit txt_Total;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repDiscountRatio;
        private DevExpress.XtraEditors.LookUpEdit lkpStore;
        private DevExpress.XtraEditors.SimpleButton btnAddCustomer;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_frm_IC_Item;
        private DevExpress.XtraBars.BarButtonItem barBtnNotesReceivable;
        private DevExpress.XtraGrid.GridControl grdLastPrices;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn colTotalPurchasePrice;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn colUOM;
        private DevExpress.XtraGrid.Columns.GridColumn colQty;
        private DevExpress.XtraGrid.Columns.GridColumn colCustNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn colInvoiceDate;
        private DevExpress.XtraGrid.Columns.GridColumn colInvoiceCode;
        private System.Windows.Forms.ToolStripMenuItem mi_CustLastPrices;
        private System.Windows.Forms.ToolStripMenuItem mi_LastPrices;
        private DevExpress.XtraEditors.SpinEdit txtExpenses;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.LabelControl txt_Balance_After;
        private DevExpress.XtraEditors.LabelControl txt_Balance_Before;
        private DevExpress.XtraEditors.LabelControl txt_MaxCredit;
        private DevExpress.XtraEditors.LabelControl lbl_IsCredit_After;
        private DevExpress.XtraEditors.LabelControl lblBlncAftr;
        private DevExpress.XtraEditors.LabelControl lbl_IsCredit_Before;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraGrid.Columns.GridColumn col_Expire;
        private DevExpress.XtraGrid.Columns.GridColumn col_Batch;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_expireDate;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraEditors.LookUpEdit lkp_SalesEmp;
        private DevExpress.XtraBars.BarSubItem barSubItemPrint;
        private DevExpress.XtraBars.BarButtonItem barbtnPrint;
        private DevExpress.XtraBars.BarButtonItem barbtnPrintF;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LookUpEdit lkp_Drawers2;
        private DevExpress.XtraEditors.SpinEdit txt_PayAcc1_Paid;
        private DevExpress.XtraEditors.LabelControl labelControl30;
        private DevExpress.XtraEditors.LabelControl labelControl31;
        private DevExpress.XtraEditors.SpinEdit txt_PayAcc2_Paid;
        private DevExpress.XtraEditors.LabelControl labelControl33;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage Page_LastPrices;
        private DevExpress.XtraTab.XtraTabPage page_AccInfo;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_Sl_Qoute;
        private DevExpress.XtraEditors.TextEdit txt_PO_No;
        private DevExpress.XtraEditors.DateEdit dtDeliverDate;
        private DevExpress.XtraEditors.DateEdit txt_DueDate;
        private DevExpress.XtraEditors.SpinEdit txt_DueDays;
        private DevExpress.XtraEditors.TextEdit txt_AttnMr;
        private DevExpress.XtraEditors.LabelControl labelControl41;
        private DevExpress.XtraGrid.Columns.GridColumn col_Length;
        private DevExpress.XtraGrid.Columns.GridColumn col_Width;
        private DevExpress.XtraGrid.Columns.GridColumn col_Height;
        private DevExpress.XtraEditors.TextEdit txtTdueDate;
        private DevExpress.XtraEditors.TextEdit txtTdeliverDate;
        private DevExpress.XtraEditors.TextEdit txtTserial;
        private DevExpress.XtraEditors.TextEdit txtTinvCode;
        private DevExpress.XtraEditors.TextEdit txtTdate;
        private DevExpress.XtraEditors.TextEdit txtTstore;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalQty;
        private DevExpress.XtraEditors.TextEdit txtTSalesEmp;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl lblShipTo;
        private DevExpress.XtraEditors.TextEdit txtTpo;
        private DevExpress.XtraEditors.MemoEdit txt_Shipping;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemDescription;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_SalesOrder;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_IC_OutTrns;
        private DevExpress.XtraGrid.Columns.GridColumn col_PiecesCount;
        private DevExpress.XtraEditors.CheckEdit chk_IsOutTrns;
        private System.Windows.Forms.ToolStripMenuItem mi_PasteRows;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemDescriptionEn;
        private DevExpress.XtraTab.XtraTabPage page_JobOrder;
        private DevExpress.XtraEditors.TextEdit textEdit4;
        private DevExpress.XtraEditors.TextEdit txt_JOJob;
        private DevExpress.XtraEditors.TextEdit textEdit2;
        private DevExpress.XtraEditors.DateEdit txt_JODeliveryDate;
        private DevExpress.XtraEditors.TextEdit textEdit7;
        private DevExpress.XtraEditors.TextEdit textEdit6;
        private DevExpress.XtraEditors.DateEdit txt_JORegDate;
        private DevExpress.XtraEditors.TextEdit txt_JOCode;
        private DevExpress.XtraEditors.TextEdit textEdit3;
        private DevExpress.XtraEditors.LookUpEdit lkp_JOStatus;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private DevExpress.XtraEditors.LookUpEdit lkp_JOPriority;
        private DevExpress.XtraEditors.TextEdit textEdit5;
        private DevExpress.XtraEditors.LookUpEdit lkp_JOSalesEmp;
        private DevExpress.XtraEditors.TextEdit textEdit8;
        private DevExpress.XtraEditors.LookUpEdit lkp_JODept;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_JO;
        private DevExpress.XtraGrid.Columns.GridColumn col_SalesTax;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_PR_Invoice;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio2;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio3;
        private System.Windows.Forms.ToolStripMenuItem mi_ExportData;
        private DevExpress.XtraEditors.LookUpEdit lkp_InvoiceBook;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.SpinEdit txtDiscountRatio;
        private DevExpress.XtraEditors.SpinEdit txtDiscountValue;
        private DevExpress.XtraEditors.SpinEdit txt_TaxValue;
        private DevExpress.XtraEditors.SpinEdit txt_DeductTaxR;
        private DevExpress.XtraEditors.SpinEdit txt_DeductTaxV;
        private DevExpress.XtraEditors.SpinEdit txt_AddTaxR;
        private DevExpress.XtraEditors.SpinEdit txt_AddTaxV;
        private DevExpress.XtraEditors.SpinEdit txtExpensesR;
        private System.Windows.Forms.ToolStripMenuItem mi_InvoiceStaticDisc;
        //private uc_Currency uc_Currency1;
        private DevExpress.XtraEditors.TextEdit txtCurrency;
        private System.Windows.Forms.Panel pnlDeliveryDate;
        private System.Windows.Forms.Panel pnlAgeDate;
        private System.Windows.Forms.Panel pnlBook;
        private System.Windows.Forms.Panel pnlInvCode;
        private System.Windows.Forms.Panel pnlDate;
        private System.Windows.Forms.Panel pnlBranch;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.Panel pnlCurrency;
        private System.Windows.Forms.Panel pnlPO;
        private System.Windows.Forms.Panel pnlSalesEmp;
        private DevExpress.XtraEditors.LabelControl lbl_Validate_MaxLimit;
        private System.Windows.Forms.Panel pnlCostCenter;
        private DevExpress.XtraEditors.LookUpEdit lkpCostCenter;
        private DevExpress.XtraEditors.TextEdit textEdit9;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Batch;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private System.Windows.Forms.ToolStripMenuItem mi_InvoiceStaticDimensions;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_IC_Transfer;
        private System.Windows.Forms.Panel pnlSrcPrc;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmdProcess;
        private DevExpress.XtraEditors.TextEdit txtSourceCode;
        private DevExpress.XtraEditors.ButtonEdit btnSourceId;
        private DevExpress.XtraTab.XtraTabPage tabExtraData;
        private DevExpress.XtraEditors.TextEdit txtDriverName;
        private DevExpress.XtraEditors.LabelControl lblDriverName;
        private DevExpress.XtraEditors.TextEdit txtDestination;
        private DevExpress.XtraEditors.LabelControl lblDestination;
        private DevExpress.XtraEditors.TextEdit txtVehicleNumber;
        private DevExpress.XtraEditors.LabelControl lblVehicleNumber;
        private DevExpress.XtraBars.BarSubItem barBtn_ConvertTo;
        private DevExpress.XtraBars.BarButtonItem barBtn_OutTrns;
        private DevExpress.XtraBars.BarButtonItem barBtn_CashNote;
        private DevExpress.XtraGrid.Columns.GridColumn col_CompanyNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryNameAr;
        private DevExpress.XtraEditors.TextEdit txtScaleSerial;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private System.Windows.Forms.ToolStripMenuItem mi_ImportExcel;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial;
        private DevExpress.XtraGrid.Columns.GridColumn grdcol_branch;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit lkp_store;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit lkp_storee;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private System.Windows.Forms.Panel pnlPostStore;
        private DevExpress.XtraEditors.DateEdit txt_Post_Date;
        private DevExpress.XtraEditors.CheckEdit chk_IsPosted;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit repManufactureDate;
        private uc_Currency uc_Currency1;
        private DevExpress.XtraEditors.LabelControl labelControl37;
        private DevExpress.XtraEditors.LabelControl labelControl38;
        private DevExpress.XtraEditors.LabelControl labelControl39;
        private DevExpress.XtraEditors.LabelControl labelControl42;
        private DevExpress.XtraEditors.LabelControl labelControl43;
        private DevExpress.XtraEditors.LabelControl labelControl44;
        private DevExpress.XtraEditors.LabelControl labelControl45;
        private DevExpress.XtraEditors.LabelControl labelControl46;
        private DevExpress.XtraEditors.SpinEdit txt_retentionR;
        private DevExpress.XtraEditors.SpinEdit txt_RetentionV;
        private DevExpress.XtraEditors.SpinEdit txt_AdvancePayR;
        private DevExpress.XtraEditors.SpinEdit txt_AdvancePayV;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.SpinEdit txt_CusTaxV;
        private DevExpress.XtraGrid.Columns.GridColumn col_CusTax;
    }
}