﻿namespace Pharmacy.Forms
{
    partial class frm_LC
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_LC));
            this.lblCloseDate = new DevExpress.XtraEditors.LabelControl();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.lblNotes = new DevExpress.XtraEditors.LabelControl();
            this.lblName = new DevExpress.XtraEditors.LabelControl();
            this.txtName = new DevExpress.XtraEditors.TextEdit();
            this.lblShipPort = new DevExpress.XtraEditors.LabelControl();
            this.txtPort = new DevExpress.XtraEditors.TextEdit();
            this.lblCode = new DevExpress.XtraEditors.LabelControl();
            this.txtLcCode = new DevExpress.XtraEditors.TextEdit();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrev = new DevExpress.XtraEditors.SimpleButton();
            this.lblOpenDate = new DevExpress.XtraEditors.LabelControl();
            this.txtAmount = new DevExpress.XtraEditors.TextEdit();
            this.lblAmount = new DevExpress.XtraEditors.LabelControl();
            this.txtNotes = new DevExpress.XtraEditors.MemoEdit();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.tab_main = new DevExpress.XtraTab.XtraTabPage();
            this.cmbStatus = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.lblLcIsOpen = new DevExpress.XtraEditors.LabelControl();
            this.txtBillOfLading = new DevExpress.XtraEditors.TextEdit();
            this.lblBillOfLanding = new DevExpress.XtraEditors.LabelControl();
            this.txtPayMethod = new DevExpress.XtraEditors.TextEdit();
            this.lblPayMethod = new DevExpress.XtraEditors.LabelControl();
            this.txtShipMethod = new DevExpress.XtraEditors.TextEdit();
            this.lblShipMethod = new DevExpress.XtraEditors.LabelControl();
            this.dtDeliverDate = new DevExpress.XtraEditors.DateEdit();
            this.dtShipDate = new DevExpress.XtraEditors.DateEdit();
            this.lblDeliverDate = new DevExpress.XtraEditors.LabelControl();
            this.lblShipDAte = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Crnc = new DevExpress.XtraEditors.LookUpEdit();
            this.lblCrnc = new DevExpress.XtraEditors.LabelControl();
            this.lblVendor = new DevExpress.XtraEditors.LabelControl();
            this.lkpVendor = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.grdDepr = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colSerial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colJournalcode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNotes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDebit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCredit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCurrency = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repCrnc = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.colCrncRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTotalDebit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTotalCredit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.dtCloseDate = new DevExpress.XtraEditors.DateEdit();
            this.dtOpenDate = new DevExpress.XtraEditors.DateEdit();
            this.tab_docs = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.btnEditPhoto = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddEmpPhoho = new DevExpress.XtraEditors.SimpleButton();
            this.btnDeleteEmpPhoto = new DevExpress.XtraEditors.SimpleButton();
            this.btnShowPhotoes = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.txtImagePath = new DevExpress.XtraEditors.TextEdit();
            this.lstPhotos = new DevExpress.XtraEditors.ListBoxControl();
            this.btnBrowse = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.txtImageDesc = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPort.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLcCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAmount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.tab_main.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbStatus.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBillOfLading.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPayMethod.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShipMethod.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtShipDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtShipDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Crnc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpVendor.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdDepr)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCrnc)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtCloseDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtCloseDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOpenDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOpenDate.Properties)).BeginInit();
            this.tab_docs.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtImagePath.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstPhotos)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtImageDesc.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // lblCloseDate
            // 
            resources.ApplyResources(this.lblCloseDate, "lblCloseDate");
            this.lblCloseDate.Name = "lblCloseDate";
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnDelete,
            this.barBtnHelp,
            this.barBtnList,
            this.barBtnClose,
            this.barBtnNew});
            this.barManager1.MaxItemId = 31;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnNew, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnDelete, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnSave, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnList, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnClose, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", "")});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 27;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnDelete
            // 
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 1;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Delete_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtnList
            // 
            resources.ApplyResources(this.barBtnList, "barBtnList");
            this.barBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.barBtnList.Id = 25;
            this.barBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.barBtnList.Name = "barBtnList";
            this.barBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnList.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_List_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 26;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // lblNotes
            // 
            resources.ApplyResources(this.lblNotes, "lblNotes");
            this.lblNotes.Name = "lblNotes";
            // 
            // lblName
            // 
            resources.ApplyResources(this.lblName, "lblName");
            this.lblName.Name = "lblName";
            // 
            // txtName
            // 
            resources.ApplyResources(this.txtName, "txtName");
            this.txtName.EnterMoveNextControl = true;
            this.txtName.Name = "txtName";
            this.txtName.Properties.AccessibleDescription = resources.GetString("txtName.Properties.AccessibleDescription");
            this.txtName.Properties.AccessibleName = resources.GetString("txtName.Properties.AccessibleName");
            this.txtName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtName.Properties.Appearance.FontSizeDelta")));
            this.txtName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtName.Properties.Appearance.FontStyleDelta")));
            this.txtName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtName.Properties.Appearance.GradientMode")));
            this.txtName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtName.Properties.Appearance.Image")));
            this.txtName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtName.Properties.AutoHeight = ((bool)(resources.GetObject("txtName.Properties.AutoHeight")));
            this.txtName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtName.Properties.Mask.AutoComplete")));
            this.txtName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtName.Properties.Mask.BeepOnError")));
            this.txtName.Properties.Mask.EditMask = resources.GetString("txtName.Properties.Mask.EditMask");
            this.txtName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtName.Properties.Mask.IgnoreMaskBlank")));
            this.txtName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtName.Properties.Mask.MaskType")));
            this.txtName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtName.Properties.Mask.PlaceHolder")));
            this.txtName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtName.Properties.Mask.SaveLiteral")));
            this.txtName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtName.Properties.Mask.ShowPlaceHolders")));
            this.txtName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtName.Properties.MaxLength = 36;
            this.txtName.Properties.NullValuePrompt = resources.GetString("txtName.Properties.NullValuePrompt");
            this.txtName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtName.Properties.NullValuePromptShowForEmptyValue")));
            this.txtName.Modified += new System.EventHandler(this.txtFaCode_Modified);
            // 
            // lblShipPort
            // 
            resources.ApplyResources(this.lblShipPort, "lblShipPort");
            this.lblShipPort.Name = "lblShipPort";
            // 
            // txtPort
            // 
            resources.ApplyResources(this.txtPort, "txtPort");
            this.txtPort.EnterMoveNextControl = true;
            this.txtPort.Name = "txtPort";
            this.txtPort.Properties.AccessibleDescription = resources.GetString("txtPort.Properties.AccessibleDescription");
            this.txtPort.Properties.AccessibleName = resources.GetString("txtPort.Properties.AccessibleName");
            this.txtPort.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtPort.Properties.Appearance.FontSizeDelta")));
            this.txtPort.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtPort.Properties.Appearance.FontStyleDelta")));
            this.txtPort.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtPort.Properties.Appearance.GradientMode")));
            this.txtPort.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtPort.Properties.Appearance.Image")));
            this.txtPort.Properties.Appearance.Options.UseTextOptions = true;
            this.txtPort.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtPort.Properties.AutoHeight = ((bool)(resources.GetObject("txtPort.Properties.AutoHeight")));
            this.txtPort.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtPort.Properties.Mask.AutoComplete")));
            this.txtPort.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtPort.Properties.Mask.BeepOnError")));
            this.txtPort.Properties.Mask.EditMask = resources.GetString("txtPort.Properties.Mask.EditMask");
            this.txtPort.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtPort.Properties.Mask.IgnoreMaskBlank")));
            this.txtPort.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtPort.Properties.Mask.MaskType")));
            this.txtPort.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtPort.Properties.Mask.PlaceHolder")));
            this.txtPort.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtPort.Properties.Mask.SaveLiteral")));
            this.txtPort.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtPort.Properties.Mask.ShowPlaceHolders")));
            this.txtPort.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtPort.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtPort.Properties.MaxLength = 36;
            this.txtPort.Properties.NullValuePrompt = resources.GetString("txtPort.Properties.NullValuePrompt");
            this.txtPort.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtPort.Properties.NullValuePromptShowForEmptyValue")));
            this.txtPort.Modified += new System.EventHandler(this.txtFaCode_Modified);
            // 
            // lblCode
            // 
            resources.ApplyResources(this.lblCode, "lblCode");
            this.lblCode.Name = "lblCode";
            // 
            // txtLcCode
            // 
            resources.ApplyResources(this.txtLcCode, "txtLcCode");
            this.txtLcCode.EnterMoveNextControl = true;
            this.txtLcCode.Name = "txtLcCode";
            this.txtLcCode.Properties.AccessibleDescription = resources.GetString("txtLcCode.Properties.AccessibleDescription");
            this.txtLcCode.Properties.AccessibleName = resources.GetString("txtLcCode.Properties.AccessibleName");
            this.txtLcCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtLcCode.Properties.Appearance.FontSizeDelta")));
            this.txtLcCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtLcCode.Properties.Appearance.FontStyleDelta")));
            this.txtLcCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtLcCode.Properties.Appearance.GradientMode")));
            this.txtLcCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtLcCode.Properties.Appearance.Image")));
            this.txtLcCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtLcCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtLcCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtLcCode.Properties.AutoHeight")));
            this.txtLcCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtLcCode.Properties.Mask.AutoComplete")));
            this.txtLcCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtLcCode.Properties.Mask.BeepOnError")));
            this.txtLcCode.Properties.Mask.EditMask = resources.GetString("txtLcCode.Properties.Mask.EditMask");
            this.txtLcCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtLcCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtLcCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtLcCode.Properties.Mask.MaskType")));
            this.txtLcCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtLcCode.Properties.Mask.PlaceHolder")));
            this.txtLcCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtLcCode.Properties.Mask.SaveLiteral")));
            this.txtLcCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtLcCode.Properties.Mask.ShowPlaceHolders")));
            this.txtLcCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtLcCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtLcCode.Properties.NullValuePrompt = resources.GetString("txtLcCode.Properties.NullValuePrompt");
            this.txtLcCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtLcCode.Properties.NullValuePromptShowForEmptyValue")));
            this.txtLcCode.Modified += new System.EventHandler(this.txtFaCode_Modified);
            // 
            // btnNext
            // 
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            this.btnNext.Name = "btnNext";
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnPrev
            // 
            resources.ApplyResources(this.btnPrev, "btnPrev");
            this.btnPrev.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrev.Image = global::Pharmacy.Properties.Resources.prev32;
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // lblOpenDate
            // 
            resources.ApplyResources(this.lblOpenDate, "lblOpenDate");
            this.lblOpenDate.Name = "lblOpenDate";
            // 
            // txtAmount
            // 
            resources.ApplyResources(this.txtAmount, "txtAmount");
            this.txtAmount.EnterMoveNextControl = true;
            this.txtAmount.Name = "txtAmount";
            this.txtAmount.Properties.AccessibleDescription = resources.GetString("txtAmount.Properties.AccessibleDescription");
            this.txtAmount.Properties.AccessibleName = resources.GetString("txtAmount.Properties.AccessibleName");
            this.txtAmount.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtAmount.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtAmount.Properties.Appearance.FontSizeDelta")));
            this.txtAmount.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtAmount.Properties.Appearance.FontStyleDelta")));
            this.txtAmount.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtAmount.Properties.Appearance.GradientMode")));
            this.txtAmount.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtAmount.Properties.Appearance.Image")));
            this.txtAmount.Properties.Appearance.Options.UseTextOptions = true;
            this.txtAmount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtAmount.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("txtAmount.Properties.AppearanceDisabled.BackColor")));
            this.txtAmount.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("txtAmount.Properties.AppearanceDisabled.FontSizeDelta")));
            this.txtAmount.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtAmount.Properties.AppearanceDisabled.FontStyleDelta")));
            this.txtAmount.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtAmount.Properties.AppearanceDisabled.ForeColor")));
            this.txtAmount.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtAmount.Properties.AppearanceDisabled.GradientMode")));
            this.txtAmount.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("txtAmount.Properties.AppearanceDisabled.Image")));
            this.txtAmount.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.txtAmount.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.txtAmount.Properties.AutoHeight = ((bool)(resources.GetObject("txtAmount.Properties.AutoHeight")));
            this.txtAmount.Properties.DisplayFormat.FormatString = "n2";
            this.txtAmount.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtAmount.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtAmount.Properties.Mask.AutoComplete")));
            this.txtAmount.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtAmount.Properties.Mask.BeepOnError")));
            this.txtAmount.Properties.Mask.EditMask = resources.GetString("txtAmount.Properties.Mask.EditMask");
            this.txtAmount.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtAmount.Properties.Mask.IgnoreMaskBlank")));
            this.txtAmount.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtAmount.Properties.Mask.MaskType")));
            this.txtAmount.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtAmount.Properties.Mask.PlaceHolder")));
            this.txtAmount.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtAmount.Properties.Mask.SaveLiteral")));
            this.txtAmount.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtAmount.Properties.Mask.ShowPlaceHolders")));
            this.txtAmount.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtAmount.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtAmount.Properties.NullText = resources.GetString("txtAmount.Properties.NullText");
            this.txtAmount.Properties.NullValuePrompt = resources.GetString("txtAmount.Properties.NullValuePrompt");
            this.txtAmount.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtAmount.Properties.NullValuePromptShowForEmptyValue")));
            this.txtAmount.Modified += new System.EventHandler(this.txtFaCode_Modified);
            // 
            // lblAmount
            // 
            resources.ApplyResources(this.lblAmount, "lblAmount");
            this.lblAmount.Name = "lblAmount";
            // 
            // txtNotes
            // 
            resources.ApplyResources(this.txtNotes, "txtNotes");
            this.txtNotes.Name = "txtNotes";
            this.txtNotes.Properties.AccessibleDescription = resources.GetString("txtNotes.Properties.AccessibleDescription");
            this.txtNotes.Properties.AccessibleName = resources.GetString("txtNotes.Properties.AccessibleName");
            this.txtNotes.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtNotes.Properties.Appearance.FontSizeDelta")));
            this.txtNotes.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtNotes.Properties.Appearance.FontStyleDelta")));
            this.txtNotes.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtNotes.Properties.Appearance.GradientMode")));
            this.txtNotes.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtNotes.Properties.Appearance.Image")));
            this.txtNotes.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNotes.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNotes.Properties.MaxLength = 175;
            this.txtNotes.Properties.NullValuePrompt = resources.GetString("txtNotes.Properties.NullValuePrompt");
            this.txtNotes.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtNotes.Properties.NullValuePromptShowForEmptyValue")));
            this.txtNotes.Modified += new System.EventHandler(this.txtFaCode_Modified);
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.tab_main;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tab_main,
            this.tab_docs});
            // 
            // tab_main
            // 
            resources.ApplyResources(this.tab_main, "tab_main");
            this.tab_main.Controls.Add(this.cmbStatus);
            this.tab_main.Controls.Add(this.lblLcIsOpen);
            this.tab_main.Controls.Add(this.txtBillOfLading);
            this.tab_main.Controls.Add(this.lblBillOfLanding);
            this.tab_main.Controls.Add(this.txtPayMethod);
            this.tab_main.Controls.Add(this.lblPayMethod);
            this.tab_main.Controls.Add(this.txtShipMethod);
            this.tab_main.Controls.Add(this.lblShipMethod);
            this.tab_main.Controls.Add(this.dtDeliverDate);
            this.tab_main.Controls.Add(this.dtShipDate);
            this.tab_main.Controls.Add(this.lblDeliverDate);
            this.tab_main.Controls.Add(this.lblShipDAte);
            this.tab_main.Controls.Add(this.lkp_Crnc);
            this.tab_main.Controls.Add(this.lblCrnc);
            this.tab_main.Controls.Add(this.lblVendor);
            this.tab_main.Controls.Add(this.lkpVendor);
            this.tab_main.Controls.Add(this.groupBox2);
            this.tab_main.Controls.Add(this.dtCloseDate);
            this.tab_main.Controls.Add(this.dtOpenDate);
            this.tab_main.Controls.Add(this.txtLcCode);
            this.tab_main.Controls.Add(this.txtNotes);
            this.tab_main.Controls.Add(this.lblNotes);
            this.tab_main.Controls.Add(this.lblCloseDate);
            this.tab_main.Controls.Add(this.txtName);
            this.tab_main.Controls.Add(this.lblName);
            this.tab_main.Controls.Add(this.txtPort);
            this.tab_main.Controls.Add(this.lblShipPort);
            this.tab_main.Controls.Add(this.lblCode);
            this.tab_main.Controls.Add(this.lblOpenDate);
            this.tab_main.Controls.Add(this.lblAmount);
            this.tab_main.Controls.Add(this.txtAmount);
            this.tab_main.Name = "tab_main";
            // 
            // cmbStatus
            // 
            resources.ApplyResources(this.cmbStatus, "cmbStatus");
            this.cmbStatus.EnterMoveNextControl = true;
            this.cmbStatus.Name = "cmbStatus";
            this.cmbStatus.Properties.AccessibleDescription = resources.GetString("cmbStatus.Properties.AccessibleDescription");
            this.cmbStatus.Properties.AccessibleName = resources.GetString("cmbStatus.Properties.AccessibleName");
            this.cmbStatus.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbStatus.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cmbStatus.Properties.Appearance.FontSizeDelta")));
            this.cmbStatus.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbStatus.Properties.Appearance.FontStyleDelta")));
            this.cmbStatus.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbStatus.Properties.Appearance.GradientMode")));
            this.cmbStatus.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cmbStatus.Properties.Appearance.Image")));
            this.cmbStatus.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbStatus.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbStatus.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("cmbStatus.Properties.AppearanceDropDown.FontSizeDelta")));
            this.cmbStatus.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbStatus.Properties.AppearanceDropDown.FontStyleDelta")));
            this.cmbStatus.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbStatus.Properties.AppearanceDropDown.GradientMode")));
            this.cmbStatus.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("cmbStatus.Properties.AppearanceDropDown.Image")));
            this.cmbStatus.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.cmbStatus.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbStatus.Properties.AutoHeight = ((bool)(resources.GetObject("cmbStatus.Properties.AutoHeight")));
            this.cmbStatus.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbStatus.Properties.Buttons"))))});
            this.cmbStatus.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmbStatus.Properties.GlyphAlignment")));
            this.cmbStatus.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbStatus.Properties.Items"), ((object)(resources.GetObject("cmbStatus.Properties.Items1"))), ((int)(resources.GetObject("cmbStatus.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbStatus.Properties.Items3"), ((object)(resources.GetObject("cmbStatus.Properties.Items4"))), ((int)(resources.GetObject("cmbStatus.Properties.Items5"))))});
            this.cmbStatus.Properties.NullValuePrompt = resources.GetString("cmbStatus.Properties.NullValuePrompt");
            this.cmbStatus.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cmbStatus.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblLcIsOpen
            // 
            resources.ApplyResources(this.lblLcIsOpen, "lblLcIsOpen");
            this.lblLcIsOpen.Name = "lblLcIsOpen";
            // 
            // txtBillOfLading
            // 
            resources.ApplyResources(this.txtBillOfLading, "txtBillOfLading");
            this.txtBillOfLading.EnterMoveNextControl = true;
            this.txtBillOfLading.Name = "txtBillOfLading";
            this.txtBillOfLading.Properties.AccessibleDescription = resources.GetString("txtBillOfLading.Properties.AccessibleDescription");
            this.txtBillOfLading.Properties.AccessibleName = resources.GetString("txtBillOfLading.Properties.AccessibleName");
            this.txtBillOfLading.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtBillOfLading.Properties.Appearance.FontSizeDelta")));
            this.txtBillOfLading.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtBillOfLading.Properties.Appearance.FontStyleDelta")));
            this.txtBillOfLading.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtBillOfLading.Properties.Appearance.GradientMode")));
            this.txtBillOfLading.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtBillOfLading.Properties.Appearance.Image")));
            this.txtBillOfLading.Properties.Appearance.Options.UseTextOptions = true;
            this.txtBillOfLading.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtBillOfLading.Properties.AutoHeight = ((bool)(resources.GetObject("txtBillOfLading.Properties.AutoHeight")));
            this.txtBillOfLading.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtBillOfLading.Properties.Mask.AutoComplete")));
            this.txtBillOfLading.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtBillOfLading.Properties.Mask.BeepOnError")));
            this.txtBillOfLading.Properties.Mask.EditMask = resources.GetString("txtBillOfLading.Properties.Mask.EditMask");
            this.txtBillOfLading.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtBillOfLading.Properties.Mask.IgnoreMaskBlank")));
            this.txtBillOfLading.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtBillOfLading.Properties.Mask.MaskType")));
            this.txtBillOfLading.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtBillOfLading.Properties.Mask.PlaceHolder")));
            this.txtBillOfLading.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtBillOfLading.Properties.Mask.SaveLiteral")));
            this.txtBillOfLading.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtBillOfLading.Properties.Mask.ShowPlaceHolders")));
            this.txtBillOfLading.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtBillOfLading.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtBillOfLading.Properties.MaxLength = 36;
            this.txtBillOfLading.Properties.NullValuePrompt = resources.GetString("txtBillOfLading.Properties.NullValuePrompt");
            this.txtBillOfLading.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtBillOfLading.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblBillOfLanding
            // 
            resources.ApplyResources(this.lblBillOfLanding, "lblBillOfLanding");
            this.lblBillOfLanding.Name = "lblBillOfLanding";
            // 
            // txtPayMethod
            // 
            resources.ApplyResources(this.txtPayMethod, "txtPayMethod");
            this.txtPayMethod.EnterMoveNextControl = true;
            this.txtPayMethod.Name = "txtPayMethod";
            this.txtPayMethod.Properties.AccessibleDescription = resources.GetString("txtPayMethod.Properties.AccessibleDescription");
            this.txtPayMethod.Properties.AccessibleName = resources.GetString("txtPayMethod.Properties.AccessibleName");
            this.txtPayMethod.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtPayMethod.Properties.Appearance.FontSizeDelta")));
            this.txtPayMethod.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtPayMethod.Properties.Appearance.FontStyleDelta")));
            this.txtPayMethod.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtPayMethod.Properties.Appearance.GradientMode")));
            this.txtPayMethod.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtPayMethod.Properties.Appearance.Image")));
            this.txtPayMethod.Properties.Appearance.Options.UseTextOptions = true;
            this.txtPayMethod.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtPayMethod.Properties.AutoHeight = ((bool)(resources.GetObject("txtPayMethod.Properties.AutoHeight")));
            this.txtPayMethod.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtPayMethod.Properties.Mask.AutoComplete")));
            this.txtPayMethod.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtPayMethod.Properties.Mask.BeepOnError")));
            this.txtPayMethod.Properties.Mask.EditMask = resources.GetString("txtPayMethod.Properties.Mask.EditMask");
            this.txtPayMethod.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtPayMethod.Properties.Mask.IgnoreMaskBlank")));
            this.txtPayMethod.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtPayMethod.Properties.Mask.MaskType")));
            this.txtPayMethod.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtPayMethod.Properties.Mask.PlaceHolder")));
            this.txtPayMethod.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtPayMethod.Properties.Mask.SaveLiteral")));
            this.txtPayMethod.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtPayMethod.Properties.Mask.ShowPlaceHolders")));
            this.txtPayMethod.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtPayMethod.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtPayMethod.Properties.MaxLength = 36;
            this.txtPayMethod.Properties.NullValuePrompt = resources.GetString("txtPayMethod.Properties.NullValuePrompt");
            this.txtPayMethod.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtPayMethod.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblPayMethod
            // 
            resources.ApplyResources(this.lblPayMethod, "lblPayMethod");
            this.lblPayMethod.Name = "lblPayMethod";
            // 
            // txtShipMethod
            // 
            resources.ApplyResources(this.txtShipMethod, "txtShipMethod");
            this.txtShipMethod.EnterMoveNextControl = true;
            this.txtShipMethod.Name = "txtShipMethod";
            this.txtShipMethod.Properties.AccessibleDescription = resources.GetString("txtShipMethod.Properties.AccessibleDescription");
            this.txtShipMethod.Properties.AccessibleName = resources.GetString("txtShipMethod.Properties.AccessibleName");
            this.txtShipMethod.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtShipMethod.Properties.Appearance.FontSizeDelta")));
            this.txtShipMethod.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtShipMethod.Properties.Appearance.FontStyleDelta")));
            this.txtShipMethod.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtShipMethod.Properties.Appearance.GradientMode")));
            this.txtShipMethod.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtShipMethod.Properties.Appearance.Image")));
            this.txtShipMethod.Properties.Appearance.Options.UseTextOptions = true;
            this.txtShipMethod.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtShipMethod.Properties.AutoHeight = ((bool)(resources.GetObject("txtShipMethod.Properties.AutoHeight")));
            this.txtShipMethod.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtShipMethod.Properties.Mask.AutoComplete")));
            this.txtShipMethod.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtShipMethod.Properties.Mask.BeepOnError")));
            this.txtShipMethod.Properties.Mask.EditMask = resources.GetString("txtShipMethod.Properties.Mask.EditMask");
            this.txtShipMethod.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtShipMethod.Properties.Mask.IgnoreMaskBlank")));
            this.txtShipMethod.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtShipMethod.Properties.Mask.MaskType")));
            this.txtShipMethod.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtShipMethod.Properties.Mask.PlaceHolder")));
            this.txtShipMethod.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtShipMethod.Properties.Mask.SaveLiteral")));
            this.txtShipMethod.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtShipMethod.Properties.Mask.ShowPlaceHolders")));
            this.txtShipMethod.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtShipMethod.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtShipMethod.Properties.MaxLength = 36;
            this.txtShipMethod.Properties.NullValuePrompt = resources.GetString("txtShipMethod.Properties.NullValuePrompt");
            this.txtShipMethod.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtShipMethod.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblShipMethod
            // 
            resources.ApplyResources(this.lblShipMethod, "lblShipMethod");
            this.lblShipMethod.Name = "lblShipMethod";
            // 
            // dtDeliverDate
            // 
            resources.ApplyResources(this.dtDeliverDate, "dtDeliverDate");
            this.dtDeliverDate.MenuManager = this.barManager1;
            this.dtDeliverDate.Name = "dtDeliverDate";
            this.dtDeliverDate.Properties.AccessibleDescription = resources.GetString("dtDeliverDate.Properties.AccessibleDescription");
            this.dtDeliverDate.Properties.AccessibleName = resources.GetString("dtDeliverDate.Properties.AccessibleName");
            this.dtDeliverDate.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dtDeliverDate.Properties.Appearance.FontSizeDelta")));
            this.dtDeliverDate.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dtDeliverDate.Properties.Appearance.FontStyleDelta")));
            this.dtDeliverDate.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dtDeliverDate.Properties.Appearance.GradientMode")));
            this.dtDeliverDate.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dtDeliverDate.Properties.Appearance.Image")));
            this.dtDeliverDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtDeliverDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtDeliverDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtDeliverDate.Properties.AutoHeight")));
            this.dtDeliverDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtDeliverDate.Properties.Buttons"))))});
            this.dtDeliverDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtDeliverDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtDeliverDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtDeliverDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtDeliverDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtDeliverDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtDeliverDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtDeliverDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtDeliverDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtDeliverDate.Properties.Mask.AutoComplete")));
            this.dtDeliverDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtDeliverDate.Properties.Mask.BeepOnError")));
            this.dtDeliverDate.Properties.Mask.EditMask = resources.GetString("dtDeliverDate.Properties.Mask.EditMask");
            this.dtDeliverDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtDeliverDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtDeliverDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtDeliverDate.Properties.Mask.MaskType")));
            this.dtDeliverDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtDeliverDate.Properties.Mask.PlaceHolder")));
            this.dtDeliverDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtDeliverDate.Properties.Mask.SaveLiteral")));
            this.dtDeliverDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtDeliverDate.Properties.Mask.ShowPlaceHolders")));
            this.dtDeliverDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtDeliverDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtDeliverDate.Properties.NullValuePrompt = resources.GetString("dtDeliverDate.Properties.NullValuePrompt");
            this.dtDeliverDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtDeliverDate.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // dtShipDate
            // 
            resources.ApplyResources(this.dtShipDate, "dtShipDate");
            this.dtShipDate.MenuManager = this.barManager1;
            this.dtShipDate.Name = "dtShipDate";
            this.dtShipDate.Properties.AccessibleDescription = resources.GetString("dtShipDate.Properties.AccessibleDescription");
            this.dtShipDate.Properties.AccessibleName = resources.GetString("dtShipDate.Properties.AccessibleName");
            this.dtShipDate.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dtShipDate.Properties.Appearance.FontSizeDelta")));
            this.dtShipDate.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dtShipDate.Properties.Appearance.FontStyleDelta")));
            this.dtShipDate.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dtShipDate.Properties.Appearance.GradientMode")));
            this.dtShipDate.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dtShipDate.Properties.Appearance.Image")));
            this.dtShipDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtShipDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtShipDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtShipDate.Properties.AutoHeight")));
            this.dtShipDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtShipDate.Properties.Buttons"))))});
            this.dtShipDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtShipDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtShipDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtShipDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtShipDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtShipDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtShipDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtShipDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtShipDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtShipDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtShipDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtShipDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtShipDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtShipDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtShipDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtShipDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtShipDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtShipDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtShipDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtShipDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtShipDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtShipDate.Properties.Mask.AutoComplete")));
            this.dtShipDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtShipDate.Properties.Mask.BeepOnError")));
            this.dtShipDate.Properties.Mask.EditMask = resources.GetString("dtShipDate.Properties.Mask.EditMask");
            this.dtShipDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtShipDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtShipDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtShipDate.Properties.Mask.MaskType")));
            this.dtShipDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtShipDate.Properties.Mask.PlaceHolder")));
            this.dtShipDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtShipDate.Properties.Mask.SaveLiteral")));
            this.dtShipDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtShipDate.Properties.Mask.ShowPlaceHolders")));
            this.dtShipDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtShipDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtShipDate.Properties.NullValuePrompt = resources.GetString("dtShipDate.Properties.NullValuePrompt");
            this.dtShipDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtShipDate.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblDeliverDate
            // 
            resources.ApplyResources(this.lblDeliverDate, "lblDeliverDate");
            this.lblDeliverDate.Name = "lblDeliverDate";
            // 
            // lblShipDAte
            // 
            resources.ApplyResources(this.lblShipDAte, "lblShipDAte");
            this.lblShipDAte.Name = "lblShipDAte";
            // 
            // lkp_Crnc
            // 
            resources.ApplyResources(this.lkp_Crnc, "lkp_Crnc");
            this.lkp_Crnc.EnterMoveNextControl = true;
            this.lkp_Crnc.MenuManager = this.barManager1;
            this.lkp_Crnc.Name = "lkp_Crnc";
            this.lkp_Crnc.Properties.AccessibleDescription = resources.GetString("lkp_Crnc.Properties.AccessibleDescription");
            this.lkp_Crnc.Properties.AccessibleName = resources.GetString("lkp_Crnc.Properties.AccessibleName");
            this.lkp_Crnc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_Crnc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Crnc.Properties.AutoHeight")));
            this.lkp_Crnc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Crnc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Crnc.Properties.Buttons"))))});
            this.lkp_Crnc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Crnc.Properties.Columns"), resources.GetString("lkp_Crnc.Properties.Columns1"), ((int)(resources.GetObject("lkp_Crnc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Crnc.Properties.Columns3"))), resources.GetString("lkp_Crnc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Crnc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Crnc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Crnc.Properties.Columns7"), resources.GetString("lkp_Crnc.Properties.Columns8"), ((int)(resources.GetObject("lkp_Crnc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Crnc.Properties.Columns10"))), resources.GetString("lkp_Crnc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_Crnc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Crnc.Properties.Columns13"))))});
            this.lkp_Crnc.Properties.NullText = resources.GetString("lkp_Crnc.Properties.NullText");
            this.lkp_Crnc.Properties.NullValuePrompt = resources.GetString("lkp_Crnc.Properties.NullValuePrompt");
            this.lkp_Crnc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Crnc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Crnc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lblCrnc
            // 
            resources.ApplyResources(this.lblCrnc, "lblCrnc");
            this.lblCrnc.Name = "lblCrnc";
            // 
            // lblVendor
            // 
            resources.ApplyResources(this.lblVendor, "lblVendor");
            this.lblVendor.Name = "lblVendor";
            // 
            // lkpVendor
            // 
            resources.ApplyResources(this.lkpVendor, "lkpVendor");
            this.lkpVendor.EnterMoveNextControl = true;
            this.lkpVendor.MenuManager = this.barManager1;
            this.lkpVendor.Name = "lkpVendor";
            this.lkpVendor.Properties.AccessibleDescription = resources.GetString("lkpVendor.Properties.AccessibleDescription");
            this.lkpVendor.Properties.AccessibleName = resources.GetString("lkpVendor.Properties.AccessibleName");
            this.lkpVendor.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpVendor.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpVendor.Properties.Appearance.FontSizeDelta")));
            this.lkpVendor.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpVendor.Properties.Appearance.FontStyleDelta")));
            this.lkpVendor.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpVendor.Properties.Appearance.GradientMode")));
            this.lkpVendor.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpVendor.Properties.Appearance.Image")));
            this.lkpVendor.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpVendor.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpVendor.Properties.AutoHeight = ((bool)(resources.GetObject("lkpVendor.Properties.AutoHeight")));
            this.lkpVendor.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpVendor.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpVendor.Properties.Buttons"))))});
            this.lkpVendor.Properties.ImmediatePopup = true;
            this.lkpVendor.Properties.NullText = resources.GetString("lkpVendor.Properties.NullText");
            this.lkpVendor.Properties.NullValuePrompt = resources.GetString("lkpVendor.Properties.NullValuePrompt");
            this.lkpVendor.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpVendor.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpVendor.Properties.View = this.gridView2;
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView2.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView2.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.HeaderPanel.GradientMode")));
            this.gridView2.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.HeaderPanel.Image")));
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView2.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.Row.FontSizeDelta")));
            this.gridView2.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.Row.FontStyleDelta")));
            this.gridView2.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.Row.GradientMode")));
            this.gridView2.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.Row.Image")));
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21});
            this.gridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView2.OptionsView.EnableAppearanceOddRow = true;
            this.gridView2.OptionsView.ShowAutoFilterRow = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.OptionsView.ShowIndicator = false;
            // 
            // gridColumn19
            // 
            resources.ApplyResources(this.gridColumn19, "gridColumn19");
            this.gridColumn19.FieldName = "VendorId";
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            resources.ApplyResources(this.gridColumn20, "gridColumn20");
            this.gridColumn20.FieldName = "VenCode";
            this.gridColumn20.Name = "gridColumn20";
            // 
            // gridColumn21
            // 
            resources.ApplyResources(this.gridColumn21, "gridColumn21");
            this.gridColumn21.FieldName = "VenNameAr";
            this.gridColumn21.Name = "gridColumn21";
            // 
            // groupBox2
            // 
            resources.ApplyResources(this.groupBox2, "groupBox2");
            this.groupBox2.Controls.Add(this.grdDepr);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.TabStop = false;
            // 
            // grdDepr
            // 
            resources.ApplyResources(this.grdDepr, "grdDepr");
            this.grdDepr.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdDepr.EmbeddedNavigator.AccessibleDescription");
            this.grdDepr.EmbeddedNavigator.AccessibleName = resources.GetString("grdDepr.EmbeddedNavigator.AccessibleName");
            this.grdDepr.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdDepr.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdDepr.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdDepr.EmbeddedNavigator.Anchor")));
            this.grdDepr.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdDepr.EmbeddedNavigator.BackgroundImage")));
            this.grdDepr.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdDepr.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdDepr.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdDepr.EmbeddedNavigator.ImeMode")));
            this.grdDepr.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdDepr.EmbeddedNavigator.MaximumSize")));
            this.grdDepr.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdDepr.EmbeddedNavigator.TextLocation")));
            this.grdDepr.EmbeddedNavigator.ToolTip = resources.GetString("grdDepr.EmbeddedNavigator.ToolTip");
            this.grdDepr.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdDepr.EmbeddedNavigator.ToolTipIconType")));
            this.grdDepr.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdDepr.EmbeddedNavigator.ToolTipTitle");
            this.grdDepr.MainView = this.gridView1;
            this.grdDepr.MenuManager = this.barManager1;
            this.grdDepr.Name = "grdDepr";
            this.grdDepr.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repCrnc});
            this.grdDepr.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.gridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Image")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.GradientMode")));
            this.gridView1.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.Image")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupRow.GradientMode")));
            this.gridView1.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupRow.Image")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.gridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Image")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Lines.FontSizeDelta")));
            this.gridView1.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Lines.FontStyleDelta")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Lines.GradientMode")));
            this.gridView1.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Lines.Image")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Row.FontSizeDelta")));
            this.gridView1.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Row.FontStyleDelta")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Row.GradientMode")));
            this.gridView1.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Row.Image")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.ColumnPanelRowHeight = 35;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colSerial,
            this.colDate,
            this.colJournalcode,
            this.colNotes,
            this.colDebit,
            this.colCredit,
            this.colCurrency,
            this.colCrncRate,
            this.colTotalDebit,
            this.colTotalCredit});
            this.gridView1.GridControl = this.grdDepr;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            this.gridView1.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            // 
            // colSerial
            // 
            resources.ApplyResources(this.colSerial, "colSerial");
            this.colSerial.FieldName = "Serial";
            this.colSerial.Name = "colSerial";
            this.colSerial.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.colSerial.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            this.colSerial.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // colDate
            // 
            resources.ApplyResources(this.colDate, "colDate");
            this.colDate.FieldName = "InsertDate";
            this.colDate.Name = "colDate";
            // 
            // colJournalcode
            // 
            resources.ApplyResources(this.colJournalcode, "colJournalcode");
            this.colJournalcode.FieldName = "JCode";
            this.colJournalcode.Name = "colJournalcode";
            // 
            // colNotes
            // 
            resources.ApplyResources(this.colNotes, "colNotes");
            this.colNotes.FieldName = "Notes";
            this.colNotes.Name = "colNotes";
            // 
            // colDebit
            // 
            resources.ApplyResources(this.colDebit, "colDebit");
            this.colDebit.DisplayFormat.FormatString = "n2";
            this.colDebit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDebit.FieldName = "Fcredit";
            this.colDebit.Name = "colDebit";
            // 
            // colCredit
            // 
            resources.ApplyResources(this.colCredit, "colCredit");
            this.colCredit.DisplayFormat.FormatString = "n2";
            this.colCredit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colCredit.FieldName = "Fdebit";
            this.colCredit.Name = "colCredit";
            // 
            // colCurrency
            // 
            resources.ApplyResources(this.colCurrency, "colCurrency");
            this.colCurrency.ColumnEdit = this.repCrnc;
            this.colCurrency.FieldName = "CrncId";
            this.colCurrency.Name = "colCurrency";
            // 
            // repCrnc
            // 
            resources.ApplyResources(this.repCrnc, "repCrnc");
            this.repCrnc.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repCrnc.Buttons"))))});
            this.repCrnc.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("repCrnc.Columns"), resources.GetString("repCrnc.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("repCrnc.Columns2"), resources.GetString("repCrnc.Columns3"), ((int)(resources.GetObject("repCrnc.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("repCrnc.Columns5"))), resources.GetString("repCrnc.Columns6"), ((bool)(resources.GetObject("repCrnc.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("repCrnc.Columns8"))))});
            this.repCrnc.Name = "repCrnc";
            // 
            // colCrncRate
            // 
            resources.ApplyResources(this.colCrncRate, "colCrncRate");
            this.colCrncRate.FieldName = "CrncRate";
            this.colCrncRate.Name = "colCrncRate";
            // 
            // colTotalDebit
            // 
            resources.ApplyResources(this.colTotalDebit, "colTotalDebit");
            this.colTotalDebit.DisplayFormat.FormatString = "n2";
            this.colTotalDebit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colTotalDebit.FieldName = "Debit";
            this.colTotalDebit.Name = "colTotalDebit";
            this.colTotalDebit.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colTotalDebit.Summary"))), resources.GetString("colTotalDebit.Summary1"), resources.GetString("colTotalDebit.Summary2"))});
            // 
            // colTotalCredit
            // 
            resources.ApplyResources(this.colTotalCredit, "colTotalCredit");
            this.colTotalCredit.DisplayFormat.FormatString = "n2";
            this.colTotalCredit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colTotalCredit.FieldName = "Credit";
            this.colTotalCredit.Name = "colTotalCredit";
            this.colTotalCredit.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colTotalCredit.Summary"))), resources.GetString("colTotalCredit.Summary1"), resources.GetString("colTotalCredit.Summary2"))});
            // 
            // dtCloseDate
            // 
            resources.ApplyResources(this.dtCloseDate, "dtCloseDate");
            this.dtCloseDate.MenuManager = this.barManager1;
            this.dtCloseDate.Name = "dtCloseDate";
            this.dtCloseDate.Properties.AccessibleDescription = resources.GetString("dtCloseDate.Properties.AccessibleDescription");
            this.dtCloseDate.Properties.AccessibleName = resources.GetString("dtCloseDate.Properties.AccessibleName");
            this.dtCloseDate.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dtCloseDate.Properties.Appearance.FontSizeDelta")));
            this.dtCloseDate.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dtCloseDate.Properties.Appearance.FontStyleDelta")));
            this.dtCloseDate.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dtCloseDate.Properties.Appearance.GradientMode")));
            this.dtCloseDate.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dtCloseDate.Properties.Appearance.Image")));
            this.dtCloseDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtCloseDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtCloseDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtCloseDate.Properties.AutoHeight")));
            this.dtCloseDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtCloseDate.Properties.Buttons"))))});
            this.dtCloseDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtCloseDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtCloseDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtCloseDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtCloseDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtCloseDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtCloseDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtCloseDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtCloseDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtCloseDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtCloseDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtCloseDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtCloseDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtCloseDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtCloseDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtCloseDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtCloseDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtCloseDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtCloseDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtCloseDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtCloseDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtCloseDate.Properties.Mask.AutoComplete")));
            this.dtCloseDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtCloseDate.Properties.Mask.BeepOnError")));
            this.dtCloseDate.Properties.Mask.EditMask = resources.GetString("dtCloseDate.Properties.Mask.EditMask");
            this.dtCloseDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtCloseDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtCloseDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtCloseDate.Properties.Mask.MaskType")));
            this.dtCloseDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtCloseDate.Properties.Mask.PlaceHolder")));
            this.dtCloseDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtCloseDate.Properties.Mask.SaveLiteral")));
            this.dtCloseDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtCloseDate.Properties.Mask.ShowPlaceHolders")));
            this.dtCloseDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtCloseDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtCloseDate.Properties.NullValuePrompt = resources.GetString("dtCloseDate.Properties.NullValuePrompt");
            this.dtCloseDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtCloseDate.Properties.NullValuePromptShowForEmptyValue")));
            this.dtCloseDate.Modified += new System.EventHandler(this.txtFaCode_Modified);
            // 
            // dtOpenDate
            // 
            resources.ApplyResources(this.dtOpenDate, "dtOpenDate");
            this.dtOpenDate.MenuManager = this.barManager1;
            this.dtOpenDate.Name = "dtOpenDate";
            this.dtOpenDate.Properties.AccessibleDescription = resources.GetString("dtOpenDate.Properties.AccessibleDescription");
            this.dtOpenDate.Properties.AccessibleName = resources.GetString("dtOpenDate.Properties.AccessibleName");
            this.dtOpenDate.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dtOpenDate.Properties.Appearance.FontSizeDelta")));
            this.dtOpenDate.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dtOpenDate.Properties.Appearance.FontStyleDelta")));
            this.dtOpenDate.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dtOpenDate.Properties.Appearance.GradientMode")));
            this.dtOpenDate.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dtOpenDate.Properties.Appearance.Image")));
            this.dtOpenDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtOpenDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtOpenDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtOpenDate.Properties.AutoHeight")));
            this.dtOpenDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtOpenDate.Properties.Buttons"))))});
            this.dtOpenDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtOpenDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtOpenDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtOpenDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtOpenDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtOpenDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtOpenDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtOpenDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtOpenDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtOpenDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtOpenDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtOpenDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtOpenDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtOpenDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtOpenDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtOpenDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtOpenDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtOpenDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtOpenDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtOpenDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtOpenDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtOpenDate.Properties.Mask.AutoComplete")));
            this.dtOpenDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtOpenDate.Properties.Mask.BeepOnError")));
            this.dtOpenDate.Properties.Mask.EditMask = resources.GetString("dtOpenDate.Properties.Mask.EditMask");
            this.dtOpenDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtOpenDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtOpenDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtOpenDate.Properties.Mask.MaskType")));
            this.dtOpenDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtOpenDate.Properties.Mask.PlaceHolder")));
            this.dtOpenDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtOpenDate.Properties.Mask.SaveLiteral")));
            this.dtOpenDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtOpenDate.Properties.Mask.ShowPlaceHolders")));
            this.dtOpenDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtOpenDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtOpenDate.Properties.NullValuePrompt = resources.GetString("dtOpenDate.Properties.NullValuePrompt");
            this.dtOpenDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtOpenDate.Properties.NullValuePromptShowForEmptyValue")));
            this.dtOpenDate.Modified += new System.EventHandler(this.txtFaCode_Modified);
            // 
            // tab_docs
            // 
            resources.ApplyResources(this.tab_docs, "tab_docs");
            this.tab_docs.Controls.Add(this.groupControl2);
            this.tab_docs.Name = "tab_docs";
            // 
            // groupControl2
            // 
            resources.ApplyResources(this.groupControl2, "groupControl2");
            this.groupControl2.AppearanceCaption.FontSizeDelta = ((int)(resources.GetObject("groupControl2.AppearanceCaption.FontSizeDelta")));
            this.groupControl2.AppearanceCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("groupControl2.AppearanceCaption.FontStyleDelta")));
            this.groupControl2.AppearanceCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("groupControl2.AppearanceCaption.GradientMode")));
            this.groupControl2.AppearanceCaption.Image = ((System.Drawing.Image)(resources.GetObject("groupControl2.AppearanceCaption.Image")));
            this.groupControl2.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl2.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControl2.Controls.Add(this.btnEditPhoto);
            this.groupControl2.Controls.Add(this.btnAddEmpPhoho);
            this.groupControl2.Controls.Add(this.btnDeleteEmpPhoto);
            this.groupControl2.Controls.Add(this.btnShowPhotoes);
            this.groupControl2.Controls.Add(this.labelControl22);
            this.groupControl2.Controls.Add(this.txtImagePath);
            this.groupControl2.Controls.Add(this.lstPhotos);
            this.groupControl2.Controls.Add(this.btnBrowse);
            this.groupControl2.Controls.Add(this.labelControl23);
            this.groupControl2.Controls.Add(this.txtImageDesc);
            this.groupControl2.Name = "groupControl2";
            // 
            // btnEditPhoto
            // 
            resources.ApplyResources(this.btnEditPhoto, "btnEditPhoto");
            this.btnEditPhoto.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("btnEditPhoto.Appearance.Font")));
            this.btnEditPhoto.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnEditPhoto.Appearance.FontSizeDelta")));
            this.btnEditPhoto.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnEditPhoto.Appearance.FontStyleDelta")));
            this.btnEditPhoto.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("btnEditPhoto.Appearance.ForeColor")));
            this.btnEditPhoto.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnEditPhoto.Appearance.GradientMode")));
            this.btnEditPhoto.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnEditPhoto.Appearance.Image")));
            this.btnEditPhoto.Appearance.Options.UseFont = true;
            this.btnEditPhoto.Appearance.Options.UseForeColor = true;
            this.btnEditPhoto.Image = global::Pharmacy.Properties.Resources.edit32;
            this.btnEditPhoto.Name = "btnEditPhoto";
            this.btnEditPhoto.TabStop = false;
            this.btnEditPhoto.Click += new System.EventHandler(this.btnEditPhoto_Click);
            // 
            // btnAddEmpPhoho
            // 
            resources.ApplyResources(this.btnAddEmpPhoho, "btnAddEmpPhoho");
            this.btnAddEmpPhoho.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddEmpPhoho.Name = "btnAddEmpPhoho";
            this.btnAddEmpPhoho.Click += new System.EventHandler(this.btnAddEmpPhoho_Click);
            // 
            // btnDeleteEmpPhoto
            // 
            resources.ApplyResources(this.btnDeleteEmpPhoto, "btnDeleteEmpPhoto");
            this.btnDeleteEmpPhoto.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("btnDeleteEmpPhoto.Appearance.Font")));
            this.btnDeleteEmpPhoto.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnDeleteEmpPhoto.Appearance.FontSizeDelta")));
            this.btnDeleteEmpPhoto.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnDeleteEmpPhoto.Appearance.FontStyleDelta")));
            this.btnDeleteEmpPhoto.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("btnDeleteEmpPhoto.Appearance.ForeColor")));
            this.btnDeleteEmpPhoto.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnDeleteEmpPhoto.Appearance.GradientMode")));
            this.btnDeleteEmpPhoto.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnDeleteEmpPhoto.Appearance.Image")));
            this.btnDeleteEmpPhoto.Appearance.Options.UseFont = true;
            this.btnDeleteEmpPhoto.Appearance.Options.UseForeColor = true;
            this.btnDeleteEmpPhoto.Image = global::Pharmacy.Properties.Resources.del;
            this.btnDeleteEmpPhoto.Name = "btnDeleteEmpPhoto";
            this.btnDeleteEmpPhoto.TabStop = false;
            this.btnDeleteEmpPhoto.Click += new System.EventHandler(this.btnDeleteEmpPhoto_Click);
            // 
            // btnShowPhotoes
            // 
            resources.ApplyResources(this.btnShowPhotoes, "btnShowPhotoes");
            this.btnShowPhotoes.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("btnShowPhotoes.Appearance.Font")));
            this.btnShowPhotoes.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnShowPhotoes.Appearance.FontSizeDelta")));
            this.btnShowPhotoes.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnShowPhotoes.Appearance.FontStyleDelta")));
            this.btnShowPhotoes.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnShowPhotoes.Appearance.GradientMode")));
            this.btnShowPhotoes.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnShowPhotoes.Appearance.Image")));
            this.btnShowPhotoes.Appearance.Options.UseFont = true;
            this.btnShowPhotoes.Name = "btnShowPhotoes";
            this.btnShowPhotoes.Click += new System.EventHandler(this.btnShowPhotoes_Click);
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl22.Appearance.Font")));
            this.labelControl22.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl22.Appearance.FontSizeDelta")));
            this.labelControl22.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl22.Appearance.FontStyleDelta")));
            this.labelControl22.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl22.Appearance.ForeColor")));
            this.labelControl22.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl22.Appearance.GradientMode")));
            this.labelControl22.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl22.Appearance.Image")));
            this.labelControl22.Name = "labelControl22";
            // 
            // txtImagePath
            // 
            resources.ApplyResources(this.txtImagePath, "txtImagePath");
            this.txtImagePath.EnterMoveNextControl = true;
            this.txtImagePath.Name = "txtImagePath";
            this.txtImagePath.Properties.AccessibleDescription = resources.GetString("txtImagePath.Properties.AccessibleDescription");
            this.txtImagePath.Properties.AccessibleName = resources.GetString("txtImagePath.Properties.AccessibleName");
            this.txtImagePath.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtImagePath.Properties.Appearance.Font")));
            this.txtImagePath.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtImagePath.Properties.Appearance.FontSizeDelta")));
            this.txtImagePath.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtImagePath.Properties.Appearance.FontStyleDelta")));
            this.txtImagePath.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtImagePath.Properties.Appearance.GradientMode")));
            this.txtImagePath.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtImagePath.Properties.Appearance.Image")));
            this.txtImagePath.Properties.Appearance.Options.UseFont = true;
            this.txtImagePath.Properties.AutoHeight = ((bool)(resources.GetObject("txtImagePath.Properties.AutoHeight")));
            this.txtImagePath.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtImagePath.Properties.Mask.AutoComplete")));
            this.txtImagePath.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.BeepOnError")));
            this.txtImagePath.Properties.Mask.EditMask = resources.GetString("txtImagePath.Properties.Mask.EditMask");
            this.txtImagePath.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.IgnoreMaskBlank")));
            this.txtImagePath.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtImagePath.Properties.Mask.MaskType")));
            this.txtImagePath.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtImagePath.Properties.Mask.PlaceHolder")));
            this.txtImagePath.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.SaveLiteral")));
            this.txtImagePath.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.ShowPlaceHolders")));
            this.txtImagePath.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtImagePath.Properties.NullValuePrompt = resources.GetString("txtImagePath.Properties.NullValuePrompt");
            this.txtImagePath.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtImagePath.Properties.NullValuePromptShowForEmptyValue")));
            this.txtImagePath.Properties.ReadOnly = true;
            // 
            // lstPhotos
            // 
            resources.ApplyResources(this.lstPhotos, "lstPhotos");
            this.lstPhotos.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lstPhotos.Appearance.Font")));
            this.lstPhotos.Appearance.FontSizeDelta = ((int)(resources.GetObject("lstPhotos.Appearance.FontSizeDelta")));
            this.lstPhotos.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lstPhotos.Appearance.FontStyleDelta")));
            this.lstPhotos.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lstPhotos.Appearance.GradientMode")));
            this.lstPhotos.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lstPhotos.Appearance.Image")));
            this.lstPhotos.Appearance.Options.UseFont = true;
            this.lstPhotos.Appearance.Options.UseTextOptions = true;
            this.lstPhotos.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lstPhotos.Name = "lstPhotos";
            this.lstPhotos.TabStop = false;
            // 
            // btnBrowse
            // 
            resources.ApplyResources(this.btnBrowse, "btnBrowse");
            this.btnBrowse.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("btnBrowse.Appearance.Font")));
            this.btnBrowse.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnBrowse.Appearance.FontSizeDelta")));
            this.btnBrowse.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnBrowse.Appearance.FontStyleDelta")));
            this.btnBrowse.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnBrowse.Appearance.GradientMode")));
            this.btnBrowse.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnBrowse.Appearance.Image")));
            this.btnBrowse.Appearance.Options.UseFont = true;
            this.btnBrowse.Image = global::Pharmacy.Properties.Resources.open;
            this.btnBrowse.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleRight;
            this.btnBrowse.Name = "btnBrowse";
            this.btnBrowse.Click += new System.EventHandler(this.btnBrowse_Click);
            // 
            // labelControl23
            // 
            resources.ApplyResources(this.labelControl23, "labelControl23");
            this.labelControl23.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl23.Appearance.Font")));
            this.labelControl23.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl23.Appearance.FontSizeDelta")));
            this.labelControl23.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl23.Appearance.FontStyleDelta")));
            this.labelControl23.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl23.Appearance.ForeColor")));
            this.labelControl23.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl23.Appearance.GradientMode")));
            this.labelControl23.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl23.Appearance.Image")));
            this.labelControl23.Name = "labelControl23";
            // 
            // txtImageDesc
            // 
            resources.ApplyResources(this.txtImageDesc, "txtImageDesc");
            this.txtImageDesc.EnterMoveNextControl = true;
            this.txtImageDesc.Name = "txtImageDesc";
            this.txtImageDesc.Properties.AccessibleDescription = resources.GetString("txtImageDesc.Properties.AccessibleDescription");
            this.txtImageDesc.Properties.AccessibleName = resources.GetString("txtImageDesc.Properties.AccessibleName");
            this.txtImageDesc.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtImageDesc.Properties.Appearance.Font")));
            this.txtImageDesc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtImageDesc.Properties.Appearance.FontSizeDelta")));
            this.txtImageDesc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtImageDesc.Properties.Appearance.FontStyleDelta")));
            this.txtImageDesc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtImageDesc.Properties.Appearance.GradientMode")));
            this.txtImageDesc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtImageDesc.Properties.Appearance.Image")));
            this.txtImageDesc.Properties.Appearance.Options.UseFont = true;
            this.txtImageDesc.Properties.Appearance.Options.UseTextOptions = true;
            this.txtImageDesc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtImageDesc.Properties.AutoHeight = ((bool)(resources.GetObject("txtImageDesc.Properties.AutoHeight")));
            this.txtImageDesc.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtImageDesc.Properties.Mask.AutoComplete")));
            this.txtImageDesc.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.BeepOnError")));
            this.txtImageDesc.Properties.Mask.EditMask = resources.GetString("txtImageDesc.Properties.Mask.EditMask");
            this.txtImageDesc.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.IgnoreMaskBlank")));
            this.txtImageDesc.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtImageDesc.Properties.Mask.MaskType")));
            this.txtImageDesc.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtImageDesc.Properties.Mask.PlaceHolder")));
            this.txtImageDesc.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.SaveLiteral")));
            this.txtImageDesc.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.ShowPlaceHolders")));
            this.txtImageDesc.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtImageDesc.Properties.NullValuePrompt = resources.GetString("txtImageDesc.Properties.NullValuePrompt");
            this.txtImageDesc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtImageDesc.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // frm_LC
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.xtraTabControl1);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrev);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_LC";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_FA_FixedAsset_FormClosing);
            this.Load += new System.EventHandler(this.frm_FA_FixedAsset_Load);
            this.Shown += new System.EventHandler(this.frm_FA_FixedAsset_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_FA_FixedAsset_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPort.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLcCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAmount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.tab_main.ResumeLayout(false);
            this.tab_main.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbStatus.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBillOfLading.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPayMethod.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShipMethod.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtShipDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtShipDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Crnc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpVendor.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdDepr)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCrnc)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtCloseDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtCloseDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOpenDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOpenDate.Properties)).EndInit();
            this.tab_docs.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtImagePath.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstPhotos)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtImageDesc.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl lblCloseDate;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.LabelControl lblNotes;
        private DevExpress.XtraEditors.LabelControl lblName;
        private DevExpress.XtraEditors.TextEdit txtName;
        private DevExpress.XtraEditors.LabelControl lblShipPort;
        private DevExpress.XtraEditors.TextEdit txtPort;
        private DevExpress.XtraBars.BarButtonItem barBtnList;
        private DevExpress.XtraEditors.LabelControl lblCode;
        private DevExpress.XtraEditors.TextEdit txtLcCode;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrev;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraEditors.LabelControl lblOpenDate;
        private DevExpress.XtraEditors.TextEdit txtAmount;
        private DevExpress.XtraEditors.LabelControl lblAmount;
        private DevExpress.XtraEditors.MemoEdit txtNotes;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage tab_docs;
        private DevExpress.XtraTab.XtraTabPage tab_main;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SimpleButton btnAddEmpPhoho;
        private DevExpress.XtraEditors.SimpleButton btnDeleteEmpPhoto;
        private DevExpress.XtraEditors.SimpleButton btnShowPhotoes;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.TextEdit txtImagePath;
        private DevExpress.XtraEditors.ListBoxControl lstPhotos;
        private DevExpress.XtraEditors.SimpleButton btnBrowse;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.TextEdit txtImageDesc;
        private DevExpress.XtraEditors.SimpleButton btnEditPhoto;
        private DevExpress.XtraEditors.DateEdit dtCloseDate;
        private DevExpress.XtraEditors.DateEdit dtOpenDate;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraGrid.GridControl grdDepr;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn colDebit;
        private DevExpress.XtraGrid.Columns.GridColumn colDate;
        private DevExpress.XtraGrid.Columns.GridColumn colSerial;
        private DevExpress.XtraGrid.Columns.GridColumn colCredit;
        private DevExpress.XtraGrid.Columns.GridColumn colTotalDebit;
        private DevExpress.XtraGrid.Columns.GridColumn colTotalCredit;
        private DevExpress.XtraEditors.GridLookUpEdit lkpVendor;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraEditors.LabelControl lblVendor;
        private DevExpress.XtraEditors.LabelControl lblCrnc;
        private DevExpress.XtraEditors.LookUpEdit lkp_Crnc;
        private DevExpress.XtraEditors.DateEdit dtDeliverDate;
        private DevExpress.XtraEditors.DateEdit dtShipDate;
        private DevExpress.XtraEditors.LabelControl lblDeliverDate;
        private DevExpress.XtraEditors.LabelControl lblShipDAte;
        private DevExpress.XtraEditors.TextEdit txtShipMethod;
        private DevExpress.XtraEditors.LabelControl lblShipMethod;
        private DevExpress.XtraEditors.TextEdit txtBillOfLading;
        private DevExpress.XtraEditors.LabelControl lblBillOfLanding;
        private DevExpress.XtraEditors.TextEdit txtPayMethod;
        private DevExpress.XtraEditors.LabelControl lblPayMethod;
        private DevExpress.XtraGrid.Columns.GridColumn colJournalcode;
        private DevExpress.XtraGrid.Columns.GridColumn colNotes;
        private DevExpress.XtraGrid.Columns.GridColumn colCurrency;
        private DevExpress.XtraGrid.Columns.GridColumn colCrncRate;
        private DevExpress.XtraEditors.LabelControl lblLcIsOpen;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbStatus;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repCrnc;
    }
}