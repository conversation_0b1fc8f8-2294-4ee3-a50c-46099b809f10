﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraPrinting;
using DAL;
using DAL.Res;
using System.Collections;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_SL_InvoiceArchive : DevExpress.XtraEditors.XtraForm
    {
        System.IO.MemoryStream rep_layout = new System.IO.MemoryStream();

        DateTime lastTime = DateTime.Now;

        UserPriv prvlg;
        bool DataModified;

        List<DAL.IC_UOM> uom_list;
        List<SL_Customer_Info> lst_Customers = new List<SL_Customer_Info>();
        List<IC_Store> stores_table;
        List<IC_Category> lst_Cat = new List<IC_Category>();
        List<ItemLkp> lstItems = new List<ItemLkp>();
        int JobOrderId = 0;
        List<JO_Status> lst_Status = new List<JO_Status>();
        List<JO_Priority> lst_Priority = new List<JO_Priority>();
        List<JO_Dept> lst_Dept = new List<JO_Dept>();

        DataTable dtSL_Details = new DataTable();
        DataTable dtUOM = new DataTable();
        DataTable dtPayAccounts = new DataTable();

        DataTable dtExpireQty = new DataTable();
        DataTable dtBatchQty = new DataTable();

        DataTable dt_SalesEmps = new DataTable();
        List<InvBomArchive> lst_invBom;

        DAL.ERPDataContext DB;

        public int invoiceId = 0;
        int userId = 0;
        int customerId = 0;

        DAL.ST_PrintInvoice printOptions;
        DAL.ST_CompanyInfo comp;

        DAL.IC_PriceLevel CustomerPriceLevel = null;
        bool? IsTaxable = null;

        bool Saved_Successfuly = false;
        IC_InTrn InTrns = null;

        int SrcProcessId;
        int SourceId;
        string sourceCode;

        public frm_SL_InvoiceArchive()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        public frm_SL_InvoiceArchive(int invoiceId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.invoiceId = invoiceId;
        }

        public frm_SL_InvoiceArchive(int invoiceId, int CustomerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.invoiceId = invoiceId;
            this.customerId = CustomerId;
        }

        public frm_SL_InvoiceArchive(int SrcProcessId, int SourceId, string sourceCode)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            cmdProcess.EditValue = this.SrcProcessId = SrcProcessId;
            btnSourceId.EditValue = this.SourceId = SourceId;
            txtSourceCode.Text = this.sourceCode = sourceCode;
        }

        public frm_SL_InvoiceArchive(IC_InTrn _intrns)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            InTrns = _intrns;
        }

        private void frm_SL_Invoice_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
            {
                RTL.LTRLayout(this);
                //flowLayoutPanel1.FlowDirection = FlowDirection.LeftToRight;
                //flowLayoutPanel2.FlowDirection = FlowDirection.LeftToRight;
            }
            else
            {
                //flowLayoutPanel1.FlowDirection = FlowDirection.RightToLeft;
                //flowLayoutPanel2.FlowDirection = FlowDirection.RightToLeft;
            }

            if (Shared.user.AccessType != (byte)AccessType.Admin)
                chk_IsPosted.Visible = txt_Post_Date.Visible = false;

            #region Available_Modules
            if (Shared.CurrencyAvailable == false)
                pnlCurrency.Visible = false;
            if (Shared.SalesManAvailable == false)
                pnlSalesEmp.Visible = false;
            if (Shared.JobOrderAvailable == false)
            {
                page_JobOrder.Visible = false;
                barBtnLoad_JO.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }

            if (!Shared.ChecksAvailable)
                barBtnNotesReceivable.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            if (Shared.user.UserChangeCostCenterInInv == false)
                pnlCostCenter.Visible = false;

            if (Shared.InvoicePostToStore)
            {
                barBtn_ConvertTo.Visibility = barBtn_OutTrns.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
                chk_IsOutTrns.Visible = false;
            }
            if (Shared.ItemsPostingAvailable)
                txtExpenses.Enabled = txtExpensesR.Enabled = false;

            if (!Shared.RealEstateAvailable)
            {
                txt_AdvancePayV.Visible = txt_AdvancePayR.Visible = txt_retentionR.Visible = txt_RetentionV.Visible = labelControl37.Visible = labelControl38.Visible =
                    labelControl39.Visible = labelControl42.Visible = labelControl43.Visible = labelControl45.Visible = labelControl44.Visible = labelControl46.Visible = false;
            }


            #endregion

            LoadPrivilege();

            grdPrInvoice.ProcessGridKey += new KeyEventHandler(grid_ProcessGridKey);
            lkp_Customers.Properties.View.Columns["CusNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;

            //ErpUtils.Allow_Incremental_Search(repItems);
            if (Shared.user.UseContainsToSearchItems)
                repItems.PopupFilterMode = PopupFilterMode.Contains;
            else
                repItems.PopupFilterMode = PopupFilterMode.StartsWith;

            //ErpUtils.Allow_Incremental_Search(repUOM);
            //ErpUtils.Allow_Incremental_Search(lkp_Customers);
            repItems.View.Columns["ItemNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemNameEn"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemCode1"].SortIndex = 0;

            lkp_Drawers.Properties.TextEditStyle = lkp_Drawers2.Properties.TextEditStyle =
                lkpStore.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;


            DB = new DAL.ERPDataContext();

            BindDataSources();

            LoadInvoice();


            printOptions = DB.ST_PrintInvoices.FirstOrDefault();
            comp = DB.ST_CompanyInfos.FirstOrDefault();

            #region hide and disable controls
            if (Shared.user.SL_Invoice_PayMethod == false)
            {
                groupControl1.Visible = false;
                cmbPayMethod.Properties.Items.RemoveAt(2);
                cmbPayMethod.Enabled = true;
                cmbPayMethod.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
                cmbPayMethod.Properties.Buttons[0].Visible = true;
                lbl_Paid.Visible = txt_paid.Visible = lbl_remains.Visible = txt_Remains.Visible = false;
            }

            if (Shared.user.HideDeliverDate)
                pnlDeliveryDate.Visible = false;
            if (Shared.user.HideAgedReceivables)
                pnlAgeDate.Visible = false;

            if (Shared.user.HideShipTo)
                lblShipTo.Visible = txt_Shipping.Visible = false;
            if (Shared.user.HidePO)
                pnlPO.Visible = false;
            if (Shared.user.HideSalesEmp)
                pnlSalesEmp.Visible = false;

            if (Shared.user.SL_Invoice_PayMethod == true &&
                Shared.user.UserEditTransactionDate == false)
                dtInvoiceDate.Enabled = false;
            #endregion

            repItems.CloseUp += new DevExpress.XtraEditors.Controls.CloseUpEventHandler(this.repItems_CloseUp);

            ErpUtils.Load_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""));
            ErpUtils.Load_MemoryStream_Layout(rep_layout, this.Name + ".repItems");
            ErpUtils.ColumnChooser(grdPrInvoice);
            ErpUtils.ColumnChooser(grdLastPrices);

            #region Hide_Show_Columns
            if (!Shared.st_Store.UseHeightDimension)
                col_Height.OptionsColumn.ShowInCustomizationForm = col_Height.Visible = false;
            if (!Shared.st_Store.UseWidthDimension)
                col_Width.OptionsColumn.ShowInCustomizationForm = col_Width.Visible = false;
            if (!Shared.st_Store.UseLengthDimension)
                col_Length.OptionsColumn.ShowInCustomizationForm = col_Length.Visible = false;
            if (!Shared.st_Store.UseHeightDimension && !Shared.st_Store.UseWidthDimension && !Shared.st_Store.UseLengthDimension)
                col_TotalQty.OptionsColumn.ShowInCustomizationForm = col_TotalQty.Visible = false;
            if (!Shared.st_Store.PiecesCount)
                col_PiecesCount.OptionsColumn.ShowInCustomizationForm = col_PiecesCount.Visible = false;
            //if (!Shared.user.Sell_ShowCrntQty)
            //    col_CurrentQty.OptionsColumn.ShowInCustomizationForm = col_CurrentQty.Visible = false;
            if (!Shared.user.UserEditSalePrice)
                col_SellPrice.OptionsColumn.AllowEdit = col_SellPrice.OptionsColumn.AllowFocus = false;
            if (Shared.user.HidePurchasePrice)
                colPurchasePrice.OptionsColumn.ShowInCustomizationForm = colPurchasePrice.Visible = false;
            if (Shared.user.HideItemDiscount)
            {
                gridView2.Columns["DiscountRatio"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountRatio"].Visible = false;
                gridView2.Columns["DiscountRatio2"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountRatio2"].Visible = false;
                gridView2.Columns["DiscountRatio3"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountRatio3"].Visible = false;
                gridView2.Columns["DiscountValue"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountValue"].Visible = false;
            }
            if (!Shared.TaxAvailable)
            {
                col_SalesTax.OptionsColumn.ShowInCustomizationForm = col_SalesTax.Visible = false;
                col_CusTax.OptionsColumn.ShowInCustomizationForm = col_CusTax.Visible = false;
            }

            //batch and expire in settings and also user profile
            if (!Shared.st_Store.ExpireDate)
                col_Expire.OptionsColumn.ShowInCustomizationForm = col_Expire.Visible = false;
            if (!Shared.st_Store.Batch)
                col_Batch.OptionsColumn.ShowInCustomizationForm = col_Batch.Visible = false;

            if (Shared.user.HideBatchColumn4User == true)
                col_Expire.OptionsColumn.ShowInCustomizationForm = col_Expire.Visible =
                col_Batch.OptionsColumn.ShowInCustomizationForm = col_Batch.Visible = false;

            if (!Shared.user.UserCanEditQty)
            {
                colQty.OptionsColumn.AllowEdit = colQty.OptionsColumn.AllowFocus = false;
                col_PiecesCount.OptionsColumn.AllowEdit = col_PiecesCount.OptionsColumn.AllowFocus = false;
            }

            if (!Shared.st_Store.Serial)
                col_Serial.OptionsColumn.ShowInCustomizationForm = col_Serial.Visible = false;

            col_Serial.Caption = Shared.IsEnglish ? Shared.st_Store.SerialNameEn : Shared.st_Store.SerialNameAr;
            col_Batch.Caption = Shared.IsEnglish ? Shared.st_Store.BatchNameEn : Shared.st_Store.BatchNameAr;
            #endregion

            if (Shared.st_Store.SalesDeductTaxAccount == null || Shared.TaxAvailable == false)
                txt_DeductTaxR.Enabled = txt_DeductTaxV.Enabled = false;

            if (Shared.st_Store.SalesAddTaxAccount == null || Shared.TaxAvailable == false)
                txt_AddTaxR.Enabled = txt_AddTaxV.Enabled = false;

            txt_TaxValue.BackColor = Color.White;
            txt_TaxValue.ForeColor = Color.DimGray;

            txtInvoiceCode.Leave += new EventHandler(txtInvoiceCode_Leave);

            if (Shared.st_Store.InvoiceWorkflow == (int)InvoiceWorkflow.BillThenInv)
            {
                barBtnLoad_SalesOrder.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
                barBtn_ConvertTo.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;

                barBtnLoad_IC_Transfer.Visibility = barBtnLoad_PR_Invoice.Visibility = barBtnLoad_Sl_Qoute.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            if (Shared.st_Store.InvoiceWorkflow == (int)InvoiceWorkflow.InvThenBill)
            {
                barBtnLoad_IC_OutTrns.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
                barBtnLoad_IC_Transfer.Visibility = barBtnLoad_PR_Invoice.Visibility = barBtnLoad_Sl_Qoute.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;

            }
            grdcol_branch.Visible = false;
            lkp_storee.DataSource = DB.IC_Stores.Select(s => new { s.StoreNameAr, s.StoreId }).ToList();
            lkp_storee.DisplayMember = "StoreNameAr";
            lkp_storee.ValueMember = "StoreId";

            // if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)//CANNOT FIND OBJECT with IsStoreOnEachSellRecord value= false
            if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
            {
                lkpStore.Enabled = false;
                lkpStore.EditValue = null;

                grdcol_branch.Visible = true;
                gridView2.FocusedColumn = grdcol_branch;

            }

            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
        }

        private void frm_SL_Invoice_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrevious.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                lkp_Customers.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                txtNotes.Focus();
                FocusItemCode1(true);
            }
            if (e.KeyCode == Keys.End && e.Modifiers == Keys.Control)
            {
                txtDiscountRatio.Focus();
            }
            if (e.KeyCode == Keys.F10)
            {
                Saved_Successfuly = false;
                barBtnSave.PerformClick();
                if (Saved_Successfuly == true)
                {
                    barBtnNew.PerformClick();
                    txtNotes.Focus();
                    FocusItemCode1(Shared.user.FocusGridInInvoices);
                }
            }
        }

        private void frm_SL_Invoice_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
            else
                e.Cancel = false;

            ErpUtils.save_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""), true);
            ErpUtils.save_MemoryStream_Layout(rep_layout, this.Name + ".repItems");

        }

        private void gridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (e.Column.FieldName == "LargeUOMFactor"
                || e.Column.FieldName == "MediumUOMFactor" || e.Column.FieldName == "TotalSellPrice" /*|| e.Column.FieldName == "CurrentQty"*/)
                return;

            DB = new DAL.ERPDataContext();
            DAL.IC_Item item = null;

            GridView view = grdPrInvoice.FocusedView as GridView;
            DataRow row = view.GetDataRow(e.RowHandle);

            #region Barcode_Init_Detail_PR

            Detail_PR detail_PR = new Detail_PR
            {
                ItemId = (int)0,
                Batch = "",
                Expire = (DateTime?)DateTime.Now,
                Length = (decimal?)1,
                Width = (decimal?)1,
                Height = (decimal?)1,
                PiecesCount = (decimal)0,
                PurchasePrice = (decimal)0,
                TotalPurchasePrice = (decimal)0,
                SellPrice = (decimal)0,
                Qty = (decimal)0,
                UOMId = (int)0,
                UOMIndex = (byte)0,
                VendorId = (int?)0
            };
            detail_PR = null;
            #endregion

            int barcodeTemplateCode = 0;
            decimal barcodeTemplateQty = 0;
            string barcodeBatch = string.Empty;
            string code1 = string.Empty;


            #region GetItem
            if (e.Column.FieldName == "ItemCode1")
            {
                if (view.GetFocusedRowCellValue("ItemCode1") != null && view.GetFocusedRowCellValue("ItemCode1").ToString() != string.Empty)
                {
                    code1 = view.GetFocusedRowCellValue("ItemCode1").ToString();

                    item = MyHelper.SearchItem(DB, code1, detail_PR, ref barcodeTemplateCode, ref barcodeTemplateQty, ref barcodeBatch, invoiceId,
                        Shared.st_Store);
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode2")
            {
                if (view.GetFocusedRowCellValue("ItemCode2").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)DAL.ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                  i.ItemType == (int)DAL.ItemType.Assembly : true
                            where i.ItemCode2.Contains(view.GetFocusedRowCellValue("ItemCode2").ToString())
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).FirstOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemId")
            {
                if (view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                      i.ItemType == (int)ItemType.Assembly : true
                            where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).SingleOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "ItemId")
            {
                if (item != null && item.ItemId > 0)
                {
                    if (item.ItemType == (int)ItemType.Subtotal)
                    {
                        row["ItemId"] = item.ItemId;
                        row["ItemCode1"] = item.ItemCode1;
                        row["ItemCode2"] = item.ItemCode2;
                        row["ItemType"] = item.ItemType;
                        row["IsExpire"] = item.IsExpire;
                        row["ItemDescription"] = item.Description;
                        row["ItemDescriptionEn"] = item.DescriptionEn;
                        row["CategoryId"] = item.Category;
                        Get_SubTotal_RowData(row, view, e.RowHandle);
                    }
                    else
                    {
                        LoadItemRow(item, row);
                        if (Shared.st_Store.PrintBarcodePerInventory && detail_PR != null)
                        {
                            row["PurchasePrice"] = Decimal.ToDouble(detail_PR.TotalPurchasePrice / (detail_PR.Qty));
                            row["PiecesCount"] = detail_PR.PiecesCount;
                            row["Length"] = detail_PR.Length.HasValue ? decimal.ToDouble(detail_PR.Length.Value) : 1;
                            row["Width"] = detail_PR.Width.HasValue ? decimal.ToDouble(detail_PR.Width.Value) : 1;
                            row["Height"] = detail_PR.Height.HasValue ? decimal.ToDouble(detail_PR.Height.Value) : 1;
                            if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                                row["Qty"] = decimal.ToDouble(detail_PR.Qty);

                            row["Batch"] = detail_PR.Batch;

                            if (detail_PR.Expire.HasValue)
                            {
                                MyHelper.GetExpireQtyDataTable(dtExpireQty);
                                row["ExpireDate"] = detail_PR.Expire.Value;
                                row["Expire"] = detail_PR.Expire.Value + detail_PR.Batch;
                            }
                            else
                            {
                                row["ExpireDate"] = DBNull.Value;
                                row["Expire"] = DBNull.Value;
                            }
                        }

                        if (Shared.st_Store.PrintBarcodePerInventory == false && barcodeTemplateCode > 0 && barcodeTemplateQty > 0)
                        {
                            row["Qty"] = decimal.ToDouble(barcodeTemplateQty);
                            row["Batch"] = barcodeBatch;
                            row["PiecesCount"] = 1;
                        }
                    }
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                    return;
                }
            }
            #endregion

            if (view.GetRowCellValue(e.RowHandle, "ItemType") != null && Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemType")) == (int)ItemType.Subtotal)
            {
                Update_First_SubTotal(view, e.RowHandle);
                return;
            }

            #region GetUomPrice
            if (e.Column.FieldName == "UOM")
            {
                if (view.GetFocusedRowCellValue("UOM").ToString() != string.Empty &&
                    view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                    int uomIndex = Convert.ToInt32(ErpUtils.GetGridLookUpValue(repUOM, view.GetRowCellValue(e.RowHandle, "UOM"), "Index"));

                    //get UOM and Factor, multiple by purchase and sell prices
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                      i.ItemType == (int)ItemType.Assembly : true
                            where i.ItemId == itmId
                            select i).SingleOrDefault();

                    decimal uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, uomIndex);
                    view.GetDataRow(e.RowHandle)["SellPrice"] = decimal.ToDouble(uom_price);

                    if (uomIndex == 0)//small                    
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.ToDouble(item.PurchasePrice);
                    if (uomIndex == 1)//medium                                           
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.ToDouble(item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor));
                    if (uomIndex == 2)//large
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.ToDouble(item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor));

                    view.GetDataRow(e.RowHandle)["UomIndex"] = uomIndex;

                }
            }
            #endregion

            #region ItemQtyEquation
            if (e.Column.FieldName == "Qty" || e.Column.FieldName == "UOM")
            {
                if (view.GetFocusedRowCellValue("UOM").ToString() != string.Empty &&
                    view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty &&
                    view.GetFocusedRowCellValue("Qty").ToString() != string.Empty)
                {
                    int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                    byte uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));
                    decimal medium = 1;
                    decimal large = 1;
                    medium = MyHelper.FractionToDouble(view.GetFocusedRowCellValue("MediumUOMFactor").ToString());
                    large = MyHelper.FractionToDouble(view.GetFocusedRowCellValue("LargeUOMFactor").ToString());
                    decimal Qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                    if (!(CustomerPriceLevel != null && (CustomerPriceLevel.IsRatio == true || (CustomerPriceLevel.IsRatio == false
                                                      && CustomerPriceLevel.IC_PriceLevelDetails.Where(x => x.ItemId == itmId).Count() > 0))))
                    {
                        decimal sellprice = MyHelper.Get_ItemPricesPerQty(itmId, Qty, uomIndex, medium, large);
                        if (sellprice > 0)
                            view.GetDataRow(e.RowHandle)["SellPrice"] = decimal.ToDouble(sellprice);
                    }
                }
            }
            #endregion

            #region Calculate Prices
            if (e.Column.FieldName == "DiscountValue" || e.Column.FieldName == "SellPrice"
                || e.Column.FieldName == "Qty" || e.Column.FieldName == "ItemId"
                || e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "UOM"
                || e.Column == col_Height || e.Column == col_Width || e.Column == col_Length || e.Column.FieldName == "Batch")
            {
                try
                {
                    if (!string.IsNullOrEmpty(view.GetFocusedRowCellValue("SellPrice").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("DiscountValue").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("Qty").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("ItemId").ToString()))
                    {
                        decimal Height = Convert.ToDecimal(view.GetFocusedRowCellValue("Height"));
                        decimal Length = Convert.ToDecimal(view.GetFocusedRowCellValue("Length"));
                        decimal Width = Convert.ToDecimal(view.GetFocusedRowCellValue("Width"));
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                            Height = Length = Width = 1;

                        decimal Qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                        decimal TotalQty = Qty * Height * Width * Length;
                        decimal SellPrice = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                        decimal PurchasePrice = Convert.ToDecimal(view.GetFocusedRowCellValue("PurchasePrice"));
                        int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                        byte uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));
                        decimal SalesTaxRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["SalesTaxRatio"]);
                        decimal CustomTaxRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["CustomTaxRatio"]);
                        bool calcTaxBeforeDisc = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);

                        decimal DiscV = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountValue"]);
                        decimal TotalSellPrice = (TotalQty * SellPrice) - DiscV;
                        if (Shared.st_Store.PriceIncludeSalesTax)/*السعر شامل الضريبة*/
                        {
                            decimal temp = calcTaxBeforeDisc ? (SalesTaxRatio * TotalQty * SellPrice) / (1 + SalesTaxRatio) :
                                (SalesTaxRatio * TotalSellPrice) / (1 + SalesTaxRatio);
                            view.SetRowCellValue(e.RowHandle, "SalesTax", decimal.ToDouble(temp));
                            view.GetDataRow(e.RowHandle)["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice - temp);// السعر الاجمالي شامل الضريبة                            


                            var totalsellp = (TotalSellPrice - temp);
                            // for custom tax
                            decimal temp2 = calcTaxBeforeDisc ? (CustomTaxRatio * TotalQty * SellPrice) / (1 + CustomTaxRatio) :
                               (CustomTaxRatio * totalsellp) / (1 + CustomTaxRatio);
                            view.SetRowCellValue(e.RowHandle, "CustomTax", decimal.ToDouble(temp2));
                            view.GetDataRow(e.RowHandle)["TotalSellPrice"] = decimal.ToDouble(totalsellp - temp2);// السعر الاجمالي شامل الضريبة                            



                        }
                        else                                            /*السعر غير شامل الضريبة*/
                        {
                            decimal temp = calcTaxBeforeDisc ? (SalesTaxRatio * TotalQty * SellPrice) : (SalesTaxRatio * TotalSellPrice);
                            view.SetRowCellValue(e.RowHandle, "SalesTax", decimal.ToDouble(temp));
                            view.GetDataRow(e.RowHandle)["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);//ضيف الضريبة على السعر الاجمالي                         
                            var totalsellp = (TotalSellPrice);
                            decimal temp2 = calcTaxBeforeDisc ? (CustomTaxRatio * TotalQty * SellPrice) : (CustomTaxRatio * totalsellp);
                            view.SetRowCellValue(e.RowHandle, "CustomTax", decimal.ToDouble(temp2));
                            view.GetDataRow(e.RowHandle)["TotalSellPrice"] = decimal.ToDouble(totalsellp);//ضيف الضريبة على السعر الاجمالي                            
                        }

                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.DistinguishAndMultiply)
                            view.GetDataRow(e.RowHandle)["PiecesCount"] = Qty;

                        //get store qty
                        decimal medium = 1;
                        decimal large = 1;
                        if (view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString() != string.Empty)
                            medium = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString());
                        if (view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString() != string.Empty)
                            large = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString());
                        int store_id;

                        //  if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                        if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                        {
                            if (gridView2.GetFocusedRowCellValue("StoreId") != DBNull.Value)
                                store_id = Convert.ToInt32(gridView2.GetFocusedRowCellValue("StoreId"));
                            else
                                store_id = 0;
                        }
                        else
                        {
                            store_id = Convert.ToInt32(lkpStore.EditValue);
                        }
                        Calculate_Qty(itmId, e, store_id, uomIndex, medium, large, view.GetDataRow(e.RowHandle)["Batch"].ToString());

                        Get_TotalAccount();
                    }
                }
                catch (Exception f)
                {
                    MessageBox.Show(f.Message);
                }
            }
            #endregion

            #region DiscountRation_changed_by_User
            if (e.Column.FieldName == "DiscountRatio" || e.Column.FieldName == "DiscountRatio2" || e.Column.FieldName == "DiscountRatio3"
                || e.Column.FieldName == "Qty" || e.Column.FieldName == "SellPrice" || e.Column.FieldName == "UOM")
            {
                try
                {
                    if (!string.IsNullOrEmpty(view.GetFocusedRowCellValue(e.Column.FieldName).ToString()))
                    {
                        decimal Height = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Height"));
                        decimal Length = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Length"));
                        decimal Width = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Width"));
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                            Height = Length = Width = 1;

                        decimal Qty = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Qty"));
                        decimal TotalQty = Qty * Height * Width * Length;

                        decimal SellPrice = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "SellPrice"));
                        decimal DiscR1 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio"]);
                        decimal DiscR2 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio2"]);
                        decimal DiscR3 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio3"]);

                        bool calcTaxBeforeDisc = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);
                        decimal salesTax = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["SalesTax"]);
                        decimal CustomTax = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["CustomTax"]);
                        decimal totalSellP = calcTaxBeforeDisc ? ((SellPrice * TotalQty) - salesTax) : SellPrice * TotalQty;
                        totalSellP = calcTaxBeforeDisc ? ((SellPrice * TotalQty) - CustomTax) : SellPrice * TotalQty;
                        decimal DiscountValue = Utilities.Calc_DiscountValue(DiscR1, DiscR2, DiscR3, totalSellP);
                        view.SetRowCellValue(e.RowHandle, "DiscountValue", decimal.ToDouble(DiscountValue));
                    }
                }
                catch { }
            }
            #endregion

            if (e.Column.FieldName == "Expire")
            {
                try
                {
                    view.SetRowCellValue(e.RowHandle, col_Batch, ErpUtils.GetGridLookUpValue(rep_expireDate, view.FocusedValue, "Batch"));
                    if (ErpUtils.GetGridLookUpValue(rep_expireDate, view.FocusedValue, "Expire") != null)
                        view.GetDataRow(e.RowHandle)["ExpireDate"] = ErpUtils.GetGridLookUpValue(rep_expireDate, view.FocusedValue, "Expire");
                    else
                        view.GetDataRow(e.RowHandle)["ExpireDate"] = DBNull.Value;
                }
                catch { }
            }

            if (e.Column.FieldName == "StoreId")
            {
                decimal medium = 1;
                decimal large = 1;
                int itmId;
                int store_id;
                byte uomIndex;

                if (view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString() != string.Empty)
                    medium = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString());
                if (view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString() != string.Empty)
                    large = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString());

                if (view.GetFocusedRowCellValue("ItemId") is DBNull)
                {
                    itmId = 0;
                    uomIndex = 0;
                }
                else
                {
                    itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                    uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));

                }
                // byte uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));

                if (view.GetFocusedRowCellValue("StoreId") is DBNull)
                {
                    store_id = 0;
                }
                else
                {
                    store_id = Convert.ToInt32(view.GetFocusedRowCellValue("StoreId"));
                }
                // store_id = Convert.ToInt32(view.GetFocusedRowCellValue("StoreId"));

                Calculate_Qty(itmId, e, store_id, uomIndex, medium, large, view.GetRowCellValue(e.RowHandle, "Batch").ToString());
            }

            //if (e.Column.FieldName == "Batch")
            //{
            //    int store_id;

            //    //  if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
            //    if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
            //    {
            //        if (gridView2.GetFocusedRowCellValue("StoreId") != DBNull.Value)
            //            store_id = Convert.ToInt32(gridView2.GetFocusedRowCellValue("StoreId"));
            //        else
            //            store_id = 0;
            //    }
            //    else
            //    {
            //        store_id = Convert.ToInt32(lkpStore.EditValue);
            //    }
            //    view.GetFocusedDataRow()["CurrentQty"] = MyHelper.GetItemQty(dtInvoiceDate.DateTime, Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"), store_id, view.GetDataRow(e.RowHandle)["Batch"]);//Convert.ToInt32(ErpUtils.GetGridLookUpValue(rep_Batch, view.GetDataRow(e.RowHandle)["Batch"], "Qty"));
            //}
            Update_First_SubTotal(view, e.RowHandle);
        }
        private void gridView1_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "UOM")
            {
                DataRow row = view.GetFocusedDataRow();

                DB = new DAL.ERPDataContext();
                DAL.IC_Item item = new DAL.IC_Item();

                if (row != null && row["ItemId"].ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                      i.ItemType == (int)ItemType.Assembly : true
                            where i.ItemId == Convert.ToInt32(row["ItemId"])
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).SingleOrDefault();

                    MyHelper.GetUOMs(item, dtUOM, uom_list);

                    if (string.IsNullOrEmpty(row["UOM"].ToString()))
                        view.GetFocusedDataRow()["UOM"] = dtUOM.Rows[0]["UomId"];
                }
            }
            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "Expire")
            {
                DataRow row = view.GetFocusedDataRow();
                if (row != null && row["ItemId"].ToString() != string.Empty && Convert.ToBoolean(row["IsExpire"]))
                {
                    MyHelper.Get_Expire_Qtys(Convert.ToInt32(row["ItemId"]), Convert.ToInt32(lkpStore.EditValue), dtInvoiceDate.DateTime, dtExpireQty);

                    if (dtExpireQty.Rows.Count <= 0)
                    {
                        view.GetFocusedDataRow()["ExpireDate"] = DBNull.Value;
                        view.GetFocusedDataRow()["Expire"] = DBNull.Value;
                        view.GetFocusedDataRow()["Batch"] = DBNull.Value;
                    }
                }
            }
            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "Batch")
            {
                DataRow row = view.GetFocusedDataRow();
                if (row != null && row["ItemId"].ToString() != string.Empty)
                {
                    MyHelper.Get_Batch_Qtys(Convert.ToInt32(row["ItemId"]), Convert.ToInt32(lkpStore.EditValue), dtInvoiceDate.DateTime, dtBatchQty);

                }
            }

        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            GridView view = sender as GridView;
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                //if (MessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDelRow : ResSLAr.MsgDelRow,
                //    Shared.IsEnglish ? ResSLEn.MsgTQues : ResSLAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                //  DialogResult.Yes)
                //    return;

                view.DeleteRow(view.FocusedRowHandle);

                Update_First_SubTotal(view, view.FocusedRowHandle);

                Get_TotalAccount();
            }
            if (e.KeyCode == Keys.Up && e.Control && e.Shift)
            {
                ErpUtils.Move_Row_Up(view);
                Update_First_SubTotal(view, view.FocusedRowHandle - 1);
                Update_First_SubTotal(view, view.FocusedRowHandle + 1);
            }
            if (e.KeyCode == Keys.Down && e.Control && e.Shift)
            {
                ErpUtils.Move_Row_Down(view);
                Update_First_SubTotal(view, view.FocusedRowHandle - 1);
                Update_First_SubTotal(view, view.FocusedRowHandle + 2);
            }
        }

        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            try
            {
                ColumnView view = sender as ColumnView;

                if (Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Subtotal)
                    return;

                int itemid = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"));
                byte uomIndex = Convert.ToByte(view.GetRowCellValue(e.RowHandle, "UomIndex"));

                if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["ItemId"], Shared.IsEnglish ? ResSLEn.txtValidateItem : ResSLAr.txtValidateItem);//"يجب اختيار الصنف";
                }
                //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                {
                    if (view.GetRowCellValue(e.RowHandle, view.Columns["StoreId"]).ToString() == string.Empty)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["StoreId"], Shared.IsEnglish ? ResPrEn.ValBranch : ResPrEn.ValBranch);//"يجب اختيار الفرع";
                    }
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["UOM"], Shared.IsEnglish ? ResSLEn.txtValidateUom : ResSLAr.txtValidateUom);//"يجب اختيار وحدة القياس");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])) <= 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Qty"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }



                #region Validate Height Length and Width
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Height"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Height"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Height"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Height"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Length"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Length"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Length"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Length"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Width"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Width"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Width"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Width"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"])) < 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }

                #endregion

                if (view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"])) < 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["SellPrice"], Shared.IsEnglish ? ResSLEn.txtValidateSPrice : ResSLAr.txtValidateSPrice);//"سعر البيع يجب أن يكون أكبر من الصفر");
                    return;
                }
                if ((Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["PurchasePrice"])) >=
                    Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"])))
                    && Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) != (int)ItemType.Service
                    && Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) != (int)ItemType.Subtotal)
                {
                    if (Shared.user.SellLessThanBuyPrice == true)
                        XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateSpriceLargerPprice : ResSLAr.txtValidateSpriceLargerPprice,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    else if (Shared.user.SellLessThanBuyPrice == false)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["PurchasePrice"], Shared.IsEnglish ? ResSLEn.txtValidateSpriceLargerPprice : ResSLAr.txtValidateSpriceLargerPprice);// "سعر الشراء يجب أن يكون أقل من سعر البيع");
                        view.SetColumnError(view.Columns["SellPrice"], Shared.IsEnglish ? ResSLEn.txtValidateSpriceLargerPprice : ResSLAr.txtValidateSpriceLargerPprice);//"سعر البيع يجب أن يكون أكبر من سعر الشراء");
                    }
                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountValue"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountValue"], Shared.IsEnglish ? ResSLEn.txtValidateDiscount : ResSLAr.txtValidateDiscount);//"يجب تحديد الخصم");
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio2"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio2"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio2"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio3"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio3"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio3"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }

                if (Shared.InvoicePostToStore)
                {
                    int str_id;
                    int item_id = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"));
                    int uom_id = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "UOM"));
                    decimal factor = 1;
                    if (uomIndex == 1)
                        factor = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "MediumUOMFactor"));
                    else if (uomIndex == 2)
                        factor = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "LargeUOMFactor"));



                    //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                    if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                        str_id = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "StoreId"));
                    else
                        str_id = Convert.ToInt32(lkpStore.EditValue);




                    decimal length = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Length"));
                    decimal width = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Width"));
                    decimal height = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Height"));

                    decimal Qty = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Qty")) * factor;
                    //decimal CurrentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item_id, str_id, view.GetRowCellValue(e.RowHandle,"Batch").ToString());
                    //if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                    //    CurrentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item_id, str_id, length, width, height,view.GetRowCellValue(e.RowHandle, "Batch").ToString());

                    #region Can't Sell over current Qty
                    //if (Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) != (int)ItemType.Service
                    //        && Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) != (int)ItemType.Subtotal
                    //        //&& invoiceId == 0
                    //        && Qty > CurrentQty)//validate when new invoice only
                    //{
                    //    if (Shared.user.SellWithNoBalance == true)
                    //    {
                    //        XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateStoreQty : ResSLAr.txtValidateStoreQty,
                    //               Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    //        return;
                    //    }
                    //    else if (Shared.user.SellWithNoBalance == false)
                    //    {
                    //        e.Valid = false;
                    //        view.SetColumnError(view.Columns["Qty"], Shared.IsEnglish ? ResSLEn.txtValidateStoreQty : ResSLAr.txtValidateStoreQty);//"الكمية لا يمكن ان تكون اكبر من الرصيدالحالي");
                    //    }
                    //}
                    #endregion

                    #region Validate_Min_Qty_In_Store

                    //if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString() != string.Empty
                    //    && view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() != string.Empty
                    //    && invoiceId == 0)//validate minimum qty only in new invoice
                    //{
                    //    // Get max Qty can be in the store
                    //    int minqty = Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, itemid, "MinQty"));

                    //    if (minqty <= 0 || Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Service
                    //        || Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Subtotal
                    //        || Shared.user.SellUnderMinQty == null)
                    //    { }
                    //    else
                    //    {
                    //        // Check if store_qty and qty to buy is less than max qty can be in the store
                    //        if ((CurrentQty - Qty) < minqty)
                    //        {
                    //            if (Shared.user.SellUnderMinQty == true)
                    //            {
                    //                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateMinQty : ResSLAr.txtValidateMinQty,// "الكمية الموجودة حاليا في المخزن بعد خصم الكمية المباعة أقل من الحد الأدنى للكمية",
                    //                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    //                return;
                    //            }
                    //            else if (Shared.user.SellUnderMinQty == false)
                    //            {
                    //                e.Valid = false;
                    //                view.SetColumnError(view.Columns["Qty"], Shared.IsEnglish ? ResSLEn.txtValidateMinQty : ResSLAr.txtValidateMinQty);// "الكمية الموجودة حاليا في المخزن بعد خصم الكمية المباعة أقل من الحد الأدنى للكمية",
                    //            }
                    //        }
                    //    }
                    //}
                    #endregion

                    #region Validate_Reorder_Level
                    //if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString() != string.Empty
                    //    && view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() != string.Empty
                    //    && invoiceId == 0)//validate minimum qty only in new invoice
                    //{
                    //    int reorder = Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, itemid, "ReorderLevel"));
                    //    if (reorder <= 0 || (Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Service
                    //        && Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Subtotal))
                    //    { }
                    //    else
                    //    {
                    //        if ((CurrentQty - Qty) < reorder)
                    //        {
                    //            if (Shared.user.SellReorder == true)
                    //            {
                    //                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateReorderQty : ResSLAr.txtValidateReorderQty,//الكمية الموجودة حاليا في المخزن بعد خصم الكمية المباعة أقل من حد الطلب
                    //                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    //            }
                    //        }
                    //    }
                    //}
                    #endregion
                }
            }
            catch
            {
                e.Valid = false;
            }
        }

        private void gridView1_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            if ((e.Row as DataRowView).Row["ItemId"] == DBNull.Value)
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore;
            else
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gridView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            Get_TotalAccount();
            grd_FocusOnItemId(Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1");

            //hide last prices grid
            //GetLastPrices(false, false);
        }

        private void grid_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
                var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                if (e.KeyCode == Keys.Enter)
                {
                    var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
                    int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

                    if (view.FocusedColumn == view.Columns["ItemId"]
                        || view.FocusedColumn == view.Columns["ItemCode1"]
                        || view.FocusedColumn == view.Columns["ItemCode2"])
                    {
                        if (Shared.user.UseBarcodeScanner)
                        {
                            grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                        }
                        else
                        {
                            string temp = view.FocusedColumn.FieldName;
                            view.FocusedColumn = view.VisibleColumns[view.Columns["ItemId"].VisibleIndex - 1];
                            if (view.GetFocusedRowCellValue(temp) == null || string.IsNullOrEmpty(view.GetFocusedRowCellValue(temp).ToString()))
                                view.FocusedColumn = view.Columns[temp];
                            return;
                        }
                    }

                    if (view.FocusedColumn == view.Columns["UOM"]
                        || view.FocusedColumn == view.Columns["Qty"]
                        || view.FocusedColumn == view.Columns["Length"]
                        || view.FocusedColumn == view.Columns["Width"]
                        || view.FocusedColumn == view.Columns["Height"]
                        || view.FocusedColumn == view.Columns["Expire"]
                        || view.FocusedColumn == view.Columns["Batch"]
                        || view.FocusedColumn == view.Columns["ItemDescription"]
                        || view.FocusedColumn == view.Columns["ItemDescriptionEn"]
                        || view.FocusedColumn == view.Columns["PiecesCount"]
                        || view.FocusedColumn == view.Columns["Serial"])
                    {
                        if (view.FocusedColumn.VisibleIndex - 1 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1].OptionsColumn.AllowFocus)
                        {
                            view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                            return;
                        }
                        else if (view.FocusedColumn.VisibleIndex - 2 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2].OptionsColumn.AllowFocus)
                        {
                            view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2];
                            return;
                        }
                        else
                            grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                    }

                    if (view.FocusedColumn == view.Columns["SellPrice"]
                        || view.FocusedColumn == view.Columns["PurchasePrice"]
                        || view.FocusedColumn == view.Columns["DiscountRatio"]
                        || view.FocusedColumn == view.Columns["DiscountRatio2"]
                        || view.FocusedColumn == view.Columns["DiscountRatio3"]
                        || view.FocusedColumn == view.Columns["DiscountValue"]
                        || view.FocusedColumn == view.Columns["Batch"])
                    {
                        grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                    }

                    if (view.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                    {
                        view.AddNewRow();
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud
                        //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                        //    view.FocusedColumn = view.Columns["StoreId"];

                    }
                    else
                    {
                        view.FocusedRowHandle = focused_row_handle + 1;
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];
                        //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                        //    view.FocusedColumn = view.Columns["StoreId"]; 

                    }

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Up
                   && view.GetFocusedRow() != null
                   && (view.GetFocusedRow() as DataRowView).IsNew == true
                   && (view.GetFocusedRowCellValue("ItemId") == null || view.GetFocusedRowCellValue("ItemId").ToString() == string.Empty))
                {
                    view.DeleteRow(view.FocusedRowHandle);
                }

            }
            catch
            { }
        }

        private void gridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "DiscountValue"
                || e.Column.FieldName == "TotalSellPrice" || e.Column.FieldName == "SellPrice"
                || e.Column.FieldName == "PurchasePrice" /*|| e.Column.FieldName == "CurrentQty"*/
                || e.Column.FieldName == "SalesTax")
            {
                if (e.Value != DBNull.Value && e.Value != null)
                    e.DisplayText = decimal.Round(Convert.ToDecimal(e.Value), 4).ToString();
            }
            if (e.Column == col_Expire)
            {
                #region Expire
                if (gridView2.GetListSourceRowCellValue(e.ListSourceRowIndex, "ExpireDate") == null || gridView2.GetListSourceRowCellValue(e.ListSourceRowIndex, "ExpireDate") == DBNull.Value)
                {
                    e.DisplayText = "";
                    return;
                }

                DateTime date = Convert.ToDateTime(gridView2.GetListSourceRowCellValue(e.ListSourceRowIndex, "ExpireDate"));
                e.DisplayText = date.Month + "-" + date.Year;
                #endregion
            }
            if (e.Column == col_Batch)
            {
                e.DisplayText = e.Value + "";
            }
            else if (e.Column.FieldName == "ManufactureDate")
            {
                if (e.Value != null && e.Value != DBNull.Value)
                    e.DisplayText = Convert.ToDateTime(e.Value).ToShortDateString();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            DB = new DAL.ERPDataContext();
            int lastInvId = (from inv in DB.SL_InvoiceArchives
                             where inv.SL_InvoiceId > invoiceId
                             where Shared.user.UserChangeStore ? true : inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId
                             orderby inv.SL_InvoiceId ascending
                             select inv.SL_InvoiceId).FirstOrDefault();

            if (lastInvId != 0)
            {
                invoiceId = lastInvId;
                LoadInvoice();
            }
            else
            {
                lastInvId = (from inv in DB.SL_InvoiceArchives
                             where Shared.user.UserChangeStore ? true : inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId
                             orderby inv.SL_InvoiceId ascending
                             select inv.SL_InvoiceId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    invoiceId = lastInvId;
                    LoadInvoice();
                }
            }
        }

        private void btnPrevious_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            DB = new DAL.ERPDataContext();
            int lastInvId = (from inv in DB.SL_InvoiceArchives
                             where inv.SL_InvoiceId < invoiceId
                             where Shared.user.UserChangeStore ? true : inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId
                             orderby inv.SL_InvoiceId descending
                             select inv.SL_InvoiceId).FirstOrDefault();

            if (lastInvId != 0)
            {
                invoiceId = lastInvId;
                LoadInvoice();
            }
            else
            {
                lastInvId = (from inv in DB.SL_InvoiceArchives
                             where Shared.user.UserChangeStore ? true : inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId
                             orderby inv.SL_InvoiceId descending
                             select inv.SL_InvoiceId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    invoiceId = lastInvId;
                    LoadInvoice();
                }
            }
        }

        private void repUOM_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            if (e.Value == null || e.Value == DBNull.Value)
                return;
            try
            {
                e.DisplayText = uom_list.Where(x => x.UOMId == Convert.ToInt32(e.Value)).FirstOrDefault().UOM;
            }
            catch
            {
            }
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            invoiceId = userId = 0;

            invoice_remains = 0;
            LoadInvoice();
            lkp_Customers_EditValueChanged(null, EventArgs.Empty);
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);

            //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
            if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                lkpStore.EditValue = 0;
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ValidData())
                return;

            #region validate_all_qts_Larger_Zero
            var Is_Qty_LessZero = (from DataRow dr in dtSL_Details.Rows
                                   where dr.RowState != DataRowState.Deleted
                                   where Convert.ToDecimal(dr["Qty"]) <= 0
                                   select dr).Count() > 0;

            if (Is_Qty_LessZero == true)
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgQtyLessZero : ResSLAr.MsgQtyLessZero, "",
                       MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }

            #endregion

            if (invoiceId == 0)
            {
                Save_Invoice();

                if (printOptions != null && comp != null && printOptions.PrintReceiptSLI == true)
                {
                    if (Convert.ToDecimal(txtNet.EditValue) >= printOptions.MinValue)
                    {
                        try
                        {
                            //new Reports.rpt_Printed_Receipt(dtSL_Details, Convert.ToDecimal(txtNet.EditValue), " رقم الفاتورة " + txtInvoiceCode.Text, printOptions, comp).ShowPreview();
                            Reports.rpt_Printed_Receipt r = new Reports.rpt_Printed_Receipt();

                            if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.repx"))
                                r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.repx");

                            r.Load_Receipt(dtSL_Details, lkp_Customers.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text, lkp_Drawers.Text,
                                txtNotes.Text, txtNet.Text, Shared.UserName, txt_Remains.Text, txtDiscountValue.Text, txtNet.Text, txt_paid.Text, txt_Total.Text);
                            r.Print(Shared.ReceiptPrinterName);
                        }
                        catch
                        { }
                    }
                }
                #region Ask_To_Create_OutTrnsBill
                //if (Shared.InvoicePostToStore == false)
                //{
                //    if (XtraMessageBox.Show(Shared.IsEnglish == true ? ResSLEn.MsgAskCreateOutTrns : ResSLAr.MsgAskCreateOutTrns,
                //    Shared.IsEnglish == true ? ResSLEn.MsgTQues : ResSLAr.MsgTQues,
                //    MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                //    {
                //        if (ErpUtils.IsFormOpen(typeof(frm_IC_OutTrns)))
                //            Application.OpenForms["frm_IC_OutTrns"].Close();

                //        if (ErpUtils.IsFormOpen(typeof(frm_IC_OutTrns)))
                //            Application.OpenForms["frm_IC_OutTrns"].BringToFront();
                //        else
                //        {
                //            new frm_IC_OutTrns((int)Process.SellInvoice, invoiceId, txtInvoiceCode.Text).Show();
                //        }
                //    }
                //}
                #endregion
            }
            else
                Save_Invoice();

            XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgSave : ResSLAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Saved_Successfuly = true;
            invoice_remains = Convert.ToDouble(txt_Remains.EditValue);

            lkp_Customers_EditValueChanged(null, EventArgs.Empty);

            //Print_Prescriptions();
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void batBtnList_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_InvoiceArchiveList)))
            {
                frm_SL_InvoiceArchiveList frm = new frm_SL_InvoiceArchiveList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_SL_InvoiceArchiveList"].BringToFront();
        }

        private void barBtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            /*
             *Delete all items added to store using this invoice 
             *Delete all invoice details 
             *Delete Jornal_Details 
             *Delete Jornal 
             *Delete Invoice
             */
            if (invoiceId == 0)
                return;

            //can't delete in closed period
            if (ErpHelper.CanSaveInClsedPeriod(dtInvoiceDate.DateTime.Date, Shared.st_Store.ClosePeriodDate, Shared.user.EditInClosedPeriod) == false)
                return;

            if (ErpHelper.CanEditPostedBill(invoiceId, (int)Process.SellInvoice,
                  Shared.OfflinePostToGL, Shared.user.UserEditPostedBills) == false)
                return;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            //can't delete linked document
            List<string> outTrnsLst = DB.IC_OutTrns.Where(x => x.ProcessId == (int)Process.SellInvoice && x.SourceId == invoiceId).Select(x => x.OutTrnsCode).ToList();
            string msg = string.Empty;
            if (outTrnsLst.Count() > 0)
            {
                msg = (Shared.IsEnglish ? ResEn.DelInvoiceDenied : ResAr.DelInvoiceDenied) + " \r\n";
                foreach (string s in outTrnsLst)
                    msg += s + " ";

                XtraMessageBox.Show(msg, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDeleteInv : ResSLAr.MsgDeleteInv,
                 Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.No)
                return;

            //var invoice_details_ids = DB.SL_InvoiceDetails.Where(s => s.SL_InvoiceId == invoiceId).Select(s => s.SL_InvoiceDetailId).ToList();
            //var invoice_itemstores = DB.IC_ItemStores.Where(s => s.ProcessId == (int)Process.SellInvoice
            //    && invoice_details_ids.Contains(s.SourceId));

            var invoice_itemstores = (from i in DB.IC_ItemStores
                                      join s in DB.SL_InvoiceDetails
                                      on i.SourceId equals s.SL_InvoiceDetailId
                                      where i.ProcessId == (int)Process.SellInvoice &&
                                      s.SL_InvoiceId == invoiceId
                                      select i).ToList();

            DB.IC_ItemStores.DeleteAllOnSubmit(invoice_itemstores);

            DB.SL_InvoiceDetails.DeleteAllOnSubmit(DB.SL_InvoiceDetails.Where(s => s.SL_InvoiceId == invoiceId));

            int jornal_id = DB.ACC_Journals.Where(s => s.ProcessId == (int)Process.SellInvoice && s.SourceId == invoiceId).Select(s => s.JournalId).FirstOrDefault();
            DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(s => s.JournalId == jornal_id));
            DB.ACC_Journals.DeleteAllOnSubmit(DB.ACC_Journals.Where(s => s.JournalId == jornal_id));

            DB.SL_Invoices.DeleteAllOnSubmit(DB.SL_Invoices.Where(s => s.SL_InvoiceId == invoiceId));

            if (cmdProcess.EditValue != null && Convert.ToInt32(cmdProcess.EditValue) == (int)Process.OutTrns &&
                 btnSourceId.EditValue != null)
            {
                var outTrn = DB.IC_OutTrns.Where(x => x.OutTrnsId == Convert.ToInt32(btnSourceId.EditValue)).FirstOrDefault();
                if (outTrn != null)
                {
                    if (DB.SL_Invoices.Where(x => x.ProcessId == (int)Process.OutTrns && x.SourceId == Convert.ToInt32(btnSourceId.EditValue)).Count() <= 1)
                        outTrn.Is_SellInv = null;
                }
            }
            if (cmdProcess.EditValue != null && Convert.ToInt32(cmdProcess.EditValue) == (int)Process.SalesOrder &&
                 btnSourceId.EditValue != null)
            {
                var pr = DB.SL_SalesOrders.Where(x => x.SL_SalesOrderId == Convert.ToInt32(btnSourceId.EditValue)).FirstOrDefault();
                if (pr != null)
                {
                    DB.SubmitChanges();

                    if (DB.SL_Invoices.Where(x => x.ProcessId == (int)Process.SalesOrder && x.SourceId == Convert.ToInt32(btnSourceId.EditValue)).Count() <= 1)
                    {
                        pr.Is_SalesInvoice = null;
                        if (Shared.InvoicePostToStore)
                            pr.Is_OutTrns = null;
                    }

                    byte costmethod = Convert.ToByte(lkpStore.Properties.GetDataSourceValue("CostMethod", lkpStore.ItemIndex));
                    List<StoreItem> lst_soItems = MyHelper.GetSoItems(DB, pr.SL_SalesOrderId);
                    List<StoreItem> lst_newStoreItem = MyHelper.PostSoReserve(FormsNames.SL_Invoice, DB, pr.SL_SalesOrderId, lst_soItems);

                    var lst_ = MyHelper.Subtract_from_store(pr.StoreId, costmethod, (int)Process.SalesOrder, pr.SalesOrderDate, lst_newStoreItem);
                    DB.IC_ItemStores.InsertAllOnSubmit(lst_);
                }
            }

            MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                (int)FormAction.Delete, (int)FormsNames.SL_Invoice);

            DB.SubmitChanges();

            invoiceId = userId = 0;
            LoadInvoice();
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);

            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDel : ResSLAr.MsgDel, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            lkp_Customers_EditValueChanged(null, EventArgs.Empty);
        }


        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.CheckDocumentSaved(invoiceId, DataModified, dtSL_Details) == false)
                return;

            DataTable dt_PrintTable = new DataTable();
            dt_PrintTable.Columns.Add("ItemCode1");
            dt_PrintTable.Columns.Add("ItemCode2");
            dt_PrintTable.Columns.Add("DiscountValue");
            dt_PrintTable.Columns.Add("Expire");
            dt_PrintTable.Columns.Add("Batch");
            dt_PrintTable.Columns.Add("SellPrice");
            dt_PrintTable.Columns.Add("Qty");
            dt_PrintTable.Columns.Add("TotalSellPrice");
            dt_PrintTable.Columns.Add("ItemName");
            dt_PrintTable.Columns.Add("UOM");
            dt_PrintTable.Columns.Add("Height");
            dt_PrintTable.Columns.Add("Width");
            dt_PrintTable.Columns.Add("Length");
            dt_PrintTable.Columns.Add("TotalQty");
            dt_PrintTable.Columns.Add("Factor");
            dt_PrintTable.Columns.Add("MUOM");
            dt_PrintTable.Columns.Add("MUOM_Factor");
            dt_PrintTable.Columns.Add("ItemType");
            dt_PrintTable.Columns.Add("ItemDescription");
            dt_PrintTable.Columns.Add("ItemDescriptionEn");
            dt_PrintTable.Columns.Add("PiecesCount");

            dt_PrintTable.Columns.Add("SalesTax");
            dt_PrintTable.Columns.Add("CustomTax");
            dt_PrintTable.Columns.Add("DiscountRatio");
            dt_PrintTable.Columns.Add("DiscountRatio2");
            dt_PrintTable.Columns.Add("DiscountRatio3");
            dt_PrintTable.Columns.Add("SalesTaxRatio");
            dt_PrintTable.Columns.Add("PicPath");
            dt_PrintTable.Columns.Add("SL_InvoiceDetailId");
            dt_PrintTable.Columns.Add("Serial");
            dt_PrintTable.Columns.Add("ManufactureDate");

            var view = grdPrInvoice.FocusedView as GridView;
            for (int i = 0; i < view.RowCount; i++)
            {
                if (view.GetRowCellDisplayText(i, "ItemCode1") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTable.NewRow();
                dr["ItemCode1"] = view.GetRowCellDisplayText(i, "ItemCode1");
                dr["ItemCode2"] = view.GetRowCellDisplayText(i, "ItemCode2");
                dr["DiscountValue"] = view.GetRowCellDisplayText(i, "DiscountValue") == "0" ? "" : view.GetRowCellDisplayText(i, "DiscountValue"); //mahmoud:18-12-2012, naser azemi issue                
                dr["Expire"] = view.GetRowCellDisplayText(i, "Expire");
                dr["Batch"] = view.GetRowCellDisplayText(i, "Batch");
                dr["SellPrice"] = view.GetRowCellDisplayText(i, "SellPrice");
                dr["Qty"] = view.GetRowCellDisplayText(i, "Qty");
                dr["TotalSellPrice"] = view.GetRowCellDisplayText(i, "TotalSellPrice");
                dr["ItemName"] = view.GetRowCellDisplayText(i, "ItemId");
                dr["UOM"] = view.GetRowCellDisplayText(i, "UOM");
                dr["Height"] = view.GetRowCellDisplayText(i, "Height");
                dr["Width"] = view.GetRowCellDisplayText(i, "Width");
                dr["Length"] = view.GetRowCellDisplayText(i, "Length");
                dr["TotalQty"] = view.GetRowCellDisplayText(i, "TotalQty");

                if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 0)
                    dr["Factor"] = 1;
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 1)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "MediumUOMFactor");
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 2)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "LargeUOMFactor");

                if (ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM") != null)
                {
                    dr["MUOM"] = uom_list.Where(x => x.UOMId == Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM"))).Select(x => x.UOM).FirstOrDefault();
                    dr["MUOM_Factor"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOMFactor");
                }
                else
                {
                    dr["MUOM"] = dr["UOM"];
                    dr["MUOM_Factor"] = dr["Factor"];
                }

                dr["ItemType"] = view.GetRowCellValue(i, "ItemType");
                dr["ItemDescription"] = view.GetRowCellDisplayText(i, "ItemDescription");
                dr["ItemDescriptionEn"] = view.GetRowCellDisplayText(i, "ItemDescriptionEn");
                dr["PiecesCount"] = view.GetRowCellDisplayText(i, "PiecesCount");

                dr["SalesTax"] = view.GetRowCellDisplayText(i, "SalesTax");
                dr["CustomTax"] = view.GetRowCellDisplayText(i, "CustomTax");
                dr["DiscountRatio"] = view.GetRowCellDisplayText(i, "DiscountRatio");
                dr["DiscountRatio2"] = view.GetRowCellDisplayText(i, "DiscountRatio2");
                dr["DiscountRatio3"] = view.GetRowCellDisplayText(i, "DiscountRatio3");
                dr["SalesTaxRatio"] = view.GetDataRow(i)["SalesTaxRatio"];
                dr["SL_InvoiceDetailId"] = view.GetDataRow(i)["SL_InvoiceDetailId"];
                dr["PicPath"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "PicPath");
                dr["Serial"] = view.GetRowCellDisplayText(i, "Serial");

                //mohammad, 15/4/2018
                dr["ManufactureDate"] = view.GetRowCellDisplayText(i, "ManufactureDate");

                dt_PrintTable.Rows.Add(dr);
            }

            string Customer = lkp_Customers.Text;
            string salesEmp_Job = lkp_SalesEmp.EditValue == null ? "" : lkp_SalesEmp.GetColumnValue("JobNameAr") + "";

            if (printOptions != null && comp != null && printOptions.PrintReceiptSLI == true)
            {
                if (Convert.ToDecimal(txtNet.EditValue) >= printOptions.MinValue)
                {
                    try
                    {
                        //new Reports.rpt_Printed_Receipt(dtSL_Details, Convert.ToDecimal(txtNet.EditValue), " رقم الفاتورة " + txtInvoiceCode.Text, printOptions, comp).ShowPreview();
                        Reports.rpt_Printed_Receipt r = new Reports.rpt_Printed_Receipt();

                        if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.repx"))
                            r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.repx");

                        r.Load_Receipt(dtSL_Details, lkp_Customers.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text, lkp_Drawers.Text,
                                txtNotes.Text, txtNet.Text,
                Shared.lst_Users.Where(x => x.UserId == userId).Select(x => x.Name).FirstOrDefault(), txt_Remains.Text,
                txtDiscountValue.Text, txtNet.Text, txt_paid.Text, txt_Total.Text);
                        r.Print(Shared.ReceiptPrinterName);
                        //r.ShowPreview();
                    }
                    catch
                    { }
                }
            }
            else
            {
                var branch = DB.IC_Stores.Where(a => a.StoreId == Convert.ToInt32(lkpStore.EditValue)).FirstOrDefault();
                string address = branch.Address;
                string tel = branch.Tel;
                string managerName = branch.ManagerName;
                DataTable dataTable=new DataTable();
                Reports.rpt_SL_Invoice r = new Reports.rpt_SL_Invoice
                    (Customer, lkp_InvoiceBook.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text,
                    cmbPayMethod.Text, lkp_Drawers.Text, txtNotes.Text, txt_Total.Text, "", txt_TaxValue.Text,
                    txtDiscountRatio.Text, txtDiscountValue.Text, txtExpensesR.Text, txtExpenses.Text, txtNet.Text,
                    txt_paid.Text, txt_Remains.Text, dt_PrintTable,
                    Shared.lst_Users.Where(x => x.UserId == userId).Select(x => x.Name).FirstOrDefault()
                    , lkp_SalesEmp.Text,
                    txt_Shipping.Text, txt_PO_No.Text, dtDeliverDate.Text, salesEmp_Job, txt_DeductTaxV.Text, txt_AddTaxV.Text,
                    Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue), lbl_IsCredit_Before.Text + " " + txt_Balance_Before.Text,
                    lbl_IsCredit_After.Text + " " + txt_Balance_After.Text,
                    txtDriverName.Text, txtVehicleNumber.Text, txtDestination.Text, cmdProcess.Text, txtSourceCode.Text,
                    txtScaleSerial.Text, "", txt_CusTaxV.Text, lkpCostCenter.Text, txt_RetentionV.Text, txt_AdvancePayV.Text,
                    txt_DueDate.Text, Convert.ToInt32(lkp_Customers.EditValue),
                    new decimal[] { 0, 0 }, gridView2.Columns["PiecesCount"].SummaryItem.SummaryValue.ToString(), address,
                    tel, managerName, txt_AttnMr.Text,"","","","",dataTable,"");

                string TemplateName = "rpt_SL_Invoice";
                if (lkp_InvoiceBook.GetColumnValue("PrintFileName") != null && lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString().Trim() != string.Empty)
                    TemplateName = lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString();

                if (System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".repx"))
                    r.LoadLayout(Shared.ReportsPath + TemplateName + ".repx");

                r.LoadData();
                if (Shared.user.ShowPrintPreview == false)
                    r.PrintDialog();
                else
                    r.ShowPreview();
            }
        }

        private void barBtnPrint_F_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.CheckDocumentSaved(invoiceId, DataModified, dtSL_Details) == false)
                return;

            DataTable dt_PrintTable = new DataTable();
            dt_PrintTable.Columns.Add("ItemCode1");
            dt_PrintTable.Columns.Add("ItemCode2");
            dt_PrintTable.Columns.Add("DiscountValue");
            dt_PrintTable.Columns.Add("Expire");
            dt_PrintTable.Columns.Add("Batch");
            dt_PrintTable.Columns.Add("SellPrice");
            dt_PrintTable.Columns.Add("Qty");
            dt_PrintTable.Columns.Add("TotalSellPrice");
            dt_PrintTable.Columns.Add("ItemName");
            dt_PrintTable.Columns.Add("UOM");
            dt_PrintTable.Columns.Add("Height");
            dt_PrintTable.Columns.Add("Width");
            dt_PrintTable.Columns.Add("Length");
            dt_PrintTable.Columns.Add("TotalQty");
            dt_PrintTable.Columns.Add("Factor");
            dt_PrintTable.Columns.Add("MUOM");
            dt_PrintTable.Columns.Add("MUOM_Factor");
            dt_PrintTable.Columns.Add("ItemType");
            dt_PrintTable.Columns.Add("ItemDescription");
            dt_PrintTable.Columns.Add("ItemDescriptionEn");
            dt_PrintTable.Columns.Add("PiecesCount");

            dt_PrintTable.Columns.Add("SalesTax");
            dt_PrintTable.Columns.Add("CustomTax");
            dt_PrintTable.Columns.Add("DiscountRatio");
            dt_PrintTable.Columns.Add("DiscountRatio2");
            dt_PrintTable.Columns.Add("DiscountRatio3");
            dt_PrintTable.Columns.Add("SalesTaxRatio");
            dt_PrintTable.Columns.Add("PicPath");
            dt_PrintTable.Columns.Add("SL_InvoiceDetailId");
            dt_PrintTable.Columns.Add("Serial");


            var view = grdPrInvoice.FocusedView as GridView;
            for (int i = 0; i < view.RowCount; i++)
            {
                if (view.GetRowCellDisplayText(i, "ItemCode1") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTable.NewRow();
                dr["ItemCode1"] = view.GetRowCellDisplayText(i, "ItemCode1");
                dr["ItemCode2"] = view.GetRowCellDisplayText(i, "ItemCode2");
                dr["DiscountValue"] = view.GetRowCellDisplayText(i, "DiscountValue") == "0" ? "" : view.GetRowCellDisplayText(i, "DiscountValue"); //mahmoud:18-12-2012, naser azemi issue                
                dr["Expire"] = view.GetRowCellDisplayText(i, "Expire");
                dr["Batch"] = view.GetRowCellDisplayText(i, "Batch");
                dr["SellPrice"] = view.GetRowCellDisplayText(i, "SellPrice");
                dr["Qty"] = view.GetRowCellDisplayText(i, "Qty");
                dr["TotalSellPrice"] = view.GetRowCellDisplayText(i, "TotalSellPrice");
                dr["ItemName"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "ItemNameEn");
                dr["UOM"] = view.GetRowCellDisplayText(i, "UOM");
                dr["Height"] = view.GetRowCellDisplayText(i, "Height");
                dr["Width"] = view.GetRowCellDisplayText(i, "Width");
                dr["Length"] = view.GetRowCellDisplayText(i, "Length");
                dr["TotalQty"] = view.GetRowCellDisplayText(i, "TotalQty");

                if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 0)
                    dr["Factor"] = 1;
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 1)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "MediumUOMFactor");
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 2)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "LargeUOMFactor");

                if (ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM") != null)
                {
                    dr["MUOM"] = uom_list.Where(x => x.UOMId == Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM"))).Select(x => x.UOM).FirstOrDefault();
                    dr["MUOM_Factor"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOMFactor");
                }
                else
                {
                    dr["MUOM"] = dr["UOM"];
                    dr["MUOM_Factor"] = dr["Factor"];
                }

                dr["ItemType"] = view.GetRowCellValue(i, "ItemType");
                dr["ItemDescription"] = view.GetRowCellDisplayText(i, "ItemDescription");
                dr["ItemDescriptionEn"] = view.GetRowCellDisplayText(i, "ItemDescriptionEn");
                dr["PiecesCount"] = view.GetRowCellDisplayText(i, "PiecesCount");

                dr["SalesTax"] = view.GetRowCellDisplayText(i, "SalesTax");
                dr["CustomTax"] = view.GetRowCellDisplayText(i, "CustomTax");
                dr["DiscountRatio"] = view.GetRowCellDisplayText(i, "DiscountRatio");
                dr["DiscountRatio2"] = view.GetRowCellDisplayText(i, "DiscountRatio2");
                dr["DiscountRatio3"] = view.GetRowCellDisplayText(i, "DiscountRatio3");
                dr["SalesTaxRatio"] = view.GetDataRow(i)["SalesTaxRatio"];
                dr["SL_InvoiceDetailId"] = view.GetDataRow(i)["SL_InvoiceDetailId"];
                dr["PicPath"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "PicPath");
                dr["Serial"] = view.GetRowCellDisplayText(i, "Serial");

                dt_PrintTable.Rows.Add(dr);
            }
            string Customer = ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "CusNameEn") + "";
            string Store = lkpStore.GetColumnValue("StoreNameEn") + "";
            string PayMethod = "";
            if (Shared.IsEnglish == false)
            {
                if (cmbPayMethod.EditValue == null)
                    PayMethod = "Cash / On Credit";
                else if (Convert.ToBoolean(cmbPayMethod.EditValue) == true)
                    PayMethod = "Cash ";
                else
                    PayMethod = "On Credit";
            }
            else
            {
                if (cmbPayMethod.EditValue == null)
                    PayMethod = "اجل/كاش";
                else if (Convert.ToBoolean(cmbPayMethod.EditValue) == true)
                    PayMethod = "كاش";
                else
                    PayMethod = "اجل";
            }
            string Drawer = DB.ACC_Accounts.Where(x => x.AccountId == Convert.ToInt32(lkp_Drawers.EditValue)).Select(x => x.AcNameEn).FirstOrDefault();
            string salesEmp = lkp_SalesEmp.EditValue == null ? "" : lkp_SalesEmp.GetColumnValue("EmpFName") + "";
            string salesEmp_Job = lkp_SalesEmp.EditValue == null ? "" : lkp_SalesEmp.GetColumnValue("JobNameEn") + "";
            var branch = DB.IC_Stores.Where(a => a.StoreId == Convert.ToInt32(lkpStore.EditValue)).FirstOrDefault();
            string address = branch.Address;
            string tel = branch.Tel;
            string managerName = branch.ManagerName;
            Shared.IsEnglish = !Shared.IsEnglish;
            DataTable dataTable = new DataTable();
            Reports.rpt_SL_Invoice r = new Reports.rpt_SL_Invoice
                (Customer, lkp_InvoiceBook.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, Store,
                PayMethod, Drawer, txtNotes.Text, txt_Total.Text, "", txt_TaxValue.Text, txtDiscountRatio.Text,
                txtDiscountValue.Text, txtExpensesR.Text, txtExpenses.Text, txtNet.Text, txt_paid.Text, txt_Remains.Text,
                dt_PrintTable,
                Shared.lst_Users.Where(x => x.UserId == userId).Select(x => x.Name).FirstOrDefault()
                , salesEmp, txt_Shipping.Text, txt_PO_No.Text, dtDeliverDate.Text, salesEmp_Job,
                txt_DeductTaxV.Text, txt_AddTaxV.Text, Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue),
                lbl_IsCredit_Before.Text + " " + txt_Balance_Before.Text, lbl_IsCredit_After.Text + " " + txt_Balance_After.Text,
                txtDriverName.Text, txtVehicleNumber.Text, txtDestination.Text, cmdProcess.Text, txtSourceCode.Text,
                txtScaleSerial.Text, "", txt_CusTaxV.Text, lkpCostCenter.Text, txt_RetentionV.Text, txt_AdvancePayV.Text,
                txt_DueDate.Text, Convert.ToInt32(lkp_Customers.EditValue), new decimal[] { 0, 0 }, gridView2.Columns["PiecesCount"].SummaryItem.SummaryValue.ToString(),
               address, tel, managerName, txt_AttnMr.Text, "", "", "","", dataTable,"");

            string TemplateName = "rpt_SL_Invoice";
            if (lkp_InvoiceBook.GetColumnValue("PrintFileName") != null && lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString().Trim() != string.Empty)
                TemplateName = lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString();

            if (System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".repx"))
                r.LoadLayout(Shared.ReportsPath + TemplateName + ".repx");

            r.LoadData();
            if (Shared.user.ShowPrintPreview == false)
                r.PrintDialog();
            else
                r.ShowPreview();
            Shared.IsEnglish = !Shared.IsEnglish;
        }

        private void btnAddCustomer_Click(object sender, EventArgs e)
        {
            int customers_count = lkp_Customers.Properties.View.RowCount;
            int LastCustId = 0;
            new frm_SL_Customer().ShowDialog();
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);
            if (lst_Customers.Count > customers_count)
            {
                lkp_Customers.Properties.DataSource = lst_Customers;
                lkp_Customers.EditValue = LastCustId;
                //lkp_Customers.EditValue = lkp_Customers.Properties.GetKeyValue(lkp_Customers.Properties.View.RowCount - 1);
            }
        }

        private void txtDiscountRatio_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            if (e.IsSpinUp == false)
            {
                if (Convert.ToDecimal(((TextEdit)sender).Text) == 0)
                    e.Handled = true;
            }
        }

        private void txtDiscountRatio_KeyPress_1(object sender, KeyPressEventArgs e)
        {
            //accepts only integer values           
            if (char.IsNumber(e.KeyChar) || e.KeyChar == '.')
            {
            }
            else
            {
                e.Handled = e.KeyChar != (char)Keys.Back;
            }
        }

        private void gridView2_ShowingEditor(object sender, CancelEventArgs e)
        {
            try
            {
                #region Expire
                if (gridView2.FocusedColumn == col_Expire
                && gridView2.GetFocusedRowCellValue("IsExpire") != null
                && gridView2.GetFocusedRowCellValue("IsExpire") != DBNull.Value)
                {
                    bool IsExpire = Convert.ToBoolean(gridView2.GetFocusedRowCellValue("IsExpire"));
                    e.Cancel = !IsExpire;
                }
                #endregion
            }
            catch { }
        }

        bool cust_IsDebit = false;
        private void lkp_Customers_EditValueChanged(object sender, EventArgs e)
        {
            cust_IsDebit = false;
            int? accountId = null;
            double maxcredit = 0;
            double balance = 0;

            txt_MaxCredit.Text = string.Empty;

            var selected_customer = lst_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).FirstOrDefault();

            if (selected_customer != null)
            {
                accountId = selected_customer.AccountId;

                if (invoiceId == 0)
                {
                    txt_Shipping.Text = selected_customer.Shipping;
                    if (selected_customer.SalesEmpId.HasValue)
                        lkp_SalesEmp.EditValue = selected_customer.SalesEmpId;
                    else
                        lkp_SalesEmp.EditValue = null;

                    txt_AttnMr.Text = selected_customer.Representative;

                    if (selected_customer.DueDaysCount.HasValue)
                    {
                        txt_DueDays.EditValue = selected_customer.DueDaysCount;
                        txt_DueDate.EditValue = dtInvoiceDate.DateTime.AddDays(selected_customer.DueDaysCount.Value);
                    }
                    else
                    {
                        txt_DueDays.EditValue = 0;
                        txt_DueDate.EditValue = dtInvoiceDate.EditValue;
                    }
                }
                if (selected_customer.PriceLevelId.HasValue)
                    CustomerPriceLevel = DB.IC_PriceLevels.Where(x => x.PriceLevelId == selected_customer.PriceLevelId).FirstOrDefault();
                else
                    CustomerPriceLevel = null;



                #region Balance_Before_and_After
                DateTime? end_date = (dtInvoiceDate.DateTime == DateTime.MinValue) ? (DateTime?)null : dtInvoiceDate.DateTime;
                if (accountId != null)
                {
                    balance = decimal.ToDouble(HelperAcc.Get_account_balance(accountId.Value, Shared.minDate, end_date));
                    if (invoiceId == 0)
                    {
                        txt_Balance_Before.Text = Math.Abs(balance).ToString("0,0.00");
                        if (balance > 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if (balance < 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_Before.Text = "";

                        double balance_after = balance - Convert.ToDouble(txt_Remains.EditValue);
                        txt_Balance_After.Text = Math.Abs(balance_after).ToString("0,0.00");
                        if (balance_after > 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if (balance_after < 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_After.Text = "";
                    }
                    else
                    {
                        txt_Balance_Before.Text = Math.Abs(balance + invoice_remains).ToString("0,0.00");
                        if ((balance + invoice_remains) > 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if ((balance + invoice_remains) < 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_Before.Text = "";

                        double balance_after = (balance + invoice_remains) - Convert.ToDouble(txt_Remains.EditValue);
                        txt_Balance_After.Text = Math.Abs(balance_after).ToString("0,0.00");
                        if (balance_after > 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if (balance_after < 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_After.Text = "";
                    }
                }
                else
                {
                    txt_Balance_Before.Text = "0";
                    txt_Balance_After.Text = "0";
                }
                #endregion

                maxcredit = selected_customer.MaxCredit;

                if (invoiceId == 0)
                    txtDiscountRatio.EditValue = selected_customer.DiscountRatio * 100;

                if (maxcredit <= 0 || Shared.user.SellCustomerOverCredit == null)
                {
                    lbl_Validate_MaxLimit.Visible = false;
                }
                else
                {
                    lbl_Validate_MaxLimit.Visible = true;
                }

                txt_MaxCredit.Text = maxcredit.ToString("0,0.00");

                if (balance <= 0)
                    cust_IsDebit = true;
                else
                    cust_IsDebit = false;

                if (balance <= 0
                    && Convert.ToDecimal(txt_Balance_After.Text) > Convert.ToDecimal(txt_MaxCredit.Text)
                    && maxcredit > 0)
                {
                    lbl_Validate_MaxLimit.Text = Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebit : ResSLAr.txtValidateCustomerMaxDebit;
                    lbl_Validate_MaxLimit.ForeColor = Color.Red;
                }
                else
                {
                    lbl_Validate_MaxLimit.Text = "";
                }
            }
        }

        private void contextMenuStrip1_Opened(object sender, EventArgs e)
        {
            var view = grdPrInvoice.FocusedView as GridView;
            var item_id = view.GetFocusedRowCellValue("ItemId");
            if (item_id == null || item_id == DBNull.Value || Convert.ToInt32(item_id) <= 0)
                mi_frm_IC_Item.Enabled = false;
            else
            {
                if (Shared.LstUserPrvlg == null || Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() > 0)
                    mi_frm_IC_Item.Enabled = true;
            }
        }

        private void mi_frm_IC_Item_Click(object sender, EventArgs e)
        {
            var view = grdPrInvoice.FocusedView as GridView;
            var item_id = view.GetFocusedRowCellValue("ItemId");
            if (item_id == null || item_id == DBNull.Value || Convert.ToInt32(item_id) <= 0)
                return;

            new frm_IC_Item(Convert.ToInt32(item_id), FormAction.Edit).ShowDialog();
        }

        private void mi_CustLastPrices_Click(object sender, EventArgs e)
        {
            //GetLastPrices(true, true);
        }

        private void mi_LastPrices_Click(object sender, EventArgs e)
        {
            //GetLastPrices(false, true);
        }


        private void Get_Items()
        {
            #region Get Items
            DB = new ERPDataContext();
            lstItems = (from i in DB.IC_Items
                        where i.ItemType != (int)ItemType.MatrixParent
                        where Shared.st_Store.SellRawMaterial == false ? i.ItemType == (int)ItemType.Assembly : true
                        where invoiceId == 0 ? i.IsDeleted == false : true
                        select new ItemLkp
                        {
                            ItemCode1 = i.ItemCode1,
                            ItemCode2 = i.ItemCode2,
                            ItemId = i.ItemId,
                            ItemNameAr = i.ItemNameAr,
                            ItemNameEn = i.ItemNameEn,
                            MaxQty = i.MaxQty,
                            MinQty = i.MinQty,
                            ReorderLevel = i.ReorderLevel,
                            IsExpire = i.IsExpire,
                            PurchasePrice = i.PurchasePrice,
                            SellPrice = i.SmallUOMPrice,
                            PicPath = i.PicPath,
                            MediumUOM = i.MediumUOM,
                            LargeUOM = i.LargeUOM,
                            CategoryNameAr = i.IC_Category.CategoryNameAr,
                            CompanyNameAr = i.IC_Company.CompanyNameAr
                        }).ToList();

            repItems.DataSource = lstItems;
            repItems.DisplayMember = "ItemNameAr";
            repItems.ValueMember = "ItemId";
            #endregion
        }

        void grd_FocusOnItemId(string columnName)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            view.FocusedColumn = view.Columns[columnName];
        }

        private void Reset()
        {
            if (Shared.InvoicePostToStore)
                chk_IsPosted.Checked = true;
            else
                chk_IsPosted.Checked = false;


            chk_IsOutTrns.Checked = false;

            txtNotes.EditValue = null;
            txtExpenses.EditValue = txtDiscountRatio.EditValue = txtDiscountValue.EditValue = txt_retentionR.EditValue = txt_RetentionV.EditValue = txt_CusTaxV.EditValue = 0;

            txt_TaxValue.EditValue = 0;
            txt_DeductTaxR.EditValue = txt_DeductTaxV.EditValue = 0;
            txt_AddTaxR.EditValue = txt_AddTaxV.EditValue = 0;

            txtExpensesR.EditValue = 0;

            txt_Total.EditValue = 0.0;
            txt_paid.EditValue = 0.0;
            txtNet.EditValue = 0.0;
            txt_PayAcc1_Paid.EditValue = 0.0;
            txt_PayAcc2_Paid.EditValue = 0.0;
            lkp_Drawers2.EditValue = null;
            txt_Remains.EditValue = 0.0;

            dtInvoiceDate.DateTime = MyHelper.Get_Server_DateTime();

            dtSL_Details.Rows.Clear();
            Page_LastPrices.PageVisible = false;

            //lkp_SalesEmp.EditValue = null;                        

            dtDeliverDate.EditValue = null;
            //txt_Shipping.Text = "";            
            txt_PO_No.Text = "";

            cmbPayMethod.EditValue = Shared.user.SL_Invoice_PayMethod;

            //SourceInvoiceName = "";
            //SourceInvoiceId = 0;

            //InTrns = null;
            //scaleweight = null;

            JobOrderId = 0;

            txtInvoiceCode.ResetBackColor();
            txtInvoiceCode.ToolTipIconType = DevExpress.Utils.ToolTipIconType.None;
            txtInvoiceCode.ToolTip = "";
            txtInvoiceCode.ErrorText = "";

            pnlSrcPrc.Visible = false;
            btnSourceId.EditValue = null;
            txtSourceCode.Text = string.Empty;
            cmdProcess.EditValue = null;

            txtDriverName.Text = txtVehicleNumber.Text = txtDestination.Text = txtScaleSerial.Text = string.Empty;
        }

        public void BindDataSources()
        {
            DB = new DAL.ERPDataContext();

            if (Shared.ItemsPostingAvailable)
            {
                if (Shared.StockIsPeriodic)
                {
                    lst_Cat = MyHelper.GetChildCategoriesList_Periodic();
                    if (lst_Cat.Where(x => x.SellAcc.HasValue == false || x.SellReturnAcc.HasValue == false
                                   || x.PurchaseAcc.HasValue == false || x.PurchaseReturnAcc.HasValue == false
                                   || x.OpenInventoryAcc.HasValue == false || x.CloseInventoryAcc.HasValue == false).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResEn.ItemPosting : ResAr.ItemPosting,
                            Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.BeginInvoke(new MethodInvoker(this.Close));
                    }
                }
                else
                {
                    lst_Cat = MyHelper.GetChildCategoriesList();
                    if (lst_Cat.Where(x => x.SellAcc.HasValue == false || x.SellReturnAcc.HasValue == false || x.COGSAcc.HasValue == false ||
                        x.InvAcc.HasValue == false).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResEn.ItemPosting : ResAr.ItemPosting,
                            Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.BeginInvoke(new MethodInvoker(this.Close));
                    }
                }
            }



            #region Get Stores
            int defaultStoreId = 0;

            if (Shared.InvoicePostToStore)
                stores_table = MyHelper.Get_Stores(true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);
            else
                stores_table = MyHelper.Get_Stores(false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);

            lkpStore.Properties.DataSource = stores_table;

            lkpStore.Properties.DisplayMember = "StoreNameAr";
            lkpStore.Properties.ValueMember = "StoreId";
            lkpStore.EditValue = defaultStoreId;
            #endregion

            #region LoadInvoicesBooks
            var empty_book = new
            {
                InvoiceBookId = (int?)null,
                InvoiceBookName = "",
                IsTaxable = (bool?)null,
                PrintFileName = (string)null,
            };

            var books = (from b in DB.ST_InvoiceBooks
                         where b.ProcessId == (int)Process.SellInvoice
                         select new
                         {
                             InvoiceBookId = (int?)b.InvoiceBookId,
                             b.InvoiceBookName,
                             b.IsTaxable,
                             b.PrintFileName,
                         }).ToList();

            if (books.Count == 0)
                pnlBook.Visible = false;

            books.Insert(0, empty_book);
            lkp_InvoiceBook.Properties.DataSource = books;
            lkp_InvoiceBook.Properties.DisplayMember = "InvoiceBookName";
            lkp_InvoiceBook.Properties.ValueMember = "InvoiceBookId";
            lkp_InvoiceBook.EditValue = Shared.user.DefaultSLInv_InvBookId;

            #endregion

            Get_Items();

            #region Get Cost Centers
            lkpCostCenter.Properties.DataSource = HelperAcc.GetCostCentersLst(true);
            lkpCostCenter.Properties.DisplayMember = "CostCenterName";
            lkpCostCenter.Properties.ValueMember = "CostCenterId";
            #endregion

            #region User Rights
            //disable dicount if user has no privilege
            if (Shared.user.UserCanWriteDiscount == false)
                txtDiscountRatio.Enabled = txtDiscountValue.Enabled = false;

            #endregion

            #region Get_PayAccounts
            int defaultAcc = HelperAcc.LoadPayAccounts(dtPayAccounts, Shared.user.UserChangeDrawer, Shared.user.DefaultDrawer,
                Shared.IsEnglish);

            lkp_Drawers.Properties.ValueMember = "AccountId";
            lkp_Drawers.Properties.DisplayMember = "AccountName";
            lkp_Drawers.EditValue = defaultAcc;
            lkp_Drawers.Properties.DataSource = dtPayAccounts;

            lkp_Drawers2.Properties.ValueMember = "AccountId";
            lkp_Drawers2.Properties.DisplayMember = "AccountName";
            lkp_Drawers2.EditValue = null;
            lkp_Drawers2.Properties.DataSource = dtPayAccounts;
            #endregion

            #region dtSL_Details
            dtSL_Details.Columns.Clear();
            dtSL_Details.Columns.Add("SL_InvoiceDetailId");
            dtSL_Details.Columns.Add("SL_InvoiceId");
            dtSL_Details.Columns.Add("ItemId");
            dtSL_Details.Columns.Add("ItemCode1");
            dtSL_Details.Columns.Add("ItemCode2");
            dtSL_Details.Columns.Add("ItemType").DefaultValue = 0;
            dtSL_Details.Columns.Add("UOM");
            dtSL_Details.Columns.Add("Qty").DefaultValue = 1;
            dtSL_Details.Columns.Add("PurchasePrice").DefaultValue = 0;
            dtSL_Details.Columns.Add("SellPrice").DefaultValue = 0;
            dtSL_Details.Columns.Add("DiscountValue").DefaultValue = 0;
            dtSL_Details.Columns.Add("DiscountRatio").DefaultValue = 0;
            dtSL_Details.Columns.Add("DiscountRatio2").DefaultValue = 0;
            dtSL_Details.Columns.Add("DiscountRatio3").DefaultValue = 0;
            dtSL_Details.Columns.Add("TotalSellPrice").DefaultValue = 0;
            //dtSL_Details.Columns.Add("CurrentQty").DefaultValue = 0;
            dtSL_Details.Columns.Add("MediumUOMFactor");
            dtSL_Details.Columns.Add("LargeUOMFactor");

            dtSL_Details.Columns.Add("UomIndex");

            dtSL_Details.Columns.Add("Length").DefaultValue = 1;
            dtSL_Details.Columns.Add("Width").DefaultValue = 1;
            dtSL_Details.Columns.Add("Height").DefaultValue = 1;
            dtSL_Details.Columns.Add("PiecesCount").DefaultValue = 0;

            dtSL_Details.Columns.Add("SalesTax").DefaultValue = 0;
            dtSL_Details.Columns.Add("SalesTaxRatio").DefaultValue = 0;

            dtSL_Details.Columns.Add("ItemDescription");
            dtSL_Details.Columns.Add("ItemDescriptionEn");

            dtSL_Details.Columns.Add("ParentItemId");
            dtSL_Details.Columns.Add("M1");
            dtSL_Details.Columns.Add("M2");
            dtSL_Details.Columns.Add("M3");

            dtSL_Details.Columns.Add("Expire");
            dtSL_Details.Columns.Add("ExpireDate");
            dtSL_Details.Columns.Add("Batch");
            dtSL_Details.Columns.Add("IsExpire");

            //used for xml export, to insert items to importer database
            dtSL_Details.Columns.Add("ItemNameAr");
            dtSL_Details.Columns.Add("ItemNameEn");

            dtSL_Details.Columns.Add("SmallUOM");
            dtSL_Details.Columns.Add("SmallUOMPrice");

            dtSL_Details.Columns.Add("MediumUOM");
            dtSL_Details.Columns.Add("MediumUOMPrice");

            dtSL_Details.Columns.Add("LargeUOM");
            dtSL_Details.Columns.Add("LargeUOMPrice");

            dtSL_Details.Columns.Add("ReorderLevel");
            dtSL_Details.Columns.Add("MaxQty");
            dtSL_Details.Columns.Add("MinQty");
            dtSL_Details.Columns.Add("calcTaxBeforeDisc");
            dtSL_Details.Columns.Add("Serial");
            dtSL_Details.Columns.Add("CategoryId");
            dtSL_Details.Columns.Add("StoreId");

            //for Elm Dawa2..Doesn't affect Qty, Mohammad 17-03-2018 
            dtSL_Details.Columns.Add("ManufactureDate");

            //fayza
            dtSL_Details.Columns.Add("CustomTaxRatio").DefaultValue = 0;
            dtSL_Details.Columns.Add("CustomTax").DefaultValue = 0;
            dtSL_Details.TableNewRow += new DataTableNewRowEventHandler(dt_TableNewRow);
            dtSL_Details.RowDeleted += new DataRowChangeEventHandler(dt_RowChanged);
            dtSL_Details.RowChanged += new DataRowChangeEventHandler(dt_RowChanged);
            grdPrInvoice.DataSource = dtSL_Details;
            #endregion

            #region GetCustomers
            int LastCustId = 0;
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);

            lkp_Customers.Properties.DisplayMember = "CusNameAr";
            lkp_Customers.Properties.ValueMember = "CustomerId";
            lkp_Customers.Properties.DataSource = lst_Customers;
            lkp_Customers.EditValue = goldencustomer;
            lkp_Customers_EditValueChanged(lkp_Customers, EventArgs.Empty);
            #endregion

            #region UOM
            uom_list = DB.IC_UOMs.ToList();
            repUOM.DisplayMember = "Uom";
            repUOM.ValueMember = "UomId";
            repUOM.DataSource = MyHelper.GetUomDataTable(dtUOM);
            #endregion

            #region Expire_Qty
            rep_expireDate.DisplayMember = "Expire";
            rep_expireDate.ValueMember = "ExpireId";
            rep_expireDate.DataSource = MyHelper.GetExpireQtyDataTable(dtExpireQty);
            #endregion

            #region Batch
            dtBatchQty = new DataTable();
            dtBatchQty.Columns.Add("Batch");
            dtBatchQty.Columns.Add("Qty");

            rep_Batch.DisplayMember = "Batch";
            rep_Batch.ValueMember = "Batch";
            rep_Batch.DataSource = dtBatchQty;
            #endregion

            #region SalesEmp
            int? defaultEmp = MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            lkp_SalesEmp.Properties.ValueMember = "EmpId";
            lkp_SalesEmp.Properties.DisplayMember = "EmpName";
            lkp_SalesEmp.Properties.DataSource = dt_SalesEmps;
            if (lkp_SalesEmp.EditValue == null)
                lkp_SalesEmp.EditValue = defaultEmp;

            #endregion

            #region JobOrder
            lst_Status = DB.JO_Status.ToList();
            lkp_JOStatus.Properties.DisplayMember = "Status";
            lkp_JOStatus.Properties.ValueMember = "StatusId";
            lkp_JOStatus.Properties.DataSource = lst_Status;

            lst_Dept = DB.JO_Depts.ToList();
            lkp_JODept.Properties.DisplayMember = "Department";
            lkp_JODept.Properties.ValueMember = "DeptId";
            lkp_JODept.Properties.DataSource = lst_Dept;

            lst_Priority = DB.JO_Priorities.ToList();
            lkp_JOPriority.Properties.DisplayMember = "Priority";
            lkp_JOPriority.Properties.ValueMember = "PriorityId";
            lkp_JOPriority.Properties.DataSource = lst_Priority;


            lkp_JOSalesEmp.Properties.ValueMember = "EmpId";
            lkp_JOSalesEmp.Properties.DisplayMember = "EmpName";
            lkp_JOSalesEmp.Properties.DataSource = dt_SalesEmps;
            #endregion

            if (Shared.st_Store.SellAsRestaurant)
            {
                lst_invBom = new List<InvBomArchive>();
                lst_invBom = (from m in DB.IC_BOMs
                              join d in DB.IC_BOMDetails
                              on m.BOMId equals d.BOMId
                              join i in DB.IC_Items
                              on d.RawItemId equals i.ItemId
                              select new InvBomArchive
                              {
                                  BOMId = m.BOMId,
                                  ProductItemId = m.ProductItemId,
                                  ProductQty = m.Qty,
                                  RawItemId = d.RawItemId,
                                  UomId = d.UomId,
                                  RawQty = d.Qty,
                                  UomIndex = d.UomId == i.SmallUOM ? (byte)0 : (d.UomId == i.MediumUOM.Value ? (byte)1 : (byte)2),
                                  Factor = d.UomId == i.SmallUOM ? 1 :
                                        (d.UomId == i.MediumUOM.Value ? i.MediumUOMFactorDecimal.Value : i.LargeUOMFactorDecimal.Value),
                                  RawItemType = i.ItemType,
                                  mtrxAttribute1 = i.mtrxAttribute1,
                                  mtrxAttribute2 = i.mtrxAttribute2,
                                  mtrxAttribute3 = i.mtrxAttribute3,
                                  mtrxParentItem = i.mtrxParentItem,
                                  MediumUOMFactorDecimal = i.MediumUOMFactorDecimal.HasValue ? i.MediumUOMFactorDecimal.Value : 1,
                                  LargeUOMFactorDecimal = i.LargeUOMFactorDecimal.HasValue ? i.LargeUOMFactorDecimal.Value : 1
                              }).ToList();
            }
        }

        private void LoadItemRow(DAL.IC_Item item, DataRow row)
        {


            if (item != null && item.ItemId > 0)
            {
                row["ItemId"] = item.ItemId;
                row["CategoryId"] = item.Category;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["ItemType"] = item.ItemType;
                row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);

                MyHelper.GetUOMs(item, dtUOM, uom_list);
                row["UOM"] = dtUOM.Rows[item.DfltSellUomIndx]["UomId"];
                row["UomIndex"] = item.DfltSellUomIndx;

                decimal uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, item.DfltSellUomIndx);
                row["SellPrice"] = decimal.ToDouble(uom_price);

                if (frm_InvoiceDiscs.SLInv_DiscR1 > 0 ||
                    frm_InvoiceDiscs.SLInv_DiscR2 > 0 ||
                    frm_InvoiceDiscs.SLInv_DiscR3 > 0)
                {
                    if (frm_InvoiceDiscs.SLInv_DiscR1 > 0)
                        row["DiscountRatio"] = decimal.ToDouble(frm_InvoiceDiscs.SLInv_DiscR1);
                    if (frm_InvoiceDiscs.SLInv_DiscR2 > 0)
                        row["DiscountRatio2"] = decimal.ToDouble(frm_InvoiceDiscs.SLInv_DiscR2);
                    if (frm_InvoiceDiscs.SLInv_DiscR3 > 0)
                        row["DiscountRatio3"] = decimal.ToDouble(frm_InvoiceDiscs.SLInv_DiscR3);
                }
                else
                {
                    row["DiscountRatio"] = decimal.ToDouble(item.SalesDiscRatio / 100);
                    row["DiscountRatio2"] = decimal.ToDouble(0);
                    row["DiscountRatio3"] = decimal.ToDouble(0);
                }

                row["DiscountValue"] = decimal.ToDouble(Utilities.Calc_DiscountValue(Convert.ToDecimal(row["DiscountRatio"]),
                    Convert.ToDecimal(row["DiscountRatio2"]), Convert.ToDecimal(row["DiscountRatio3"]), Convert.ToDecimal(row["SellPrice"])));

                if (IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable")))
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                else if (IsTaxable == true)
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                else
                    row["SalesTaxRatio"] = 0;

                row["calcTaxBeforeDisc"] = item.calcTaxBeforeDisc;

                row["Qty"] = "1";

                row["Length"] = Decimal.ToDouble(item.Length);
                row["Width"] = Decimal.ToDouble(item.Width);
                row["Height"] = Decimal.ToDouble(item.Height);

                if (frm_InvoiceDimenstions.SLInv_Height > 0)
                    row["Height"] = decimal.ToDouble(frm_InvoiceDimenstions.SLInv_Height);
                if (frm_InvoiceDimenstions.SLInv_Width > 0)
                    row["Width"] = decimal.ToDouble(frm_InvoiceDimenstions.SLInv_Width);
                if (frm_InvoiceDimenstions.SLInv_Length > 0)
                    row["Length"] = decimal.ToDouble(frm_InvoiceDimenstions.SLInv_Length);


                row["TotalSellPrice"] = "0";

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);



                if (item.DfltSellUomIndx == 0)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);
                else if (item.DfltSellUomIndx == 1)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice) * decimal.ToDouble(MyHelper.FractionToDouble(item.MediumUOMFactor));
                else if (item.DfltSellUomIndx == 2)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice) * decimal.ToDouble(MyHelper.FractionToDouble(item.LargeUOMFactor));

                row["IsExpire"] = item.IsExpire;
                if (item.IsExpire == true)
                {
                    MyHelper.Get_Expire_Qtys(item.ItemId, Convert.ToInt32(lkpStore.EditValue), dtInvoiceDate.DateTime, dtExpireQty);
                    if (dtExpireQty.Rows.Count == 0)
                    {
                        row["ExpireDate"] = DBNull.Value;
                        row["Expire"] = DBNull.Value;
                        row["Batch"] = DBNull.Value;
                    }
                }

                MyHelper.Get_Batch_Qtys(item.ItemId, Convert.ToInt32(lkpStore.EditValue), dtInvoiceDate.DateTime, dtBatchQty);


                //if (Shared.user.Sell_ShowCrntQty == true)
                //{
                //    decimal currentQty;
                //    //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                //    if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                //        if (row["StoreId"] is DBNull)
                //            currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item.ItemId, 0, row["Batch"].ToString());

                //        else
                //            currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item.ItemId, Convert.ToInt32(row["StoreId"]),row["Batch"].ToString());

                //    else

                //        currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item.ItemId, Convert.ToInt32(lkpStore.EditValue), row["Batch"].ToString());

                //    currentQty = MyHelper.getCalculatedUomQty(currentQty, Convert.ToByte(row["UomIndex"]), MyHelper.FractionToDouble(item.MediumUOMFactor), MyHelper.FractionToDouble(item.LargeUOMFactor));
                //    row["CurrentQty"] = decimal.ToDouble(currentQty);
                //}
                //else
                //{
                //    row["CurrentQty"] = decimal.ToDouble(0);
                //}

                row["ItemDescription"] = item.Description;
                row["ItemDescriptionEn"] = item.DescriptionEn;

                row["ParentItemId"] = item.mtrxParentItem;
                row["M1"] = item.mtrxAttribute1;
                row["M2"] = item.mtrxAttribute2;
                row["M3"] = item.mtrxAttribute3;

                #region data for xml export
                row["ItemNameAr"] = item.ItemNameAr;
                row["ItemNameEn"] = item.ItemNameEn;

                row["SmallUOM"] = item.SmallUOM;
                row["SmallUOMPrice"] = item.SmallUOMPrice;

                if (item.MediumUOM.HasValue)
                {
                    row["MediumUOM"] = item.MediumUOM;
                    row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                    row["MediumUOMPrice"] = item.MediumUOMPrice;
                }
                if (item.LargeUOM.HasValue)
                {
                    row["LargeUOM"] = item.LargeUOM;
                    row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);
                    row["LargeUOMPrice"] = item.LargeUOMPrice;
                }

                row["ReorderLevel"] = item.ReorderLevel;
                row["MaxQty"] = item.MaxQty;
                row["MinQty"] = item.MinQty;
                #endregion
            }
        }


        double invoice_remains = 0;
        private void GetInvoice(int invId)
        {
            grdcol_branch.Visible = false;

            DB = new DAL.ERPDataContext();

            var inv = DB.SL_InvoiceArchives.Where(v => v.SL_InvoiceId == invId).SingleOrDefault();
            if (inv != null)
            {
                userId = inv.UserId;
                lkp_InvoiceBook.EditValue = inv.InvoiceBookId;
                invoice_remains = decimal.ToDouble(inv.Remains);

                lkp_Drawers.EditValue = inv.DrawerAccountId.ToString();

                lkp_Customers.EditValue = inv.CustomerId;

                txtInvoiceCode.Text = inv.InvoiceCode;
                dtInvoiceDate.EditValue = inv.InvoiceDate;


                if (inv.StoreId == 0)
                {
                    grdcol_branch.Visible = true;
                    lkpStore.EditValue = null;
                    lkpStore.Enabled = false;
                }
                else
                {
                    grdcol_branch.Visible = false;

                    lkpStore.EditValue = inv.StoreId;
                    lkpStore.Enabled = true;
                }


                lkpCostCenter.EditValue = inv.CostCenterId;
                txtNotes.Text = inv.Notes;
                chk_IsOutTrns.Checked = (inv.Is_OutTrans == true);

                uc_Currency1.lkp_Crnc.EditValue = inv.CrncId;
                uc_Currency1.txtRate.EditValue = inv.CrncRate;


                GetInvoiceDetails(invId);

                txtDiscountRatio.EditValue = decimal.ToDouble(decimal.Round(inv.DiscountRatio * 100, 4));
                txt_DeductTaxR.EditValue = decimal.ToDouble(decimal.Round(inv.DeductTaxRatio * 100, 4));
                txt_AddTaxR.EditValue = decimal.ToDouble(decimal.Round(inv.AddTaxRatio * 100, 4));
                txtExpensesR.EditValue = decimal.ToDouble(decimal.Round(inv.ExpensesRatio * 100, 4));

                txtDiscountValue.EditValue = decimal.ToDouble(inv.DiscountValue);
                txt_TaxValue.EditValue = decimal.ToDouble(inv.TaxValue);
                txt_DeductTaxV.EditValue = decimal.ToDouble(inv.DeductTaxValue);
                txt_AddTaxV.EditValue = decimal.ToDouble(inv.AddTaxValue);
                txtExpenses.EditValue = decimal.ToDouble(inv.Expenses);


                txt_CusTaxV.EditValue = decimal.ToDouble(inv.CustomTaxValue);
                txt_retentionR.EditValue = decimal.ToDouble(decimal.Round(inv.RetentionRatio * 100, 4));
                txt_RetentionV.EditValue = decimal.ToDouble(inv.RetentionValue);
                txt_AdvancePayR.EditValue = decimal.ToDouble(decimal.Round(inv.AdvancePaymentRatio * 100, 4));
                txt_AdvancePayV.EditValue = decimal.ToDouble(inv.AdvancePaymentValue);

                txtNet.EditValue = decimal.ToDouble(inv.Net);
                txt_PayAcc1_Paid.EditValue = decimal.ToDouble(inv.Paid);

                if (inv.PayAccountId2.HasValue)
                    lkp_Drawers2.EditValue = inv.PayAccountId2.ToString();
                else
                    lkp_Drawers2.EditValue = null;

                if (inv.PayAcc2_Paid.HasValue)
                    txt_PayAcc2_Paid.EditValue = decimal.ToDouble(inv.PayAcc2_Paid.Value);
                else
                    txt_PayAcc2_Paid.EditValue = 0;

                txt_Remains.EditValue = decimal.ToDouble(inv.Remains);
                cmbPayMethod.EditValue = inv.PayMethod;

                chk_IsPosted.Checked = inv.Is_Posted;
                txt_Post_Date.EditValue = inv.PostDate;


                lkp_SalesEmp.EditValue = inv.SalesEmpId;

                txt_PO_No.Text = inv.PurchaseOrderNo;
                txt_Shipping.Text = inv.Shipping;
                dtDeliverDate.EditValue = inv.DeliverDate;

                lkp_Customers_EditValueChanged(null, EventArgs.Empty);

                txt_DueDate.EditValue = inv.DueDate;

                txt_AttnMr.Text = inv.AttnMr;

                txtScaleSerial.Text = inv.ScaleWeightSerial;
                txtDriverName.Text = inv.DriverName;
                txtVehicleNumber.Text = inv.VehicleNumber;
                txtDestination.Text = inv.Destination;


                //if (inv.IsArchived != null)//posted offline before
                //{
                //    barBtnSave.Enabled = false;
                //    barBtnDelete.Enabled = false;
                //    (grdPrInvoice.FocusedView as GridView).OptionsBehavior.ReadOnly = true;
                //}
                //else
                //{
                //    barBtnSave.Enabled = true;
                //    if (Shared.LstUserPrvlg == null)
                //        barBtnDelete.Enabled = true;
                //    else
                //    {
                //        prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();
                //        barBtnDelete.Enabled = prvlg.CanDel;
                //    }
                //    (grdPrInvoice.FocusedView as GridView).OptionsBehavior.ReadOnly = false;
                //}

            }
            else
                invoice_remains = 0;
        }

        private void GetInvoiceDetails(int invoiceId)
        {
            dtSL_Details.Rows.Clear();
            DB = new DAL.ERPDataContext();
            var details = (from d in DB.SL_InvoiceDetailarchives
                           where d.SL_InvoiceArchiveId == invoiceId
                           join i in DB.IC_Items on d.ItemId equals i.ItemId
                           orderby d.SL_InvoiceDetailArchiveId
                           select new { detail = d, item = i }).ToList();

            foreach (var d in details)
            {
                DataRow row = dtSL_Details.NewRow();

                row["SL_InvoiceDetailId"] = d.detail.SL_InvoiceDetailArchiveId;
                row["SL_InvoiceId"] = d.detail.SL_InvoiceArchiveId;
                row["ItemId"] = d.detail.ItemId;
                row["CategoryId"] = d.item.Category;
                row["ItemCode1"] = d.item.ItemCode1;
                row["ItemCode2"] = d.item.ItemCode2;
                row["ItemType"] = d.item.ItemType;
                row["UOM"] = d.detail.UOMId;
                row["UomIndex"] = d.detail.UOMIndex;
                row["Qty"] = decimal.ToDouble(d.detail.Qty);
                row["StoreId"] = d.detail.StoreId;

                if (d.detail.Height != null)
                    row["Height"] = decimal.ToDouble(d.detail.Height.Value);
                if (d.detail.Length != null)
                    row["Length"] = decimal.ToDouble(d.detail.Length.Value);
                if (d.detail.Width != null)
                    row["Width"] = decimal.ToDouble(d.detail.Width.Value);
                row["PiecesCount"] = decimal.ToDouble(d.detail.PiecesCount);

                if (d.detail.UOMIndex == 0)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice);
                else if (d.detail.UOMIndex == 1)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice * MyHelper.FractionToDouble(d.item.MediumUOMFactor));
                else if (d.detail.UOMIndex == 2)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice * MyHelper.FractionToDouble(d.item.LargeUOMFactor));

                row["SellPrice"] = decimal.ToDouble(d.detail.SellPrice);

                row["SalesTaxRatio"] = d.detail.SalesTaxRatio;
                row["SalesTax"] = decimal.ToDouble(d.detail.SalesTax);
                row["calcTaxBeforeDisc"] = d.item.calcTaxBeforeDisc;

                row["DiscountValue"] = decimal.ToDouble(d.detail.DiscountValue);
                row["DiscountRatio"] = decimal.ToDouble(d.detail.DiscountRatio);
                row["DiscountRatio2"] = decimal.ToDouble(d.detail.DiscountRatio2);
                row["DiscountRatio3"] = decimal.ToDouble(d.detail.DiscountRatio3);

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(d.item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(d.item.LargeUOMFactor);
                if (d.detail.Expire.HasValue)
                    row["ExpireDate"] = d.detail.Expire;
                row["Batch"] = d.detail.Batch;
                row["Serial"] = d.detail.Serial;
                row["IsExpire"] = d.item.IsExpire;
                //fayza
                row["CustomTaxRatio"] = decimal.ToDouble(d.detail.CustomTaxRatio);
                row["CustomTax"] = decimal.ToDouble(d.detail.CustomTax);
                //get store qty   
                //if (Shared.user.Sell_ShowCrntQty == true)
                //{

                //    decimal currentQty = 0;

                //    if (d.detail.SL_Invoice.StoreId == 0)
                //        currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.StoreId.Value,d.detail.Batch);
                //    else
                //        currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.SL_Invoice.StoreId,d.detail.Batch);

                //    //decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.SL_Invoice.StoreId);
                //    currentQty = MyHelper.getCalculatedUomQty(currentQty, d.detail.UOMIndex, MyHelper.FractionToDouble(d.item.MediumUOMFactor), MyHelper.FractionToDouble(d.item.LargeUOMFactor));
                //    row["CurrentQty"] = decimal.ToDouble(currentQty);
                //}
                //else
                //{
                //    row["CurrentQty"] = decimal.ToDouble(0);
                //}
                row["TotalSellPrice"] = decimal.ToDouble(d.detail.TotalSellPrice);

                row["ItemDescription"] = d.detail.ItemDescription;
                row["ItemDescriptionEn"] = d.detail.ItemDescriptionEn;

                row["ParentItemId"] = d.item.mtrxParentItem;
                row["M1"] = d.item.mtrxAttribute1;
                row["M2"] = d.item.mtrxAttribute2;
                row["M3"] = d.item.mtrxAttribute3;

                row["ManufactureDate"] = d.detail.ManufactureDate;

                dtSL_Details.Rows.Add(row);
            }
            dtSL_Details.AcceptChanges();
            DataModified = false;

        }

        public void LoadInvoice()
        {
            Reset();

            if (invoiceId > 0)
            {
                GetInvoice(invoiceId);
            }
            else
            {
                //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                {
                    lkpStore.Enabled = false;
                    lkpStore.EditValue = null;
                    grdcol_branch.Visible = true;

                }
                else
                {
                    lkpStore.Enabled = true;
                    lkpStore.ItemIndex = 0;

                    lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);
                    grdcol_branch.Visible = false;



                }

                txtNotes.Text = Shared.user.InvoicesNotes;
                //lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);

                //barBtnSave.Enabled = true;
                //if (Shared.LstUserPrvlg == null)
                //    barBtnDelete.Enabled = true;
                //else
                //{
                //    prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();
                //    barBtnDelete.Enabled = prvlg.CanDel;
                //}
                (grdPrInvoice.FocusedView as GridView).OptionsBehavior.ReadOnly = false;

                FocusItemCode1(Shared.user.FocusGridInInvoices);

                if (customerId != 0)
                    lkp_Customers.EditValue = customerId;
            }



            DataModified = false;
        }

        private void FocusItemCode1(bool focusGrid)
        {
            if (focusGrid)
            {
                grdPrInvoice.Focus();
                var view = (grdPrInvoice.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud            
            }
            else
                lkp_Customers.Focus();
        }


        public void Save_Invoice()
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            grdPrInvoice.RefreshDataSource();
            view.RefreshData();
            Get_TotalAccount();

            //save invoice
            DB = new DAL.ERPDataContext();
            DAL.SL_Invoice sl;
            if (invoiceId > 0)
            {
                sl = DB.SL_Invoices.Where(x => x.SL_InvoiceId == invoiceId).FirstOrDefault();
                sl.LastUpdateUserId = Shared.UserId;
                sl.LastUpdateDate = DateTime.Now;

                MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                (int)FormAction.Edit, (int)FormsNames.SL_Invoice);
            }
            else
            {
                sl = new SL_Invoice();
                sl.UserId = Shared.UserId;
                sl.JornalId = 0;
                if (chk_IsOutTrns.Checked)//when just loaded from invoice
                    sl.Is_OutTrans = true;
                DB.SL_Invoices.InsertOnSubmit(sl);

                MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                    (int)FormAction.Add, (int)FormsNames.SL_Invoice);
            }

            #region SL Inv
            if (lkp_InvoiceBook.EditValue == null || Convert.ToInt32(lkp_InvoiceBook.EditValue) == 0)
                sl.InvoiceBookId = null;
            else
                sl.InvoiceBookId = Convert.ToInt32(lkp_InvoiceBook.EditValue);

            if (lkpCostCenter.EditValue == null || lkpCostCenter.EditValue.ToString() == string.Empty || Convert.ToInt32(lkpCostCenter.EditValue) == 0)
                sl.CostCenterId = null;
            else
                sl.CostCenterId = Convert.ToInt32(lkpCostCenter.EditValue);

            sl.CustomerId = Convert.ToInt32(lkp_Customers.EditValue);
            sl.InvoiceCode = txtInvoiceCode.Text.Trim();
            sl.InvoiceDate = dtInvoiceDate.DateTime;

            sl.StoreId = Convert.ToInt32(lkpStore.EditValue);

            sl.Notes = txtNotes.Text;

            //if (Shared.InvoicePostToStore)
            //    sl.Is_OutTrans = true;
            //else
            //    sl.Is_OutTrans = chk_IsOutTrns.Checked;

            if (cmdProcess.EditValue == null)
                sl.ProcessId = null;
            else
                sl.ProcessId = Convert.ToInt32(cmdProcess.EditValue);

            if (btnSourceId.EditValue == null || btnSourceId.EditValue.ToString() == string.Empty)
                sl.SourceId = null;
            else
                sl.SourceId = Convert.ToInt32(btnSourceId.EditValue);

            sl.PayMethod = (bool?)cmbPayMethod.EditValue;
            sl.DiscountRatio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
            sl.DiscountValue = Convert.ToDecimal(txtDiscountValue.EditValue);

            sl.ExpensesRatio = Convert.ToDecimal(txtExpensesR.EditValue) / 100;
            sl.Expenses = Convert.ToDecimal(txtExpenses.EditValue);

            sl.TaxValue = Convert.ToDecimal(txt_TaxValue.EditValue);
            sl.DeductTaxValue = Convert.ToDecimal(txt_DeductTaxV.EditValue);
            sl.DeductTaxRatio = Convert.ToDecimal(txt_DeductTaxR.EditValue) / 100;

            sl.AddTaxValue = Convert.ToDecimal(txt_AddTaxV.EditValue);
            sl.AddTaxRatio = Convert.ToDecimal(txt_AddTaxR.EditValue) / 100;

            sl.Net = Convert.ToDecimal(txtNet.EditValue);
            sl.Paid = Convert.ToDecimal(txt_PayAcc1_Paid.EditValue);

            if (lkp_Drawers2.EditValue != null)
                sl.PayAccountId2 = Convert.ToInt32(lkp_Drawers2.EditValue);
            else
                sl.PayAccountId2 = null;
            sl.PayAcc2_Paid = Convert.ToDecimal(txt_PayAcc2_Paid.EditValue);

            sl.Remains = Convert.ToDecimal(txt_Remains.EditValue);
            sl.DrawerAccountId = Convert.ToInt32(lkp_Drawers.EditValue);
            sl.CustomTaxValue = Convert.ToDecimal(txt_CusTaxV.EditValue);
            sl.RetentionValue = Convert.ToDecimal(txt_RetentionV.EditValue);
            sl.RetentionRatio = Convert.ToDecimal(txt_retentionR.EditValue) / 100;
            sl.AdvancePaymentValue = Convert.ToDecimal(txt_AdvancePayV.EditValue);
            sl.AdvancePaymentRatio = Convert.ToDecimal(txt_AdvancePayR.EditValue) / 100;

            if (lkp_SalesEmp.EditValue == null)
            {
                sl.SalesEmpId = null;
            }
            else
            {
                sl.SalesEmpId = Convert.ToInt32(lkp_SalesEmp.EditValue);
            }

            if (dtDeliverDate.EditValue != null)
                sl.DeliverDate = dtDeliverDate.DateTime;
            else
                sl.DeliverDate = null;
            sl.Shipping = txt_Shipping.Text;
            sl.PurchaseOrderNo = txt_PO_No.Text;



            sl.Is_Posted = chk_IsPosted.Checked;
            if (txt_Post_Date.EditValue == null)
                sl.PostDate = null;
            else
                sl.PostDate = txt_Post_Date.DateTime;



            if (txt_DueDate.EditValue == null || txt_DueDate.DateTime == DateTime.MinValue)
                sl.DueDate = dtInvoiceDate.DateTime;
            else
                sl.DueDate = txt_DueDate.DateTime;

            sl.AttnMr = txt_AttnMr.Text;

            sl.CrncId = Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue);
            sl.CrncRate = Convert.ToDecimal(uc_Currency1.txtRate.EditValue);

            sl.ScaleWeightSerial = txtScaleSerial.Text;
            sl.DriverName = txtDriverName.Text;
            sl.VehicleNumber = txtVehicleNumber.Text;
            sl.Destination = txtDestination.Text;
            #endregion

            userId = sl.UserId;
            DB.SubmitChanges();

            #region Delete ItemStore & SL Detail

            var invoice_itemstores = (from i in DB.IC_ItemStores
                                      join s in DB.SL_InvoiceDetails
                                      on i.SourceId equals s.SL_InvoiceDetailId
                                      where i.ProcessId == (int)Process.SellInvoice &&
                                      s.SL_InvoiceId == invoiceId
                                      select i).ToList();

            DB.IC_ItemStores.DeleteAllOnSubmit(invoice_itemstores);
            DB.SL_InvoiceDetails.DeleteAllOnSubmit(sl.SL_InvoiceDetails);
            #endregion

            decimal CostOfSoldGoods = 0;//used for continual inventory
            byte costmethod = Convert.ToByte(lkpStore.Properties.GetDataSourceValue("CostMethod", lkpStore.ItemIndex));
            List<StoreItem> lst_outitems = new List<StoreItem>();
            var lst = new List<IC_ItemStore>();

            for (int x = 0; x < dtSL_Details.Rows.Count; x++)
            {
                #region SL Detail
                if (dtSL_Details.Rows[x].RowState == DataRowState.Deleted)
                    continue;

                decimal MediumUOMFactor = 1;
                decimal LargeUOMFactor = 1;

                DAL.SL_InvoiceDetail detail = new DAL.SL_InvoiceDetail();
                detail.SL_InvoiceId = sl.SL_InvoiceId;
                detail.ItemId = Convert.ToInt32(dtSL_Details.Rows[x]["ItemId"]);
                detail.UOMId = Convert.ToInt32(dtSL_Details.Rows[x]["UOM"]);
                detail.UOMIndex = Convert.ToByte(dtSL_Details.Rows[x]["UomIndex"]);
                detail.Qty = Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]);


                #region check
                // if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null) 

                if (/*DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null||*/lkpStore.EditValue == null)
                {
                    detail.StoreId = Convert.ToInt32(dtSL_Details.Rows[x]["StoreId"]);

                }
                #endregion



                detail.Height = Convert.ToDecimal(dtSL_Details.Rows[x]["Height"]);
                detail.Length = Convert.ToDecimal(dtSL_Details.Rows[x]["Length"]);
                detail.Width = Convert.ToDecimal(dtSL_Details.Rows[x]["Width"]);
                detail.PiecesCount = Convert.ToDecimal(dtSL_Details.Rows[x]["PiecesCount"]);

                #region Expire
                if (dtSL_Details.Rows[x]["ExpireDate"] == DBNull.Value || dtSL_Details.Rows[x]["ExpireDate"] == null)
                    detail.Expire = null;
                else
                {
                    DateTime temp = Convert.ToDateTime(dtSL_Details.Rows[x]["ExpireDate"]);
                    temp = temp.AddDays(-temp.Day + 1);
                    detail.Expire = temp;
                }

                if (dtSL_Details.Rows[x]["Batch"] == DBNull.Value
                    || dtSL_Details.Rows[x]["Batch"].ToString().Trim() == string.Empty)
                    detail.Batch = null;
                else
                    detail.Batch = dtSL_Details.Rows[x]["Batch"].ToString();

                if (dtSL_Details.Rows[x]["Serial"] == DBNull.Value
                    || dtSL_Details.Rows[x]["Serial"].ToString().Trim() == string.Empty)
                    detail.Serial = null;
                else
                    detail.Serial = dtSL_Details.Rows[x]["Serial"].ToString();
                #endregion

                detail.SellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]);
                detail.SalesTax = Convert.ToDecimal(dtSL_Details.Rows[x]["SalesTax"]);

                //CustomTaxRatio
                detail.CustomTaxRatio = Convert.ToDecimal(dtSL_Details.Rows[x]["CustomTaxRatio"]);
                detail.CustomTax = Convert.ToDecimal(dtSL_Details.Rows[x]["CustomTax"]);

                detail.SalesTaxRatio = Convert.ToDecimal(dtSL_Details.Rows[x]["SalesTaxRatio"]);

                detail.DiscountValue = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountValue"]);
                detail.DiscountRatio = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio"]);
                detail.DiscountRatio2 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio2"]);
                detail.DiscountRatio3 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio3"]);
                detail.TotalSellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["TotalSellPrice"]);
                detail.ItemDescription = dtSL_Details.Rows[x]["ItemDescription"].ToString();
                detail.ItemDescriptionEn = dtSL_Details.Rows[x]["ItemDescriptionEn"].ToString();

                if (dtSL_Details.Rows[x]["MediumUOMFactor"].ToString() != string.Empty)
                    MediumUOMFactor = MyHelper.FractionToDouble(dtSL_Details.Rows[x]["MediumUOMFactor"].ToString());
                if (dtSL_Details.Rows[x]["LargeUOMFactor"].ToString() != string.Empty)
                    LargeUOMFactor = MyHelper.FractionToDouble(dtSL_Details.Rows[x]["LargeUOMFactor"].ToString());
                //if()// تسجيل المخزن على مستوى الصنف

                if (dtSL_Details.Rows[x]["ManufactureDate"] != null && dtSL_Details.Rows[x]["ManufactureDate"] != DBNull.Value)
                    detail.ManufactureDate = Convert.ToDateTime(dtSL_Details.Rows[x]["ManufactureDate"]);


                #endregion

                DB.SL_InvoiceDetails.InsertOnSubmit(detail);

                /*Commented*/
                #region Subtract from Store OR just Calc costOfSoldGoods
                /*
                if (sl.IsArchived == null)         //Autopost Mood
                {
                    bool CalcCostNoInsert = (Shared.InvoicePostToStore == false && Shared.StockIsPeriodic == false); //just calc cost of sold goods

                    if (Shared.InvoicePostToStore == true || CalcCostNoInsert)//just calc cost of sold goods
                    {
                        //Add Item To Store
                        byte costmethod = Convert.ToByte(lkpStore.Properties.GetDataSourceValue("CostMethod", lkpStore.ItemIndex));
                        //check if item is not service subtract from store
                        if (Convert.ToInt32(dtSL_Details.Rows[x]["ItemType"]) != (int)ItemType.Service
                        && Convert.ToInt32(dtSL_Details.Rows[x]["ItemType"]) != (int)ItemType.Subtotal)
                        {
                            decimal total_Qty = (detail.Height.HasValue ? detail.Height.Value : 1)
                                    * (detail.Length.HasValue ? detail.Length.Value : 1)
                                    * (detail.Width.HasValue ? detail.Width.Value : 1)
                                    * detail.Qty;

                            if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                                total_Qty = detail.Qty;

                            if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply)
                                detail.CostPrice = MyHelper.Subtract_from_store(detail.ItemId, detail.SL_Invoice.StoreId, detail.SL_InvoiceDetailId,
                                    (int)Process.SellInvoice, detail.UOMIndex, total_Qty, MediumUOMFactor, LargeUOMFactor,
                                    costmethod, sl.InvoiceDate, detail.Expire, detail.Batch, 0, 0, 0, detail.PiecesCount,
                                    ParentItemId, M1, M2, M3, detail.TotalSellPrice * sl.CrncRate, null, null, CalcCostNoInsert);
                            else
                                detail.CostPrice = MyHelper.Subtract_from_store(detail.ItemId, detail.SL_Invoice.StoreId, detail.SL_InvoiceDetailId,
                                    (int)Process.SellInvoice, detail.UOMIndex, total_Qty, MediumUOMFactor, LargeUOMFactor,
                                    costmethod, sl.InvoiceDate, detail.Expire, detail.Batch,
                                    detail.Length.Value, detail.Width.Value, detail.Height.Value, detail.PiecesCount,
                                    ParentItemId, M1, M2, M3, detail.TotalSellPrice * sl.CrncRate, null, null, CalcCostNoInsert);

                            CostOfSoldGoods += detail.CostPrice;

                        }
                        else
                            detail.CostPrice = 0;
                    }
                }
        */
                #endregion

                /*Subtract from strore*/
                if (Shared.InvoicePostToStore == true && sl.Is_Posted)
                {

                    if (sl.IsArchived == null)         //Autopost Mood
                    {
                        if (Shared.InvoicePostToStore == true || Shared.StockIsPeriodic == false)
                        {
                            int itemType = Convert.ToInt32(dtSL_Details.Rows[x]["ItemType"]);
                            if (itemType != (int)ItemType.Subtotal)
                            {

                                if (dtSL_Details.Rows[x]["MediumUOMFactor"].ToString() != string.Empty)
                                    MediumUOMFactor = MyHelper.FractionToDouble(dtSL_Details.Rows[x]["MediumUOMFactor"].ToString());
                                if (dtSL_Details.Rows[x]["LargeUOMFactor"].ToString() != string.Empty)
                                    LargeUOMFactor = MyHelper.FractionToDouble(dtSL_Details.Rows[x]["LargeUOMFactor"].ToString());

                                int? ParentItemId = ErpUtils.GetMtrxVal(dtSL_Details.Rows[x]["ParentItemId"]);
                                int? M1 = ErpUtils.GetMtrxVal(dtSL_Details.Rows[x]["M1"]);
                                int? M2 = ErpUtils.GetMtrxVal(dtSL_Details.Rows[x]["M2"]);
                                int? M3 = ErpUtils.GetMtrxVal(dtSL_Details.Rows[x]["M3"]);

                                if (Shared.st_Store.SellAsRestaurant && itemType == (int)ItemType.Assembly)
                                {
                                    //get item bom
                                    List<InvBomArchive> bom = lst_invBom.Where(b => b.ProductItemId == detail.ItemId).ToList();

                                    //subtract bom items
                                    if (bom.Count > 0)
                                    {
                                        int sellPriceCounter = 0;//used to add sell price on first bom item
                                        foreach (InvBomArchive b in bom)
                                        {
                                            lst_outitems.Add(new StoreItem(Shared.st_Store.MultiplyDimensions, b.RawItemId, b.RawItemType,
                                                detail.SL_InvoiceDetailId, b.UomIndex, detail.Qty * b.RawQty * b.Factor,
                                                b.MediumUOMFactorDecimal, b.LargeUOMFactorDecimal, null, null, null, null, 1, 1, 1, 0,
                                                b.mtrxParentItem, b.mtrxAttribute1, b.mtrxAttribute2, b.mtrxAttribute3,
                                                sellPriceCounter == 0 ? detail.TotalSellPrice * sl.CrncRate : 0, 0, detail, null, null,
                                                Convert.ToInt32(dtSL_Details.Rows[x]["CategoryId"]), detail.Pack));

                                            sellPriceCounter++;
                                        }
                                    }

                                }
                                else
                                {
                                    lst_outitems.Add(new StoreItem(Shared.st_Store.MultiplyDimensions, detail.ItemId, itemType, detail.SL_InvoiceDetailId, detail.UOMIndex, detail.Qty,
                                        MediumUOMFactor, LargeUOMFactor, detail.Expire, detail.Batch, null, null, detail.Length.Value, detail.Width.Value, detail.Height.Value,
                                        detail.PiecesCount, ParentItemId, M1, M2, M3, detail.TotalSellPrice * sl.CrncRate, 0, detail, detail.Serial, detail.Serial2,
                                        Convert.ToInt32(dtSL_Details.Rows[x]["CategoryId"]), detail.Pack));
                                }
                            }
                        }
                    }



                }
            }
            DB.SubmitChanges();
            if (Shared.InvoicePostToStore == true && sl.Is_Posted)
            {
                lst_outitems.ForEach(x => x.SourceId = ((SL_InvoiceDetail)x.Source).SL_InvoiceDetailId);
                lst = MyHelper.Subtract_from_store(sl.StoreId, costmethod, (int)Process.SellInvoice, sl.InvoiceDate, lst_outitems);
                CostOfSoldGoods = lst.Select(x => x.PurchasePrice).ToList().DefaultIfEmpty(0).Sum();
                lst_outitems.ForEach(x => ((SL_InvoiceDetail)x.Source).CostPrice = x.TotalCost);

                sl.TotalCostPrice = CostOfSoldGoods;

                if (Shared.InvoicePostToStore)
                    DB.IC_ItemStores.InsertAllOnSubmit(lst);

                DB.SubmitChanges();
            }

            #region Source Invoice
            if (cmdProcess.EditValue != null && Convert.ToInt32(cmdProcess.EditValue) == (int)Process.sl_Qoute &&
                btnSourceId.EditValue != null)
            {
                var qt = (from q in DB.SL_Quotes
                          where q.SL_QuoteId == Convert.ToInt32(btnSourceId.EditValue)
                          select q).FirstOrDefault();
                if (qt != null)
                    qt.Status = Convert.ToInt32(SlQouteStatus.Done);
            }
            if (cmdProcess.EditValue != null && Convert.ToInt32(cmdProcess.EditValue) == (int)Process.SalesOrder &&
                btnSourceId.EditValue != null)
            {
                var so = (from s in DB.SL_SalesOrders
                          where s.SL_SalesOrderId == Convert.ToInt32(btnSourceId.EditValue)
                          select s).FirstOrDefault();

                if (so != null)
                {
                    if (Shared.InvoicePostToStore == false)
                    {
                        so.Is_SalesInvoice = true;
                    }
                    else
                    {
                        so.Is_SalesInvoice = true;
                        so.Is_OutTrns = true;

                        List<StoreItem> lst_soItems = MyHelper.GetSoItems(DB, so.SL_SalesOrderId);
                        List<StoreItem> lst_newStoreItem = MyHelper.PostSoReserve(FormsNames.SL_Invoice, DB, so.SL_SalesOrderId, lst_soItems);

                        var lst_ = MyHelper.Subtract_from_store(so.StoreId, costmethod, (int)Process.SalesOrder, so.SalesOrderDate, lst_newStoreItem);
                        DB.IC_ItemStores.InsertAllOnSubmit(lst_);
                    }
                }
            }
            if (cmdProcess.EditValue != null && Convert.ToInt32(cmdProcess.EditValue) == (int)Process.Scale &&
                   btnSourceId.EditValue != null)
            {
                var sc = (from q in DB.ScaleWeights
                          where q.ScaleWeightId == Convert.ToInt32(btnSourceId.EditValue)
                          select q).FirstOrDefault();
                if (sc != null)
                    sc.InvoiceId = sl.SL_InvoiceId;
            }
            if (cmdProcess.EditValue != null && Convert.ToInt32(cmdProcess.EditValue) == (int)Process.OutTrns &&
                   btnSourceId.EditValue != null)
            {
                var outTrn = (from s in DB.IC_OutTrns
                              where s.OutTrnsId == Convert.ToInt32(btnSourceId.EditValue)
                              select s).FirstOrDefault();

                if (outTrn != null && Shared.InvoicePostToStore == false)
                {
                    outTrn.Is_SellInv = true;

                    if (outTrn.ProcessId != null && outTrn.SourceId != null && outTrn.ProcessId == (int)Process.SalesOrder)
                    {
                        var slOrder = DB.SL_SalesOrders.Where(x => x.SL_SalesOrderId == outTrn.SourceId).FirstOrDefault();
                        if (slOrder != null)
                            slOrder.Is_SalesInvoice = true;
                    }
                }
            }
            if (JobOrderId != 0)
            {
                var jo = DB.JO_JobOrders.Where(x => x.JobOrderId == JobOrderId).FirstOrDefault();
                if (jo != null)
                {
                    sl.JobOrderId = JobOrderId;
                    jo.JOCode = txt_JOCode.Text.Trim();
                    jo.RegDate = txt_JORegDate.DateTime;
                    jo.DeliveryDate = txt_JODeliveryDate.DateTime;
                    jo.Job = txt_JOJob.Text.Trim();

                    if (lkp_JOSalesEmp.EditValue == null)
                        jo.SalesEmp = null;
                    else
                        jo.SalesEmp = Convert.ToInt32(lkp_JOSalesEmp.EditValue);

                    jo.Dept = Convert.ToInt32(lkp_JODept.EditValue);
                    jo.Status = Convert.ToInt32(lkp_JOStatus.EditValue);
                    jo.Priority = Convert.ToInt32(lkp_JOPriority.EditValue);
                    jo.Notes = txtNotes.Text;
                }
            }
            #endregion

            DB.SubmitChanges();

            invoiceId = sl.SL_InvoiceId;
            if (sl.IsArchived == null)         //Auto Post Mood
                CreateJournal(DB, sl, CostOfSoldGoods,
                    (cmdProcess.EditValue != null && Convert.ToInt32(cmdProcess.EditValue) == (int)Process.SalesOrder) ? txtSourceCode.Text.Trim() : string.Empty,
                    lst_Cat, lst_outitems, lst);

            DataModified = false;
            dtSL_Details.AcceptChanges();
        }



        private void CreateJournal(ERPDataContext DB, SL_Invoice pr, decimal costOfSoldGoods, string soCode,
            List<IC_Category> lstCats, List<StoreItem> lstSoldItems, List<IC_ItemStore> lstInvItems)
        {
            string salesOrderNote = soCode == string.Empty ? string.Empty : ((Shared.IsEnglish == true ? ResSLEn.soCode : ResSLAr.soCode) + " " + soCode);
            string note =
                (Shared.IsEnglish == true ? ResSLEn.txtSLInvNumber : ResSLAr.txtSLInvNumber) + " " + pr.InvoiceCode + "  - " +
                    (Shared.IsEnglish == true ? "to " : "الى ") + " " + lkp_Customers.Text + " " + salesOrderNote;

            int BranchId = Convert.ToInt32(lkpStore.EditValue);
            if (lkpStore.GetColumnValue("ParentId") != null)
                BranchId = Convert.ToInt32(lkpStore.GetColumnValue("ParentId"));

            int CustomerAccountId = lst_Customers.Where(x => x.CustomerId == pr.CustomerId).Select(x => x.AccountId.Value).FirstOrDefault();

            #region Save_Jornal
            DAL.ACC_Journal jornal;
            if (pr.JornalId > 0)
                jornal = DB.ACC_Journals.Where(x => x.JournalId == pr.JornalId).FirstOrDefault();
            else
            {
                jornal = new ACC_Journal();
                jornal.InsertDate = dtInvoiceDate.DateTime;
                jornal.InsertUser = Shared.UserId;
                jornal.JCode = HelperAcc.Get_Jornal_Code();
                jornal.IsPosted = !Shared.OfflinePostToGL;
                jornal.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(jornal.InsertDate, jornal.ProcessId);
                DB.ACC_Journals.InsertOnSubmit(jornal);
            }

            jornal.JNumber = pr.InvoiceCode;
            jornal.LastUpdateDate = MyHelper.Get_Server_DateTime();
            jornal.LastUpdateUser = Shared.UserId;
            jornal.JNotes = note;
            jornal.ProcessId = (int)Process.SellInvoice;
            jornal.SourceId = pr.SL_InvoiceId;
            jornal.InsertDate = dtInvoiceDate.DateTime;
            jornal.StoreId = BranchId;
            jornal.CrncId = pr.CrncId;
            jornal.CrncRate = pr.CrncRate;
            #endregion

            DB.SubmitChanges();
            pr.JornalId = jornal.JournalId;

            DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(x => x.JournalId == pr.JornalId));

            decimal total_Sells = pr.Net - pr.TaxValue + pr.DiscountValue - pr.Expenses + pr.DeductTaxValue - pr.AddTaxValue;

            //int? costCenter = pr.CostCenterId;
            //int? costCenter = Convert.ToInt32(lkpStore.GetColumnValue("CostCenterId"));      // تحميل مركز تكلفة المخزن
            //if (costCenter == 0)
            //    costCenter = null;

            #region Sell
            /*قيد البيع*/
            /*من حســاب كل من*/
            /* حساب العميل*/
            DAL.ACC_JournalDetail jornal_Detail_2 = new DAL.ACC_JournalDetail();
            jornal_Detail_2.JournalId = jornal.JournalId;
            jornal_Detail_2.AccountId = CustomerAccountId;     //حساب عميل  
            jornal_Detail_2.CostCenter = pr.CostCenterId;
            jornal_Detail_2.Credit = 0;
            jornal_Detail_2.Debit = total_Sells + pr.Expenses + pr.TaxValue - pr.DeductTaxValue + pr.AddTaxValue;
            jornal_Detail_2.Notes = note;
            jornal_Detail_2.DueDate = pr.DueDate;
            jornal_Detail_2.CrncId = pr.CrncId;
            jornal_Detail_2.CrncRate = pr.CrncRate;
            DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_2);

            /*من حساب ضريبة الخصم و الاضافة*/
            #region Deduct_Tax_Value
            if (pr.DeductTaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_deduct_Tax = new DAL.ACC_JournalDetail();
                jornal_Detail_deduct_Tax.JournalId = jornal.JournalId;
                jornal_Detail_deduct_Tax.AccountId = Shared.st_Store.SalesDeductTaxAccount.Value;
                jornal_Detail_deduct_Tax.CostCenter = pr.CostCenterId;

                jornal_Detail_deduct_Tax.Credit = 0;
                jornal_Detail_deduct_Tax.Debit = pr.DeductTaxValue;
                jornal_Detail_deduct_Tax.Notes = note;
                jornal_Detail_deduct_Tax.CrncId = pr.CrncId;
                jornal_Detail_deduct_Tax.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_deduct_Tax);
            }
            #endregion


            #region Sell Revenue
            if (!Shared.ItemsPostingAvailable)
            {
                DAL.ACC_JournalDetail jornal_Detail_1 = new DAL.ACC_JournalDetail();
                jornal_Detail_1.JournalId = jornal.JournalId;
                jornal_Detail_1.AccountId = Convert.ToInt32(lkpStore.GetColumnValue("SellAccount"));      // حساب مبيعات المخزن
                jornal_Detail_1.CostCenter = pr.CostCenterId;
                jornal_Detail_1.Credit = total_Sells + pr.Expenses;      //اجمالي مبيعات + مصاريف النقل 
                jornal_Detail_1.Debit = 0;
                jornal_Detail_1.Notes = note;
                jornal_Detail_1.CrncId = pr.CrncId;
                jornal_Detail_1.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_1);
            }
            else
            {
                //get sold and store data
                var soldItems1 = (from x in lstSoldItems
                                  group x by new { x.CategoryId, x.ItemId } into grp
                                  select new
                                  {
                                      CatId = grp.Key.CategoryId,
                                      ItemId = grp.Key.ItemId,
                                      TotalCost = 0,
                                      TotalSell = grp.Sum(z => z.TotalLocalSellPrice)
                                  }).ToList();

                var storeItems1 = (from x in lstInvItems
                                   group x by x.ItemId into grp
                                   select new
                                   {
                                       ItemId = grp.Key,
                                       catId = soldItems1.Where(x => x.ItemId == grp.Key).Select(x => x.CatId).First(),
                                       TotalCost = grp.Sum(z => z.PurchasePrice),
                                       TotalSell = 0
                                   }).ToList();

                //group by category
                var soldItems2 = (from x in soldItems1
                                  group x by x.CatId into grp
                                  select new ItemPostingArchive
                                  {
                                      catId = grp.Key,
                                      Cost = 0,
                                      Price = grp.Sum(z => z.TotalSell)
                                  }).ToList();

                var storeItems2 = (from x in storeItems1
                                   group x by x.catId into grp
                                   select new ItemPostingArchive
                                   {
                                       catId = grp.Key,
                                       Cost = grp.Sum(z => z.TotalCost),
                                       Price = 0
                                   }).ToList();

                //summary rows per category
                List<ItemPostingArchive> lstRows;
                if (!Shared.StockIsPeriodic)
                    lstRows = (from x in soldItems2.Union(storeItems2)
                               group x by x.catId into grp
                               select new ItemPostingArchive
                               {
                                   catId = grp.Key,
                                   Cost = grp.Sum(x => x.Cost),
                                   Price = grp.Sum(x => x.Price),
                                   SellAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.SellAcc.Value).First(),
                                   COGSAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.COGSAcc.Value).First(),
                                   InvAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.InvAcc.Value).First(),
                               }).ToList();
                else
                    lstRows = (from x in soldItems2.Union(storeItems2)
                               group x by x.catId into grp
                               select new ItemPostingArchive
                               {
                                   catId = grp.Key,
                                   Cost = grp.Sum(x => x.Cost),
                                   Price = grp.Sum(x => x.Price),
                                   SellAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.SellAcc.Value).First(),
                               }).ToList();
                //post sales
                var salesRows = from x in lstRows
                                    //where x.Price > 0
                                group x by x.SellAcc into grp
                                select new
                                {
                                    SellAcc = grp.Key,
                                    Price = grp.Sum(x => x.Price)
                                };
                foreach (var r in salesRows)
                {

                    DAL.ACC_JournalDetail jornal_Detail_1 = new DAL.ACC_JournalDetail();
                    jornal_Detail_1.JournalId = jornal.JournalId;
                    jornal_Detail_1.AccountId = r.SellAcc;
                    jornal_Detail_1.CostCenter = pr.CostCenterId;
                    jornal_Detail_1.Credit = r.Price / pr.CrncRate;
                    jornal_Detail_1.Debit = 0;
                    jornal_Detail_1.Notes = note;
                    jornal_Detail_1.CrncId = pr.CrncId;
                    jornal_Detail_1.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_1);
                }

                if (!Shared.StockIsPeriodic)
                {
                    //post cogs
                    var cogsRows = from x in lstRows
                                       //where x.Cost > 0
                                   group x by x.COGSAcc into grp
                                   select new
                                   {
                                       InvAcc = grp.Key,
                                       Cost = grp.Sum(x => x.Cost)
                                   };
                    foreach (var r in cogsRows)
                    {
                        DAL.ACC_JournalDetail jdCost1 = new DAL.ACC_JournalDetail();
                        jdCost1.JournalId = jornal.JournalId;
                        jdCost1.AccountId = r.InvAcc;
                        jdCost1.Credit = 0;
                        jdCost1.Debit = r.Cost / pr.CrncRate;
                        jdCost1.CostCenter = pr.CostCenterId;
                        jdCost1.Notes = note;
                        jdCost1.CrncId = pr.CrncId;
                        jdCost1.CrncRate = pr.CrncRate;
                        DB.ACC_JournalDetails.InsertOnSubmit(jdCost1);
                    }

                    //post inv
                    var invRows = from x in lstRows
                                      //where x.Cost > 0
                                  group x by x.InvAcc into grp
                                  select new
                                  {
                                      InvAcc = grp.Key,
                                      Cost = grp.Sum(x => x.Cost)
                                  };
                    foreach (var r in invRows)
                    {
                        DAL.ACC_JournalDetail jdCost2 = new DAL.ACC_JournalDetail();
                        jdCost2.JournalId = jornal.JournalId;
                        jdCost2.AccountId = r.InvAcc;
                        jdCost2.Credit = r.Cost / pr.CrncRate;
                        jdCost2.Debit = 0;
                        jdCost2.CostCenter = pr.CostCenterId;
                        jdCost2.Notes = note;
                        jdCost2.CrncId = pr.CrncId;
                        jdCost2.CrncRate = pr.CrncRate;
                        DB.ACC_JournalDetails.InsertOnSubmit(jdCost2);
                    }
                }
            }
            #endregion

            /*الى حساب ضريبة المبيعات*/
            if (pr.TaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_tax = new DAL.ACC_JournalDetail();
                jornal_Detail_tax.JournalId = jornal.JournalId;
                jornal_Detail_tax.AccountId = Shared.st_Store.TaxAcc.Value;//  حساب ضريبة المبيعات 
                jornal_Detail_tax.CostCenter = pr.CostCenterId;
                jornal_Detail_tax.Credit = pr.TaxValue;
                jornal_Detail_tax.Debit = 0;
                jornal_Detail_tax.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                jornal_Detail_tax.CrncId = pr.CrncId;
                jornal_Detail_tax.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_tax);
            }
            /*الى حساب ضريبة الاضافة*/
            if (pr.AddTaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_addtax = new DAL.ACC_JournalDetail();
                jornal_Detail_addtax.JournalId = jornal.JournalId;
                jornal_Detail_addtax.AccountId = Shared.st_Store.SalesAddTaxAccount.Value;//حساب ضريبة الاضافة 
                jornal_Detail_addtax.CostCenter = pr.CostCenterId;
                jornal_Detail_addtax.Credit = pr.AddTaxValue;
                jornal_Detail_addtax.Debit = 0;
                jornal_Detail_addtax.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                jornal_Detail_addtax.CrncId = pr.CrncId;
                jornal_Detail_addtax.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_addtax);
            }

            #endregion

            #region Paid
            /*قيد السداد*/
            if (pr.Paid > 0 || (pr.PayAccountId2.HasValue && pr.PayAcc2_Paid.HasValue && pr.PayAcc2_Paid.Value > 0))
            {
                /* من حساب الخزينة*/
                if (pr.Paid > 0)
                {
                    DAL.ACC_JournalDetail jornal_Detail_4 = new DAL.ACC_JournalDetail();
                    jornal_Detail_4.JournalId = jornal.JournalId;
                    jornal_Detail_4.AccountId = Convert.ToInt32(lkp_Drawers.EditValue);                 // حساب الخزينة
                    jornal_Detail_4.CostCenter = pr.CostCenterId;
                    jornal_Detail_4.Credit = 0;
                    jornal_Detail_4.Debit = pr.Paid;
                    jornal_Detail_4.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                    jornal_Detail_4.CrncId = pr.CrncId;
                    jornal_Detail_4.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_4);
                }
                /*من حساب الخزينة 2*/
                if (pr.PayAccountId2 != null && pr.PayAcc2_Paid.HasValue && pr.PayAcc2_Paid.Value > 0)
                {
                    DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                    jornal_Detail_5.JournalId = jornal.JournalId;
                    jornal_Detail_5.AccountId = pr.PayAccountId2.Value;          // حساب الخزينة
                    jornal_Detail_5.CostCenter = pr.CostCenterId;
                    jornal_Detail_5.Credit = 0;
                    jornal_Detail_5.Debit = pr.PayAcc2_Paid.Value;
                    jornal_Detail_5.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                    jornal_Detail_5.CrncId = pr.CrncId;
                    jornal_Detail_5.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);
                }
                /*1 الى حساب العميل*/
                DAL.ACC_JournalDetail jornal_Detail_3 = new DAL.ACC_JournalDetail();
                jornal_Detail_3.JournalId = jornal.JournalId;
                jornal_Detail_3.AccountId = CustomerAccountId;        // حساب عميل 
                jornal_Detail_3.CostCenter = pr.CostCenterId;
                jornal_Detail_3.Credit = pr.Paid + (pr.PayAcc2_Paid.HasValue ? pr.PayAcc2_Paid.Value : 0);
                jornal_Detail_3.Debit = 0;
                jornal_Detail_3.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                jornal_Detail_3.CrncId = pr.CrncId;
                jornal_Detail_3.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_3);
            }
            #endregion

            #region Discount
            /*قيد الخصم*/
            /*1 الى حساب العميل*/
            if (pr.DiscountValue > 0)
            {
                /* من حساب الخصم المسموح به*/
                DAL.ACC_JournalDetail jornal_Detail_6 = new DAL.ACC_JournalDetail();
                jornal_Detail_6.JournalId = jornal.JournalId;

                var account = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.SalesDiscountAcc).FirstOrDefault();
                if (!account.HasValue)
                {
                    MessageBox.Show(Shared.IsEnglish ? ResSLEn.txtDiscountAccount : ResSLAr.txtDiscountAccount);
                    return;
                }
                jornal_Detail_6.AccountId = Shared.ItemsPostingAvailable ? Shared.st_Store.SalesDiscountAcc.Value : stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.SalesDiscountAcc.Value).First();//  حساب الخصم النقدي المسموح به
                jornal_Detail_6.Credit = 0;
                jornal_Detail_6.Debit = pr.DiscountValue;
                jornal_Detail_6.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jornal_Detail_6.CrncId = pr.CrncId;
                jornal_Detail_6.CrncRate = pr.CrncRate;
                jornal_Detail_6.CostCenter = pr.CostCenterId;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_6);

                DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                jornal_Detail_5.JournalId = jornal.JournalId;
                jornal_Detail_5.AccountId = CustomerAccountId;        //حساب عميل
                jornal_Detail_5.CostCenter = pr.CostCenterId;
                jornal_Detail_5.Credit = Convert.ToDecimal(pr.DiscountValue);
                jornal_Detail_5.Debit = 0;
                jornal_Detail_5.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jornal_Detail_5.CrncId = pr.CrncId;
                jornal_Detail_5.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);
            }
            #endregion

            #region CostOfSoldGoods
            if (Shared.ItemsPostingAvailable == false && Shared.StockIsPeriodic == false)
            {
                costOfSoldGoods = costOfSoldGoods / pr.CrncRate;            //Convert to Journal Currency

                DAL.ACC_JournalDetail jdCost1 = new DAL.ACC_JournalDetail();
                jdCost1.JournalId = jornal.JournalId;
                jdCost1.AccountId = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostOfSoldGoodsAcc.Value).First();//حساب تكلفة البضاعة المباعة
                jdCost1.Credit = 0;
                jdCost1.Debit = costOfSoldGoods;
                jdCost1.Notes = note;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jdCost1.CrncId = pr.CrncId;
                jdCost1.CrncRate = pr.CrncRate;
                jdCost1.CostCenter = pr.CostCenterId;
                DB.ACC_JournalDetails.InsertOnSubmit(jdCost1);

                DAL.ACC_JournalDetail jdCost2 = new DAL.ACC_JournalDetail();
                jdCost2.JournalId = jornal.JournalId;
                jdCost2.AccountId = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.PurchaseAccount).First();//المخزون
                jdCost2.CostCenter = pr.CostCenterId;
                jdCost2.Credit = costOfSoldGoods;
                jdCost2.Debit = 0;
                jdCost2.Notes = note;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jdCost2.CrncId = pr.CrncId;
                jdCost2.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jdCost2);
            }
            #endregion

            DB.SubmitChanges();
        }
        public void Calculate_Qty(int itmId, CellValueChangedEventArgs e, int store_id, byte uomIndex, decimal medium, decimal large, string batch)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            DataRow row = view.GetDataRow(e.RowHandle);
            //if (Shared.user.Sell_ShowCrntQty == true)
            //{
            //    decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, itmId, store_id,batch);
            //    currentQty = MyHelper.getCalculatedUomQty(currentQty, uomIndex, medium, large);

            //    view.GetDataRow(e.RowHandle)["CurrentQty"] = decimal.ToDouble(currentQty);
            //}
            //else
            //{
            //    view.GetDataRow(e.RowHandle)["CurrentQty"] = decimal.ToDouble(0);
            //}
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    btnAddCustomer.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.frm_JO_JobOrder).Count() < 1)
                {
                    barBtnLoad_JO.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_SalesOrder).Count() < 1)
                {
                    barBtnLoad_SalesOrder.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Quote).Count() < 1)
                {
                    barBtnLoad_Sl_Qoute.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.IC_OutTrns).Count() < 1)
                {
                    barBtn_OutTrns.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.ACC_RecieveNote).Count() < 1)
                    barBtnNotesReceivable.Enabled = false;
                else
                {
                    if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.ACC_RecieveNote).FirstOrDefault().CanAdd == false)
                        barBtnNotesReceivable.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.CashIn).Count() < 1)
                    barBtn_CashNote.Enabled = false;


                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
                if (!prvlg.CanPrint)
                {
                    barbtnPrint.Enabled = false;
                    barbtnPrintF.Enabled = false;
                    barSubItemPrint.Enabled = false;
                }
            }
        }

        private void lkp_Customers_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        DialogResult ChangesMade()
        {
            if (barBtnSave.Enabled == false)
                return DialogResult.No;
            if (
                DataModified ||
                dtSL_Details.GetChanges(DataRowState.Added) != null ||
                dtSL_Details.GetChanges(DataRowState.Modified) != null ||
                dtSL_Details.GetChanges(DataRowState.Deleted) != null
                )
            {
                DialogResult r = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDataModified : ResSLAr.MsgDataModified, "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    barBtnSave.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous                    
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        //private bool ValidData()
        //{
        //    DB = new ERPDataContext();

        //    //can't post in closed period
        //    if (ErpHelper.CanSaveInClsedPeriod(dtInvoiceDate.DateTime.Date, Shared.st_Store.ClosePeriodDate, Shared.user.EditInClosedPeriod) == false)
        //        return false;

        //    ((GridView)grdPrInvoice.FocusedView).FocusedRowHandle += 1;
        //    if (invoiceId == 0)
        //    {
        //        if (prvlg != null && !prvlg.CanAdd)
        //        {
        //            // "عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
        //            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvNew : ResSLAr.MsgPrvNew, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //            return false;
        //        }
        //    }
        //    if (invoiceId > 0)
        //    {
        //        if (prvlg != null && !prvlg.CanEdit)
        //        {
        //            //"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
        //            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvEdit : ResSLAr.MsgPrvEdit, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //            return false;
        //        }
        //        if (ErpHelper.CanEditPostedBill(invoiceId, (int)Process.SellInvoice, Shared.OfflinePostToGL,
        //            Shared.user.UserEditPostedBills) == false)
        //            return false;
        //    }

        //    if (lkp_Customers.EditValue == null)
        //    {
        //        XtraMessageBox.Show(
        //                Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgSelectCustomer,
        //                Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
        //                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        //        lkp_Customers.Focus();

        //        return false;
        //    }

        //    if (Shared.StockIsPeriodic && Convert.ToDecimal(txtDiscountValue.EditValue) > 0 && Shared.st_Store.SalesDiscountAcc.HasValue == false)
        //    {
        //        //check sales discount Account

        //        XtraMessageBox.Show(
        //                Shared.IsEnglish == true ? ResEn.MsgDiscountAcc : ResAr.MsgDiscountAcc,
        //                Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
        //                MessageBoxButtons.OK, MessageBoxIcon.Warning);

        //        return false;
        //    }

        //    if (Convert.ToDecimal(txt_TaxValue.EditValue) > 0 && Shared.st_Store.TaxAcc.HasValue == false)
        //    {
        //        //يجب تحديد حساب الضرائب
        //        //check sales tax Account

        //        XtraMessageBox.Show(
        //                Shared.IsEnglish == true ? ResEn.MsgSalesTaxAcc : ResAr.MsgSalesTaxAcc,
        //                Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
        //                MessageBoxButtons.OK, MessageBoxIcon.Warning);

        //        return false;
        //    }

        //    if (string.IsNullOrEmpty(txtInvoiceCode.Text.Trim()))
        //    {
        //        XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateInvNumber : ResSLAr.txtValidateInvNumber, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //        txtInvoiceCode.Focus();
        //        return false;
        //    }

        //    if (Shared.st_Store.InvoicesCodeRedundancy == false)
        //    {
        //        bool code_exist = InvCodeExist();
        //        if (invoiceId == 0 && code_exist && Shared.st_Store.GenerateNewInvCodeOnSave == true)
        //        {
        //            lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);
        //            return ValidData();
        //        }
        //        if (ErpHelper.ValidateInvCodeExist(code_exist, txtInvoiceCode) == false)
        //            return false;
        //    }

        //    if (Shared.SalesManAvailable && Shared.st_Store.SalesEmpMandatory && lkp_SalesEmp.EditValue == null)
        //    {
        //        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.SalesEmpMandatory : ResSLAr.SalesEmpMandatory,
        //            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        //        lkp_SalesEmp.Focus();
        //        return false;
        //    }

        //    /*منع*/
        //    if (Shared.user.SellCustomerOverCredit == false && (cust_IsDebit == true
        //                && (Convert.ToDecimal(txt_MaxCredit.Text) > 0) &&
        //                (Convert.ToDecimal(txt_Balance_After.Text) > Convert.ToDecimal(txt_MaxCredit.Text))))
        //    {
        //        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebit : ResSLAr.txtValidateCustomerMaxDebit,
        //            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        //        txt_Remains.Focus();
        //        return false;
        //    }
        //    /*تحذير*/
        //    else if (Shared.user.SellCustomerOverCredit == true && (cust_IsDebit == true
        //                && (Convert.ToDecimal(txt_MaxCredit.Text) > 0) &&
        //                (Convert.ToDecimal(txt_Balance_After.Text) > Convert.ToDecimal(txt_MaxCredit.Text))))
        //    {
        //        DialogResult dr = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebitWarn : ResSLAr.txtValidateCustomerMaxDebitWarn,
        //            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
        //        if (dr == DialogResult.No)
        //        {
        //            txt_Remains.Focus();
        //            return false;
        //        }
        //    }

        //    //warn group max credit
        //    /*منع*/
        //    if (invoiceId == 0)
        //    {
        //        SL_Customer_Info custInfo = lst_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).Select(x => x).First();

        //        if (Shared.user.SellCustomerOverCredit == false)
        //        {
        //            if (custInfo.GrpId != null && custInfo.GroupMaxCredit > 0 &&
        //                    (Convert.ToDecimal(txt_Remains.Text) + (HelperAcc.Get_account_balance(custInfo.GroupAccountId.Value) * -1) > custInfo.GroupMaxCredit))
        //            {
        //                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustGrpMaxDebit : ResSLAr.txtValidateCustGrpMaxDebit,
        //                Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        //                txt_Remains.Focus();
        //                return false;
        //            }
        //        }
        //        /*تحذير*/
        //        else if (Shared.user.SellCustomerOverCredit == true)
        //        {
        //            if (custInfo.GrpId != null && custInfo.GroupMaxCredit > 0 &&
        //                    (Convert.ToDecimal(txt_Remains.Text) + (HelperAcc.Get_account_balance(custInfo.GroupAccountId.Value) * -1) > custInfo.GroupMaxCredit))
        //            {
        //                DialogResult dr = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustGrpMaxDebitWarn : ResSLAr.txtValidateCustGrpMaxDebitWarn,
        //                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
        //                if (dr == DialogResult.No)
        //                {
        //                    txt_Remains.Focus();
        //                    return false;
        //                }
        //            }
        //        }
        //    }

        //    int deleted_rows = 0;
        //    if (dtSL_Details.GetChanges(DataRowState.Deleted) != null)
        //        deleted_rows = dtSL_Details.GetChanges(DataRowState.Deleted).Rows.Count;

        //    if (dtSL_Details.Rows.Count - deleted_rows <= 0)
        //    {
        //        //يجب تسجيل صنف علي الاقل في الفاتوره
        //        XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateNoRows : ResSLAr.txtValidateNoRows, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //        grdPrInvoice.Focus();
        //        return false;
        //    }

        //    if (dtSL_Details.Rows[dtSL_Details.Rows.Count - 1].RowState != DataRowState.Deleted
        //        && dtSL_Details.Rows[dtSL_Details.Rows.Count - 1]["ItemId"] == DBNull.Value)
        //    {
        //        dtSL_Details.Rows[dtSL_Details.Rows.Count - 1].Delete();
        //        grdPrInvoice.RefreshDataSource();
        //    }

        //    //Validate user can make on credit invoice
        //    if (Shared.user.UserMakeOnCreditInv == false)
        //    {
        //        if (Convert.ToDecimal(txt_Remains.EditValue) > 0)
        //        {
        //            XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateOnCreditInv : ResSLAr.txtValidateOnCreditInv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //            txt_PayAcc1_Paid.Focus();
        //            return false;
        //        }
        //    }
        //    if (!grdPrInvoice.DefaultView.UpdateCurrentRow())
        //        return false;

        //    string msg = "";

        //    if (Shared.user.SellWithNoBalance != null && Shared.InvoicePostToStore)     //عدم التدخل                
        //    {
        //        if (MyHelper.Validate_detailsQty(Shared.st_Store.MultiplyDimensions, dtInvoiceDate.DateTime, Convert.ToInt32(lkpStore.EditValue), dtSL_Details, out msg))
        //            return true;
        //        else
        //        {
        //            if (Shared.user.SellWithNoBalance == false)     //منع
        //            {
        //                XtraMessageBox.Show(this.LookAndFeel, msg,
        //                    Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
        //                return false;
        //            }
        //            else if (Shared.user.SellWithNoBalance == true)
        //            {
        //                if (XtraMessageBox.Show(this.LookAndFeel, msg + "\r\n" + (Shared.IsEnglish ? ResEn.MsgContinue : ResAr.MsgContinue),
        //                    Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) != DialogResult.Yes)
        //                    return false;
        //            }
        //        }
        //    }

        //    if (chk_IsPosted.Checked == true && txt_Post_Date.EditValue == null)
        //    {
        //        XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResPrEn.txtValidatePostDate : ResPrAr.txtValidatePostDate, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        //        txt_Post_Date.Focus();
        //        return false;
        //    }



        //    if (btnSourceId.EditValue == null && Shared.st_Store.InvoiceWorkflow == (int)InvoiceWorkflow.BillThenInv)
        //    {
        //        XtraMessageBox.Show(this.LookAndFeel, (Shared.IsEnglish ? ResEn.SelectBill : ResAr.SelectBill),
        //            Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
        //        return false;
        //    }

        //    return true;
        //}

        private bool ValidData()
        {
            DB = new ERPDataContext();

            //can't post in closed period
            if (ErpHelper.CanSaveInClsedPeriod(dtInvoiceDate.DateTime.Date, Shared.st_Store.ClosePeriodDate, Shared.user.EditInClosedPeriod) == false)
                return false;

            ((GridView)grdPrInvoice.FocusedView).FocusedRowHandle += 1;
            if (invoiceId == 0)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    // "عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvNew : ResSLAr.MsgPrvNew, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (invoiceId > 0)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    //"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvEdit : ResSLAr.MsgPrvEdit, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
                if (ErpHelper.CanEditPostedBill(invoiceId, (int)Process.SellInvoice, Shared.OfflinePostToGL,
                    Shared.user.UserEditPostedBills) == false)
                    return false;
            }

            if (lkp_Customers.EditValue == null)
            {
                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgSelectCustomer,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_Customers.Focus();

                return false;
            }

            var ccc = lkp_Customers.GetSelectedDataRow();

            if (lkp_Customers.GetSelectedDataRow() != null &&
                ((SL_Customer_Info)lkp_Customers.GetSelectedDataRow()).Is_Blocked == true)
            {
                XtraMessageBox.Show(
                       Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgBlockedCustomer,
                       Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                       MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_Customers.Focus();

                return false;
            }

            if (Shared.StockIsPeriodic && Convert.ToDecimal(txtDiscountValue.EditValue) > 0 && Shared.st_Store.SalesDiscountAcc.HasValue == false)
            {
                //check sales discount Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgDiscountAcc : ResAr.MsgDiscountAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }

            if (Convert.ToDecimal(txt_TaxValue.EditValue) > 0 && Shared.st_Store.TaxAcc.HasValue == false)
            {
                //يجب تحديد حساب الضرائب
                //check sales tax Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSalesTaxAcc : ResAr.MsgSalesTaxAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }
            if (Convert.ToDecimal(txt_CusTaxV.EditValue) > 0 && Shared.st_Store.CustomTaxAcc.HasValue == false)
            {
                //يجب تحديد حساب الجدول
                //check Custom tax Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgCusTaxAcc : ResAr.MsgCusTaxAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }
            if (Convert.ToDecimal(txt_RetentionV.EditValue) > 0 && !Shared.RealEstateAvailable)
            {
                MessageBox.Show(Shared.IsEnglish ? "Please Select Retention Account from Setting" : "برجاء اختيار حساب الاحتفاظ من الاعدادات", Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            if (Convert.ToDecimal(txt_AdvancePayV.EditValue) > 0 && !Shared.RealEstateAvailable)
            {
                MessageBox.Show(Shared.IsEnglish ? "Please Select Advance Payment Account from Setting" : "برجاء اختيار حساب الدفعات المقدمة من الاعدادات", Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            if (string.IsNullOrEmpty(txtInvoiceCode.Text.Trim()))
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateInvNumber : ResSLAr.txtValidateInvNumber, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                txtInvoiceCode.Focus();
                return false;
            }

            if (Shared.st_Store.InvoicesCodeRedundancy == false)
            {
                bool code_exist = InvCodeExist();
                if (invoiceId == 0 && code_exist && Shared.st_Store.GenerateNewInvCodeOnSave == true)
                {
                    lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);
                    return ValidData();
                }
                if (ErpHelper.ValidateInvCodeExist(code_exist, txtInvoiceCode) == false)
                    return false;
            }

            if (Shared.SalesManAvailable && Shared.st_Store.SalesEmpMandatory && lkp_SalesEmp.EditValue == null)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.SalesEmpMandatory : ResSLAr.SalesEmpMandatory,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_SalesEmp.Focus();
                return false;
            }

            /*منع*/
            if (Shared.user.SellCustomerOverCredit == false && (cust_IsDebit == true
                        && (Convert.ToDecimal(txt_MaxCredit.Text) > 0) &&
                        (Convert.ToDecimal(txt_Balance_After.Text) > Convert.ToDecimal(txt_MaxCredit.Text))))
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebit : ResSLAr.txtValidateCustomerMaxDebit,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_Remains.Focus();
                return false;
            }
            /*تحذير*/
            else if (Shared.user.SellCustomerOverCredit == true && (cust_IsDebit == true
                        && (Convert.ToDecimal(txt_MaxCredit.Text) > 0) &&
                        (Convert.ToDecimal(txt_Balance_After.Text) > Convert.ToDecimal(txt_MaxCredit.Text))))
            {
                DialogResult dr = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebitWarn : ResSLAr.txtValidateCustomerMaxDebitWarn,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (dr == DialogResult.No)
                {
                    txt_Remains.Focus();
                    return false;
                }
            }

            //warn group max credit
            /*منع*/
            if (invoiceId == 0)
            {
                SL_Customer_Info custInfo = lst_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).Select(x => x).First();

                if (Shared.user.SellCustomerOverCredit == false)
                {
                    if (custInfo.GrpId != null && custInfo.GroupMaxCredit > 0 &&
                            (Convert.ToDecimal(txt_Remains.Text) + (HelperAcc.Get_account_balance(custInfo.GroupAccountId.Value) * -1) > custInfo.GroupMaxCredit))
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustGrpMaxDebit : ResSLAr.txtValidateCustGrpMaxDebit,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txt_Remains.Focus();
                        return false;
                    }
                }
                /*تحذير*/
                else if (Shared.user.SellCustomerOverCredit == true)
                {
                    if (custInfo.GrpId != null && custInfo.GroupMaxCredit > 0 &&
                            (Convert.ToDecimal(txt_Remains.Text) + (HelperAcc.Get_account_balance(custInfo.GroupAccountId.Value) * -1) > custInfo.GroupMaxCredit))
                    {
                        DialogResult dr = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustGrpMaxDebitWarn : ResSLAr.txtValidateCustGrpMaxDebitWarn,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                        if (dr == DialogResult.No)
                        {
                            txt_Remains.Focus();
                            return false;
                        }
                    }
                }
            }

            int deleted_rows = 0;
            if (dtSL_Details.GetChanges(DataRowState.Deleted) != null)
                deleted_rows = dtSL_Details.GetChanges(DataRowState.Deleted).Rows.Count;

            if (dtSL_Details.Rows.Count - deleted_rows <= 0)
            {
                //يجب تسجيل صنف علي الاقل في الفاتوره
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateNoRows : ResSLAr.txtValidateNoRows, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                grdPrInvoice.Focus();
                return false;
            }

            if (dtSL_Details.Rows[dtSL_Details.Rows.Count - 1].RowState != DataRowState.Deleted
                && dtSL_Details.Rows[dtSL_Details.Rows.Count - 1]["ItemId"] == DBNull.Value)
            {
                dtSL_Details.Rows[dtSL_Details.Rows.Count - 1].Delete();
                grdPrInvoice.RefreshDataSource();
            }

            //Validate user can make on credit invoice
            if (Shared.user.UserMakeOnCreditInv == false)
            {
                if (Convert.ToDecimal(txt_Remains.EditValue) > 0)
                {
                    XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateOnCreditInv : ResSLAr.txtValidateOnCreditInv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    txt_PayAcc1_Paid.Focus();
                    return false;
                }
            }
            if (!grdPrInvoice.DefaultView.UpdateCurrentRow())
                return false;

            string msg = "";

            if (Shared.user.SellWithNoBalance != null && Shared.InvoicePostToStore)     //عدم التدخل                
            {
                if (MyHelper.Validate_detailsQty(Shared.st_Store.MultiplyDimensions, dtInvoiceDate.DateTime, Convert.ToInt32(lkpStore.EditValue), dtSL_Details, out msg))
                    return true;
                else
                {
                    if (Shared.user.SellWithNoBalance == false)     //منع
                    {
                        XtraMessageBox.Show(this.LookAndFeel, msg,
                            Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                        return false;
                    }
                    else if (Shared.user.SellWithNoBalance == true)
                    {
                        if (XtraMessageBox.Show(this.LookAndFeel, msg + "\r\n" + (Shared.IsEnglish ? ResEn.MsgContinue : ResAr.MsgContinue),
                            Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) != DialogResult.Yes)
                            return false;
                    }
                }
            }

            if (chk_IsPosted.Checked == true && txt_Post_Date.EditValue == null)
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResPrEn.txtValidatePostDate : ResPrAr.txtValidatePostDate, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                txt_Post_Date.Focus();
                return false;
            }



            if (btnSourceId.EditValue == null && Shared.st_Store.InvoiceWorkflow == (int)InvoiceWorkflow.BillThenInv)
            {
                XtraMessageBox.Show(this.LookAndFeel, (Shared.IsEnglish ? ResEn.SelectBill : ResAr.SelectBill),
                    Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }

            return true;
        }
        private void lkpStore_EditValueChanged(object sender, EventArgs e)
        {
            if ((lkp_InvoiceBook.EditValue == null || Convert.ToInt32(lkp_InvoiceBook.EditValue) == 0) && invoiceId < 1)
            {
                #region GetNextInvNumber
                string lastNumber;
                //check saving store in system to define store_id
                //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)

                    lastNumber = (from x in DB.SL_InvoiceArchives
                                  where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                  where x.InvoiceBookId == null
                                  orderby x.InvoiceDate descending
                                  orderby x.SL_InvoiceId descending
                                  select x.InvoiceCode).FirstOrDefault();
                else
                    lastNumber = (from x in DB.SL_InvoiceArchives
                                  join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                  where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                  where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                                  s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                                  where x.InvoiceBookId == null
                                  orderby x.InvoiceDate descending
                                  orderby x.SL_InvoiceId descending
                                  select x.InvoiceCode).FirstOrDefault();
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);
                #endregion
            }
            else if (lkp_InvoiceBook.EditValue != null && Convert.ToInt32(lkp_InvoiceBook.EditValue) != 0 && invoiceId < 1)
            {
                #region GetNextInvNumber
                var lastNumber = (from x in DB.SL_InvoiceArchives
                                  where x.InvoiceBookId == Convert.ToInt32(lkp_InvoiceBook.EditValue)
                                  orderby x.InvoiceDate descending
                                  orderby x.SL_InvoiceId descending
                                  select x.InvoiceCode).FirstOrDefault();
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);
                #endregion
            }
            IsTaxable = (bool?)lkp_InvoiceBook.GetColumnValue("IsTaxable");

            if (lkpStore.EditValue != null && Convert.ToInt32(lkpStore.EditValue) != 0)
                lkpCostCenter.EditValue = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostCenter).First();



        }

        private void frm_SL_Invoice_Shown(object sender, EventArgs e)
        {
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مبيعات جديدة");
        }

        //void GetLastPrices(bool vendorInvoices, bool showLastPrices)
        //{
        //    if (!showLastPrices)
        //    {
        //        Page_LastPrices.PageVisible = false;
        //        return;
        //    }

        //    GridView view = grdPrInvoice.FocusedView as GridView;
        //    if (view.GetFocusedRowCellValue("ItemId") == null)
        //        return;

        //    int ItemId = 0;
        //    Int32.TryParse(view.GetFocusedRowCellValue("ItemId").ToString(), out ItemId);
        //    if (ItemId < 1)
        //    {
        //        return;
        //    }
        //    Page_LastPrices.PageVisible = true;
        //    xtraTabControl1.SelectedTabPage = Page_LastPrices;

        //    if (vendorInvoices)
        //    {
        //        Page_LastPrices.Text = Shared.IsEnglish ? ResSLEn.txtLastCustomerPPrices : ResSLAr.txtLastCustomerPPrices; //"اخر اسعار بيع لعميل";
        //        DAL.ERPDataContext DB = new DAL.ERPDataContext();
        //        var lastInvoices = (from p in DB.SL_Invoices
        //                            join d in DB.SL_InvoiceDetails
        //                            on p.SL_InvoiceId equals d.SL_InvoiceId
        //                            where d.ItemId == ItemId
        //                            where p.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)
        //                            where Shared.user.UserChangeStore ? true : p.StoreId == Shared.user.DefaultStore
        //                            select new
        //                            {
        //                                InvoiceCode = p.InvoiceCode,
        //                                InvoiceDate = p.InvoiceDate,
        //                                UOM = DB.IC_UOMs.Where(u => u.UOMId == d.UOMId).Select(u => u.UOM).FirstOrDefault(),
        //                                Qty = decimal.ToDouble(d.Qty),
        //                                SellPrice = decimal.ToDouble(d.SellPrice),
        //                                TotalSellPrice = decimal.ToDouble(d.TotalSellPrice)
        //                            }).OrderByDescending(p => p.InvoiceDate).Take(10);
        //        colCustNameAr.Visible = false;
        //        grdLastPrices.DataSource = lastInvoices;
        //    }
        //    else
        //    {
        //        Page_LastPrices.Text = Shared.IsEnglish ? ResSLEn.txtLastPPrices : ResSLAr.txtLastPPrices; //"اخر اسعار بيع";
        //        var lastInvoices = (from p in DB.SL_Invoices
        //                            join v in DB.SL_Customers
        //                            on p.CustomerId equals v.CustomerId
        //                            join d in DB.SL_InvoiceDetails
        //                            on p.SL_InvoiceId equals d.SL_InvoiceId
        //                            where d.ItemId == ItemId
        //                            where Shared.user.UserChangeStore ? true : p.StoreId == Shared.user.DefaultStore
        //                            select new
        //                            {
        //                                InvoiceCode = p.InvoiceCode,
        //                                InvoiceDate = p.InvoiceDate,
        //                                CusNameAr = v.CusNameAr,
        //                                UOM = DB.IC_UOMs.Where(u => u.UOMId == d.UOMId).Select(u => u.UOM).FirstOrDefault(),
        //                                Qty = decimal.ToDouble(d.Qty),
        //                                SellPrice = decimal.ToDouble(d.SellPrice),
        //                                TotalSellPrice = decimal.ToDouble(d.TotalSellPrice)
        //                            }).OrderByDescending(p => p.InvoiceDate).Take(10);
        //        colCustNameAr.VisibleIndex = 4;
        //        colCustNameAr.Visible = true;
        //        grdLastPrices.DataSource = lastInvoices;
        //    }
        //}

        private void rep_expireDate_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            #region Expire
            if (e.Value == null || e.Value == DBNull.Value)
                return;
            try
            {
                DateTime date = Convert.ToDateTime(e.DisplayText);
                e.DisplayText = date.Month + "-" + date.Year;
            }
            catch
            { }

            #endregion
        }

        private void gridView5_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            #region Expire
            if (e.Column.FieldName == "Expire")
            {
                if (e.Value == null || e.Value == DBNull.Value)
                    return;
                try
                {
                    DateTime date = Convert.ToDateTime(e.Value);
                    e.DisplayText = date.Month + "-" + date.Year;
                }
                catch
                { }
            }
            #endregion
        }


        private void lkp_Drawers2_EditValueChanged(object sender, EventArgs e)
        {
            if (lkp_Drawers2.EditValue != null && Convert.ToInt32(lkp_Drawers2.EditValue) == Convert.ToInt32(lkp_Drawers.EditValue))
                lkp_Drawers2.EditValue = null;

            if (lkp_Drawers2.EditValue == null)
            {
                txt_PayAcc2_Paid.EditValue = 0;
                txt_PayAcc2_Paid.Enabled = false;
            }
            else
            {
                txt_PayAcc2_Paid.Enabled = true;
            }
        }

        private void txt_DueDays_Leave(object sender, EventArgs e)
        {
            if (Convert.ToInt32(txt_DueDays.EditValue) < 0)
                txt_DueDays.EditValue = 0;

            txt_DueDate.EditValue = dtInvoiceDate.DateTime.AddDays(Convert.ToInt32(txt_DueDays.EditValue));
        }

        private void txt_DueDate_EditValueChanged(object sender, EventArgs e)
        {
            if (((DateEdit)sender).Name == "dtInvoiceDate")
            {
                if (invoiceId == 0)
                    txt_DueDate.EditValue = dtInvoiceDate.DateTime.AddDays(Convert.ToInt32(txt_DueDays.EditValue));
            }
            if (((DateEdit)sender).Name == "txt_DueDate")
            {

                txt_DueDays.EditValue = txt_DueDate.DateTime.Date.Subtract(dtInvoiceDate.DateTime.Date).Days;
            }

            //if (txt_DueDate.EditValue != null && txt_DueDate.DateTime.Date < dtInvoiceDate.DateTime.Date)
            //    txt_DueDate.EditValue = dtInvoiceDate.EditValue;

            //if (txt_DueDate.DateTime != DateTime.MinValue)
            //    txt_DueDays.EditValue = (txt_DueDate.DateTime - dtInvoiceDate.DateTime).Days;
        }

        private void btn_AddMatrixItems_Click(object sender, EventArgs e)
        {

            new frm_IC_MatrixAddInv().ShowDialog();

            foreach (var d in frm_IC_MatrixAddInv.lst_InvMatrixItems)
            {
                var item = DB.IC_Items.Where(x => x.ItemId == d.ItemId).FirstOrDefault();

                #region Can't Sell over current Qty
                //decimal CurrentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.ItemId, Convert.ToInt32(lkpStore.EditValue));

                //if (invoiceId == 0
                //        && d.Qty > CurrentQty)//validate when new invoice only
                //{
                //    if (Shared.user.SellWithNoBalance.HasValue)
                //        continue;
                //}
                #endregion

                DataRow row = dtSL_Details.NewRow();
                row["ItemId"] = item.ItemId;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);
                row["SellPrice"] = Decimal.ToDouble(item.SmallUOMPrice);
                row["DiscountRatio"] = "0";
                row["DiscountValue"] = "0";

                if (IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable")))
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                else if (IsTaxable == true)
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                else
                    row["SalesTaxRatio"] = 0;

                row["calcTaxBeforeDisc"] = item.calcTaxBeforeDisc;

                row["Qty"] = decimal.ToDouble(d.Qty);

                decimal salestaxratio = Convert.ToDecimal(row["SalesTaxRatio"]);
                decimal CustomTaxRatio = Convert.ToDecimal(row["CustomTaxRatio"]);
                decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);
                decimal TotalSellPrice = d.Qty * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                if (Shared.st_Store.PriceIncludeSalesTax)    /*السعر شامل الضريبة*/
                {
                    decimal temp = item.calcTaxBeforeDisc ? (salestaxratio * d.Qty * Convert.ToDecimal(row["SellPrice"])) / (1 + salestaxratio) :
                        (salestaxratio * TotalSellPrice) / (1 + salestaxratio);
                    row["SalesTax"] = decimal.ToDouble(temp);
                    row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice - temp);// السعر الاجمالي شامل الضريبة                            

                    var totalsellp = (TotalSellPrice - temp);
                    //custom
                    decimal temp2 = item.calcTaxBeforeDisc ? (CustomTaxRatio * d.Qty * Convert.ToDecimal(row["SellPrice"])) / (1 + CustomTaxRatio) :
                       (CustomTaxRatio * totalsellp) / (1 + CustomTaxRatio);
                    row["CustomTax"] = decimal.ToDouble(temp2);
                    row["TotalSellPrice"] = decimal.ToDouble(totalsellp - temp2);// السعر الاجمالي شامل الضريبة                            

                }
                else
                {
                    decimal temp = item.calcTaxBeforeDisc ? (salestaxratio * d.Qty * Convert.ToDecimal(row["SellPrice"])) : (salestaxratio * TotalSellPrice);
                    row["SalesTax"] = decimal.ToDouble(temp);
                    row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);//ضيف الضريبة على السعر الاجمالي                            

                    var totalsellp = (TotalSellPrice);
                    //custom tax
                    decimal temp2 = item.calcTaxBeforeDisc ? (CustomTaxRatio * d.Qty * Convert.ToDecimal(row["SellPrice"])) : (CustomTaxRatio * totalsellp);
                    row["CustomTax"] = decimal.ToDouble(temp2);
                    row["TotalSellPrice"] = decimal.ToDouble(totalsellp);
                }

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);
                MyHelper.GetUOMs(item, dtUOM, uom_list);
                row["UOM"] = dtUOM.Rows[0]["UomId"];
                row["UomIndex"] = "0";
                row["IsExpire"] = item.IsExpire;

                //if (Shared.user.Sell_ShowCrntQty == true)
                //{
                //    decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.ItemId, Convert.ToInt32(lkpStore.EditValue) );
                //    row["CurrentQty"] = decimal.ToDouble(currentQty);
                //}
                //else
                //{
                //    row["CurrentQty"] = decimal.ToDouble(0);
                //}
                row["ItemType"] = item.ItemType;

                row["ItemDescription"] = item.Description;
                row["ItemDescriptionEn"] = item.DescriptionEn;

                row["ParentItemId"] = item.mtrxParentItem;
                row["M1"] = item.mtrxAttribute1;
                row["M2"] = item.mtrxAttribute2;
                row["M3"] = item.mtrxAttribute3;

                row["Length"] = Decimal.ToDouble(item.Length);
                row["Width"] = Decimal.ToDouble(item.Width);
                row["Height"] = Decimal.ToDouble(item.Height);

                dtSL_Details.Rows.Add(row);
            }
        }

        private void gridView2_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            var view = ((sender) as GridView);
            decimal totalqty = 0;
            try
            {
                if (e.Column == col_TotalQty
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType") != DBNull.Value
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty") != DBNull.Value
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length") != DBNull.Value
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width") != DBNull.Value
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height") != DBNull.Value)
                {
                    if (Convert.ToInt32(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType")) != (int)ItemType.Subtotal)
                    {
                        totalqty = Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height"));
                    }
                    else
                    {
                        int i = e.ListSourceRowIndex < 0 ? view.RowCount - 1 : e.ListSourceRowIndex - 1;
                        while (Convert.ToInt32(view.GetListSourceRowCellValue(i, "ItemType")) != (int)ItemType.Subtotal && i >= 0)
                        {
                            if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                                totalqty += Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Length")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Height")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Width")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Qty"));
                            i--;
                        }
                    }
                    e.Value = decimal.ToDouble(totalqty);
                }
            }
            catch { }
        }

        private void Get_SubTotal_RowData(DataRow row, GridView view, int CurrentRowHandle)
        {
            row["Qty"] = 0;
            row["Length"] = 0;
            row["Width"] = 0;
            row["Height"] = 0;
            row["DiscountRatio"] = 0;
            row["UOM"] = 0;
            row["UomIndex"] = 0;
            //row["CurrentQty"] = 0;
            row["ItemDescription"] = "";
            row["ItemDescriptionEn"] = "";

            int i = CurrentRowHandle < 0 ? view.RowCount - 1 : CurrentRowHandle - 1;
            decimal TotalSellPrice = 0, TotalDiscountValue = 0, TotalSalesTax = 0, TotalCusTax = 0,
                TotalPiecesCount = 0, TotalQty = 0, SellPrice = 0;

            while (Convert.ToInt32(view.GetRowCellValue(i, "ItemType")) != (int)ItemType.Subtotal && i >= 0)
            {
                TotalQty += Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));
                TotalSellPrice += Convert.ToDecimal(view.GetRowCellValue(i, "TotalSellPrice"));
                TotalDiscountValue += Convert.ToDecimal(view.GetRowCellValue(i, "DiscountValue"));

                decimal totalrowqty = Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));
                if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                    totalrowqty = Convert.ToDecimal(view.GetRowCellValue(i, "Length")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Height")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Width")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));

                TotalSalesTax += Convert.ToDecimal(view.GetRowCellValue(i, "SalesTax"));
                TotalCusTax += Convert.ToDecimal(view.GetRowCellValue(i, "CustomTax"));
                SellPrice += (Convert.ToDecimal(view.GetRowCellValue(i, "SellPrice")) * totalrowqty);
                TotalPiecesCount += Convert.ToDecimal(view.GetRowCellValue(i, "PiecesCount"));
                i--;
            }

            row["Qty"] = decimal.ToDouble(TotalQty);
            row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);
            row["DiscountValue"] = decimal.ToDouble(TotalDiscountValue);
            row["SalesTax"] = decimal.ToDouble(TotalSalesTax);
            row["CustomTax"] = decimal.ToDouble(TotalCusTax);
            row["SellPrice"] = decimal.ToDouble(SellPrice);
            row["PiecesCount"] = decimal.ToDouble(TotalPiecesCount);

        }
        private void Update_First_SubTotal(GridView view, int CurrentRowHandle)
        {
            if (CurrentRowHandle >= 0)
            {
                for (int i = CurrentRowHandle; i < view.RowCount; i++)
                {
                    if (Convert.ToInt32(view.GetRowCellValue(i, "ItemType")) == (int)ItemType.Subtotal)
                    {
                        Get_SubTotal_RowData(view.GetDataRow(i), view, i);
                        return;
                    }
                }
            }
            else if (Convert.ToInt32(view.GetFocusedRowCellValue("ItemType")) == (int)ItemType.Subtotal)
                Get_SubTotal_RowData(view.GetFocusedDataRow(), view, CurrentRowHandle);
        }

        private void gridView2_RowStyle(object sender, RowStyleEventArgs e)
        {
            if (e.RowHandle >= 0 && Convert.ToInt32(gridView2.GetRowCellValue(e.RowHandle, "ItemType")) == (int)ItemType.Subtotal)
            {
                e.HighPriority = true;
                e.Appearance.BackColor = Shared.user.SubtotalBackcolor == null ? Color.Yellow : Color.FromName(Shared.user.SubtotalBackcolor);
            }
        }

        private void Print_Prescriptions()
        {
            //GridView view = grdPrInvoice.FocusedView as GridView;
            //DataTable dt = new DataTable();
            //dt.Columns.Add("ItemName");
            //dt.Columns.Add("ItemDescription");
            //dt.Columns.Add("Qty");

            //for (int i = 0; i < view.RowCount; i++)
            //{
            //    if (view.GetRowCellDisplayText(i, "ItemCode1") == string.Empty)
            //        continue;

            //    dt.Rows.Add(view.GetRowCellDisplayText(i, "ItemId"), view.GetRowCellDisplayText(i, "ItemDescription"), view.GetRowCellValue(i, "Qty"));
            //}
            //new Reports.rpt_PharmacyPrescriptionLabels(dt, 1, null, txt_AttnMr.Text, dtInvoiceDate.Text).ShowPreview();
        }

        private void mi_PasteRows_Click(object sender, EventArgs e)
        {
            foreach (DataRow dr in Reports.ReportsUtils.dt_Copied_Rows.Rows)
            {
                decimal totalqty = Convert.ToDecimal(dr["Qty"]);
                if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                    totalqty = Convert.ToDecimal(dr["Qty"]) * Convert.ToDecimal(dr["Length"]) * Convert.ToDecimal(dr["Width"])
                        * Convert.ToDecimal(dr["Height"]);

                DataRow row = dtSL_Details.NewRow();
                int itemId = Convert.ToInt32(dr["ItemId"]);
                var item = DB.IC_Items.Where(x => x.ItemId == itemId).FirstOrDefault();
                LoadItemRow(item, row);
                row["Qty"] = Convert.ToDouble(dr["Qty"]);
                row["Height"] = dr["Height"];
                row["Length"] = dr["Length"];
                row["Width"] = dr["Width"];
                row["PiecesCount"] = dr["PiecesCount"];
                row["ExpireDate"] = dr["Expire"];
                row["Expire"] = dr["Expire"];
                row["Batch"] = dr["Batch"];
                row["Serial"] = dr["Serial"];
                if (dr["SellPrice"] != DBNull.Value)
                    row["SellPrice"] = dr["SellPrice"];

                row["TotalSellPrice"] = decimal.ToDouble(Convert.ToDecimal(row["SellPrice"]) * totalqty);
                dtSL_Details.Rows.Add(row);
            }
        }

        private void txtDiscountValue_Leave(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }



        private void txtDiscountRatio_EditValueChanged(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        //private void Get_TotalAccount()
        //{
        //    gridView1.RefreshData();
        //    try
        //    {
        //        decimal total_sell = 0;
        //        decimal total_salestax = 0;
        //        decimal net = 0;
        //        decimal discount_ratio = 0;
        //        decimal Discount_value = 0;

        //        foreach (DataRow dr in dtSL_Details.Rows)
        //        {
        //            if (dr.RowState == DataRowState.Deleted)
        //                continue;

        //            if (Convert.ToInt32(dr["ItemType"]) != (int)ItemType.Subtotal)
        //            {
        //                total_sell += Convert.ToDecimal(dr["TotalSellPrice"]);
        //                total_salestax += (Convert.ToDecimal(dr["SalesTax"]));
        //            }
        //        }

        //        #region Discount
        //        discount_ratio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
        //        Discount_value = Convert.ToDecimal(txtDiscountValue.EditValue);

        //        if (discount_ratio > 0)
        //        {
        //            if (Utilities.ValuesNotEqual(total_sell * discount_ratio, Discount_value))
        //                Discount_value = total_sell * discount_ratio;
        //        }
        //        //else
        //        //    Discount_value = 0;
        //        #endregion

        //        #region Deduct_Tax
        //        decimal deductTax_ratio = Convert.ToDecimal(txt_DeductTaxR.EditValue) / 100;
        //        decimal deductTax_value = Convert.ToDecimal(txt_DeductTaxV.EditValue);

        //        if (deductTax_ratio > 0)
        //        {
        //            if (Utilities.ValuesNotEqual(total_sell * deductTax_ratio, deductTax_value))
        //                deductTax_value = total_sell * deductTax_ratio;
        //        }
        //        //else
        //        //    deductTax_value = 0;
        //        #endregion

        //        #region Add_Tax
        //        decimal addTax_ratio = Convert.ToDecimal(txt_AddTaxR.EditValue) / 100;
        //        decimal addTax_value = Convert.ToDecimal(txt_AddTaxV.EditValue);

        //        if (addTax_ratio > 0)
        //        {
        //            if (Utilities.ValuesNotEqual(total_sell * addTax_ratio, addTax_value))
        //                addTax_value = total_sell * addTax_ratio;
        //        }
        //        //else
        //        //    addTax_value = 0;
        //        #endregion

        //        #region Expenses
        //        decimal expenses_ratio = Convert.ToDecimal(txtExpensesR.EditValue) / 100;
        //        decimal expenses_value = Convert.ToDecimal(txtExpenses.EditValue);

        //        if (expenses_ratio > 0)
        //        {
        //            if (Utilities.ValuesNotEqual(total_sell * expenses_ratio, expenses_value))
        //                expenses_value = total_sell * expenses_ratio;
        //        }
        //        //else
        //        //    expenses_value = 0;
        //        #endregion


        //        total_sell = decimal.Round(total_sell, 4);
        //        total_salestax = decimal.Round(total_salestax, 4);
        //        deductTax_value = decimal.Round(deductTax_value, 4);
        //        addTax_value = decimal.Round(addTax_value, 4);
        //        Discount_value = decimal.Round(Discount_value, 4);
        //        expenses_value = decimal.Round(expenses_value, 4);

        //        net = total_sell + total_salestax + addTax_value - deductTax_value - Discount_value + expenses_value;

        //        txtNet.EditValue = decimal.ToDouble(net);
        //        txtDiscountValue.EditValue = decimal.ToDouble(Discount_value);
        //        txtExpenses.EditValue = decimal.ToDouble(expenses_value);
        //        txt_DeductTaxV.EditValue = decimal.ToDouble(deductTax_value);
        //        txt_AddTaxV.EditValue = decimal.ToDouble(addTax_value);
        //        txt_TaxValue.EditValue = decimal.ToDouble(total_salestax);

        //        txt_Total.EditValue = decimal.ToDouble(total_sell);

        //        if (Shared.user.SL_Invoice_PayMethod == false)          //اجل
        //        {
        //            if (Convert.ToDecimal(txt_paid.EditValue) == 0)
        //                txt_Remains.EditValue = decimal.ToDouble(net);
        //            txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue));
        //        }
        //        else
        //        {
        //            if (Convert.ToDecimal(txt_Remains.EditValue) == 0 && Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) == 0)
        //                txt_PayAcc1_Paid.EditValue = decimal.ToDouble(net);
        //            txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue));
        //        }
        //    }
        //    catch { }
        //}

        private void Get_TotalAccount()
        {
            gridView1.RefreshData();
            try
            {
                decimal total_sell = 0;
                decimal total_salestax = 0, total_Custax = 0;
                decimal net = 0;
                decimal discount_ratio = 0;
                decimal Discount_value = 0;
                decimal Retention_ratio = 0;
                decimal Retention_value = 0;
                decimal AdvancePay_ratio = 0;
                decimal AdvancePay_value = 0;
                foreach (DataRow dr in dtSL_Details.Rows)
                {
                    if (dr.RowState == DataRowState.Deleted)
                        continue;

                    if (Convert.ToInt32(dr["ItemType"]) != (int)ItemType.Subtotal)
                    {
                        total_sell += Convert.ToDecimal(dr["TotalSellPrice"]);
                        total_salestax += (Convert.ToDecimal(dr["SalesTax"]));
                        total_Custax += (Convert.ToDecimal(dr["CustomTax"]));
                    }
                }

                #region Discount
                discount_ratio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
                Discount_value = Convert.ToDecimal(txtDiscountValue.EditValue);

                if (discount_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * discount_ratio, Discount_value))
                        Discount_value = total_sell * discount_ratio;
                }
                //else
                //    Discount_value = 0;
                #endregion

                #region Deduct_Tax
                decimal deductTax_ratio = Convert.ToDecimal(txt_DeductTaxR.EditValue) / 100;
                decimal deductTax_value = Convert.ToDecimal(txt_DeductTaxV.EditValue);

                if (deductTax_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * deductTax_ratio, deductTax_value))
                        deductTax_value = total_sell * deductTax_ratio;
                }
                //else
                //    deductTax_value = 0;
                #endregion

                #region Add_Tax
                decimal addTax_ratio = Convert.ToDecimal(txt_AddTaxR.EditValue) / 100;
                decimal addTax_value = Convert.ToDecimal(txt_AddTaxV.EditValue);

                if (addTax_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * addTax_ratio, addTax_value))
                        addTax_value = total_sell * addTax_ratio;
                }
                //else
                //    addTax_value = 0;
                #endregion

                #region Expenses
                decimal expenses_ratio = Convert.ToDecimal(txtExpensesR.EditValue) / 100;
                decimal expenses_value = Convert.ToDecimal(txtExpenses.EditValue);

                if (expenses_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * expenses_ratio, expenses_value))
                        expenses_value = total_sell * expenses_ratio;
                }
                //else
                //    expenses_value = 0;
                #endregion

                #region Add_CusTax
                //decimal CusTaxR = Convert.ToDecimal(txt_CusTaxR.EditValue) / 100;
                //decimal CusTaxV = Convert.ToDecimal(txt_CusTaxV.EditValue);

                //if (CusTaxR > 0)
                //{
                //    if (Utilities.ValuesNotEqual(total_sell * CusTaxR, CusTaxV))
                //        CusTaxV = total_sell * CusTaxR;
                //}

                #endregion

                #region Retention
                Retention_ratio = Convert.ToDecimal(txt_retentionR.EditValue) / 100;
                Retention_value = Convert.ToDecimal(txt_RetentionV.EditValue);

                if (Retention_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * Retention_ratio, Retention_value))
                        Retention_value = total_sell * Retention_ratio;
                }

                #endregion
                #region Advance Payment
                AdvancePay_ratio = Convert.ToDecimal(txt_AdvancePayR.EditValue) / 100;
                AdvancePay_value = Convert.ToDecimal(txt_AdvancePayV.EditValue);

                if (AdvancePay_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * AdvancePay_ratio, AdvancePay_value))
                        AdvancePay_value = total_sell * AdvancePay_ratio;
                }

                #endregion



                total_sell = decimal.Round(total_sell, 4);
                total_salestax = decimal.Round(total_salestax, 4);
                total_Custax = decimal.Round(total_Custax, 4);
                deductTax_value = decimal.Round(deductTax_value, 4);
                addTax_value = decimal.Round(addTax_value, 4);
                // CusTaxV = decimal.Round(CusTaxV, 4);
                Discount_value = decimal.Round(Discount_value, 4);
                expenses_value = decimal.Round(expenses_value, 4);
                Retention_value = decimal.Round(Retention_value, 4);
                AdvancePay_value = decimal.Round(AdvancePay_value, 4);
                net = total_sell + total_salestax + total_Custax + addTax_value/*+ CusTaxV*/ - deductTax_value - Discount_value - Retention_value - AdvancePay_value + expenses_value;

                txtNet.EditValue = decimal.ToDouble(net);
                txtDiscountValue.EditValue = decimal.ToDouble(Discount_value);
                txtExpenses.EditValue = decimal.ToDouble(expenses_value);
                txt_DeductTaxV.EditValue = decimal.ToDouble(deductTax_value);
                txt_AddTaxV.EditValue = decimal.ToDouble(addTax_value);
                txt_CusTaxV.EditValue = decimal.ToDouble(total_Custax);
                txt_TaxValue.EditValue = decimal.ToDouble(total_salestax);
                //Advance payment and retention
                txt_AdvancePayV.EditValue = decimal.ToDouble(AdvancePay_value);
                //  txt_AdvancePayR.EditValue = decimal.ToDouble(AdvancePay_ratio);
                txt_RetentionV.EditValue = decimal.ToDouble(Retention_value);
                //  txt_retentionR.EditValue = decimal.ToDouble(Retention_ratio);

                txt_Total.EditValue = decimal.ToDouble(total_sell);

                if (Shared.user.SL_Invoice_PayMethod == false)          //اجل
                {
                    if (Convert.ToDecimal(txt_paid.EditValue) == 0)
                        txt_Remains.EditValue = decimal.ToDouble(net);
                    txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue));
                }
                else
                {
                    if (Convert.ToDecimal(txt_Remains.EditValue) == 0 && Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) == 0)
                        txt_PayAcc1_Paid.EditValue = decimal.ToDouble(net);
                    txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue));
                }
            }
            catch { }
        }

        private void txt_paid_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (Shared.user.SL_Invoice_PayMethod == true)
                {
                    if (Convert.ToDecimal(txt_Remains.EditValue) <= 0)
                    {
                        cmbPayMethod.EditValue = true;
                    }
                    else if (Convert.ToDecimal(txt_paid.EditValue) == 0)
                    {
                        cmbPayMethod.EditValue = false;
                    }
                    else
                        cmbPayMethod.EditValue = null;
                }

                if ((sender as TextEdit).Name == "txt_paid")
                    txt_Remains.EditValue = Convert.ToDecimal(txtNet.EditValue) - Convert.ToDecimal(txt_paid.EditValue);
                else if ((sender as TextEdit).Name == "txt_Remains")
                {
                    txt_paid.EditValue = Convert.ToDecimal(txtNet.EditValue) - Convert.ToDecimal(txt_Remains.EditValue);
                    //lkp_Customers_EditValueChanged(null, EventArgs.Empty);

                    double balance_before = Convert.ToDouble(txt_Balance_Before.Text);
                    if (cust_IsDebit == true)
                        balance_before = balance_before * -1;

                    double balance_after = balance_before - Convert.ToDouble(txt_Remains.EditValue);
                    txt_Balance_After.Text = Math.Abs(balance_after).ToString("0,0.00");
                    if (balance_after > 0)
                        lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                    else if (balance_after < 0)
                        lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                    else
                        lbl_IsCredit_After.Text = "";

                    double max_debit = Convert.ToDouble(txt_MaxCredit.Text);
                    if (balance_after <= 0 && Math.Abs(balance_after) > max_debit && max_debit > 0)
                    {
                        lbl_Validate_MaxLimit.Text = Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebit : ResSLAr.txtValidateCustomerMaxDebit;
                        lbl_Validate_MaxLimit.ForeColor = Color.Red;
                    }
                    else
                    {
                        lbl_Validate_MaxLimit.Text = "";
                    }
                }
            }
            catch
            { }

        }

        private void txt_PayAcc1_Paid_EditValueChanged(object sender, EventArgs e)
        {
            if (Convert.ToDecimal(txt_PayAcc1_Paid.EditValue) < 0)
                txt_PayAcc1_Paid.EditValue = 0;
            if (Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) < 0)
                txt_PayAcc2_Paid.EditValue = 0;

            txt_paid.EditValue = Decimal.ToDouble(Convert.ToDecimal(txt_PayAcc1_Paid.EditValue) + Convert.ToDecimal(txt_PayAcc2_Paid.EditValue));
        }


        void dt_TableNewRow(object sender, DataTableNewRowEventArgs e)
        {
            Get_TotalAccount();
        }
        void dt_RowChanged(object sender, DataRowChangeEventArgs e)
        {
            Get_TotalAccount();
        }

        private void mi_ExportData_Click(object sender, EventArgs e)
        {
            ErpUtils.Save_DataTable_To_XML(dtSL_Details);
        }

        private void mi_InvoiceStaticDisc_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_InvoiceDiscs)))
                Application.OpenForms["frm_InvoiceDiscs"].Close();
            else
                new frm_InvoiceDiscs(Process.SellInvoice).Show();
        }

        private void mi_InvoiceStaticDimensions_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_InvoiceDimenstions)))
                Application.OpenForms["frm_InvoiceDimenstions"].Close();
            else
                new frm_InvoiceDimenstions(Process.SellInvoice).Show();
        }

        void txtInvoiceCode_Leave(object sender, EventArgs e)
        {
            if (Shared.st_Store.InvoicesCodeRedundancy == null)
                return;

            bool code_exist = InvCodeExist();
            ErpHelper.ValidateInvCodeExist(code_exist, txtInvoiceCode);
        }

        public bool InvCodeExist()
        {
            int? InvbookId = (int?)lkp_InvoiceBook.EditValue;

            return (from x in DB.SL_InvoiceArchives
                    join s in DB.IC_Stores on x.StoreId equals s.StoreId
                    where InvbookId == null && Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                    where InvbookId == null && Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                       s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                    where x.InvoiceBookId == null ? true : x.InvoiceBookId == Convert.ToInt32(lkp_InvoiceBook.EditValue)
                    where x.InvoiceCode == txtInvoiceCode.Text
                    where x.SL_InvoiceId != invoiceId
                    select x.InvoiceCode).Count() > 0;

        }


        private void repItems_Popup(object sender, EventArgs e)
        {
            if (rep_layout.Length > 0)
            {
                rep_layout.Seek(0, System.IO.SeekOrigin.Begin);
                (sender as GridLookUpEdit).Properties.View.RestoreLayoutFromStream(rep_layout);
            }

            (sender as GridLookUpEdit).Properties.View.ClearColumnsFilter();
        }

        private void repItems_CloseUp(object sender, DevExpress.XtraEditors.Controls.CloseUpEventArgs e)
        {
            rep_layout = new System.IO.MemoryStream();
            (sender as GridLookUpEdit).Properties.View.SaveLayoutToStream(rep_layout);
        }

        private void labelControl29_Click(object sender, EventArgs e)
        {

        }

        private void mi_ImportExcel_Click(object sender, EventArgs e)
        {
            ErpUtils.LoadInvItemsFromExcel(ref dtSL_Details, invoiceId, LoadItemRow, Process.SellInvoice);
        }

        private void grdPrInvoice_Click(object sender, EventArgs e)
        {

        }

        private void chk_IsPosted_EditValueChanged(object sender, EventArgs e)
        {
            if (chk_IsPosted.Checked == true)
            {
                txt_Post_Date.Enabled = true;
                txt_Post_Date.EditValue = dtInvoiceDate.EditValue;
            }
            else
            {
                txt_Post_Date.Enabled = false;
                txt_Post_Date.EditValue = null;
            }
        }

        private void dtInvoiceDate_EditValueChanged(object sender, EventArgs e)
        {
            if (chk_IsPosted.Checked == true)
                txt_Post_Date.EditValue = dtInvoiceDate.EditValue;

            txt_DueDate_EditValueChanged(txt_DueDate, e);
        }
    }

    public class ItemPostingArchive
    {
        public int catId { get; set; }
        public decimal Price { get; set; }
        public decimal Cost { get; set; }

        public int SellAcc { get; set; }
        public int COGSAcc { get; set; }
        public int InvAcc { get; set; }

        public int SellReturnAcc { get; set; }

        public int PurchaseAcc { get; set; }
        public int OpenInvAcc { get; set; }
        public int CloseInvAcc { get; set; }
    }

    public class InvBomArchive
    {
        public int BOMId { get; set; }
        public int ProductItemId { get; set; }
        public decimal ProductQty { get; set; }
        public int RawItemId { get; set; }
        public int UomId { get; set; }
        public decimal RawQty { get; set; }
        public decimal Factor { get; set; }

        public int RawItemType { get; set; }

        public byte UomIndex { get; set; }

        public int? mtrxAttribute1 { get; set; }

        public int? mtrxAttribute2 { get; set; }

        public int? mtrxAttribute3 { get; set; }

        public int? mtrxParentItem { get; set; }

        public decimal MediumUOMFactorDecimal { get; set; }

        public decimal LargeUOMFactorDecimal { get; set; }
    }
}