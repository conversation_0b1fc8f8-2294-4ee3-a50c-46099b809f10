using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Linq;
using System.Data;
using DAL;

namespace Reports
{
    public partial class rpt_RecieveNotes : DevExpress.XtraReports.UI.XtraReport
    {
        DataTable dt_PayNotes;
        string userName;
        public rpt_RecieveNotes()
        {
            InitializeComponent();
        }
        public rpt_RecieveNotes(DataTable dt_payNotes, string userName)
        {
            InitializeComponent();
            getReportHeader();
            dt_PayNotes = dt_payNotes;
            this.userName = userName;
            //LoadData();
        }

        public void LoadData()
        {
            this.DataSource = dt_PayNotes;
            lbl_PaperDetails.DataBindings.Add("Text", this.DataSource, "PaperDetails");
            lbl_Serial.DataBindings.Add("Text", this.DataSource, "Serial");
            lbl_RegDate.DataBindings.Add("Text", this.DataSource, "RegDate");
            lbl_DueDate.DataBindings.Add("Text", this.DataSource, "DueDate");
            lbl_Amount.DataBindings.Add("Text", this.DataSource, "Amount");
            lbl_PayCondition.DataBindings.Add("Text", this.DataSource, "PayCondition");
            lbl_Notes.DataBindings.Add("Text", this.DataSource, "Notes");            
            lbl_BankName.DataBindings.Add("Text", this.DataSource, "BankName");
            lbl_Account.DataBindings.Add("Text", this.DataSource, "Account");            
            lbl_RespondDate.DataBindings.Add("Text", this.DataSource, "RespondDate");
            lbl_Reason.DataBindings.Add("Text", this.DataSource, "Reason");
            lblTotalWords.DataBindings.Add("Text", this.DataSource, "TotalWords");

            lbl_Address.DataBindings.Add("Text", this.DataSource, "Address");
            lbl_IdNumber.DataBindings.Add("Text", this.DataSource, "NoteNumber");         
            lbl_User.Text = userName;


        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }     
    }
}
