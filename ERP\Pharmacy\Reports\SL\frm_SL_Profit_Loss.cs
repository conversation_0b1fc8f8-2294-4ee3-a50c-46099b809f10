﻿using DAL;
using DAL.Res;
using DevExpress.XtraEditors;
using DevExpress.XtraReports.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Reports.SL
{
    public partial class frm_SL_Profit_Loss : Form
    {

        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int store_id1, store_id2, CustomerId1, CustomerId2, custGroupId, salesEmpId;

        string custGroupAccNumber;

        byte FltrTyp_Store, fltrTyp_Date, FltrTyp_Customer;

        private void frm_SL_Profit_Loss_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grd_data, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column == col_Index)
                e.Value = e.ListSourceRowIndex + 1;
        }

        DateTime date1, date2;

        public frm_SL_Profit_Loss(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Store, int store_id1, int store_id2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2, int custGroupId, string custGroupAccNumber,
                int salesEmpId)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Store = fltrTyp_Store;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;

            this.store_id1 = store_id1;
            this.store_id2 = store_id2;

            this.date1 = date1;
            this.date2 = date2;
            //if (date2 == Shared.minDate)
            //    this.date2 = Shared.maxDate;
            this.CustomerId1 = customerId1;
            this.CustomerId2 = customerId2;
            this.salesEmpId = salesEmpId;
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            rep_Currency.DataSource = Shared.lstCurrency;
            rep_Currency.ValueMember = "CrncId";
            rep_Currency.DisplayMember = "crncName";

            getReportHeader();
        }


        private void frm_SL_Profit_Loss_Load(object sender, EventArgs e)
        {
            ReportsUtils.Load_Grid_Layout(grd_data, this.Name.Replace("frm_", "Rpt_"));

            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            ERPDataContext DB = new DAL.ERPDataContext();


            var data1 = (from i in DB.SL_Invoices

                         where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                         i.InvoiceDate.Date >= date1.Date && i.InvoiceDate.Date <= date2.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                         i.InvoiceDate.Date >= date1.Date : true
                         where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                         i.InvoiceDate.Date <= date2.Date : true

                         where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId



                         join d in DB.SL_InvoiceDetails on i.SL_InvoiceId equals d.SL_InvoiceId

                         where FltrTyp_Store == 1 ? (i.StoreId == store_id1 /*|| d.StoreId == store_id1*/) : true
                         where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
                        (i.StoreId >= store_id1 /*|| d.StoreId >= store_id1*/) && (i.StoreId <= store_id2 /*|| d.StoreId <= store_id2*/) : true
                         where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
                         (i.StoreId >= store_id1 /*|| d.StoreId >= store_id1*/) : true
                         where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
                         (i.StoreId <= store_id2 /*|| d.StoreId <= store_id2*/) : true


                         join c in DB.SL_Customers.DefaultIfEmpty() on i.CustomerId equals c.CustomerId

                         where FltrTyp_Customer == 1 ? c.CustomerId == CustomerId1 : true
                         where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 != 0) ?
                         c.CustomerId >= CustomerId1 && c.CustomerId <= CustomerId2 : true
                         where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 == 0) ?
                         c.CustomerId >= CustomerId1 : true
                         where (FltrTyp_Customer == 2 && CustomerId1 == 0 && CustomerId2 != 0) ?
                         c.CustomerId <= CustomerId2 : true

                         join a in DB.ACC_Accounts.DefaultIfEmpty() on c.AccountId equals a.AccountId
                         where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)


                         let CategoryId = DB.SL_Customers.SingleOrDefault(s => s.CustomerId == i.CustomerId).CategoryId
                         let CustGroup = DB.SL_Group_Customers

                         select new
                         {
                             InvoiceDate = i.InvoiceDate,
                             InvoiceCode = i.InvoiceCode,
                             CusName = DB.SL_Customers.SingleOrDefault(s => s.CustomerId == i.CustomerId).CusNameAr,
                             CGNameAr = DB.SL_CustomerGroups.SingleOrDefault(g => g.CustomerGroupId == CategoryId).CGNameAr,
                             SalesEmpName = DB.HR_Employees.SingleOrDefault(m => m.EmpId == i.SalesEmpId).EmpName,
                             Net = i.Net,
                             d.SL_InvoiceDetailId,
                             i.SL_InvoiceId,
                             Store = DB.IC_Stores.SingleOrDefault(s => s.StoreId == i.StoreId).StoreNameAr,
                             i.CrncId,
                             i.CrncRate,
                             Net_Local = i.CrncRate * i.Net,
                             CustGroup = CustGroup.Where(x => x.GroupId == c.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault()
                         }).Distinct().ToList();



            var data2 = (from d in data1
                         join s in DB.IC_ItemStores.DefaultIfEmpty() on d.SL_InvoiceDetailId equals s.SourceId
                         where s.ProcessId == 2 || s.ProcessId == 20

                         group new { d, s } by new { d.SL_InvoiceId } into grp
                         let purchasePrice = grp.Select(x => x.s.PurchasePrice).ToList().Sum()
                         let net = grp.Select(x => x.d.Net).FirstOrDefault()
                         let netLocal = grp.Select(x => x.d.Net_Local).FirstOrDefault()
                         select new
                         {
                             SL_InvoiceId = grp.Key.SL_InvoiceId,
                             Profit_Loss = Math.Round(netLocal - purchasePrice, 3),
                             purchasePrice = purchasePrice
                         }).ToList();



            var data3 = (from i in DB.SL_Invoices

                         where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                         i.InvoiceDate.Date >= date1.Date && i.InvoiceDate.Date <= date2.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                         i.InvoiceDate.Date >= date1.Date : true
                         where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                         i.InvoiceDate.Date <= date2.Date : true

                         where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                         where FltrTyp_Store == 1 ? (i.StoreId == store_id1) : true
                         where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
                        (i.StoreId >= store_id1) && (i.StoreId <= store_id2) : true
                         where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
                         (i.StoreId >= store_id1) : true
                         where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
                         (i.StoreId <= store_id2) : true


                         join c in DB.SL_Customers.DefaultIfEmpty() on i.CustomerId equals c.CustomerId

                         where FltrTyp_Customer == 1 ? c.CustomerId == CustomerId1 : true
                         where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 != 0) ?
                         c.CustomerId >= CustomerId1 && c.CustomerId <= CustomerId2 : true
                         where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 == 0) ?
                         c.CustomerId >= CustomerId1 : true
                         where (FltrTyp_Customer == 2 && CustomerId1 == 0 && CustomerId2 != 0) ?
                         c.CustomerId <= CustomerId2 : true

                         join a in DB.ACC_Accounts.DefaultIfEmpty() on c.AccountId equals a.AccountId
                         where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                         join ot in DB.IC_OutTrns on i.SL_InvoiceId equals ot.SourceId
                         where ot.Is_SellInv == true && ot.ProcessId == 2

                         join otd in DB.IC_OutTrnsDetails on ot.OutTrnsId equals otd.OutTrnsId
                         let groupId = DB.SL_Customers.SingleOrDefault(s => s.CustomerId == i.CustomerId).CategoryId
                         let CustGroup = DB.SL_Group_Customers

                         select new
                         {
                             InvoiceDate = i.InvoiceDate,
                             InvoiceCode = i.InvoiceCode,
                             CusName = DB.SL_Customers.SingleOrDefault(s => s.CustomerId == i.CustomerId).CusNameAr,
                             CGNameAr = DB.SL_CustomerGroups.SingleOrDefault(g => g.CustomerGroupId == groupId).CGNameAr,
                             SalesEmpName = DB.HR_Employees.SingleOrDefault(m => m.EmpId == i.SalesEmpId).EmpName,
                             Net = i.Net,
                             i.SL_InvoiceId,
                             Store = DB.IC_Stores.SingleOrDefault(s => s.StoreId == i.StoreId).StoreNameAr,
                             otd.OutTrnsDetailId,
                             i.CrncId,
                             i.CrncRate,
                             Net_Local = i.CrncRate * i.Net,
                             CustGroup = CustGroup.Where(x => x.GroupId == c.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault()
                         }).Distinct().ToList();


            var data4 = (from d in data3
                         join s in DB.IC_ItemStores.DefaultIfEmpty() on d.OutTrnsDetailId equals s.SourceId
                         where s.ProcessId == 20

                         group new { d, s } by new { d.SL_InvoiceId } into grp
                         let purchasePrice = grp.Select(x => x.s.PurchasePrice).ToList().Sum()
                         let net = grp.Select(x => x.d.Net).FirstOrDefault()
                         let netLocal = grp.Select(x => x.d.Net_Local).FirstOrDefault()
                         select new
                         {
                             SL_InvoiceId = grp.Key.SL_InvoiceId,
                             Profit_Loss = Math.Round(netLocal - purchasePrice, 3),
                             purchasePrice = purchasePrice
                         }).ToList();


            var data5 = (from d1 in data1
                         join d2 in data2 on d1.SL_InvoiceId equals d2.SL_InvoiceId

                         select new
                         {
                             d1.InvoiceDate,
                             d1.InvoiceCode,
                             d1.CusName,
                             d1.CGNameAr,
                             d1.SalesEmpName,
                             d1.Net,
                             d2.Profit_Loss,
                             Profit_LossPercentage = Math.Round((d1.Net_Local == 0 ? -1 : d2.Profit_Loss / d1.Net_Local) * 100, 2),/*+"%"*/
                             d1.Store,
                             d2.purchasePrice,
                             Profit_Loss_Sign = d2.Profit_Loss > 0 ? "+" : "-",
                             d1.CrncId,
                             d1.CrncRate,
                             Net_Local = d1.CrncRate * d1.Net,
                             CustGroup = d1.CustGroup
                         }).ToList().Distinct();

            var data6 = (from d1 in data3
                         join d2 in data4 on d1.SL_InvoiceId equals d2.SL_InvoiceId

                         select new
                         {
                             d1.InvoiceDate,
                             d1.InvoiceCode,
                             d1.CusName,
                             d1.CGNameAr,
                             d1.SalesEmpName,
                             d1.Net,
                             d2.Profit_Loss,
                             Profit_LossPercentage = Math.Round((d1.Net_Local == 0 ? -1 : d2.Profit_Loss / d1.Net_Local) * 100, 2),/*+"%"*/
                             d1.Store,
                             d2.purchasePrice,
                             Profit_Loss_Sign = d2.Profit_Loss > 0 ? "+" : "-",
                             d1.CrncId,
                             d1.CrncRate,
                             Net_Local = d1.CrncRate * d1.Net,
                             CustGroup = d1.CustGroup
                         }).ToList().Distinct();


            grd_data.DataSource = data5.Union(data6);
        }
        private void barBtnClose_ItemClick_1(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();

        }

        private void barButtonItem5_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grd_data, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        private void barBtn_PreviewData_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grd_data.MinimumSize = grd_data.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grd_data, true, true).ShowPreview();
                grd_data.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }


        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_InvoicesHeaders).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }

    }
}
