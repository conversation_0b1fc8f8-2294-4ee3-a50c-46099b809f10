﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using ExcelDataReader;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors.Repository;
using Tulpep.NotificationWindow;

namespace Pharmacy.Forms
{
    public partial class frm_E_RecievedInvoices : DevExpress.XtraEditors.XtraForm
    {

        ERPDataContext DB = new ERPDataContext();

        public frm_E_RecievedInvoices()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private async void frm_E_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
            {
                RTL.LTRLayout(this);
                this.col_typeNameAr.Visible = false;
            }
            else
            {
                this.col_typeNameEn.Visible = false;
            }


            LoadPrivilege();
            await populateGrid();

        }

        private async Task populateGrid()
        {

            var documentsInDb = DB.E_RecievedDocuments;
            if (!documentsInDb.Any())
            {
                #region PopupNotifier
                PopupNotifier Syncpopup = new PopupNotifier();
                Syncpopup.TitleText = "مزامنة";
                Syncpopup.ContentText = "جاري مزامنة المستندات مع البوابة، يرجى الانتظار";
                Syncpopup.Image = Properties.Resources.N_journal;
                Syncpopup.Popup();// show  
                #endregion

                var dateFrom = DateTime.Today.AddDays(-30).ToString("M/d/yyyy");
                var dateTo = DateTime.Today.ToString("M/d/yyyy");

                var documents = await ErpHelper.GetRecievedDocumentsAsync(dateFrom, dateTo);
                ErpHelper.PopulateRecievedDocumentsTable(documents, out bool isNewAdded);

                //hide popup
                Syncpopup.Hide();

                //Save settings 
                if (Properties.Settings.Default["RecievedDocumentsLastSyncDate"] != null)
                {
                    Properties.Settings.Default["RecievedDocumentsLastSyncDate"] = DateTime.Now.ToString("yyyy-MM-dd") + "T" + DateTime.Now.ToString("HH:mm:ss");
                    Properties.Settings.Default.Save();
                }
            }

            var allDocuments = DB.E_RecievedDocuments.ToList();
            grdDocuments.DataSource = allDocuments;
        }

        private void frm_E_InvoiceList_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                dt1.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                grdDocuments.Focus();
            }
        }

        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Shared.CarsAvailable == false)
            {
                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].Close();

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].BringToFront();
                else
                    new frm_SL_Invoice().Show();
            }
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private async void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            await populateGrid();
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    mi_Reject.Enabled = false;
                }

                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barMnu_Print.Enabled = barBtn_Print1.Enabled = barBtn_PrintData.Enabled = false;
            }
        }


        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مبيعات جديدة");
        }

        private void barBtn_Print1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdDocuments.MinimumSize = grdDocuments.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdDocuments, false).ShowPreview();
            grdDocuments.MinimumSize = new Size(0, 0);
        }

        private void barBtn_PrintData_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdDocuments.MinimumSize = grdDocuments.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdDocuments, false, true).ShowPreview();
            grdDocuments.MinimumSize = new Size(0, 0);
        }

        private void barbtn_ImportInvoice_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            importInvoices();
        }

        public void importInvoices()
        {
            ERPDataContext DB = new ERPDataContext();
            DataTable dtUOM = new DataTable();
            List<DAL.IC_UOM> uom_list;
            MyHelper.GetUomDataTable(dtUOM);
            uom_list = DB.IC_UOMs.ToList();

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();


                    int count = 1;
                    string invCode = "";
                    int invId = 0;
                    decimal totalInvoice = 0;
                    bool newInvoice = true;
                    int index = 1;

                    var taxesFromSheet = result.Tables[0].Rows[0].ItemArray
                        .Select((x, i) => new { Tax = x.ToString().ToLowerInvariant(), Index = i })
                        .Where(x => x.Tax.Contains("tax"))
                        .OrderBy(x => x.Tax)
                        .GroupBy(x => x.Tax.Substring(0, x.Tax.IndexOf('.')))
                        .ToList();

                    var taxesFromSheetCount = taxesFromSheet.Count();

                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;

                            if (result.Tables[0].Rows.IndexOf(d) == 0) { continue; }

                            if (!newInvoice && invCode != "" && invCode != Convert.ToString(d[0]))
                            {
                                frm_SL_Invoice frmsl = new frm_SL_Invoice();
                                frmsl.Show();
                                frmsl.invoiceId = invId;
                                frmsl.LoadInvoice();
                                frmsl.Save_Invoice();
                                frmsl.Close();
                                newInvoice = true;
                                totalInvoice = 0;
                            }

                            if (newInvoice || invCode != Convert.ToString(d[0]))
                            {
                                SL_Invoice inv = new SL_Invoice();
                                // invoice Code
                                inv.InvoiceCode = invCode = Convert.ToString(d[0]);
                                //Check Invoice Code
                                var invoiceIds = DB.SL_Invoices.Where(x => x.InvoiceCode == inv.InvoiceCode).Select(x => x.SL_InvoiceId).ToList();

                                if (invoiceIds.Count > 0)
                                {
                                    XtraMessageBox.Show(
                                         string.Format(string.Format(" كود الفاتورة {0}  موجود", Convert.ToString(d[0])))
                                         , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                                // Customer
                                int csId = DB.SL_Customers.Where(x => x.CusNameAr == Convert.ToString(d[1])).Select(x => x.CustomerId).FirstOrDefault();
                                if (csId > 0)
                                {
                                    inv.CustomerId = csId;
                                }
                                else
                                {

                                    XtraMessageBox.Show(
                                        string.Format(string.Format("العميل {0} غير موجود", Convert.ToString(d[1])))
                                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                                // Invoice Date
                                if (string.IsNullOrEmpty(Convert.ToString(d[2])))
                                    inv.InvoiceDate = MyHelper.Get_Server_DateTime();
                                else inv.InvoiceDate = Convert.ToDateTime(Convert.ToString(d[2]));
                                // Store
                                int storeId = DB.IC_Stores.Where(x => x.StoreNameAr == Convert.ToString(d[3])).Select(x => x.StoreId).FirstOrDefault();
                                if (storeId > 0)
                                {
                                    inv.StoreId = storeId;
                                }
                                else
                                {

                                    XtraMessageBox.Show(
                                        string.Format(string.Format("المخزن/الفرع {0} غير موجود", Convert.ToString(d[3])))
                                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                                // CostCenter
                                if (!string.IsNullOrEmpty(Convert.ToString(d[4])))
                                {
                                    int ccId = DB.ACC_CostCenters.Where(x => x.CostCenterName == Convert.ToString(d[4])).Select(x => x.CostCenterId).FirstOrDefault();
                                    if (ccId > 0)
                                    {
                                        inv.CostCenterId = ccId;
                                    }
                                }
                                // Currency
                                if (!string.IsNullOrEmpty(Convert.ToString(d[5])))
                                {
                                    inv.CrncId = DB.ST_Currencies.Where(c => c.crncName == Convert.ToString(d[5])).Select(c => c.CrncId).FirstOrDefault();

                                }
                                else { inv.CrncId = 0; }
                                // Currency rate
                                if (!string.IsNullOrEmpty(Convert.ToString(d[6])))
                                {
                                    inv.CrncRate = Convert.ToDecimal(d[6]);
                                }
                                else { inv.CrncId = 1; }

                                // Paid
                                if (!string.IsNullOrEmpty(Convert.ToString(d[7])))
                                {
                                    inv.Paid = Convert.ToDecimal(d[7]);
                                }
                                else
                                {
                                    inv.Paid = 0;
                                }
                                // Discount per invoice
                                if (!string.IsNullOrEmpty(Convert.ToString(d[8])))
                                {
                                    inv.DiscountValue = Convert.ToDecimal(d[8]);
                                }
                                else
                                {
                                    inv.DiscountValue = 0;
                                }

                                // Discount per invoice
                                if (!string.IsNullOrEmpty(Convert.ToString(d[8])))
                                {
                                    inv.DiscountValue = Convert.ToDecimal(d[8]);
                                }
                                else
                                {
                                    inv.DiscountValue = 0;
                                }
                                // Net
                                //if (!string.IsNullOrEmpty(Convert.ToString(d[9])))
                                //{
                                //    inv.Net = Convert.ToDecimal(d[9]);
                                //}
                                //else
                                //{
                                inv.Net = (decimal)(from DataRow r in result.Tables[0].Rows
                                                    where r[0] == d[0]
                                                    select Convert.ToDouble(r[9])).Sum();

                                //}

                                inv.JornalId = 0;
                                newInvoice = false;

                                DB.SL_Invoices.InsertOnSubmit(inv);
                                DB.SubmitChanges();
                                invId = inv.SL_InvoiceId;
                            }

                            {
                                if (string.IsNullOrEmpty(Convert.ToString(d[10]))) continue;

                                IC_Item Item = DB.IC_Items.Where(x => x.ItemNameAr == Convert.ToString(d[10])).FirstOrDefault();
                                if (Item != null)
                                {
                                    SL_InvoiceDetail detail = new SL_InvoiceDetail();
                                    detail.SL_InvoiceId = invId;
                                    detail.ItemId = Item.ItemId;
                                    detail.UOMIndex = Item.DfltSellUomIndx;
                                    if (string.IsNullOrEmpty(Convert.ToString(d[11])))
                                    {
                                        MyHelper.GetUOMs(Item, dtUOM, uom_list);
                                        detail.UOMId = Convert.ToInt32(dtUOM.Rows[Item.DfltSellUomIndx]["UomId"]);
                                    }
                                    else
                                    {
                                        detail.UOMId = DB.IC_UOMs.Where(i => i.UOM == Convert.ToString(d[11])).Select(x => x.UOMId).FirstOrDefault();
                                    }
                                    if (string.IsNullOrEmpty(Convert.ToString(d[12])) || Convert.ToString(d[12]) == "0") continue;
                                    detail.Qty = Convert.ToDecimal(d[12]);

                                    detail.Length = string.IsNullOrEmpty(Convert.ToString(d[13])) ? Item.Length : decimal.Parse(Convert.ToString(d[13]));
                                    detail.Width = string.IsNullOrEmpty(Convert.ToString(d[14])) ? Item.Width : decimal.Parse(Convert.ToString(d[14]));
                                    detail.Height = string.IsNullOrEmpty(Convert.ToString(d[15])) ? Item.Height : decimal.Parse(Convert.ToString(d[15]));

                                    detail.SellPrice = string.IsNullOrEmpty(Convert.ToString(d[16])) ? 0 : decimal.Parse(Convert.ToString(d[16]));

                                    #region Discount on Item
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[17])))
                                    {
                                        detail.DiscountRatio = string.IsNullOrEmpty(Convert.ToString(d[17])) ? 0 : decimal.Parse(Convert.ToString(d[17]));
                                        detail.DiscountValue = (detail.SellPrice * detail.Qty * detail.DiscountRatio) / 100;

                                    }

                                    if (d[18] != DBNull.Value && d[18] != null &&
                                        (!string.IsNullOrEmpty(Convert.ToString(d[18])) || Convert.ToString(d[18]) != "0"))
                                    {
                                        detail.DiscountValue = decimal.Parse(Convert.ToString(d[18]));
                                        detail.DiscountRatio = ((detail.DiscountValue * 100) / (detail.SellPrice * detail.Qty)) / 100;

                                    }
                                    if (Convert.ToString(d[18]) == "0" && Convert.ToString(d[17]) == "0")
                                    {
                                        detail.DiscountRatio = 0;
                                        detail.DiscountValue = 0;
                                    }
                                    #endregion
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[19])))
                                    {
                                        detail.bonusDiscount = decimal.Parse(Convert.ToString(d[19]));
                                    }
                                    //public static void CalcTotalPrice(int RowHandle, GridView view, decimal TotalQty, decimal SellPrice,
                                    //decimal SalesTaxRatio, decimal CustomTaxRatio, bool calcTaxBeforeDisc, decimal DiscV, decimal EtaxRatio)
                                    detail.TotalSellPrice = string.IsNullOrEmpty(Convert.ToString(d[9])) ? 0 : decimal.Parse(Convert.ToString(d[9]));

                                    totalInvoice += detail.TotalSellPrice;// (detail.SellPrice * detail.Qty) - detail.DiscountValue;
                                                                          //var invoice = DB.SL_Invoices.FirstOrDefault(a => a.SL_InvoiceId == invId);
                                                                          //invoice.Net = totalInvoice;
                                    DB.SL_InvoiceDetails.InsertOnSubmit(detail);
                                    DB.SubmitChanges();

                                    #region Tax on Item

                                    var itemTaxCodeIndex = 20;
                                    var itemTaxRatioIndex = 21;
                                    var itemTaxValueIndex = 22;
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[itemTaxCodeIndex])))
                                    {
                                        var subTaxInDb = DB.E_TaxableTypes
                                                    .Where(s => s.Code == d[itemTaxCodeIndex].ToString())
                                                    .Select(s => new { Id = s.E_TaxableTypeId, ParentId = s.ParentTaxId })
                                                    .FirstOrDefault();


                                        if (subTaxInDb == null)
                                        {
                                            XtraMessageBox.Show(
                                            string.Format(string.Format("{0} لا يوجد ضريبة بهذا الاسم", Convert.ToString(d[itemTaxCodeIndex])))
                                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                        }

                                        else if (subTaxInDb.ParentId == null)
                                        {
                                            XtraMessageBox.Show(
                                            string.Format(string.Format(" {ليست  ضريبة فرعية {0", Convert.ToString(d[itemTaxCodeIndex])))
                                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                                        }

                                        else
                                        {
                                            var subTax = new SL_InvoiceDetailSubTaxValue();
                                            subTax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                                            subTax.esubTypeId = subTaxInDb.Id;
                                            subTax.TaxRatio = string.IsNullOrEmpty(Convert.ToString(d[itemTaxRatioIndex])) ? 0 : decimal.Parse(Convert.ToString(d[itemTaxRatioIndex]));
                                            subTax.value = string.IsNullOrEmpty(Convert.ToString(d[itemTaxValueIndex])) ? 0 : decimal.Parse(Convert.ToString(d[itemTaxValueIndex]));
                                            DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(subTax);
                                            DB.SubmitChanges();
                                        }

                                    }

                                    #endregion

                                    #region Old Tax on Item Code
                                    //while (result.Tables[0].Columns.Count > ccount)
                                    //{
                                    //    if (string.IsNullOrEmpty(Convert.ToString(d[ccount])))
                                    //    {
                                    //        ccount++;
                                    //        continue;
                                    //    }
                                    //    else
                                    //    {
                                    //        string subt = Convert.ToString(d[ccount]);
                                    //        var subtaxId = DB.E_TaxableTypes.Where(s => s.Code == subt).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                                    //        if (subtaxId > 0)
                                    //        {
                                    //            SL_InvoiceDetailSubTaxValue subtax = new SL_InvoiceDetailSubTaxValue();
                                    //            subtax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                                    //            subtax.esubTypeId = subtaxId;
                                    //            ccount++;

                                    //            subtax.TaxRatio = string.IsNullOrEmpty(Convert.ToString(d[ccount])) ? 0 : decimal.Parse(Convert.ToString(d[ccount]));
                                    //            ccount++;
                                    //            subtax.value = string.IsNullOrEmpty(Convert.ToString(d[ccount])) ? 0 : decimal.Parse(Convert.ToString(d[ccount]));
                                    //            DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(subtax);
                                    //            DB.SubmitChanges();
                                    //            ccount++;
                                    //        }
                                    //        else break;

                                    //    }
                                    //}
                                    #endregion

                                    #region Dynamic Taxes From Excel Sheet
                                    if (taxesFromSheetCount > 0)
                                    {
                                        foreach (var taxGroup in taxesFromSheet)
                                        {
                                            var taxCodeIndex = taxGroup.ElementAtOrDefault(0).Index;
                                            var taxRatioIndex = taxGroup.ElementAtOrDefault(1).Index;
                                            var taxValueIndex = taxGroup.ElementAtOrDefault(2).Index;

                                            if (!string.IsNullOrEmpty(Convert.ToString(d[taxCodeIndex])))
                                            {


                                                var taxInDB = DB.E_TaxableTypes
                                                               .Where(s => s.Code == d[taxCodeIndex].ToString())
                                                               .Select(s => new { Id = s.E_TaxableTypeId, ParentId = s.ParentTaxId })
                                                               .FirstOrDefault();


                                                if (taxInDB == null)
                                                {
                                                    XtraMessageBox.Show(
                                                    string.Format(string.Format("{0} لا يوجد ضريبة بهذا الاسم", Convert.ToString(d[taxCodeIndex])))
                                                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                                }

                                                else if (taxInDB.ParentId == null)
                                                {
                                                    XtraMessageBox.Show(
                                                     string.Format(string.Format(" {ليست  ضريبة فرعية {0", Convert.ToString(d[taxCodeIndex])))
                                                     , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                                }

                                                else
                                                {
                                                    var subTax = new SL_InvoiceDetailSubTaxValue();
                                                    subTax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                                                    subTax.esubTypeId = taxInDB.Id;
                                                    subTax.TaxRatio = string.IsNullOrEmpty(Convert.ToString(d[taxRatioIndex])) ? 0 : decimal.Parse(Convert.ToString(d[taxRatioIndex]));
                                                    subTax.value = string.IsNullOrEmpty(Convert.ToString(d[taxValueIndex])) ? 0 : decimal.Parse(Convert.ToString(d[taxValueIndex]));
                                                    DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(subTax);
                                                    DB.SubmitChanges();

                                                }
                                            }
                                        }
                                    }
                                    #endregion

                                    if (index == result.Tables[0].Rows.Count - 1)
                                    {
                                        frm_SL_Invoice frmsl = new frm_SL_Invoice();
                                        frmsl.Show();
                                        frmsl.invoiceId = invId;
                                        frmsl.LoadInvoice();
                                        frmsl.Save_Invoice();
                                        frmsl.Close();
                                        newInvoice = true;
                                        totalInvoice = 0;
                                    }
                                    index++;
                                }
                                else
                                {
                                    XtraMessageBox.Show(
                                  string.Format(string.Format("الصنف {0} غير موجود", Convert.ToString(d[10])))
                                  , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الفواتير")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الفواتير بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }

        private void mi_Reject_Click(object sender, EventArgs e)
        {
            popup.Show();
        }

        private async void btn_rejectDocument_Click(object sender, EventArgs e)
        {
            var view = grdDocuments.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;

            if (focused_row_index < 0)
                return;

            var uuid = Convert.ToString(view.GetRowCellValue(focused_row_index, col_uuid));
            var status = Convert.ToString(view.GetRowCellValue(focused_row_index, col_status));

            if (status != "Valid")
            {
                var msg = Shared.IsEnglish ? "valid documents can only rejected" : "لا يمكن حذف مستند حالته غير صحيحة";
                MessageBox.Show(msg, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            try
            {
                using (HttpClient client = new HttpClient())
                {

                    UriBuilder uriBuilder = new UriBuilder(Properties.Settings.Default.BackEndPoint);
                    uriBuilder.Port = Properties.Settings.Default.BackEndPort;
                    client.BaseAddress = uriBuilder.Uri;
                    client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

                    var rejectReasons = txtRejectReasons.Text;
                    var response = await client.GetAsync($"api/Einvoice/RejectDocument?uuid={uuid}&reason={rejectReasons}");

                    if (response.IsSuccessStatusCode)
                        MessageBox.Show(Shared.IsEnglish ? $"Invoice with uuid: {uuid} Deleted Successfully" : "تم حذف المستند بنجاح");


                }
            }
            catch (Exception)
            {
                MessageBox.Show(Shared.IsEnglish ? "try again. Some thing went wrong!" : "حاول لاحقا, يوجد خطأ ما");
                throw;
            }

        }

        private void btn_cancelReject_Click(object sender, EventArgs e)
        {
            popup.Hide();
        }

        private void grdDocuments_DoubleClick(object sender, EventArgs e)
        {
            var view = grdDocuments.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            var publicUrl = view.GetRowCellValue(focused_row_index, col_Url).ToString();

            System.Diagnostics.Process.Start(publicUrl);
        }
    }
}