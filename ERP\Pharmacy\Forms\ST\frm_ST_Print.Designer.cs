﻿namespace Pharmacy.Forms
{
    partial class frm_ST_Print
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_ST_Print));
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.txtFooterLine1 = new DevExpress.XtraEditors.TextEdit();
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Save = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.cmbPrintReceiptPOS = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.chkPrintLogo = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtFooterLine2 = new DevExpress.XtraEditors.TextEdit();
            this.chkPrintInvoiceId = new DevExpress.XtraEditors.CheckEdit();
            this.chkPrintCompName = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtMinValue = new DevExpress.XtraEditors.TextEdit();
            this.cmbPrintReceiptSLI = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.chkPrintDate = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.txt_NoOfPrint = new DevExpress.XtraEditors.TextEdit();
            this.lkp_printers = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_AddTaxValue = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFooterLine1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPrintReceiptPOS.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFooterLine2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintInvoiceId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintCompName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMinValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPrintReceiptSLI.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_NoOfPrint.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_printers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_AddTaxValue.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // txtFooterLine1
            // 
            resources.ApplyResources(this.txtFooterLine1, "txtFooterLine1");
            this.txtFooterLine1.Name = "txtFooterLine1";
            this.txtFooterLine1.Properties.AccessibleDescription = resources.GetString("txtFooterLine1.Properties.AccessibleDescription");
            this.txtFooterLine1.Properties.AccessibleName = resources.GetString("txtFooterLine1.Properties.AccessibleName");
            this.txtFooterLine1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtFooterLine1.Properties.Appearance.FontSizeDelta")));
            this.txtFooterLine1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtFooterLine1.Properties.Appearance.FontStyleDelta")));
            this.txtFooterLine1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtFooterLine1.Properties.Appearance.GradientMode")));
            this.txtFooterLine1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtFooterLine1.Properties.Appearance.Image")));
            this.txtFooterLine1.Properties.Appearance.Options.UseTextOptions = true;
            this.txtFooterLine1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtFooterLine1.Properties.AutoHeight = ((bool)(resources.GetObject("txtFooterLine1.Properties.AutoHeight")));
            this.txtFooterLine1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtFooterLine1.Properties.Mask.AutoComplete")));
            this.txtFooterLine1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtFooterLine1.Properties.Mask.BeepOnError")));
            this.txtFooterLine1.Properties.Mask.EditMask = resources.GetString("txtFooterLine1.Properties.Mask.EditMask");
            this.txtFooterLine1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtFooterLine1.Properties.Mask.IgnoreMaskBlank")));
            this.txtFooterLine1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtFooterLine1.Properties.Mask.MaskType")));
            this.txtFooterLine1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtFooterLine1.Properties.Mask.PlaceHolder")));
            this.txtFooterLine1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtFooterLine1.Properties.Mask.SaveLiteral")));
            this.txtFooterLine1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtFooterLine1.Properties.Mask.ShowPlaceHolders")));
            this.txtFooterLine1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtFooterLine1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtFooterLine1.Properties.NullValuePrompt = resources.GetString("txtFooterLine1.Properties.NullValuePrompt");
            this.txtFooterLine1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtFooterLine1.Properties.NullValuePromptShowForEmptyValue")));
            this.txtFooterLine1.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowMoveBarOnToolbar = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtn_Save,
            this.barBtn_Help,
            this.barButtonClose});
            this.barManager1.MaxItemId = 26;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Save),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtn_Save
            // 
            resources.ApplyResources(this.barBtn_Save, "barBtn_Save");
            this.barBtn_Save.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Save.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtn_Save.Id = 0;
            this.barBtn_Save.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtn_Save.Name = "barBtn_Save";
            this.barBtn_Save.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Save.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barButtonClose
            // 
            resources.ApplyResources(this.barButtonClose, "barButtonClose");
            this.barButtonClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barButtonClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barButtonClose.Id = 25;
            this.barButtonClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barButtonClose.Name = "barButtonClose";
            this.barButtonClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barButtonClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // cmbPrintReceiptPOS
            // 
            resources.ApplyResources(this.cmbPrintReceiptPOS, "cmbPrintReceiptPOS");
            this.cmbPrintReceiptPOS.MenuManager = this.barManager1;
            this.cmbPrintReceiptPOS.Name = "cmbPrintReceiptPOS";
            this.cmbPrintReceiptPOS.Properties.AccessibleDescription = resources.GetString("cmbPrintReceiptPOS.Properties.AccessibleDescription");
            this.cmbPrintReceiptPOS.Properties.AccessibleName = resources.GetString("cmbPrintReceiptPOS.Properties.AccessibleName");
            this.cmbPrintReceiptPOS.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbPrintReceiptPOS.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cmbPrintReceiptPOS.Properties.Appearance.FontSizeDelta")));
            this.cmbPrintReceiptPOS.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbPrintReceiptPOS.Properties.Appearance.FontStyleDelta")));
            this.cmbPrintReceiptPOS.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbPrintReceiptPOS.Properties.Appearance.GradientMode")));
            this.cmbPrintReceiptPOS.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cmbPrintReceiptPOS.Properties.Appearance.Image")));
            this.cmbPrintReceiptPOS.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbPrintReceiptPOS.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbPrintReceiptPOS.Properties.AutoHeight = ((bool)(resources.GetObject("cmbPrintReceiptPOS.Properties.AutoHeight")));
            this.cmbPrintReceiptPOS.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbPrintReceiptPOS.Properties.Buttons"))))});
            this.cmbPrintReceiptPOS.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmbPrintReceiptPOS.Properties.GlyphAlignment")));
            this.cmbPrintReceiptPOS.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPrintReceiptPOS.Properties.Items"), ((object)(resources.GetObject("cmbPrintReceiptPOS.Properties.Items1"))), ((int)(resources.GetObject("cmbPrintReceiptPOS.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPrintReceiptPOS.Properties.Items3"), ((object)(resources.GetObject("cmbPrintReceiptPOS.Properties.Items4"))), ((int)(resources.GetObject("cmbPrintReceiptPOS.Properties.Items5"))))});
            this.cmbPrintReceiptPOS.Properties.NullValuePrompt = resources.GetString("cmbPrintReceiptPOS.Properties.NullValuePrompt");
            this.cmbPrintReceiptPOS.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cmbPrintReceiptPOS.Properties.NullValuePromptShowForEmptyValue")));
            this.cmbPrintReceiptPOS.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // chkPrintLogo
            // 
            resources.ApplyResources(this.chkPrintLogo, "chkPrintLogo");
            this.chkPrintLogo.MenuManager = this.barManager1;
            this.chkPrintLogo.Name = "chkPrintLogo";
            this.chkPrintLogo.Properties.AccessibleDescription = resources.GetString("chkPrintLogo.Properties.AccessibleDescription");
            this.chkPrintLogo.Properties.AccessibleName = resources.GetString("chkPrintLogo.Properties.AccessibleName");
            this.chkPrintLogo.Properties.AutoHeight = ((bool)(resources.GetObject("chkPrintLogo.Properties.AutoHeight")));
            this.chkPrintLogo.Properties.Caption = resources.GetString("chkPrintLogo.Properties.Caption");
            this.chkPrintLogo.Properties.DisplayValueChecked = resources.GetString("chkPrintLogo.Properties.DisplayValueChecked");
            this.chkPrintLogo.Properties.DisplayValueGrayed = resources.GetString("chkPrintLogo.Properties.DisplayValueGrayed");
            this.chkPrintLogo.Properties.DisplayValueUnchecked = resources.GetString("chkPrintLogo.Properties.DisplayValueUnchecked");
            this.chkPrintLogo.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // txtFooterLine2
            // 
            resources.ApplyResources(this.txtFooterLine2, "txtFooterLine2");
            this.txtFooterLine2.Name = "txtFooterLine2";
            this.txtFooterLine2.Properties.AccessibleDescription = resources.GetString("txtFooterLine2.Properties.AccessibleDescription");
            this.txtFooterLine2.Properties.AccessibleName = resources.GetString("txtFooterLine2.Properties.AccessibleName");
            this.txtFooterLine2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtFooterLine2.Properties.Appearance.FontSizeDelta")));
            this.txtFooterLine2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtFooterLine2.Properties.Appearance.FontStyleDelta")));
            this.txtFooterLine2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtFooterLine2.Properties.Appearance.GradientMode")));
            this.txtFooterLine2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtFooterLine2.Properties.Appearance.Image")));
            this.txtFooterLine2.Properties.Appearance.Options.UseTextOptions = true;
            this.txtFooterLine2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtFooterLine2.Properties.AutoHeight = ((bool)(resources.GetObject("txtFooterLine2.Properties.AutoHeight")));
            this.txtFooterLine2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtFooterLine2.Properties.Mask.AutoComplete")));
            this.txtFooterLine2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtFooterLine2.Properties.Mask.BeepOnError")));
            this.txtFooterLine2.Properties.Mask.EditMask = resources.GetString("txtFooterLine2.Properties.Mask.EditMask");
            this.txtFooterLine2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtFooterLine2.Properties.Mask.IgnoreMaskBlank")));
            this.txtFooterLine2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtFooterLine2.Properties.Mask.MaskType")));
            this.txtFooterLine2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtFooterLine2.Properties.Mask.PlaceHolder")));
            this.txtFooterLine2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtFooterLine2.Properties.Mask.SaveLiteral")));
            this.txtFooterLine2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtFooterLine2.Properties.Mask.ShowPlaceHolders")));
            this.txtFooterLine2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtFooterLine2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtFooterLine2.Properties.NullValuePrompt = resources.GetString("txtFooterLine2.Properties.NullValuePrompt");
            this.txtFooterLine2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtFooterLine2.Properties.NullValuePromptShowForEmptyValue")));
            this.txtFooterLine2.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // chkPrintInvoiceId
            // 
            resources.ApplyResources(this.chkPrintInvoiceId, "chkPrintInvoiceId");
            this.chkPrintInvoiceId.MenuManager = this.barManager1;
            this.chkPrintInvoiceId.Name = "chkPrintInvoiceId";
            this.chkPrintInvoiceId.Properties.AccessibleDescription = resources.GetString("chkPrintInvoiceId.Properties.AccessibleDescription");
            this.chkPrintInvoiceId.Properties.AccessibleName = resources.GetString("chkPrintInvoiceId.Properties.AccessibleName");
            this.chkPrintInvoiceId.Properties.AutoHeight = ((bool)(resources.GetObject("chkPrintInvoiceId.Properties.AutoHeight")));
            this.chkPrintInvoiceId.Properties.Caption = resources.GetString("chkPrintInvoiceId.Properties.Caption");
            this.chkPrintInvoiceId.Properties.DisplayValueChecked = resources.GetString("chkPrintInvoiceId.Properties.DisplayValueChecked");
            this.chkPrintInvoiceId.Properties.DisplayValueGrayed = resources.GetString("chkPrintInvoiceId.Properties.DisplayValueGrayed");
            this.chkPrintInvoiceId.Properties.DisplayValueUnchecked = resources.GetString("chkPrintInvoiceId.Properties.DisplayValueUnchecked");
            this.chkPrintInvoiceId.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // chkPrintCompName
            // 
            resources.ApplyResources(this.chkPrintCompName, "chkPrintCompName");
            this.chkPrintCompName.MenuManager = this.barManager1;
            this.chkPrintCompName.Name = "chkPrintCompName";
            this.chkPrintCompName.Properties.AccessibleDescription = resources.GetString("chkPrintCompName.Properties.AccessibleDescription");
            this.chkPrintCompName.Properties.AccessibleName = resources.GetString("chkPrintCompName.Properties.AccessibleName");
            this.chkPrintCompName.Properties.AutoHeight = ((bool)(resources.GetObject("chkPrintCompName.Properties.AutoHeight")));
            this.chkPrintCompName.Properties.Caption = resources.GetString("chkPrintCompName.Properties.Caption");
            this.chkPrintCompName.Properties.DisplayValueChecked = resources.GetString("chkPrintCompName.Properties.DisplayValueChecked");
            this.chkPrintCompName.Properties.DisplayValueGrayed = resources.GetString("chkPrintCompName.Properties.DisplayValueGrayed");
            this.chkPrintCompName.Properties.DisplayValueUnchecked = resources.GetString("chkPrintCompName.Properties.DisplayValueUnchecked");
            this.chkPrintCompName.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // txtMinValue
            // 
            resources.ApplyResources(this.txtMinValue, "txtMinValue");
            this.txtMinValue.Name = "txtMinValue";
            this.txtMinValue.Properties.AccessibleDescription = resources.GetString("txtMinValue.Properties.AccessibleDescription");
            this.txtMinValue.Properties.AccessibleName = resources.GetString("txtMinValue.Properties.AccessibleName");
            this.txtMinValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMinValue.Properties.Appearance.FontSizeDelta")));
            this.txtMinValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMinValue.Properties.Appearance.FontStyleDelta")));
            this.txtMinValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMinValue.Properties.Appearance.GradientMode")));
            this.txtMinValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMinValue.Properties.Appearance.Image")));
            this.txtMinValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMinValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMinValue.Properties.AutoHeight = ((bool)(resources.GetObject("txtMinValue.Properties.AutoHeight")));
            this.txtMinValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMinValue.Properties.Mask.AutoComplete")));
            this.txtMinValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMinValue.Properties.Mask.BeepOnError")));
            this.txtMinValue.Properties.Mask.EditMask = resources.GetString("txtMinValue.Properties.Mask.EditMask");
            this.txtMinValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMinValue.Properties.Mask.IgnoreMaskBlank")));
            this.txtMinValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMinValue.Properties.Mask.MaskType")));
            this.txtMinValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMinValue.Properties.Mask.PlaceHolder")));
            this.txtMinValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMinValue.Properties.Mask.SaveLiteral")));
            this.txtMinValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMinValue.Properties.Mask.ShowPlaceHolders")));
            this.txtMinValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMinValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMinValue.Properties.NullText = resources.GetString("txtMinValue.Properties.NullText");
            this.txtMinValue.Properties.NullValuePrompt = resources.GetString("txtMinValue.Properties.NullValuePrompt");
            this.txtMinValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMinValue.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMinValue.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtMinValue_Spin);
            // 
            // cmbPrintReceiptSLI
            // 
            resources.ApplyResources(this.cmbPrintReceiptSLI, "cmbPrintReceiptSLI");
            this.cmbPrintReceiptSLI.MenuManager = this.barManager1;
            this.cmbPrintReceiptSLI.Name = "cmbPrintReceiptSLI";
            this.cmbPrintReceiptSLI.Properties.AccessibleDescription = resources.GetString("cmbPrintReceiptSLI.Properties.AccessibleDescription");
            this.cmbPrintReceiptSLI.Properties.AccessibleName = resources.GetString("cmbPrintReceiptSLI.Properties.AccessibleName");
            this.cmbPrintReceiptSLI.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbPrintReceiptSLI.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cmbPrintReceiptSLI.Properties.Appearance.FontSizeDelta")));
            this.cmbPrintReceiptSLI.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbPrintReceiptSLI.Properties.Appearance.FontStyleDelta")));
            this.cmbPrintReceiptSLI.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbPrintReceiptSLI.Properties.Appearance.GradientMode")));
            this.cmbPrintReceiptSLI.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cmbPrintReceiptSLI.Properties.Appearance.Image")));
            this.cmbPrintReceiptSLI.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbPrintReceiptSLI.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbPrintReceiptSLI.Properties.AutoHeight = ((bool)(resources.GetObject("cmbPrintReceiptSLI.Properties.AutoHeight")));
            this.cmbPrintReceiptSLI.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbPrintReceiptSLI.Properties.Buttons"))))});
            this.cmbPrintReceiptSLI.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmbPrintReceiptSLI.Properties.GlyphAlignment")));
            this.cmbPrintReceiptSLI.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPrintReceiptSLI.Properties.Items"), ((object)(resources.GetObject("cmbPrintReceiptSLI.Properties.Items1"))), ((int)(resources.GetObject("cmbPrintReceiptSLI.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPrintReceiptSLI.Properties.Items3"), ((object)(resources.GetObject("cmbPrintReceiptSLI.Properties.Items4"))), ((int)(resources.GetObject("cmbPrintReceiptSLI.Properties.Items5"))))});
            this.cmbPrintReceiptSLI.Properties.NullValuePrompt = resources.GetString("cmbPrintReceiptSLI.Properties.NullValuePrompt");
            this.cmbPrintReceiptSLI.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cmbPrintReceiptSLI.Properties.NullValuePromptShowForEmptyValue")));
            this.cmbPrintReceiptSLI.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // chkPrintDate
            // 
            resources.ApplyResources(this.chkPrintDate, "chkPrintDate");
            this.chkPrintDate.MenuManager = this.barManager1;
            this.chkPrintDate.Name = "chkPrintDate";
            this.chkPrintDate.Properties.AccessibleDescription = resources.GetString("chkPrintDate.Properties.AccessibleDescription");
            this.chkPrintDate.Properties.AccessibleName = resources.GetString("chkPrintDate.Properties.AccessibleName");
            this.chkPrintDate.Properties.AutoHeight = ((bool)(resources.GetObject("chkPrintDate.Properties.AutoHeight")));
            this.chkPrintDate.Properties.Caption = resources.GetString("chkPrintDate.Properties.Caption");
            this.chkPrintDate.Properties.DisplayValueChecked = resources.GetString("chkPrintDate.Properties.DisplayValueChecked");
            this.chkPrintDate.Properties.DisplayValueGrayed = resources.GetString("chkPrintDate.Properties.DisplayValueGrayed");
            this.chkPrintDate.Properties.DisplayValueUnchecked = resources.GetString("chkPrintDate.Properties.DisplayValueUnchecked");
            this.chkPrintDate.Modified += new System.EventHandler(this.controls_Modified);
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // txt_NoOfPrint
            // 
            resources.ApplyResources(this.txt_NoOfPrint, "txt_NoOfPrint");
            this.txt_NoOfPrint.Name = "txt_NoOfPrint";
            this.txt_NoOfPrint.Properties.AccessibleDescription = resources.GetString("txt_NoOfPrint.Properties.AccessibleDescription");
            this.txt_NoOfPrint.Properties.AccessibleName = resources.GetString("txt_NoOfPrint.Properties.AccessibleName");
            this.txt_NoOfPrint.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_NoOfPrint.Properties.Appearance.FontSizeDelta")));
            this.txt_NoOfPrint.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_NoOfPrint.Properties.Appearance.FontStyleDelta")));
            this.txt_NoOfPrint.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_NoOfPrint.Properties.Appearance.GradientMode")));
            this.txt_NoOfPrint.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_NoOfPrint.Properties.Appearance.Image")));
            this.txt_NoOfPrint.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_NoOfPrint.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_NoOfPrint.Properties.AutoHeight = ((bool)(resources.GetObject("txt_NoOfPrint.Properties.AutoHeight")));
            this.txt_NoOfPrint.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_NoOfPrint.Properties.Mask.AutoComplete")));
            this.txt_NoOfPrint.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_NoOfPrint.Properties.Mask.BeepOnError")));
            this.txt_NoOfPrint.Properties.Mask.EditMask = resources.GetString("txt_NoOfPrint.Properties.Mask.EditMask");
            this.txt_NoOfPrint.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_NoOfPrint.Properties.Mask.IgnoreMaskBlank")));
            this.txt_NoOfPrint.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_NoOfPrint.Properties.Mask.MaskType")));
            this.txt_NoOfPrint.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_NoOfPrint.Properties.Mask.PlaceHolder")));
            this.txt_NoOfPrint.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_NoOfPrint.Properties.Mask.SaveLiteral")));
            this.txt_NoOfPrint.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_NoOfPrint.Properties.Mask.ShowPlaceHolders")));
            this.txt_NoOfPrint.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_NoOfPrint.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_NoOfPrint.Properties.NullText = resources.GetString("txt_NoOfPrint.Properties.NullText");
            this.txt_NoOfPrint.Properties.NullValuePrompt = resources.GetString("txt_NoOfPrint.Properties.NullValuePrompt");
            this.txt_NoOfPrint.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_NoOfPrint.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_NoOfPrint.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtMinValue_Spin);
            // 
            // lkp_printers
            // 
            resources.ApplyResources(this.lkp_printers, "lkp_printers");
            this.lkp_printers.EnterMoveNextControl = true;
            this.lkp_printers.MenuManager = this.barManager1;
            this.lkp_printers.Name = "lkp_printers";
            this.lkp_printers.Properties.AccessibleDescription = resources.GetString("lkp_printers.Properties.AccessibleDescription");
            this.lkp_printers.Properties.AccessibleName = resources.GetString("lkp_printers.Properties.AccessibleName");
            this.lkp_printers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_printers.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_printers.Properties.Appearance.FontSizeDelta")));
            this.lkp_printers.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_printers.Properties.Appearance.FontStyleDelta")));
            this.lkp_printers.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_printers.Properties.Appearance.GradientMode")));
            this.lkp_printers.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_printers.Properties.Appearance.Image")));
            this.lkp_printers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_printers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_printers.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_printers.Properties.AutoHeight")));
            this.lkp_printers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_printers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_printers.Properties.Buttons"))))});
            this.lkp_printers.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_printers.Properties.Columns"), resources.GetString("lkp_printers.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_printers.Properties.Columns2"), resources.GetString("lkp_printers.Properties.Columns3"))});
            this.lkp_printers.Properties.DisplayMember = "AccountNameEn";
            this.lkp_printers.Properties.NullText = resources.GetString("lkp_printers.Properties.NullText");
            this.lkp_printers.Properties.NullValuePrompt = resources.GetString("lkp_printers.Properties.NullValuePrompt");
            this.lkp_printers.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_printers.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_printers.Properties.ValueMember = "AccountId";
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Name = "labelControl17";
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // lkp_AddTaxValue
            // 
            resources.ApplyResources(this.lkp_AddTaxValue, "lkp_AddTaxValue");
            this.lkp_AddTaxValue.Name = "lkp_AddTaxValue";
            this.lkp_AddTaxValue.Properties.AccessibleDescription = resources.GetString("lkp_AddTaxValue.Properties.AccessibleDescription");
            this.lkp_AddTaxValue.Properties.AccessibleName = resources.GetString("lkp_AddTaxValue.Properties.AccessibleName");
            this.lkp_AddTaxValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_AddTaxValue.Properties.Appearance.FontSizeDelta")));
            this.lkp_AddTaxValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_AddTaxValue.Properties.Appearance.FontStyleDelta")));
            this.lkp_AddTaxValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_AddTaxValue.Properties.Appearance.GradientMode")));
            this.lkp_AddTaxValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_AddTaxValue.Properties.Appearance.Image")));
            this.lkp_AddTaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_AddTaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_AddTaxValue.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_AddTaxValue.Properties.AutoHeight")));
            this.lkp_AddTaxValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lkp_AddTaxValue.Properties.Mask.AutoComplete")));
            this.lkp_AddTaxValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lkp_AddTaxValue.Properties.Mask.BeepOnError")));
            this.lkp_AddTaxValue.Properties.Mask.EditMask = resources.GetString("lkp_AddTaxValue.Properties.Mask.EditMask");
            this.lkp_AddTaxValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lkp_AddTaxValue.Properties.Mask.IgnoreMaskBlank")));
            this.lkp_AddTaxValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lkp_AddTaxValue.Properties.Mask.MaskType")));
            this.lkp_AddTaxValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lkp_AddTaxValue.Properties.Mask.PlaceHolder")));
            this.lkp_AddTaxValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lkp_AddTaxValue.Properties.Mask.SaveLiteral")));
            this.lkp_AddTaxValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lkp_AddTaxValue.Properties.Mask.ShowPlaceHolders")));
            this.lkp_AddTaxValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lkp_AddTaxValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lkp_AddTaxValue.Properties.NullText = resources.GetString("lkp_AddTaxValue.Properties.NullText");
            this.lkp_AddTaxValue.Properties.NullValuePrompt = resources.GetString("lkp_AddTaxValue.Properties.NullValuePrompt");
            this.lkp_AddTaxValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_AddTaxValue.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // frm_ST_Print
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.lkp_AddTaxValue);
            this.Controls.Add(this.lkp_printers);
            this.Controls.Add(this.labelControl17);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.txt_NoOfPrint);
            this.Controls.Add(this.chkPrintDate);
            this.Controls.Add(this.cmbPrintReceiptSLI);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.txtMinValue);
            this.Controls.Add(this.cmbPrintReceiptPOS);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.chkPrintCompName);
            this.Controls.Add(this.txtFooterLine1);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.chkPrintInvoiceId);
            this.Controls.Add(this.chkPrintLogo);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.txtFooterLine2);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_ST_Print";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_ST_Print_FormClosing);
            this.Load += new System.EventHandler(this.frm_ST_Print_Load);
            ((System.ComponentModel.ISupportInitialize)(this.txtFooterLine1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPrintReceiptPOS.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFooterLine2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintInvoiceId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintCompName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMinValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPrintReceiptSLI.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkPrintDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_NoOfPrint.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_printers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_AddTaxValue.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.TextEdit txtFooterLine1;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtn_Save;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbPrintReceiptPOS;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.CheckEdit chkPrintLogo;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.TextEdit txtFooterLine2;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit txtMinValue;
        private DevExpress.XtraEditors.CheckEdit chkPrintCompName;
        private DevExpress.XtraEditors.CheckEdit chkPrintInvoiceId;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbPrintReceiptSLI;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraBars.BarButtonItem barButtonClose;
        private DevExpress.XtraEditors.CheckEdit chkPrintDate;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.TextEdit txt_NoOfPrint;
        private DevExpress.XtraEditors.LookUpEdit lkp_printers;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.TextEdit lkp_AddTaxValue;
    }
}