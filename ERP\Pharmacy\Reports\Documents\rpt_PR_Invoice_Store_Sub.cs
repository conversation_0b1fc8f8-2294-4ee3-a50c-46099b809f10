using System;
using System.Data;
using System.Data.Linq;
using System.Linq;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;

namespace Reports
{
    public partial class rpt_PR_Invoice_Store_Sub : DevExpress.XtraReports.UI.XtraReport
    {
        public rpt_PR_Invoice_Store_Sub(int InvoiceId,int BranchId)
        {
            InitializeComponent();

            if (Shared.st_Store.PiecesCount == false)
            {
                //lbl_PiecesCount.Visible = xrTableCell2.Visible = false;
                //xrTableCell7.WidthF += 47;
                //lbl_Qty.WidthF += 47;
            }

            ERPDataContext DB = new ERPDataContext();

            var emps = DB.HR_Employees.ToList();

            IList data = null;
           
            //if (processId == (int)Process.SellInvoice)
            {
                data = (from qotD in DB.PR_InvoiceDetails
                        join inv in DB.PR_Invoices on qotD.PR_InvoiceId equals inv.PR_InvoiceId
                        join u in DB.IC_UOMs
                        on qotD.UOMId equals u.UOMId
                        join i in DB.IC_Items
                        on qotD.ItemId equals i.ItemId
                        //join s in DB.IC_Stores on qotD.StoreId equals s.StoreId
                            where qotD.PR_InvoiceId == InvoiceId && inv.StoreId ==BranchId
                            select new
                            {
                                ItemId = i.ItemNameAr,
                                Qty = decimal.ToDouble(qotD.Qty),
                                UOM = u.UOM,
                            }).ToList();
                this.DataSource = data;
            }
           

            lbl_Item.DataBindings.Add("Text", data, "ItemId");
            lbl_Qty.DataBindings.Add("Text", data, "Qty");
            lbl_Uom.DataBindings.Add("Text", data, "UOM");
        }

    }
}
