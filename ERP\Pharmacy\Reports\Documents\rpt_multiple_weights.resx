<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.PaperKind" type="System.Drawing.Printing.PaperKind, System.Drawing">
    <value>A4</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="xrTableCell1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell3.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>244, 155, 24</value>
  </data>
  <data name="xrTableCell4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell13.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="xrTableRow1.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="Detail.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>LightGray</value>
  </data>
  <data name="lbl_Item.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>243, 243, 243</value>
  </data>
  <data name="lbl_PiecesCount.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>243, 243, 243</value>
  </data>
  <data name="xrTable1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="xrTableCell4.Text" xml:space="preserve">
    <value>الصنف</value>
  </data>
  <data name="xrTable1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="BottomMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrTable4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell12.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>244, 155, 24</value>
  </data>
  <data name="TopMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrTable2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTable2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>304.676239, 25.00001</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>xrTableCell2</value>
  </data>
  <data name="$this.PageWidth" type="System.Int32, mscorlib">
    <value>827</value>
  </data>
  <data name="xrTableCell4.Weight" type="System.Double, mscorlib">
    <value>0.854443103303283</value>
  </data>
  <data name="xrTableCell11.Weight" type="System.Double, mscorlib">
    <value>0.341302681035325</value>
  </data>
  <data name="lbl_Item.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableRow4.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTableCell2.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <data name="xrTable2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell2.Weight" type="System.Double, mscorlib">
    <value>0.60874676282154183</value>
  </data>
  <data name="xrTable1.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell1.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell10.Text" xml:space="preserve">
    <value>الوزن</value>
  </data>
  <data name="xrTableCell1.Weight" type="System.Double, mscorlib">
    <value>0.889441430151438</value>
  </data>
  <data name="xrTableCell5.Text" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="xrTableCell4.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell4.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>244, 155, 24</value>
  </data>
  <data name="xrTableCell1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>244, 155, 24</value>
  </data>
  <data name="xrTableCell5.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>عدد</value>
  </data>
  <data name="ReportHeader.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrTableCell11.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Weight.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>243, 243, 243</value>
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>39, 177, 5, 39</value>
  </data>
  <data name="xrTableCell2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable2.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>DarkGray</value>
  </data>
  <data name="TopMargin.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>LightGray</value>
  </data>
  <data name="lbl_PiecesCount.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell5.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>244, 155, 24</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>25</value>
  </data>
  <data name="lbl_Weight.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableRow2.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTableCell5.Weight" type="System.Double, mscorlib">
    <value>0.34962726531825838</value>
  </data>
  <data name="xrTableRow4.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTable4.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>DarkGray</value>
  </data>
  <data name="xrTableCell3.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableRow1.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="Detail.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="ReportHeader.HeightF" type="System.Single, mscorlib">
    <value>30.0000286</value>
  </data>
  <data name="BottomMargin.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>LightGray</value>
  </data>
  <data name="lbl_Item.Text" xml:space="preserve">
    <value>lbl_Item</value>
  </data>
  <data name="xrTableCell13.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>244, 155, 24</value>
  </data>
  <data name="xrTableCell11.Text" xml:space="preserve">
    <value>عدد</value>
  </data>
  <data name="lbl_Weight.Text" xml:space="preserve">
    <value>lbl_Weight</value>
  </data>
  <data name="lbl_Weight.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell13.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>5</value>
  </data>
  <data name="xrTableCell10.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="lbl_Weight.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>304.676239, 25</value>
  </data>
  <data name="xrTableCell12.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_PiecesCount.Text" xml:space="preserve">
    <value>lbl_PiecesCount</value>
  </data>
  <data name="lbl_Weight.Weight" type="System.Double, mscorlib">
    <value>1.5486335244662355</value>
  </data>
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>304.676239, 5.000019</value>
  </data>
  <data name="xrTableCell10.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>244, 155, 24</value>
  </data>
  <data name="xrTable4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 5.000019</value>
  </data>
  <data name="xrTableCell1.Text" xml:space="preserve">
    <value>الوزن</value>
  </data>
  <data name="xrTableCell10.Weight" type="System.Double, mscorlib">
    <value>0.889441430151438</value>
  </data>
  <data name="xrTableCell10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>243, 243, 243</value>
  </data>
  <data name="xrTableCell12.Text" xml:space="preserve">
    <value>الصنف</value>
  </data>
  <data name="ReportHeader.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>LightGray</value>
  </data>
  <data name="xrTableCell11.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>244, 155, 24</value>
  </data>
  <data name="xrTableCell12.Weight" type="System.Double, mscorlib">
    <value>0.854443103303283</value>
  </data>
  <data name="lbl_Item.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell11.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell12.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="lbl_PiecesCount.Weight" type="System.Double, mscorlib">
    <value>0.59425237312263857</value>
  </data>
  <data name="lbl_PiecesCount.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>304.676239, 25.00001</value>
  </data>
  <data name="xrTableCell3.Weight" type="System.Double, mscorlib">
    <value>0.341302681035325</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>39</value>
  </data>
  <data name="lbl_PiecesCount.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="xrTableCell13.Text" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="$this.PageHeight" type="System.Int32, mscorlib">
    <value>1169</value>
  </data>
  <data name="xrTableCell13.Weight" type="System.Double, mscorlib">
    <value>0.34962726531825838</value>
  </data>
  <data name="lbl_Item.Weight" type="System.Double, mscorlib">
    <value>1.4876967299525332</value>
  </data>
  <data name="xrTable4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
</root>