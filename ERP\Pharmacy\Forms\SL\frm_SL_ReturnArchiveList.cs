﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_SL_ReturnArchiveList : DevExpress.XtraEditors.XtraForm
    {
        int customerId;
        DateTime dateFrom, dateTo;
        public bool IsOpenForSelect = false;
        public static int SelectedInvId = 0;
        public static string SelectedInvCode;

        public frm_SL_ReturnArchiveList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        public frm_SL_ReturnArchiveList(int customerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.customerId = customerId;
        }

        public frm_SL_ReturnArchiveList(bool _IsOpenForSelection)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            IsOpenForSelect = _IsOpenForSelection;            
        }

        private void frm_SL_ReturnList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            if (Shared.SL_R_FromDate != null)
                dt1.DateTime = dateFrom = Shared.SL_R_FromDate.Value;
            else
            {
                dateFrom = Shared.minDate;
                dt1.EditValue = null;
            }

            if (Shared.SL_R_ToDate != null)
                dt2.DateTime = dateTo = Shared.SL_R_ToDate.Value;
            else
            {
                dateTo = Shared.maxDate;
                dt2.EditValue = null;
            }

            var view = grdCategory.FocusedView as GridView;
            if (dt1.EditValue == null && dt2.EditValue == null || customerId > 0 || view.RowCount < 1)
                GetInvoices();

            #region SalesEmp
            DataTable dt_SalesEmps = new DataTable();
            DAL.MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            rep_salesEmp.DataSource = dt_SalesEmps;
            rep_salesEmp.DisplayMember = "EmpName";
            rep_salesEmp.ValueMember = "EmpId";
            #endregion

            #region invoice bbok
            ERPDataContext DB = new ERPDataContext();
            rep_InvoiceBook.DataSource = DB.ST_InvoiceBooks.Where(x => x.ProcessId == (int)DAL.Process.SellReturn)
                .Select(x => new { x.InvoiceBookId, x.InvoiceBookName }).ToList();
            rep_InvoiceBook.DisplayMember = "InvoiceBookName";
            rep_InvoiceBook.ValueMember = "InvoiceBookId";
            #endregion

            #region Currencies
            repCrncy.DataSource = Shared.lstCurrency;
            repCrncy.ValueMember = "CrncId";
            repCrncy.DisplayMember = "crncName";
            #endregion

            if (Shared.InvoicePostToStore)
                col_Is_InTrans.Visible = false;

            ErpUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grdCategory);
            ErpUtils.Tab_Enter_Process(grdCategory);
            LoadPrivilege();
        }       


        private void frm_SL_ReturnList_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (dt1.EditValue != null)
                Shared.SL_R_FromDate = dateFrom;
            else
                Shared.SL_R_FromDate = null;

            if (dt2.EditValue != null)
                Shared.SL_R_ToDate = dateTo;
            else
                Shared.SL_R_ToDate = null;

            ErpUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }

        private void frm_SL_ReturnList_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                dt1.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                grdCategory.Focus();
            }
        }


        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
            //    Application.OpenForms["frm_SL_Return"].Close();

            //if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
            //    Application.OpenForms["frm_SL_Return"].BringToFront();
            //else
            //    new frm_SL_Return().Show();
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GetInvoices();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_Invoice();
        }


        private void grdCategory_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_Invoice();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void NBI_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            if (((NavBarItem)sender).Name == "NBI_Customers")
            {
                frmMain.OpenSL_Customer();
            }

            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int strId = Convert.ToInt32(view.GetFocusedRowCellValue(colStore));
            string strName = view.GetFocusedRowCellDisplayText(col_StoreId).ToString();
            string strFltr = (Shared.IsEnglish == true ? ResSLEn.txtStore : ResSLAr.txtStore)//"المخزن: " 
                + strName;
                        
           
        }

        private void dt1_EditValueChanged(object sender, EventArgs e)
        {
            if (dateFrom != DateTime.MinValue && dateTo != DateTime.MinValue)
            {
                if (dt1.DateTime != DateTime.MinValue)
                    dateFrom = dt1.DateTime;
                else
                    dateFrom = Shared.minDate;

                if (dt2.DateTime != DateTime.MinValue)
                    dateTo = dt2.DateTime;
                else
                {
                    dateTo = Shared.maxDate;
                }

            }
        }

        private void btnClearSearch_Click(object sender, EventArgs e)
        {
            dateFrom = Shared.minDate;
            dateTo = Shared.maxDate;
            dt1.EditValue = null;
            dt2.EditValue = null;
        }


        private void GetInvoices()
        {
            int focusedIndex = (grdCategory.FocusedView as GridView).FocusedRowHandle;

            DAL.ERPDataContext pharm = new DAL.ERPDataContext();
            var Invoices = (from c in pharm.SL_ReturnArchives
                            join v in pharm.SL_Customers on c.CustomerId equals v.CustomerId
                            join s in pharm.IC_Stores on c.StoreId equals s.StoreId
                            where customerId == 0 ? true : c.CustomerId == customerId
                            where c.ReturnDate.Date >= dateFrom && c.ReturnDate.Date <= dateTo
                            where Shared.user.UserChangeStore ? true : c.StoreId == Shared.user.DefaultStore
                            where Shared.user.AccessOtherUserTrns ? true : c.UserId == Shared.UserId

                            orderby c.ReturnDate
                            select new
                            {
                                DiscountRatio = decimal.ToDouble(c.DiscountRatio),
                                DiscountValue = decimal.ToDouble(c.DiscountValue),
                                Expenses = decimal.ToDouble(c.Expenses),
                                Net = decimal.ToDouble(c.Net),
                                Paid = decimal.ToDouble(c.Paid),
                                Remains = decimal.ToDouble(c.Remains),
                                c.ReturnCode,
                                c.ReturnDate,
                                c.JornalId,
                                c.Notes,
                                c.PayMethod,
                                c.SL_ReturnId,
                                StoreId = s.StoreNameAr,
                                Store = s.StoreId,
                                c.UserId,
                                CustomerId = v.CusNameAr,
                                CustId = v.CustomerId,
                                c.Is_InTrans,
                                c.SalesEmpId,
                                c.AddTaxValue,
                                c.DeductTaxValue,
                                c.TaxValue,
                                c.InvoiceBookId,
                                c.CrncId,
                                c.CrncRate,
                                c.DriverName,
                                c.VehicleNumber,
                                c.Destination
                            }).ToList();

            grdCategory.DataSource = Invoices;
            (grdCategory.FocusedView as GridView).FocusedRowHandle = focusedIndex;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    NBI_Customers.Enabled = false;
                    mi_OpenDealer.Enabled = false;
                }

                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Return).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void Open_Selected_Invoice()
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_SL_ReturnId));
            if (IsOpenForSelect == true)
            {
                SelectedInvId = inv_id;
                SelectedInvCode = view.GetRowCellValue(focused_row_index, col_ReturnCode).ToString();
                this.Close();
                return;
            }
            else
            {
                if (ErpUtils.IsFormOpen(typeof(frm_SL_ReturnArchive)))
                    Application.OpenForms["frm_SL_ReturnArchive"].Close();

                if (ErpUtils.IsFormOpen(typeof(frm_SL_ReturnArchive)))
                    Application.OpenForms["frm_SL_ReturnArchive"].BringToFront();
                else
                    new frm_SL_ReturnArchive(inv_id).Show();
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مردود مبيعات جديدة");
        }

        private void mi_OpenDealer_Click(object sender, EventArgs e)
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int DealerId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_CustId));

            if (ErpUtils.IsFormOpen(typeof(frm_SL_Customer)))
                Application.OpenForms["frm_SL_Customer"].Close();

            new frm_SL_Customer(DealerId).Show();
        }
    }
}