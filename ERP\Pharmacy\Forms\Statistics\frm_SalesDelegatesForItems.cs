﻿using DAL;
using DAL.Res;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy.Forms
{
    public partial class frm_SalesDelegatesForItems : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new DAL.ERPDataContext();
        UserPriv prvlg;
        DataTable dt_SalesEmps = new DataTable();

        public frm_SalesDelegatesForItems()
        {
            RTL.EnCulture(Shared.IsEnglish);

            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
        }

        private void frm_SalesDelegatesForItems_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
            LoadPrivilege();
            dtFromDate.EditValue = MyHelper.Get_Server_DateTime();
            dtToDate.EditValue = MyHelper.Get_Server_DateTime();
            int? defaultEmp = MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            lkp_SalesEmp.Properties.ValueMember = "EmpId";
            lkp_SalesEmp.Properties.DisplayMember = Shared.IsEnglish ? "EmpFName" : "EmpName";
            lkp_SalesEmp.Properties.DataSource = dt_SalesEmps;
            if (lkp_SalesEmp.EditValue == null)
                lkp_SalesEmp.EditValue = defaultEmp;

            //var lstItm = (from i in DB.IC_Items
            //              where i.ItemType != (int)DAL.ItemType.MatrixParent &&
            //              i.ItemType != (int)DAL.ItemType.Subtotal
            //              select new { i.ItemNameAr,i.ItemNameEn,i.ItemId } ).ToList();



            chkLst.Properties.DisplayMember = "ItemNameAr";
            chkLst.Properties.ValueMember = "ItemId";
            //chkLst.Properties.DataSource = lstItm;
        }

        private void barBtnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (lkp_SalesEmp.EditValue == null)
            {
                XtraMessageBox.Show(Shared.IsEnglish == true ?"Please Select Sales Employee ":"برجاء اختيار مندوب المبيعات",
                    Shared.IsEnglish == true ? ResAccEn.MsgTWarn : ResAccAr.MsgTWarn,
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }


            var ItemList = chkLst.Properties.GetItems().GetCheckedValues();
            DateTime From = Convert.ToDateTime(dtFromDate.EditValue);
            DateTime To = Convert.ToDateTime(dtToDate.EditValue);
            int salesEmpId = Convert.ToInt32(lkp_SalesEmp.EditValue);
            

            var data = (from d in DB.SL_InvoiceDetails
                         join t in DB.IC_Items.DefaultIfEmpty()
                         on d.ItemId equals t.ItemId
                         where ItemList.Count()>0? ItemList.Contains(d.ItemId):true
                         join i in DB.SL_Invoices
                         on d.SL_InvoiceId equals i.SL_InvoiceId
                         where dtFromDate.EditValue == null ? true : (i.InvoiceDate.Date >= From.Date)
                         where dtToDate.EditValue == null ? true : (i.InvoiceDate.Date <= To.Date)

                         
                         where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId
                         join m in DB.HR_Employees
                            on i.SalesEmpId equals m.EmpId

                         group new { t, i, d, m } by new { i.SalesEmpId, d.ItemId } into igroup

                         //where ItemList.Contains(igroup.Select(a=>a.d.ItemId).FirstOrDefault())
                         select new
                         {
                             ItemName = igroup.Select(g => g.t.ItemNameAr).FirstOrDefault(),
                             Qty = igroup.Sum(g => g.d.UOMIndex == 0 ? g.d.Qty : g.d.UOMIndex == 1 ? (g.d.Qty * Convert.ToDecimal(g.t.MediumUOMFactor)) : (g.d.Qty * Convert.ToDecimal(g.t.LargeUOMFactor))),
                             Delegate = igroup.Select(g => g.m.EmpName).FirstOrDefault(),
                             //ItemId = igroup.Select(g => g.t.ItemId).FirstOrDefault(),
                            // DelegateId = igroup.Select(g => g.m.EmpId).FirstOrDefault(),
                             TotalSellPrice= igroup.Sum(g => g.d.TotalSellPrice)
                         }).Distinct().ToList();

            chartControl1.DataSource = data;

        }

        private void lkp_SalesEmp_EditValueChanged(object sender, EventArgs e)
        {
            int salesEmpId =Convert.ToInt32(lkp_SalesEmp.EditValue);

            var lstItm = (from d in DB.SL_InvoiceDetails
                        join t in DB.IC_Items.DefaultIfEmpty()
                        on d.ItemId equals t.ItemId

                        join i in DB.SL_Invoices
                        on d.SL_InvoiceId equals i.SL_InvoiceId
                        where i.SalesEmpId == salesEmpId
                        select new
                        {
                            t.ItemNameAr,
                            t.ItemNameEn,
                            t.ItemId

                        }).Distinct().ToList();
            //var lstItm = (from i in DB.SL_Invoices
            //      where i.SalesEmpId == salesEmpId
            //      join d in DB.SL_InvoiceDetails
            //       on i.SL_InvoiceId equals d.SL_InvoiceId
            //        join t in DB.IC_Items
            //            on d.ItemId equals t.ItemId
            //        select new
            //        {
            //            t.ItemNameAr,
            //            t.ItemNameEn,
            //            t.ItemId

            //        }).Distinct().ToList();

            chkLst.Properties.DisplayMember = "ItemNameAr";
            chkLst.Properties.ValueMember = "ItemId";
            chkLst.Properties.DataSource = lstItm;

        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        private void printableComponentLink1_CreateReportHeaderArea(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            string ReportName = this.Text;
            string dateFilters = string.Empty;
            string otherFilters = string.Empty;

            //create filters line
            if (dtFromDate.EditValue != null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtFrom : ResAccAr.txtFrom) +
                    dtFromDate.DateTime.ToShortDateString() +
                    (Shared.IsEnglish == true ? ResAccEn.txtTo : ResAccAr.txtTo) +
                    dtToDate.DateTime.ToShortDateString();

            else if (dtFromDate.EditValue != null && dtToDate.EditValue == null)
                dateFilters =
                    (Shared.IsEnglish == true ? ResAccEn.txtFromDate : ResAccAr.txtFromDate) +
                    dtFromDate.DateTime.ToShortDateString();
            else if (dtFromDate.EditValue == null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtToDate : ResAccAr.txtToDate) +
                    dtToDate.DateTime.ToShortDateString();
            else
                dateFilters = "";


            ErpUtils.CreateReportHeader(e, ReportName, dateFilters, otherFilters);
        }
        private void printableComponentLink1_CreateReportFooter(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            RectangleF recTotal = new RectangleF((float)10, (float)17, 740, (float)25);

            e.Graph.StringFormat = Shared.IsEnglish ? new BrickStringFormat(StringAlignment.Near) : new BrickStringFormat(StringAlignment.Far);
            e.Graph.Font = new Font("Times New Roman", 13, FontStyle.Regular);
            e.Graph.ForeColor = Color.Black;
            e.Graph.DefaultBrickStyle.BorderColor = Color.Transparent;
            e.Graph.BackColor = Color.Snow;


            //string total = txtTotal.Text;

            //e.Graph.DrawString(total, recTotal);            
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (this.Width == 1376)
                chartControl1.Width = this.Width - 300;
            //chartControl1.Height = this.Height - 130;
            PrintingSystem printSystem = new PrintingSystem(this.components);
            PrintableComponentLink printLink;
            if (this.components == null)
                printLink = new PrintableComponentLink();
            else
                printLink = new PrintableComponentLink(this.components);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).BeginInit();

            printSystem.Links.AddRange(new object[] {
            printLink});

            printLink.Component = this.chartControl1;

            printLink.PaperKind = System.Drawing.Printing.PaperKind.A4;
            printLink.Landscape = true;
            printLink.Margins = new System.Drawing.Printing.Margins(5, 5, 135, 50);
            printLink.PrintingSystem = printSystem;
            printLink.PrintingSystemBase = printSystem;

            printLink.CreateMarginalHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportHeaderArea);
            printLink.CreateReportFooterArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportFooter);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).EndInit();

            printLink.CreateDocument();
            printLink.ShowPreview();
            chartControl1.Width = this.Width - 51;
        }



        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SalesDelegatesForItems).FirstOrDefault();

                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnOk.Enabled = false;
            }
        }
    }
}
