﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_Warranty : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, customerId1, customerId2, custGroupId,
            storeId1, storeId2, salesEmpId;
        byte FltrTyp_item, fltrTyp_Date, FltrTyp_Customer, FltrTyp_Category,
            FltrTyp_Store, FltrTyp_InvBook;
        string categoryNum;
        DateTime date1, date2;        
        string custGroupAccNumber;
        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();        
        public int count;

        byte FltrTyp_Company;
        int companyId;
        public frm_SL_Warranty(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            byte FltrTyp_Category, string categoryNum,
            int custGroupId, string custGroupAccNumber,
            byte FltrTyp_Store, int storeId1, int storeId2,
            int salesEmpId,
            byte FltrTyp_InvBook, string InvBooks, byte FltrTyp_Company, int companyId)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;            
            this.FltrTyp_Category = FltrTyp_Category;
            this.FltrTyp_Store = FltrTyp_Store;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;

            this.date1 = date1;
            this.date2 = date2;
            if (date2 == Shared.minDate)
                this.date2 = Shared.maxDate;

            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            this.categoryNum = categoryNum;

            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;
            this.storeId1 = storeId1;
            this.storeId2 = storeId2;
            this.salesEmpId = salesEmpId;            

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();

            LoadData();
            col_PiecesCount.Visible = Shared.st_Store.PiecesCount;
            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));
            //LoadPrivilege();
            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            ERPDataContext DB = new ERPDataContext();

            var stores = DB.IC_Stores.ToList();
            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= storeId2 && store.StoreId >= storeId1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (storeId1 > 0 && (store.StoreId == storeId1 || store.ParentId == storeId1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }

            #region sales
            var data = (
                    from c in DB.SL_Customers
                    join a in DB.ACC_Accounts
                        on c.AccountId equals a.AccountId
                    where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                    where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                    where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                    c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                    where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                    c.CustomerId >= customerId1 : true
                    where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                    c.CustomerId <= customerId2 : true

                    join i in DB.SL_Invoices on c.CustomerId equals i.CustomerId

                    // where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true
                    //update
                    join d in DB.SL_InvoiceDetails on i.SL_InvoiceId equals d.SL_InvoiceId
                  where lstStores.Count > 0 ?( lstStores.Contains(i.StoreId)|| lstStores.Contains(d.StoreId.Value)) : true

                    where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                    where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                    join s in DB.SL_InvoiceDetails on i.SL_InvoiceId equals s.SL_InvoiceId

                    where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                    where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                    s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                    where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                    s.ItemId >= itemId1 : true
                    where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                    s.ItemId <= itemId2 : true

                    join t in DB.IC_Items on s.ItemId equals t.ItemId
                    where FltrTyp_Company == 1 ? t.Company == companyId : true

                    join g in DB.IC_Categories on t.Category equals g.CategoryId
                    where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true
                    where t.WarrantyMonths > 0
                    join u in DB.IC_UOMs on s.UOMId equals u.UOMId

                    from emp in DB.HR_Employees.Where(emp => emp.SalesRep && emp.EmpId == i.SalesEmpId).DefaultIfEmpty()
                    from custGrp in DB.SL_CustomerGroups.Where(custGrp => custGrp.CustomerGroupId == c.CategoryId).DefaultIfEmpty()

                    let EndDate = i.InvoiceDate.AddMonths(t.WarrantyMonths)

                    select new
                    {
                        CusNameAr = c.CusNameAr,
                        InvoiceCode = i.InvoiceCode,
                        InvoiceDate = i.InvoiceDate,
                        ItemNameAr = t.ItemNameAr,
                        Qty = s.Qty,
                        UOM = u.UOM,
                        PiecesCount = s.PiecesCount,
                        SellPrice = s.SellPrice,
                        TotalSellValue = s.Qty * s.SellPrice,
                        s.DiscountRatio,
                        s.DiscountRatio2,
                        s.DiscountRatio3,
                        DicVal1 = (s.Qty * s.SellPrice) * s.DiscountRatio,
                        DicVal2 = ((s.Qty * s.SellPrice) - ((s.Qty * s.SellPrice) * s.DiscountRatio)) * s.DiscountRatio2,
                        DicVal3 = 
                        ((s.Qty * s.SellPrice) - 
                        ((s.Qty * s.SellPrice) * s.DiscountRatio) - 
                        (((s.Qty * s.SellPrice) - ((s.Qty * s.SellPrice) * s.DiscountRatio)) * s.DiscountRatio2)) * s.DiscountRatio3,
                        s.DiscountValue,
                        s.SalesTax,
                        //TotalSellPrice = ((s.Qty * s.SellPrice) - s.DiscountValue) +
                        //(Shared.st_Store.PriceIncludeSalesTax ? s.SalesTax * -1 : s.SalesTax)
                        TotalSellPrice = s.TotalSellPrice,
                        s.ItemDescription,
                        s.ItemDescriptionEn,
                        i.DriverName,
                        i.VehicleNumber,
                        i.Destination,
                        SalesEmp = emp == null ? null : emp.EmpName,
                        s.ItemId,
                        s.Height,
                        s.Length,
                        s.Width,
                        s.Expire,
                        s.Batch,
                        EndDate = EndDate,
                        i.Notes,
                        custGrp = custGrp == null ? string.Empty : custGrp.CGNameAr,
                        c.Tel,
                        c.Mobile,
                        c.Address,
                        c.CusCode,
                       Store = i.StoreId==0? DB.IC_Stores.SingleOrDefault(c => c.StoreId == d.StoreId).StoreNameAr: DB.IC_Stores.SingleOrDefault(c => c.StoreId == i.StoreId).StoreNameAr

                    }).Where(x=> x.EndDate.Date >= date1 && x.EndDate.Date <= date2).ToList().Distinct().ToList();

                grdCategory.DataSource = data;
                count = data.Count;
                #endregion
            
            if (FltrTyp_Customer == 1)
                col_CusNameAr.Visible = false;

            if (FltrTyp_item == 1)
                col_ItemNameAr.Visible = false;
        }

        private void mi_CopyRows_Click(object sender, EventArgs e)
        {
            ReportsUtils.dt_Copied_Rows.Clear();
            foreach (int i in gridView1.GetSelectedRows())
            {
                DataRow row = ReportsUtils.dt_Copied_Rows.NewRow();
                row["ItemId"] = Convert.ToInt32(gridView1.GetRowCellValue(i, "ItemId"));
                row["Qty"] = Convert.ToDouble(gridView1.GetRowCellValue(i, col_Qty));

                if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply && Convert.ToDouble(gridView1.GetRowCellValue(i, "Height")) <= 0)
                    row["Height"] = 1;
                else
                    row["Height"] = Convert.ToDouble(gridView1.GetRowCellValue(i, "Height"));

                if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply && Convert.ToDouble(gridView1.GetRowCellValue(i, "Length")) <= 0)
                    row["Length"] = 1;
                else
                    row["Length"] = Convert.ToDouble(gridView1.GetRowCellValue(i, "Length"));

                if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply && Convert.ToDouble(gridView1.GetRowCellValue(i, "Width")) <= 0)
                    row["Width"] = 1;
                else
                    row["Width"] = Convert.ToDouble(gridView1.GetRowCellValue(i, "Width"));

                row["PiecesCount"] = Convert.ToDouble(gridView1.GetRowCellValue(i, "PiecesCount"));

                if (gridView1.GetRowCellValue(i, "Expire") != null)
                    row["Expire"] = Convert.ToDateTime(gridView1.GetRowCellValue(i, "Expire"));
                if (gridView1.GetRowCellValue(i, "Batch") != null)
                    row["Batch"] = gridView1.GetRowCellValue(i, "Batch").ToString();

                row["SellPrice"] = gridView1.GetRowCellValue(i, "SellPrice");

                ReportsUtils.dt_Copied_Rows.Rows.Add(row);
            }
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "Index")
                e.Value = e.RowHandle() + 1;
        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SL_Warranty).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }

    }
}