﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;

namespace Reports
{
 
    public partial class  frm_SubTaxNet: DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters;
        byte fltrTyp_Date;
        DateTime date1, date2;
        public static  bool findData;



        public frm_SubTaxNet(string reportName, string dateFilter, string otherFilters,
            DateTime date1, DateTime date2, byte FltrTyp_Date )
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
        
            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.date1 = date1;
            this.date2 = date2;        
            this.fltrTyp_Date = FltrTyp_Date;                
            LoadData();

            ReportsUtils.ColumnChooser(grdSubTaxNet);
        }

        void LoadData()
        {
            lblReportName.Text = reportName;
            if (date2 <= Shared.minDate)
                date2 = Shared.maxDate;

            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            lblDateFilter.Text = dateFilter;
            ERPDataContext DB = new ERPDataContext();
            var data = (from v in DB.SL_Invoices
                        join u in DB.SL_InvoiceDetails on v.SL_InvoiceId equals u.SL_InvoiceId
                        join d in DB.SL_InvoiceDetailSubTaxValues on u.SL_InvoiceDetailId equals d.InvoiceDetailId
                         where fltrTyp_Date == 1 ? v.InvoiceDate.Date == date1.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                         v.InvoiceDate.Date >= date1.Date && v.InvoiceDate.Date <= date2.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                         v.InvoiceDate.Date >= date1.Date : true
                         where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                         v.InvoiceDate.Date <= date2.Date : true
                         group d by d.esubTypeId into g

                        let  returnData = (from r in DB.SL_Returns
                                   join rd in DB.SL_ReturnDetails on r.SL_ReturnId equals rd.SL_ReturnId
                                   join d in DB.SlReturnInvoiceDetailSubTaxValues on rd.SL_ReturnDetailId equals d.ReturnInvoiceDetailId
                                   where fltrTyp_Date == 1 ? r.ReturnDate.Date == date1.Date : true
                                   where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                   r.ReturnDate.Date >= date1.Date && r.ReturnDate.Date <= date2.Date : true
                                   where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                   r.ReturnDate.Date >= date1.Date : true
                                   where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                   r.ReturnDate.Date <= date2.Date : true
                                   where  d.esubTypeId == g.Key
                                   select d).ToList()
                        let addData = (from r in DB.SL_Adds
                                           join rd in DB.SL_Add_Details on r.SL_AddId equals rd.SL_AddId
                                           join d in DB.Sl_Add_DetailSubTaxValues on rd.SL_Add_DetailId equals d.Sl_AddDetailId
                                           where fltrTyp_Date == 1 ? r.ReturnDate.Date == date1.Date : true
                                           where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                           r.ReturnDate.Date >= date1.Date && r.ReturnDate.Date <= date2.Date : true
                                           where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                           r.ReturnDate.Date >= date1.Date : true
                                           where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                           r.ReturnDate.Date <= date2.Date : true
                                           where d.esubTypeId == g.Key
                                           select d).ToList()

                        let subTax = DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == g.Key)
                        select new
                        {
                            SubTaxSlInvouce = Convert.ToDouble(g.Sum(a => a.value)),
                            subTaxID = g.Key,
                            subTaxName = subTax != null ? subTax.DescriptionAr : "",
                            subTaxParent = (subTax != null || subTax.ParentTaxId != null) ? DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == subTax.ParentTaxId).DescriptionAr : "",
                            SubTaxReturnInvouce = returnData.Count() > 0 ? Convert.ToDouble(returnData.Sum(a => a.value)) : 0,
                            SubTaxAddInvouce = addData.Count() > 0 ? Convert.ToDouble(addData.Sum(a => a.value)) : 0,
                            Net =Convert.ToDouble(g.Sum(a => a.value) + (addData.Count() > 0 ? addData.Sum(a => a.value) : 0) - (returnData.Count() > 0 ? returnData.Sum(a => a.value) : 0)),
                        }).ToList();

      
             grdSubTaxNet.DataSource = data;
            //var data = (from v in DB.E_TaxableTypes
            //            join u in DB.SL_InvoiceDetailSubTaxValues on v.E_TaxableTypeId equals u.esubTypeId
            //            join d in DB.SL_InvoiceDetails on u.InvoiceDetailId equals d.SL_InvoiceDetailId
            //            join ss in DB.SL_Invoices on d.SL_InvoiceId equals ss.SL_InvoiceId
            //            group u by u.esubTypeId into g
            //            let returnData = DB.SlReturnInvoiceDetailSubTaxValues.Where(a => a.esubTypeId == g.Key).ToList()
            //            let addData = DB.Sl_Add_DetailSubTaxValues.Where(a => a.esubTypeId == g.Key).ToList()
            //            let subTax=DB.E_TaxableTypes.FirstOrDefault(a=>a.E_TaxableTypeId == g.Key)
            //            select new
            //           {
            //               SubTaxSlInvouce = g.Sum(a => a.value),
            //               subTaxID = g.Key,
            //               subTaxName= subTax!=null?subTax.DescriptionAr:"",
            //               subTaxParent= (subTax != null && subTax.ParentTaxId !=null) ? DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == subTax.ParentTaxId).DescriptionAr:"",
            //               SubTaxReturnInvouce = returnData.Count()>0? returnData.Sum(a=>a.value):0,
            //               SubTaxAddInvouce = addData.Count()>0 ? addData.Sum(a => a.value) : 0,
            //               Net= g.Sum(a => a.value) + (addData.Count() > 0 ? addData.Sum(a => a.value) : 0 )- (returnData.Count() > 0 ? returnData.Sum(a => a.value) : 0),
            //            }).ToList();
        }



        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "Index")
                e.Value = e.RowHandle() + 1;
        }

        public bool LoadPrivilege()
        {

            if (Shared.LstUserPrvlg != null)
            {
                /*TO BE DONE AMIR*/
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SubTaxNet).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }

            }
            return true;
        }


        private void frm_ImExp_Fines_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdSubTaxNet, this.Name.Replace("frm_", "Rpt_"));
            LoadPrivilege();
        }

        private void frm_ImExp_Fines_FormClosing(object sender, FormClosingEventArgs e)
        {
           ReportsUtils.save_Grid_Layout(grdSubTaxNet, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdSubTaxNet.MinimumSize = grdSubTaxNet.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdSubTaxNet, true).ShowPreview();
            grdSubTaxNet.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdSubTaxNet.MinimumSize = grdSubTaxNet.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdSubTaxNet, true, true).ShowPreview();
                grdSubTaxNet.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

  
    }
}