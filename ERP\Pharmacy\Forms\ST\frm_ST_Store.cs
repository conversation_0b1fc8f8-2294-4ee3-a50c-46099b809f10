﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraReports.UI;
using Reports;
using System.IO;

namespace Pharmacy.Forms
{
    public partial class frm_ST_Store : DevExpress.XtraEditors.XtraForm
    {
        List<acc> lstAccounts = new List<acc>();
        DAL.ERPDataContext DBbind = new DAL.ERPDataContext();
        int crncId;
        bool DataModified;
        public frm_ST_Store()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            grp_Libra.Visible = Shared.LibraAvailabe;
        }

        private void frm_ST_Store_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            if (Shared.CarsAvailable == false)
            {
                gbmatrix.Visible = false;
            }

            if (Shared.StockIsPeriodic)
            {
                tabContinual.PageVisible = false;
                //pnlPeriodicItemPosting.Visible = true;
                tabInvType.SelectedTabPage = tabPerdiodic;
            }
            if (Shared.HRAvailable == false)
                tab_Hr.PageVisible = false;

            if (Shared.StockIsPeriodic == false)
            {
                tabPerdiodic.PageVisible = false;
                lblInventory.Visible = lkp_InventoryAcc.Visible = false;
                lkp_InventoryAcc.EditValue = null;
                //pnlPeriodicItemPosting.Visible = false;
                tabInvType.SelectedTabPage = tabContinual;
            }
            if (Shared.EgyptPharmacyTaxAvailable == true)
            {
                labelControl43.Visible = false;
                lkpCustomTax.Visible = false;
                lkpCustomTax.EditValue = null;
            }
            if (Shared.ItemsPostingAvailable)
            {
                tabPerdiodic.PageVisible = false;
                tabContinual.PageVisible = false;
                tab_ItemsPost.PageVisible = true;
                tabInvType.SelectedTabPage = tab_ItemsPost;
            }

            if (Shared.SysModelIsERP == false)
            {
                btnInvBooks.Visible = false;
            }

            if (!Shared.RealEstateAvailable)
            {
                grpboxOther.Visible = false;
            }

            if (Shared.InvoicePostToStore == false)
                lkp_intermediateInventoryAcc.Visible = lbl_IntermediateAcc.Visible = true;

            if (Shared.PrInvoicePostToStore == false)
                lkp_intermediatePrInventoryAcc.Visible = lbl_IntermediateAcc.Visible = true;

            grp_Trans_Labor_Revenue.Visible = Shared.LibraAvailabe;

            grdDue.DataSource = null;// DBbind.ST_DuePeriods;
            grd_IncomeTaxDisc.DataSource = null;// DBbind.ST_IncomeTaxDiscounts;
            grd_IncomeTaxLevel.DataSource = null;// DBbind.ST_IncomeTaxLevels;

            //grdPrintSamples
            grdPrintSamples.DataSource = null;// DBbind.ST_PrintSamples;
            lstAccounts = HelperAcc.LoadAccountsTree(0, true);

            #region Bind Accounts
            //Assets
            lkp_FixedAssets.Properties.DataSource =
            lkp_DrawersAcc.Properties.DataSource =
            lkp_BanksAcc.Properties.DataSource =
             lkp_VisaAccs.Properties.DataSource =
            lkp_CustomersAcc.Properties.DataSource =
            lkp_NotesReceivableAcc.Properties.DataSource =
            lkp_UnderCollectionAcc.Properties.DataSource =
            lkp_InventoryAcc.Properties.DataSource =
            //Liabilities
            lkp_VendorsAcc.Properties.DataSource =
            lkp_CapitalAcc.Properties.DataSource =
            lkp_NotesPayableAcc.Properties.DataSource =
            lkp_DepreciationAcc.Properties.DataSource =
            //tax
            lkp_TaxAcc.Properties.DataSource =
            lkpPurchaseDeductTaxAccount.Properties.DataSource =
            lkpSalesDeductTaxAccount.Properties.DataSource =
            lkpPurchaseAddTaxAccount.Properties.DataSource =
            lkpCustomTax.Properties.DataSource =
            lkpSalesAddTaxAccount.Properties.DataSource =
            //Expenses
            lkp_ManufacturingExpAcc.Properties.DataSource =
            //Merchedaising
            lkp_MerchandisingAcc.Properties.DataSource =
            lkp_PurchasesAcc.Properties.DataSource =
            lkp_PurchasesReturnAcc.Properties.DataSource =
            lkp_SalesAcc.Properties.DataSource =
            lkp_SalesAcc2.Properties.DataSource =
            lkp_SalesReturnAcc.Properties.DataSource =
            lkp_SalesReturnAcc2.Properties.DataSource =
            lkp_CostOfSoldGoods.Properties.DataSource =
            lkp_OpenInventoryAcc.Properties.DataSource =
            lkp_CloseInventoryAcc.Properties.DataSource =
            lkp_PurchaseDiscountAcc.Properties.DataSource =
            lkp_SalesDiscount.Properties.DataSource =
            lkp_ItemPostPurchaseDisc.Properties.DataSource =
            lkp_ItemPostSalesDisc.Properties.DataSource =
            lkp_CapitalPL.Properties.DataSource =
            //HR
            lkp_HrLoanAcc.Properties.DataSource =
            lkp_HrSalaryAcc.Properties.DataSource =
            lkp_HrAccuredSalaryAccount.Properties.DataSource =
            lkp_DebitNotesAccount.Properties.DataSource =
            lkp_CreditNotesAccount.Properties.DataSource =
            lkp_LcAccount.Properties.DataSource =
            lkp_RealStateSellRvnuAcc.Properties.DataSource =
            lkp_revenueAcc.Properties.DataSource =
            lkp_ExpensesAcc.Properties.DataSource =
            lkp_AdvancePayment.Properties.DataSource =
            lkp_Retention.Properties.DataSource =
            lkp_LaborRevenue.Properties.DataSource =
            lkp_TransferRevenue.Properties.DataSource =
            lkp_intermediateInventoryAcc.Properties.DataSource =
            lkp_intermediatePrInventoryAcc.Properties.DataSource =
            lkp_InsuranceAcc.Properties.DataSource =
            lkp_BusinessGainAcc.Properties.DataSource =
            lkp_NetTaxAcc.Properties.DataSource =
            lkp_PenalityAcc.Properties.DataSource =
            lkp_AbsenceAcc.Properties.DataSource =
            lkp_VacationAcc.Properties.DataSource =
            lkp_CustodyAcc.Properties.DataSource =
            lkp_PR_ReturnCostACC.Properties.DataSource =
            lstAccounts;

            //Assets
            lkp_FixedAssets.Properties.ValueMember =
            lkp_DrawersAcc.Properties.ValueMember =
            lkp_BanksAcc.Properties.ValueMember =
             lkp_VisaAccs.Properties.ValueMember =
            lkp_CustomersAcc.Properties.ValueMember =
            lkp_NotesReceivableAcc.Properties.ValueMember =
            lkp_UnderCollectionAcc.Properties.ValueMember =
            lkp_InventoryAcc.Properties.ValueMember =
            //Liabilities
            lkp_VendorsAcc.Properties.ValueMember =
            lkp_CapitalAcc.Properties.ValueMember =
            lkp_NotesPayableAcc.Properties.ValueMember =
            lkp_DepreciationAcc.Properties.ValueMember =
            //Tax
            lkp_TaxAcc.Properties.ValueMember =
            lkpPurchaseDeductTaxAccount.Properties.ValueMember =
            lkpSalesDeductTaxAccount.Properties.ValueMember =
            lkpPurchaseAddTaxAccount.Properties.ValueMember =
           lkpCustomTax.Properties.ValueMember =
            lkpSalesAddTaxAccount.Properties.ValueMember =
            //Expenses
            lkp_ManufacturingExpAcc.Properties.ValueMember =
            //Merchedaising
            lkp_MerchandisingAcc.Properties.ValueMember =
            lkp_PurchasesAcc.Properties.ValueMember =
            lkp_PurchasesReturnAcc.Properties.ValueMember =
            lkp_SalesAcc.Properties.ValueMember =
            lkp_SalesAcc2.Properties.ValueMember =
            lkp_SalesReturnAcc.Properties.ValueMember =
            lkp_SalesReturnAcc2.Properties.ValueMember =
            lkp_CostOfSoldGoods.Properties.ValueMember =
            lkp_OpenInventoryAcc.Properties.ValueMember =
            lkp_CloseInventoryAcc.Properties.ValueMember =
            lkp_PurchaseDiscountAcc.Properties.ValueMember =
            lkp_SalesDiscount.Properties.ValueMember =
            lkp_ItemPostPurchaseDisc.Properties.ValueMember =
            lkp_ItemPostSalesDisc.Properties.ValueMember =
            //HR
            lkp_HrLoanAcc.Properties.ValueMember =
            lkp_HrSalaryAcc.Properties.ValueMember =
            lkp_HrAccuredSalaryAccount.Properties.ValueMember =
            lkp_DebitNotesAccount.Properties.ValueMember =
            lkp_CreditNotesAccount.Properties.ValueMember =
            lkp_LcAccount.Properties.ValueMember =
            lkp_RealStateSellRvnuAcc.Properties.ValueMember =
            lkp_revenueAcc.Properties.ValueMember =
            lkp_ExpensesAcc.Properties.ValueMember =
            lkp_Retention.Properties.ValueMember =
            lkp_AdvancePayment.Properties.ValueMember =
            lkp_LaborRevenue.Properties.ValueMember =
            lkp_TransferRevenue.Properties.ValueMember =
            lkp_intermediateInventoryAcc.Properties.ValueMember =
            lkp_intermediatePrInventoryAcc.Properties.ValueMember =
            lkp_PR_ReturnCostACC.Properties.ValueMember =
            lkp_InsuranceAcc.Properties.ValueMember =
            lkp_BusinessGainAcc.Properties.ValueMember =
            lkp_NetTaxAcc.Properties.ValueMember =
            lkp_PenalityAcc.Properties.ValueMember =
            lkp_AbsenceAcc.Properties.ValueMember =
            lkp_VacationAcc.Properties.ValueMember =
            lkp_CustodyAcc.Properties.ValueMember =
            "AccId";

            //Assets
            lkp_FixedAssets.Properties.DisplayMember =
            lkp_DrawersAcc.Properties.DisplayMember =
            lkp_BanksAcc.Properties.DisplayMember =
             lkp_VisaAccs.Properties.DisplayMember =
            lkp_CustomersAcc.Properties.DisplayMember =
            lkp_NotesReceivableAcc.Properties.DisplayMember =
            lkp_UnderCollectionAcc.Properties.DisplayMember =
            lkp_InventoryAcc.Properties.DisplayMember =
            //Liabilities
            lkp_VendorsAcc.Properties.DisplayMember =
            lkp_CapitalAcc.Properties.DisplayMember =
            lkp_NotesPayableAcc.Properties.DisplayMember =
            lkp_DepreciationAcc.Properties.DisplayMember =
            //Tax
            lkp_TaxAcc.Properties.DisplayMember =
            lkpPurchaseDeductTaxAccount.Properties.DisplayMember =
            lkpSalesDeductTaxAccount.Properties.DisplayMember =
            lkpPurchaseAddTaxAccount.Properties.DisplayMember =
            lkpCustomTax.Properties.DisplayMember =
            lkpSalesAddTaxAccount.Properties.DisplayMember =
            //Expenses
            lkp_ManufacturingExpAcc.Properties.DisplayMember =
            //Merchedaising
            lkp_MerchandisingAcc.Properties.DisplayMember =
            lkp_PurchasesAcc.Properties.DisplayMember =
            lkp_PurchasesReturnAcc.Properties.DisplayMember =
            lkp_SalesAcc.Properties.DisplayMember =
            lkp_SalesAcc2.Properties.DisplayMember =
            lkp_SalesReturnAcc.Properties.DisplayMember =
            lkp_SalesReturnAcc2.Properties.DisplayMember =
            lkp_CostOfSoldGoods.Properties.DisplayMember =
            lkp_OpenInventoryAcc.Properties.DisplayMember =
            lkp_CloseInventoryAcc.Properties.DisplayMember =
            lkp_PurchaseDiscountAcc.Properties.DisplayMember =
            lkp_SalesDiscount.Properties.DisplayMember =
            lkp_ItemPostPurchaseDisc.Properties.DisplayMember =
            lkp_ItemPostSalesDisc.Properties.DisplayMember =
            lkp_CapitalPL.Properties.DisplayMember =
            lkp_intermediateInventoryAcc.Properties.DisplayMember =
            lkp_intermediatePrInventoryAcc.Properties.DisplayMember =
            lkp_PR_ReturnCostACC.Properties.DisplayMember =
            //HR
            lkp_HrLoanAcc.Properties.DisplayMember =
            lkp_HrSalaryAcc.Properties.DisplayMember =
            lkp_HrAccuredSalaryAccount.Properties.DisplayMember =
            lkp_DebitNotesAccount.Properties.DisplayMember =
            lkp_CreditNotesAccount.Properties.DisplayMember =
            lkp_LcAccount.Properties.DisplayMember =
            lkp_RealStateSellRvnuAcc.Properties.DisplayMember =
            lkp_revenueAcc.Properties.DisplayMember =
            lkp_ExpensesAcc.Properties.DisplayMember =
            lkp_Retention.Properties.DisplayMember =
            lkp_AdvancePayment.Properties.DisplayMember =
            lkp_LaborRevenue.Properties.DisplayMember =
            lkp_TransferRevenue.Properties.DisplayMember =
            lkp_InsuranceAcc.Properties.DisplayMember =
            lkp_BusinessGainAcc.Properties.DisplayMember =
            lkp_NetTaxAcc.Properties.DisplayMember =
            lkp_PenalityAcc.Properties.DisplayMember =
            lkp_AbsenceAcc.Properties.DisplayMember =
            lkp_VacationAcc.Properties.DisplayMember =
            lkp_CustodyAcc.Properties.DisplayMember =
            "AccName";

            #endregion

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var StoreData = (from d in DB.ST_Stores
                             select d).FirstOrDefault();

            if (StoreData == null)
                return;
            else
            {
                lkp_Libra.EditValue = StoreData.Libra_UOM_Id;
                lkp_KG_PR.EditValue = StoreData.KG_PR_UOM_Id;
                lkp_KG_SL.EditValue = StoreData.KG_SL_UOM_Id;
                txt_KG_PR_Factor.EditValue = StoreData.KG_PR_Factor;
                txt_KG_SL_Factor.EditValue = StoreData.KG_SL_Factor;

                //mohammad 5-9-2018
                chk_ApproveSalesOrder.Checked = StoreData.MustApproveSalesOrder;
                //Mohammad 24-7-2019
                imgCostDistributionMethod.EditValue = StoreData.CostDistributionMethod;

                txtMainCrncName.Text = StoreData.MainCurrencyName;
                txtPound1.Text = StoreData.CurrencyPound1;
                txtPound2.Text = StoreData.CurrencyPound2;
                txtPound3.Text = StoreData.CurrencyPound3;
                txtPiaster1.Text = StoreData.CurrencyPiaster1;
                txtPiaster2.Text = StoreData.CurrencyPiaster2;
                txtPiaster3.Text = StoreData.CurrencyPiaster3;
                txtCurrencyDigitsCount.EditValue = StoreData.CurrencyDigitsCount;

                txtMyDocReportPathFolder.Text = StoreData.MyDocReportPathFolder;
                txtSecondReportPath.Text = StoreData.SecondReportPath;
                txtAttachPath.Text = StoreData.AttachmentPath;

                chkBuyAssembly.Checked = StoreData.BuyAssembly;
                chkSellRawMaterial.Checked = StoreData.SellRawMaterial;
                chkSerialForAssembly.Checked = StoreData.SerialForAssembly;
                chk_manfProductsOnly.Checked = StoreData.ManufactureProductsOnly;

                chkExpire.Checked = StoreData.ExpireDate;
                ExpireDisplay.EditValue = StoreData.ExpireDisplay;
                chkPiecesCount.Checked = StoreData.PiecesCount;
                chkPackCount.Checked = StoreData.PackCount.HasValue ? StoreData.PackCount.Value : false;
                chk_UseLastCostPrice.Checked = StoreData.UseLastCostPrice.HasValue ? StoreData.UseLastCostPrice.Value : false;
                chk_UseQC.Checked = StoreData.UseQC;
                chk_Serial.Checked = StoreData.Serial;

                chk_StoreSales.Checked = StoreData.IsStoreOnEachSellRecord;
                chk_StorePurchase.Checked = StoreData.IsStoreOnEachPurchRecord;

                // Max limit for sales order & sales invoices
                chk_max_SL_Order.Checked = StoreData.IsMaxSalesOrder != null ? StoreData.IsMaxSalesOrder.Value : false;
                chk_max_SL_Invoice.Checked = StoreData.IsMaxSalesInvoice != null ? StoreData.IsMaxSalesInvoice.Value : false;

                txt_BatchNameAr.Text = StoreData.BatchNameAr;
                txt_BatchNameEn.Text = StoreData.BatchNameEn;
                txt_SerialNameAr.Text = StoreData.SerialNameAr;
                txt_SerialNameEn.Text = StoreData.SerialNameEn;

                // Adel: Update serial 2
                txt_SerialNameAr2.Text = StoreData.Serial2NameAr;
                txt_SerialNameEn2.Text = StoreData.Serial2NameEn;
                //Samar=========//
                CompanyAr.Text = StoreData.CompanyAr;
                CompanyEn.Text = StoreData.CompanyEn;
                CategoryAr.Text = StoreData.CategoryAr;
                CategoryEn.Text = StoreData.CategoryEn;
                // Adel: Update Pieces Count
                txtPiecesCountAr.Text = StoreData.PiecesCountNameAr;
                txtPiecesCountEn.Text = StoreData.PiecesCountNameEn;

                // Adel: Update Barcode Settings
                txtItemBarcode.Text = StoreData.bcItemCount.ToString();
                txtScaleBarcode.Text = StoreData.bcScalePrice.ToString();
                if (StoreData.isPrice.HasValue)
                    rdo_ScaleBarcodeType.EditValue = Convert.ToInt32(StoreData.isPrice);
                txtPrice.Text = StoreData.bcScaleCount.ToString();
                txtIdentifier.Text = StoreData.bcIdentifier.ToString();

                chkBatch.Checked = StoreData.Batch;
                chk_AutoSerialBatch.Checked = StoreData.PurchaseAutoSerialBatch;
                chk_EncodePrInvPerVendor.Checked = StoreData.EncodePrInvPerVendor;

                chkPriceIncludeSalesTax.Checked = StoreData.PriceIncludeSalesTax;
                chkPriceIncludePurchaseTax.Checked = StoreData.PriceIncludePurchaseTax;
                chkCalcPurchaseTaxPerItem.Checked = StoreData.CalcPurchaseTaxPerItem;


                dtCloseDate.EditValue = StoreData.ClosePeriodDate;
                dtLastEvaluation.EditValue = StoreData.LastEvaluationDate;
                slDefaultDecimalPoint.EditValue = StoreData.DefaultRoundingPoints ?? 4;
                rd_IsFutureDueDate.EditValue = StoreData.isFutureDueDate;

                if (StoreData.MustApprovePurchaseQuote.HasValue)
                    chk_ApprovePrQuote.Checked = StoreData.MustApprovePurchaseQuote.Value;
                if (StoreData.MustApprovePurchaseOrder.HasValue)
                    chk_ApprovePrOrder.Checked = StoreData.MustApprovePurchaseOrder.Value;
                #region HR

                txt_TotalMonthDays_HR.EditValue = StoreData.TotalMonthDays_HR;
                if (StoreData.NetSalaryTax.HasValue)
                    txtNetSalaryTax.EditValue = decimal.ToDouble(StoreData.NetSalaryTax.Value);
                txtExemption.EditValue = decimal.ToDouble(StoreData.ExemptionRatio.HasValue ? StoreData.ExemptionRatio.Value : 0);
                if (StoreData.TotalSalaryType != null)
                    chkTotalSalaryType.Checked = StoreData.TotalSalaryType.Value;
                chkEgyptianLaw.Checked = StoreData.EgyptionLaw.HasValue ? StoreData.EgyptionLaw.Value : true;

                // Insurance
                txtMinSalary.EditValue = StoreData.MinInsureSalary == null ? 0 : decimal.ToDouble(StoreData.MinInsureSalary.Value);
                txtMaxSalary.EditValue = StoreData.MaxInsureSalary == null ? 0 : decimal.ToDouble(StoreData.MaxInsureSalary.Value);
                txtComShare.EditValue = StoreData.companyShare == null ? 0 : decimal.ToDouble(StoreData.companyShare.Value);
                txtEmpShare.EditValue = StoreData.EmpShare == null ? 0 : decimal.ToDouble(StoreData.EmpShare.Value);
                /////////////////////////////////////////////

                chk_AttendanceIs2Shifts.Checked = StoreData.AttendanceIs2Shifts;
                chk_DelayOnAttendTimeOnly.Checked = StoreData.DelayOnAttendTimeOnly;

                chk_GulfHRAvailable.Checked = StoreData.GulfHRAvailable;
                chk_IncomeTaxAvailable.Checked = StoreData.IncomeTaxAvailable;
                #endregion
                chk_Registered.Checked = StoreData.UseRegisteredNotes;
                chk_SalesOrderReserveGood.Checked = StoreData.SalesOrderReserveGood;
                chk_GroupPOSItems.Checked = StoreData.GroupPOSItems;
                chk_SellAsRestaurant.Checked = StoreData.SellAsRestaurant;
                chk_SalesEmpMandatory.Checked = StoreData.SalesEmpMandatory;
                ch_Authorize.Checked = StoreData.ch_Authorize == null ? false : StoreData.ch_Authorize.Value;
                chk_allowMoreThanTax.Checked = StoreData.E_AllowMoreThanTax == null ? false : StoreData.E_AllowMoreThanTax.Value;
                
                chkOutstanding.Checked = StoreData.OutstandingRecieveNote == null ? false : StoreData.OutstandingRecieveNote.Value;
                chk_SalesOrderforClient.Checked = StoreData.SalesOrderforClient == null ? false : StoreData.SalesOrderforClient.Value;
                chk_CsTypeValidation.Checked = StoreData.chk_CsTypeValidation == null ? false : StoreData.chk_CsTypeValidation.Value;

                chkMediumUom.Checked = StoreData.UseMediumUom;
                chkLargeUom.Checked = StoreData.UseLargeUom;

                chkLength.Checked = StoreData.UseLengthDimension;
                chkWidth.Checked = StoreData.UseWidthDimension;
                chkHeight.Checked = StoreData.UseHeightDimension;
                chkMultiplyDimensions.EditValue = StoreData.MultiplyDimensions;

                #region Get Accounts
                //Assets
                lkp_FixedAssets.EditValue = StoreData.FixedAssets;
                lkp_DrawersAcc.EditValue = StoreData.DrawersAcc;
                lkp_BanksAcc.EditValue = StoreData.BanksAcc;
                lkp_VisaAccs.EditValue = StoreData.VisaAccount;
                lkp_CustomersAcc.EditValue = StoreData.CustomersAcc;
                lkp_NotesReceivableAcc.EditValue = StoreData.NotesReceivableAcc;
                lkp_UnderCollectionAcc.EditValue = StoreData.RecieveNotesUnderCollectAccId;
                lkp_InventoryAcc.EditValue = StoreData.InventoryAcc;
                lkp_CustodyAcc.EditValue = StoreData.CustodyAcc;

                //Liabilities
                lkp_VendorsAcc.EditValue = StoreData.VendorsAcc;
                lkp_CapitalAcc.EditValue = StoreData.CapitalAcc;
                lkp_NotesPayableAcc.EditValue = StoreData.NotesPayableAcc;
                lkp_DepreciationAcc.EditValue = StoreData.DepreciationAcc;

                //Tax
                lkp_TaxAcc.EditValue = StoreData.TaxAcc;
                lkpPurchaseDeductTaxAccount.EditValue = StoreData.PurchaseDeductTaxAccount;
                lkpSalesDeductTaxAccount.EditValue = StoreData.SalesDeductTaxAccount;
                lkpPurchaseAddTaxAccount.EditValue = StoreData.PurchaseAddTaxAccount;
                lkpCustomTax.EditValue = StoreData.CustomTaxAcc;
                lkpSalesAddTaxAccount.EditValue = StoreData.SalesAddTaxAccount;

                //Merchedaisisng
                lkp_MerchandisingAcc.EditValue = StoreData.MerchandisingAcc;
                lkp_PurchasesAcc.EditValue = StoreData.PurchasesAcc;
                lkp_PurchasesReturnAcc.EditValue = StoreData.PurchasesReturnAcc;
                lkp_SalesAcc.EditValue = StoreData.SalesAcc;
                lkp_SalesAcc2.EditValue = StoreData.SalesAcc;
                lkp_SalesReturnAcc.EditValue = StoreData.SalesReturnAcc;
                lkp_SalesReturnAcc2.EditValue = StoreData.SalesReturnAcc;
                lkp_CostOfSoldGoods.EditValue = StoreData.CostOfSoldGoodsAcc;
                lkp_OpenInventoryAcc.EditValue = StoreData.OpenInventoryAcc;
                lkp_CloseInventoryAcc.EditValue = StoreData.CloseInventoryAcc;
                lkp_PurchaseDiscountAcc.EditValue = StoreData.PurchaseDiscountAcc;
                lkp_ItemPostPurchaseDisc.EditValue = StoreData.PurchaseDiscountAcc;
                lkp_SalesDiscount.EditValue = StoreData.SalesDiscountAcc;
                lkp_ItemPostSalesDisc.EditValue = StoreData.SalesDiscountAcc;
                lkp_CapitalPL.EditValue = StoreData.CapitalProfitLoss;
                lkp_intermediateInventoryAcc.EditValue = StoreData.intermediateInventoryAcc;
                lkp_intermediatePrInventoryAcc.EditValue = StoreData.intermediate_PR_InventoryAcc;
                lkp_PR_ReturnCostACC.EditValue = StoreData.ReturnCostACC;
                //Expenses
                lkp_ManufacturingExpAcc.EditValue = StoreData.ManufacturingExpAcc;

                //HR
                lkp_HrLoanAcc.EditValue = StoreData.HrLoansAccount;
                lkp_HrSalaryAcc.EditValue = StoreData.HrSalaryExpensesAccount;
                lkp_HrAccuredSalaryAccount.EditValue = StoreData.HrAccruedSalaryAccount;
                lkp_InsuranceAcc.EditValue = StoreData.InsuranceAcc;
                lkp_BusinessGainAcc.EditValue = StoreData.BusinessGainAcc;
                lkp_NetTaxAcc.EditValue = StoreData.NetTaxAcc;
                lkp_VacationAcc.EditValue = StoreData.ACC_VacationAcount;
                lkp_PenalityAcc.EditValue = StoreData.ACC_PenaltyAcount;
                lkp_AbsenceAcc.EditValue = StoreData.ACC_AbsenceAcount;
                //Debit & Credit Notes
                lkp_DebitNotesAccount.EditValue = StoreData.DebitNoteAcc;
                lkp_CreditNotesAccount.EditValue = StoreData.CreditNoteAcc;

                lkp_LcAccount.EditValue = StoreData.LetterOfCreditAcc;
                lkp_RealStateSellRvnuAcc.EditValue = StoreData.RealStateSellRvnuAcc;

                //Revenue & Expenses
                lkp_revenueAcc.EditValue = StoreData.RevenueAcc;
                lkp_ExpensesAcc.EditValue = StoreData.ExpensesAcc;

                //rentention and advanced payment
                lkp_AdvancePayment.EditValue = StoreData.AdvancePaymentAcc;
                lkp_Retention.EditValue = StoreData.RetentionAcc;

                //Libra Revenues, Trnsfer and Labor

                lkp_TransferRevenue.EditValue = StoreData.TransferRevenue;
                lkp_LaborRevenue.EditValue = StoreData.LaborRevenue;
                #endregion

                rd_AutoInvSerialForStore.EditValue = StoreData.AutoInvSerialForStore;
                chk_AutoPostSales.Checked = StoreData.AutoPostSales;

                rd_InvoicesCodeRedundancy.EditValue = StoreData.InvoicesCodeRedundancy;
                chk_GenerateNewInvCodeOnSave.Checked = StoreData.GenerateNewInvCodeOnSave;
                rd_InvoicesCodeRedundancy_EditValueChanged(rd_InvoicesCodeRedundancy, EventArgs.Empty);
                chk_EncodeItemsPerCategory.Checked = StoreData.EncodeItemsPerCategory;
                rdo_InvoiceWorkflow.EditValue = StoreData.InvoiceWorkflow;
                rdo_PrInvoiceWorkflow.EditValue = StoreData.PrInvoiceWorkflow;

                if (StoreData.SlInvoice_mustB_Approved.HasValue)
                    chk_SlInvoice_mustB_Approved.Checked = StoreData.SlInvoice_mustB_Approved.Value;

                rd_PrintBarcodePerInventory.EditValue = StoreData.PrintBarcodePerInventory;
                txt_BarcodePrefix.EditValue = StoreData.BarcodePrefix;
                txt_BarcodeItemCodeLength.EditValue = StoreData.BarcodeItemCodeLength;
                txt_BarcodeBatchCodeLength.EditValue = StoreData.BarcodeBatchCodeLength;
                txt_BarcodeQtyLength.EditValue = StoreData.BarcodeQtyLength;
                txtMallTotalSize.EditValue = decimal.ToDouble(StoreData.TotalMallSize.GetValueOrDefault(0));
                chk_UseBarcodeMatchTable.Checked = StoreData.UseBarcodeMatchTable;
                txt_PriorityAr.Text = StoreData.PriorityNameAr;
                txt_PriorityEn.Text = StoreData.PriorityNameEn;
                txt_SalesEmployeeAr.Text = StoreData.SalesEmployeeNameAr;
                txt_SalesEmployeeEn.Text = StoreData.SalesEmployeeNameEn;
                txt_DepartmentEn.Text = StoreData.DepartmentNameEn;
                txt_DepartmentAr.Text = StoreData.DepartmentNameAr;
                txt_DeliveryEmployeeAr.Text = StoreData.DeliveryEmployeeNameAr;
                txt_DeliveryEmployeeEn.Text = StoreData.DeliveryEmployeeNameEn;
                txt_StatusAr.Text = StoreData.StatusNameAr;
                txt_StatusEn.Text = StoreData.StatusNameEn;
                // add matrix setting
                lkp_mtrixModel.EditValue = StoreData.Mtrx_Geir;
                lkp_mtrixColor.EditValue = StoreData.Mtrx_Color;
                lkp_mtrixCC.EditValue = StoreData.Mtrx_Liter;
                // 
                txt_ExpiredateAr.Text = StoreData.ExpireDateNameAr;
                txt_ExpiredateEn.Text = StoreData.ExpireDateNameEn;
                //ch_usecarsystem.Checked =Convert.ToBoolean(StoreData.UseCarSystem);

                //if (StoreData.hasChkSum.HasValue)
                //    chk_ChkSum.Checked = StoreData.hasChkSum.Value;

                grdCrnc.DataSource = DBbind.ST_Currencies;
                //grdCrncChange.DataSource = DBbind.ST_CurrencyChanges;
            }


            #region Libra
            lkp_Libra.Properties.DataSource = lkp_KG_PR.Properties.DataSource = lkp_KG_SL.Properties.DataSource = DB.IC_UOMs;
            lkp_Libra.Properties.DisplayMember = lkp_KG_PR.Properties.DisplayMember = lkp_KG_SL.Properties.DisplayMember = "UOM";
            lkp_Libra.Properties.ValueMember = lkp_KG_PR.Properties.ValueMember = lkp_KG_SL.Properties.ValueMember = "UOMId";
            #endregion


            //matrix
           // var Matrixs = (from m in DB.IC_Matrixes
           //                select new
           //                {
           //                    Id = m.MatrixId,
           //                    Name = m.MatrixName
           //                }).ToList();
           // lkp_mtrixCC.Properties.DataSource = Matrixs;
           // lkp_mtrixModel.Properties.DataSource = Matrixs;
           // lkp_mtrixColor.Properties.DataSource = Matrixs;

           // lkp_mtrixCC.Properties.ValueMember =
           // lkp_mtrixModel.Properties.ValueMember =
           // lkp_mtrixColor.Properties.ValueMember = "Id";
           // lkp_mtrixCC.Properties.DisplayMember =
           //lkp_mtrixModel.Properties.DisplayMember =
           //lkp_mtrixColor.Properties.DisplayMember = "Name";
            //update 19/9/2017
            var CompanyData = (from d in DB.ST_CompanyInfos
                               select d).FirstOrDefault();
            if (CompanyData == null)
                return;
            else
                rd_StockType.EditValue = CompanyData.StockIsPeriodic;


            if (!Shared.MaufacturingAvailable)
            {
                chkBuyAssembly.Checked = true;
                chkSellRawMaterial.Checked = true;
                chkSerialForAssembly.Checked = false;
                chk_manfProductsOnly.Checked = true;
                chkBuyAssembly.Visible = false;
                chkSellRawMaterial.Visible = false;
                chkSerialForAssembly.Visible = false;
                chk_manfProductsOnly.Visible = false;
                tabManufacturing.Visible = false;
                tabManufacturing.PageVisible = false;
            }
            if (!Shared.CurrencyAvailable)
            {
                grpCrnc1.Enabled = grpCrncChange.Enabled = false;
            }


            //cannot change this setting if there any item in stores
            //var itemstores = DB.IC_ItemStores.Count();
            //if (itemstores > 0)
            //{
            //    chkExpire.Enabled = false;
            //    chkMultiplyDimensions.Enabled = false;
            //    chkPiecesCount.Enabled = false;
            //    chk_UseQC.Enabled = false;
            //    chk_Serial.Enabled = false;
            //    rd_PrintBarcodePerInventory.Enabled = false;
            //    grp_BarcodeTemplate.Enabled = false;
            //}

            //reports
            txtReportMyDocPath.Text = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\";
            Shared.ReportsPath = ErpUtils.GetReportsPath(StoreData);
            Shared.AttachmentsPath = ErpUtils.GetAttachmentsPath(StoreData);

            var InvTypes = (from p in DB.LKP_Processes
                            where p.ProcessId == (int)Process.SellInvoice
                 || p.ProcessId == (int)Process.PurchaseInvoice
                 || p.ProcessId == (int)Process.CashIn
                 || p.ProcessId == (int)Process.CashOut
                            select new
                            {
                                p.ProcessId,
                                ProcessName = Shared.IsEnglish ? p.ProcessEnglishName : p.ProcessName
                            }).ToList();

            lkpProcess.DataSource = InvTypes;
            lkpProcess.DisplayMember = "ProcessName";
            lkpProcess.ValueMember = "ProcessId";
        }

        private void frm_ST_Store_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }


        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (lkp_ExpensesAcc.EditValue == null || lkp_revenueAcc.EditValue == null)
            {
                xtraTabControl1.SelectedTabPage = tab_Accounting;
                lkp_revenueAcc.Focus();

                XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResEn.SelectrevExpAcc : ResAr.SelectrevExpAcc
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                return;
            }
            SaveData();

        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }


        private void controls_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        private void SaveData()
        {
            gv_Crnc.UpdateCurrentRow();
            gvDue.UpdateCurrentRow();
            gv_IncomeTaxDisc.UpdateCurrentRow();
            gv_IncomeTaxLevel.UpdateCurrentRow();
            gv_PrintSamples.UpdateCurrentRow();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var StoreDatas = from d in DB.ST_Stores
                             select d;
            foreach (ST_Store StoreData in StoreDatas)
            {
                if (lkp_Libra.EditValue != null)
                    StoreData.Libra_UOM_Id = Convert.ToInt32(lkp_Libra.EditValue);
                if (lkp_KG_PR.EditValue != null)
                    StoreData.KG_PR_UOM_Id = Convert.ToInt32(lkp_KG_PR.EditValue);
                if (lkp_KG_SL.EditValue != null)
                    StoreData.KG_SL_UOM_Id = Convert.ToInt32(lkp_KG_SL.EditValue);
                if (!string.IsNullOrEmpty(txt_KG_PR_Factor.Text))
                    StoreData.KG_PR_Factor = txt_KG_PR_Factor.Text;
                if (!string.IsNullOrEmpty(txt_KG_SL_Factor.Text))
                    StoreData.KG_SL_Factor = txt_KG_SL_Factor.Text;

                StoreData.MainCurrencyName = txtMainCrncName.Text;
                StoreData.CurrencyPound1 = txtPound1.Text;
                StoreData.CurrencyPound2 = txtPound2.Text;
                StoreData.CurrencyPound3 = txtPound3.Text;
                StoreData.CurrencyPiaster1 = txtPiaster1.Text;
                StoreData.CurrencyPiaster2 = txtPiaster2.Text;
                StoreData.CurrencyPiaster3 = txtPiaster3.Text;
                StoreData.CurrencyDigitsCount = Convert.ToInt32(txtCurrencyDigitsCount.EditValue);

                StoreData.MyDocReportPathFolder = txtMyDocReportPathFolder.Text;
                StoreData.SecondReportPath = txtSecondReportPath.Text;
                StoreData.AttachmentPath = txtAttachPath.Text;
                //update forcreating directory for each tax system //commented 
                #region test
                // Directory.CreateDirectory(txtSecondReportPath.Text + "\\"+"Tax");
                //Directory.CreateDirectory(txtSecondReportPath.Text + "\\" + "NoTax");
                //Directory.CreateDirectory(txtSecondReportPath.Text + "\\" + "UnDefined");


                #endregion



                StoreData.BuyAssembly = (bool)chkBuyAssembly.Checked;
                StoreData.SellRawMaterial = (bool)chkSellRawMaterial.Checked;
                StoreData.SerialForAssembly = (bool)chkSerialForAssembly.Checked;
                StoreData.ManufactureProductsOnly = (bool)chk_manfProductsOnly.Checked;

                StoreData.ExpireDate = (bool)chkExpire.Checked;
                if (ExpireDisplay.EditValue != null)
                    StoreData.ExpireDisplay = (bool)ExpireDisplay.EditValue;
                StoreData.PiecesCount = (bool)chkPiecesCount.Checked;
                StoreData.UseQC = (bool)chk_UseQC.Checked;
                StoreData.Serial = (bool)chk_Serial.Checked;
                StoreData.PackCount = chkPackCount.Checked;
                StoreData.UseLastCostPrice = chk_UseLastCostPrice.Checked;


                StoreData.IsStoreOnEachPurchRecord = (bool)chk_StorePurchase.Checked;
                StoreData.IsStoreOnEachSellRecord = (bool)chk_StoreSales.Checked;

                // Max limit for sales order & sales invoices
                StoreData.IsMaxSalesInvoice = (bool)chk_max_SL_Invoice.Checked;
                StoreData.IsMaxSalesOrder = (bool)chk_max_SL_Order.Checked;

                //Mohammad 5-9-2018
                StoreData.MustApproveSalesOrder = chk_ApproveSalesOrder.Checked;
                //Mohammad 24-7-2019
                if (imgCostDistributionMethod.EditValue != null)
                    StoreData.CostDistributionMethod = Convert.ToInt32(imgCostDistributionMethod.EditValue);

                StoreData.BatchNameAr = txt_BatchNameAr.Text;
                StoreData.BatchNameEn = txt_BatchNameEn.Text;
                StoreData.SerialNameAr = txt_SerialNameAr.Text;
                StoreData.SerialNameEn = txt_SerialNameEn.Text;

                // Adel: Update serial 2
                StoreData.Serial2NameAr = txt_SerialNameAr2.Text;
                StoreData.Serial2NameEn = txt_SerialNameEn2.Text;
                //Samar=========//
                StoreData.CompanyAr = CompanyAr.Text;
                StoreData.CompanyEn = CompanyEn.Text;
                StoreData.CategoryAr = CategoryAr.Text;
                StoreData.CategoryEn = CategoryEn.Text;
                // Adel: Update Pieces Count
                StoreData.PiecesCountNameAr = txtPiecesCountAr.Text;
                StoreData.PiecesCountNameEn = txtPiecesCountEn.Text;

                // Adel: Update Barcode Settings
                if (!string.IsNullOrEmpty(txtIdentifier.Text))
                    StoreData.bcIdentifier = Convert.ToInt32(txtIdentifier.Text);
                if (!string.IsNullOrEmpty(txtItemBarcode.Text))
                    StoreData.bcItemCount = Convert.ToInt32(txtItemBarcode.Text);
                if (!string.IsNullOrEmpty(txtScaleBarcode.Text))
                    StoreData.bcScaleCount = Convert.ToInt32(txtScaleBarcode.Text);

                if (rdo_ScaleBarcodeType.EditValue != null)
                    StoreData.isPrice = Convert.ToBoolean(rdo_ScaleBarcodeType.EditValue);
                if (!string.IsNullOrEmpty(txtPrice.Text))
                    StoreData.bcScalePrice = Convert.ToInt32(txtPrice.Text);

                StoreData.Batch = (bool)chkBatch.Checked;
                StoreData.PurchaseAutoSerialBatch = (bool)chk_AutoSerialBatch.Checked;

                StoreData.PriceIncludeSalesTax = (bool)chkPriceIncludeSalesTax.Checked;
                StoreData.PriceIncludePurchaseTax = (bool)chkPriceIncludePurchaseTax.Checked;
                StoreData.CalcPurchaseTaxPerItem = (bool)chkCalcPurchaseTaxPerItem.Checked;

                if (dtCloseDate.EditValue == null)
                    StoreData.ClosePeriodDate = null;
                else
                    StoreData.ClosePeriodDate = dtCloseDate.DateTime.Date;

                if (dtLastEvaluation.EditValue == null)
                    StoreData.LastEvaluationDate = null;
                else
                    StoreData.LastEvaluationDate = dtLastEvaluation.DateTime.Date;

                StoreData.isFutureDueDate = Convert.ToBoolean(rd_IsFutureDueDate.EditValue);
                StoreData.DefaultRoundingPoints = Convert.ToByte(slDefaultDecimalPoint.EditValue);

                //if (chk_ApprovePrOrder.Checked)
                StoreData.MustApprovePurchaseOrder = chk_ApprovePrOrder.Checked;
                StoreData.MustApprovePurchaseQuote = chk_ApprovePrQuote.Checked;
                //else

                #region HR
                StoreData.TotalMonthDays_HR = (int?)txt_TotalMonthDays_HR.Value;
                StoreData.NetSalaryTax = (decimal?)txtNetSalaryTax.Value;
                StoreData.ExemptionRatio = (decimal?)txtExemption.Value;
                StoreData.TotalSalaryType = chkTotalSalaryType.Checked;
                StoreData.EgyptionLaw = chkEgyptianLaw.Checked;
                // Insurance
                StoreData.MinInsureSalary = txtMinSalary.Value;
                StoreData.MaxInsureSalary = txtMaxSalary.Value;
                StoreData.companyShare = txtComShare.Value;
                StoreData.EmpShare = txtEmpShare.Value;
                /////////////////////////////
                ///
                StoreData.AttendanceIs2Shifts = chk_AttendanceIs2Shifts.Checked;
                StoreData.DelayOnAttendTimeOnly = chk_DelayOnAttendTimeOnly.Checked;

                StoreData.GulfHRAvailable = chk_GulfHRAvailable.Checked;
                StoreData.IncomeTaxAvailable = chk_IncomeTaxAvailable.Checked;
                #endregion

                StoreData.UseRegisteredNotes = chk_Registered.Checked;
                StoreData.SalesOrderReserveGood = chk_SalesOrderReserveGood.Checked;
                StoreData.GroupPOSItems = chk_GroupPOSItems.Checked;
                StoreData.SellAsRestaurant = chk_SellAsRestaurant.Checked;
                StoreData.SalesEmpMandatory = chk_SalesEmpMandatory.Checked;
                StoreData.ch_Authorize = ch_Authorize.Checked;
                StoreData.E_AllowMoreThanTax = chk_allowMoreThanTax.Checked;
                
                StoreData.OutstandingRecieveNote = chkOutstanding.Checked;
                StoreData.SalesOrderforClient = chk_SalesOrderforClient.Checked;
                StoreData.chk_CsTypeValidation = chk_CsTypeValidation.Checked;

                StoreData.UseMediumUom = (bool)chkMediumUom.Checked;
                StoreData.UseLargeUom = (bool)chkLargeUom.Checked;

                StoreData.UseLengthDimension = (bool)chkLength.Checked;
                StoreData.UseWidthDimension = (bool)chkWidth.Checked;
                StoreData.UseHeightDimension = (bool)chkHeight.Checked;
                StoreData.MultiplyDimensions = Convert.ToByte(chkMultiplyDimensions.EditValue);

                #region Set Accounts
                //Assets
                if (lkp_FixedAssets.EditValue == null)
                    StoreData.FixedAssets = null;
                else
                    StoreData.FixedAssets = Convert.ToInt32(lkp_FixedAssets.EditValue);

                if (lkp_DrawersAcc.EditValue == null)
                    StoreData.DrawersAcc = null;
                else
                    StoreData.DrawersAcc = Convert.ToInt32(lkp_DrawersAcc.EditValue);

                if (lkp_BanksAcc.EditValue == null)
                    StoreData.BanksAcc = null;
                else
                    StoreData.BanksAcc = Convert.ToInt32(lkp_BanksAcc.EditValue);
                if (lkp_VisaAccs.EditValue == null)
                    StoreData.VisaAccount = null;
                else
                    StoreData.VisaAccount = Convert.ToInt32(lkp_VisaAccs.EditValue);

                if (lkp_CustomersAcc.EditValue == null)
                    StoreData.CustomersAcc = null;
                else
                    StoreData.CustomersAcc = Convert.ToInt32(lkp_CustomersAcc.EditValue);

                if (lkp_NotesReceivableAcc.EditValue == null)
                    StoreData.NotesReceivableAcc = null;
                else
                    StoreData.NotesReceivableAcc = Convert.ToInt32(lkp_NotesReceivableAcc.EditValue);

                if (lkp_UnderCollectionAcc.EditValue == null)
                    StoreData.RecieveNotesUnderCollectAccId = null;
                else
                    StoreData.RecieveNotesUnderCollectAccId = Convert.ToInt32(lkp_UnderCollectionAcc.EditValue);

                if (lkp_InventoryAcc.EditValue == null)
                    StoreData.InventoryAcc = null;
                else
                    StoreData.InventoryAcc = Convert.ToInt32(lkp_InventoryAcc.EditValue);

                if (lkp_CustodyAcc.EditValue == null)
                    StoreData.CustodyAcc = null;
                else
                    StoreData.CustodyAcc = Convert.ToInt32(lkp_CustodyAcc.EditValue);

                //Liabilities
                if (lkp_VendorsAcc.EditValue == null)
                    StoreData.VendorsAcc = null;
                else
                    StoreData.VendorsAcc = Convert.ToInt32(lkp_VendorsAcc.EditValue);

                if (lkp_CapitalAcc.EditValue == null)
                    StoreData.CapitalAcc = null;
                else
                    StoreData.CapitalAcc = Convert.ToInt32(lkp_CapitalAcc.EditValue);

                if (lkp_NotesPayableAcc.EditValue == null)
                    StoreData.NotesPayableAcc = null;
                else
                    StoreData.NotesPayableAcc = Convert.ToInt32(lkp_NotesPayableAcc.EditValue);

                if (lkp_DepreciationAcc.EditValue == null)
                    StoreData.DepreciationAcc = null;
                else
                    StoreData.DepreciationAcc = Convert.ToInt32(lkp_DepreciationAcc.EditValue);

                //Tax
                if (lkp_TaxAcc.EditValue == null)
                    StoreData.TaxAcc = null;
                else
                    StoreData.TaxAcc = Convert.ToInt32(lkp_TaxAcc.EditValue);

                if (lkpPurchaseDeductTaxAccount.EditValue == null)
                    StoreData.PurchaseDeductTaxAccount = null;
                else
                    StoreData.PurchaseDeductTaxAccount = Convert.ToInt32(lkpPurchaseDeductTaxAccount.EditValue);

                if (lkpSalesDeductTaxAccount.EditValue == null)
                    StoreData.SalesDeductTaxAccount = null;
                else
                    StoreData.SalesDeductTaxAccount = Convert.ToInt32(lkpSalesDeductTaxAccount.EditValue);

                if (lkpPurchaseAddTaxAccount.EditValue == null)
                    StoreData.PurchaseAddTaxAccount = null;
                else
                    StoreData.PurchaseAddTaxAccount = Convert.ToInt32(lkpPurchaseAddTaxAccount.EditValue);
                if (lkpCustomTax.EditValue == null)
                    StoreData.CustomTaxAcc = null;
                else
                    StoreData.CustomTaxAcc = Convert.ToInt32(lkpCustomTax.EditValue);

                if (lkpSalesAddTaxAccount.EditValue == null)
                    StoreData.SalesAddTaxAccount = null;
                else
                    StoreData.SalesAddTaxAccount = Convert.ToInt32(lkpSalesAddTaxAccount.EditValue);


                #region Merchedaisisng
                if (Shared.StockIsPeriodic)
                {
                    StoreData.CostOfSoldGoodsAcc = null;

                    if (lkp_MerchandisingAcc.EditValue == null)
                        StoreData.MerchandisingAcc = null;
                    else
                        StoreData.MerchandisingAcc = Convert.ToInt32(lkp_MerchandisingAcc.EditValue);

                    if (lkp_PurchasesAcc.EditValue == null)
                        StoreData.PurchasesAcc = null;
                    else
                        StoreData.PurchasesAcc = Convert.ToInt32(lkp_PurchasesAcc.EditValue);

                    if (lkp_PurchasesReturnAcc.EditValue == null)
                        StoreData.PurchasesReturnAcc = null;
                    else
                        StoreData.PurchasesReturnAcc = Convert.ToInt32(lkp_PurchasesReturnAcc.EditValue);

                    if (lkp_SalesAcc.EditValue == null)
                        StoreData.SalesAcc = null;
                    else
                        StoreData.SalesAcc = Convert.ToInt32(lkp_SalesAcc.EditValue);

                    if (lkp_SalesReturnAcc.EditValue == null)
                        StoreData.SalesReturnAcc = null;
                    else
                        StoreData.SalesReturnAcc = Convert.ToInt32(lkp_SalesReturnAcc.EditValue);

                    if (lkp_OpenInventoryAcc.EditValue == null)
                        StoreData.OpenInventoryAcc = null;
                    else
                        StoreData.OpenInventoryAcc = Convert.ToInt32(lkp_OpenInventoryAcc.EditValue);

                    if (lkp_CloseInventoryAcc.EditValue == null)
                        StoreData.CloseInventoryAcc = null;
                    else
                        StoreData.CloseInventoryAcc = Convert.ToInt32(lkp_CloseInventoryAcc.EditValue);

                    if (lkp_PurchaseDiscountAcc.EditValue == null)
                        StoreData.PurchaseDiscountAcc = null;
                    else
                        StoreData.PurchaseDiscountAcc = Convert.ToInt32(lkp_PurchaseDiscountAcc.EditValue);

                    if (lkp_SalesDiscount.EditValue == null)
                        StoreData.SalesDiscountAcc = null;
                    else
                        StoreData.SalesDiscountAcc = Convert.ToInt32(lkp_SalesDiscount.EditValue);

                    if (lkp_CapitalPL.EditValue == null)
                        StoreData.CapitalProfitLoss = null;
                    else
                        StoreData.CapitalProfitLoss = Convert.ToInt32(lkp_CapitalPL.EditValue);
                }
                else
                {
                    StoreData.MerchandisingAcc =
                        StoreData.PurchasesAcc =
                        StoreData.PurchasesReturnAcc =
                        StoreData.OpenInventoryAcc =
                        StoreData.CloseInventoryAcc =
                        StoreData.SalesDiscountAcc =
                        StoreData.PurchaseDiscountAcc = null;

                    if (lkp_SalesAcc2.EditValue == null)
                        StoreData.SalesAcc = null;
                    else
                        StoreData.SalesAcc = Convert.ToInt32(lkp_SalesAcc2.EditValue);

                    if (lkp_SalesReturnAcc2.EditValue == null)
                        StoreData.SalesReturnAcc = null;
                    else
                        StoreData.SalesReturnAcc = Convert.ToInt32(lkp_SalesReturnAcc2.EditValue);

                    if (lkp_CostOfSoldGoods.EditValue == null)
                        StoreData.CostOfSoldGoodsAcc = null;
                    else
                        StoreData.CostOfSoldGoodsAcc = Convert.ToInt32(lkp_CostOfSoldGoods.EditValue);
                }

                if (Shared.ItemsPostingAvailable)
                {
                    StoreData.MerchandisingAcc =
                        StoreData.PurchasesAcc =
                        StoreData.PurchasesReturnAcc =
                        //StoreData.SalesAcc =
                        //StoreData.SalesReturnAcc = //we need them both for Income Statment //mohammad 17-03-2021
                        StoreData.OpenInventoryAcc =
                        StoreData.CloseInventoryAcc = null;

                    if (lkp_ItemPostPurchaseDisc.EditValue == null)
                        StoreData.PurchaseDiscountAcc = null;
                    else
                        StoreData.PurchaseDiscountAcc = Convert.ToInt32(lkp_ItemPostPurchaseDisc.EditValue);

                    if (lkp_ItemPostSalesDisc.EditValue == null)
                        StoreData.SalesDiscountAcc = null;
                    else
                        StoreData.SalesDiscountAcc = Convert.ToInt32(lkp_ItemPostSalesDisc.EditValue);


                    //////Item Posting - Periodic 18-02-2018 Mohammad
                    //if (Shared.StockIsPeriodic)
                    //{
                    //    StoreData.PeriodicCloseInventory = Convert.ToInt32(lkp_CloseInventory_ItmPst);
                    //    StoreData.PeriodicOpenInventory = Convert.ToInt32(lkp_OpenInventory_ItmPst);
                    //}
                    ////////////////////////////////////
                }


                #endregion

                //Expenses
                if (lkp_ManufacturingExpAcc.EditValue == null)
                    StoreData.ManufacturingExpAcc = null;
                else
                    StoreData.ManufacturingExpAcc = Convert.ToInt32(lkp_ManufacturingExpAcc.EditValue);

                //HR
                if (lkp_HrLoanAcc.EditValue == null)
                    StoreData.HrLoansAccount = null;
                else
                    StoreData.HrLoansAccount = Convert.ToInt32(lkp_HrLoanAcc.EditValue);

                if (lkp_HrSalaryAcc.EditValue == null)
                    StoreData.HrSalaryExpensesAccount = null;
                else
                    StoreData.HrSalaryExpensesAccount = Convert.ToInt32(lkp_HrSalaryAcc.EditValue);

                if (lkp_HrAccuredSalaryAccount.EditValue == null)
                    StoreData.HrAccruedSalaryAccount = null;
                else
                    StoreData.HrAccruedSalaryAccount = Convert.ToInt32(lkp_HrAccuredSalaryAccount.EditValue);

                if (lkp_InsuranceAcc.EditValue == null)
                    StoreData.InsuranceAcc = null;
                else
                    StoreData.InsuranceAcc = Convert.ToInt32(lkp_InsuranceAcc.EditValue);

                if (lkp_BusinessGainAcc.EditValue == null)
                    StoreData.BusinessGainAcc = null;
                else
                    StoreData.BusinessGainAcc = Convert.ToInt32(lkp_BusinessGainAcc.EditValue);

                if (lkp_NetTaxAcc.EditValue == null)
                    StoreData.NetTaxAcc = null;
                else
                    StoreData.NetTaxAcc = Convert.ToInt32(lkp_NetTaxAcc.EditValue);

                if (lkp_PenalityAcc.EditValue == null)
                    StoreData.ACC_PenaltyAcount = null;
                else
                    StoreData.ACC_PenaltyAcount = Convert.ToInt32(lkp_PenalityAcc.EditValue);
                
                if (lkp_AbsenceAcc.EditValue == null)
                    StoreData.ACC_AbsenceAcount = null;
                else
                    StoreData.ACC_AbsenceAcount = Convert.ToInt32(lkp_AbsenceAcc.EditValue);
                
                if (lkp_VacationAcc.EditValue == null)
                    StoreData.ACC_VacationAcount = null;
                else
                    StoreData.ACC_VacationAcount = Convert.ToInt32(lkp_VacationAcc.EditValue);

                //Debit & Credit Notes
                if (lkp_DebitNotesAccount.EditValue == null)
                    StoreData.DebitNoteAcc = null;
                else
                    StoreData.DebitNoteAcc = Convert.ToInt32(lkp_DebitNotesAccount.EditValue);

                if (lkp_CreditNotesAccount.EditValue == null)
                    StoreData.CreditNoteAcc = null;
                else
                    StoreData.CreditNoteAcc = Convert.ToInt32(lkp_CreditNotesAccount.EditValue);

                if (lkp_CreditNotesAccount.EditValue == null)
                    StoreData.CreditNoteAcc = null;
                else
                    StoreData.CreditNoteAcc = Convert.ToInt32(lkp_CreditNotesAccount.EditValue);

                if (lkp_LcAccount.EditValue == null)
                    StoreData.LetterOfCreditAcc = null;
                else
                    StoreData.LetterOfCreditAcc = Convert.ToInt32(lkp_LcAccount.EditValue);

                if (lkp_RealStateSellRvnuAcc.EditValue == null)
                    StoreData.RealStateSellRvnuAcc = null;
                else
                    StoreData.RealStateSellRvnuAcc = Convert.ToInt32(lkp_RealStateSellRvnuAcc.EditValue);

                //Revenue & Expenses
                if (lkp_revenueAcc.EditValue == null)
                    StoreData.RevenueAcc = null;
                else
                    StoreData.RevenueAcc = Convert.ToInt32(lkp_revenueAcc.EditValue);

                if (lkp_ExpensesAcc.EditValue == null)
                    StoreData.ExpensesAcc = null;
                else
                    StoreData.ExpensesAcc = Convert.ToInt32(lkp_ExpensesAcc.EditValue);

                //retention and advance payment
                if (lkp_AdvancePayment.EditValue == null)
                    StoreData.AdvancePaymentAcc = null;
                else
                    StoreData.AdvancePaymentAcc = Convert.ToInt32(lkp_AdvancePayment.EditValue);
                if (lkp_Retention.EditValue == null)
                    StoreData.RetentionAcc = null;
                else
                    StoreData.RetentionAcc = Convert.ToInt32(lkp_Retention.EditValue);

                if (lkp_intermediateInventoryAcc.EditValue == null)
                    StoreData.intermediateInventoryAcc = null;
                else
                    StoreData.intermediateInventoryAcc = Convert.ToInt32(lkp_intermediateInventoryAcc.EditValue);

                if (lkp_intermediatePrInventoryAcc.EditValue == null)
                    StoreData.intermediate_PR_InventoryAcc = null;
                else
                    StoreData.intermediate_PR_InventoryAcc = Convert.ToInt32(lkp_intermediatePrInventoryAcc.EditValue);

                if (lkp_PR_ReturnCostACC.EditValue == null)
                    StoreData.ReturnCostACC = null;
                else
                    StoreData.ReturnCostACC = Convert.ToInt32(lkp_PR_ReturnCostACC.EditValue);
                #endregion

                StoreData.AutoInvSerialForStore = (bool?)rd_AutoInvSerialForStore.EditValue;
                StoreData.EncodePrInvPerVendor = chk_EncodePrInvPerVendor.Checked;

                StoreData.AutoPostSales = chk_AutoPostSales.Checked;

                StoreData.InvoicesCodeRedundancy = (bool?)rd_InvoicesCodeRedundancy.EditValue;
                StoreData.GenerateNewInvCodeOnSave = chk_GenerateNewInvCodeOnSave.Checked;
                StoreData.EncodeItemsPerCategory = chk_EncodeItemsPerCategory.Checked;
                StoreData.InvoiceWorkflow = Convert.ToInt32(rdo_InvoiceWorkflow.EditValue);
                StoreData.PrInvoiceWorkflow = Convert.ToInt32(rdo_PrInvoiceWorkflow.EditValue);

                StoreData.SlInvoice_mustB_Approved = chk_SlInvoice_mustB_Approved.Checked;

                StoreData.PrintBarcodePerInventory = Convert.ToBoolean(rd_PrintBarcodePerInventory.EditValue);
                StoreData.BarcodePrefix = txt_BarcodePrefix.Text.Trim();
                StoreData.BarcodeItemCodeLength = Convert.ToInt32(txt_BarcodeItemCodeLength.EditValue);
                StoreData.BarcodeBatchCodeLength = Convert.ToInt32(txt_BarcodeBatchCodeLength.EditValue);
                StoreData.BarcodeQtyLength = Convert.ToInt32(txt_BarcodeQtyLength.EditValue);
                StoreData.TotalMallSize = Convert.ToDecimal(txtMallTotalSize.EditValue);
                StoreData.UseBarcodeMatchTable = chk_UseBarcodeMatchTable.Checked;


                StoreData.PriorityNameAr = txt_PriorityAr.Text;
                StoreData.PriorityNameEn = txt_PriorityEn.Text;
                StoreData.SalesEmployeeNameAr = txt_SalesEmployeeAr.Text;
                StoreData.SalesEmployeeNameEn = txt_SalesEmployeeEn.Text;
                StoreData.DepartmentNameEn = txt_DepartmentEn.Text;
                StoreData.DepartmentNameAr = txt_DepartmentAr.Text;
                StoreData.DeliveryEmployeeNameAr = txt_DeliveryEmployeeAr.Text;
                StoreData.DeliveryEmployeeNameEn = txt_DeliveryEmployeeEn.Text;
                StoreData.StatusNameAr = txt_StatusAr.Text;
                StoreData.StatusNameEn = txt_StatusEn.Text;

                // matrix settings
                StoreData.Mtrx_Color = Convert.ToInt32(lkp_mtrixColor.EditValue);
                StoreData.Mtrx_Liter = Convert.ToInt32(lkp_mtrixCC.EditValue);
                StoreData.Mtrx_Geir = Convert.ToInt32(lkp_mtrixModel.EditValue);
                //
                StoreData.ExpireDateNameAr = txt_ExpiredateAr.Text;
                StoreData.ExpireDateNameEn = txt_ExpiredateEn.Text;
                //StoreData.UseCarSystem = ch_usecarsystem.Checked;
                //update 19/9/2017
                var CompanyData = (from d in DB.ST_CompanyInfos
                                   select d).FirstOrDefault();
                CompanyData.StockIsPeriodic = Convert.ToBoolean(rd_StockType.EditValue);

                //StoreData.hasChkSum = chk_ChkSum.Checked;


                Shared.ReportsPath = ErpUtils.GetReportsPath(StoreData);
                Shared.AttachmentsPath = ErpUtils.GetAttachmentsPath(StoreData);

                if (lkp_LaborRevenue.EditValue != null)
                    StoreData.LaborRevenue = Convert.ToInt32(lkp_LaborRevenue.EditValue);
                else StoreData.LaborRevenue = null;
                if (lkp_TransferRevenue.EditValue != null)
                    StoreData.TransferRevenue = Convert.ToInt32(lkp_TransferRevenue.EditValue);
                else StoreData.TransferRevenue = null;
            }

            MyHelper.UpdateST_UserLog(DB, "", "",
(int)FormAction.Edit, (int)FormsNames.ST_Store);

            DB.SubmitChanges();

            //commented by mohammad 02112020
            //if (chkExpire.Checked)
            //{
            //    var stores = DB.IC_Stores.Select(s => s);
            //    foreach (var store in stores)
            //    {
            //        store.CostMethod = (byte)CostMethod.FIFO;
            //    }
            //    DB.SubmitChanges();
            //}

            DBbind.SubmitChanges();

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResAccEn.MsgSave : ResAccAr.MsgSave//"تم الحفظ بنجاح"
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            DataModified = false;
        }

        DialogResult ChangesMade()
        {

            if (DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgDataModified : ResAccAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "                                        
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void DoValidate()
        {
            chkBuyAssembly.DoValidate();
            chkSellRawMaterial.DoValidate();
            chkSerialForAssembly.DoValidate();
            chk_manfProductsOnly.DoValidate();

            chk_AutoSerialBatch.DoValidate();

            chkExpire.DoValidate();
            chkPiecesCount.DoValidate();
            chkBatch.DoValidate();
            chkLength.DoValidate();
            chkWidth.DoValidate();
            chkHeight.DoValidate();
            chkMultiplyDimensions.DoValidate();

            chkPriceIncludeSalesTax.DoValidate();
            chkPriceIncludePurchaseTax.DoValidate();
            chkCalcPurchaseTaxPerItem.DoValidate();

            chkMediumUom.DoValidate();
            chkLargeUom.DoValidate();

            dtCloseDate.DoValidate();
            dtLastEvaluation.DoValidate();
            rd_IsFutureDueDate.DoValidate();
            chk_AttendanceIs2Shifts.DoValidate();
            chk_Registered.DoValidate();
            rd_StockType.DoValidate();

            //Assets
            lkp_FixedAssets.DoValidate();
            lkp_DrawersAcc.DoValidate();
            lkp_BanksAcc.DoValidate();
            lkp_VisaAccs.DoValidate();
            lkp_CustomersAcc.DoValidate();
            lkp_NotesReceivableAcc.DoValidate();
            lkp_UnderCollectionAcc.DoValidate();
            lkp_InventoryAcc.DoValidate();
            lkp_CustodyAcc.DoValidate();
            //Liabilities
            lkp_VendorsAcc.DoValidate();
            lkp_CapitalAcc.DoValidate();
            lkp_NotesPayableAcc.DoValidate();
            //tax
            lkp_TaxAcc.DoValidate();
            lkpPurchaseDeductTaxAccount.DoValidate();
            lkpSalesDeductTaxAccount.DoValidate();
            lkpPurchaseAddTaxAccount.DoValidate();
            lkpCustomTax.DoValidate();
            lkpSalesAddTaxAccount.DoValidate();
            //Expenses
            lkp_ManufacturingExpAcc.DoValidate();
            //Merchedaising
            lkp_MerchandisingAcc.DoValidate();
            lkp_PurchasesAcc.DoValidate();
            lkp_PurchasesReturnAcc.DoValidate();
            lkp_SalesAcc.DoValidate();
            lkp_SalesAcc2.DoValidate();
            lkp_SalesReturnAcc.DoValidate();
            lkp_SalesReturnAcc2.DoValidate();
            lkp_CostOfSoldGoods.DoValidate();
            lkp_OpenInventoryAcc.DoValidate();
            lkp_CloseInventoryAcc.DoValidate();
            lkp_PurchaseDiscountAcc.DoValidate();
            lkp_SalesDiscount.DoValidate();
            lkp_CapitalPL.DoValidate();
            //HR
            lkp_HrLoanAcc.DoValidate();
            lkp_HrSalaryAcc.DoValidate();
            lkp_HrAccuredSalaryAccount.DoValidate();
            lkp_InsuranceAcc.DoValidate();
            lkp_BusinessGainAcc.DoValidate();
            lkp_NetTaxAcc.DoValidate();
            lkp_PenalityAcc.DoValidate();
            lkp_AbsenceAcc.DoValidate();
            lkp_VacationAcc.DoValidate();
            //Debit Credit Notes
            lkp_DebitNotesAccount.DoValidate();
            lkp_CreditNotesAccount.DoValidate();
            // retention and advance payment
            lkp_AdvancePayment.DoValidate();
            lkp_Retention.DoValidate();


            lkp_LcAccount.DoValidate();
            lkp_RealStateSellRvnuAcc.DoValidate();

            chk_AutoPostSales.DoValidate();

            txtPound1.DoValidate();
            txtPound2.DoValidate();
            txtPound3.DoValidate();
            txtPiaster1.DoValidate();
            txtPiaster2.DoValidate();
            txtPiaster3.DoValidate();
            txtCurrencyDigitsCount.DoValidate();

            txt_BarcodePrefix.DoValidate();
            txt_BarcodeItemCodeLength.DoValidate();
            txt_BarcodeBatchCodeLength.DoValidate();
            txt_BarcodeQtyLength.DoValidate();

            txtMallTotalSize.DoValidate();

            txtReportMyDocPath.DoValidate();
            txtSecondReportPath.DoValidate();
            txtAttachPath.DoValidate();
        }


        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "إعدادات");
        }

        private void btnRedesignReport_Click(object sender, EventArgs e)
        {
            if (cmbReport.EditValue == null)
                return;


            if (string.IsNullOrEmpty(Shared.ReportsPath))
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.path : ResRptAr.path, "",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (!System.IO.Directory.Exists(@Shared.ReportsPath))
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.path : ResRptAr.path,
                    "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.path2 + " \n\r" + @Shared.ReportsPath :
                ResRptAr.path2 + " \n\r" + @Shared.ReportsPath,
                "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            string reportName = string.Empty;
            if (cmbReport.EditValue.ToString() == "1")
            {
                Reports.rpt_SL_Invoice r = new Reports.rpt_SL_Invoice();
                if (Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_SL_Invoice.en-US.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_SL_Invoice.en-US.repx");
                else if (!Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_SL_Invoice.ar-EG.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_SL_Invoice.ar-EG.repx");
                else if (Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_SL_Invoice.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_SL_Invoice.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "2")
            {
                Reports.rpt_PR_Invoice r = new Reports.rpt_PR_Invoice();
                if (Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_PR_Invoice.en-US.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PR_Invoice.en-US.repx");
                else if (System.IO.File.Exists(Shared.ReportsPath + "rpt_PR_Invoice.ar-EG.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PR_Invoice.ar-EG.repx");
                else if (System.IO.File.Exists(Shared.ReportsPath + "rpt_PR_Invoice.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PR_Invoice.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "3")
            {
                Reports.rpt_SL_ReturnInvoice r = new Reports.rpt_SL_ReturnInvoice();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_SL_ReturnInvoice.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_SL_ReturnInvoice.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "4")
            {
                Reports.rpt_PR_ReturnInvoice r = new Reports.rpt_PR_ReturnInvoice();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_PR_ReturnInvoice.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PR_ReturnInvoice.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "5")
            {
                Reports.rpt_PayNotes r = new Reports.rpt_PayNotes();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_PayNotes.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PayNotes.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "6")
            {
                Reports.rpt_RecieveNotes r = new Reports.rpt_RecieveNotes();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_RecieveNotes.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_RecieveNotes.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "7")
            {
                Reports.rpt_Printed_Receipt r = new Reports.rpt_Printed_Receipt();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "8")
            {
                Reports.rpt_CashNote r = new Reports.rpt_CashNote();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_CashNote.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_CashNote.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "9")
            {
                Reports.rpt_manufacture r = new Reports.rpt_manufacture();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_manufacture.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_manufacture.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "10")
            {
                Reports.rpt_RevExp r = new Reports.rpt_RevExp();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_RevExp.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_RevExp.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "11")
            {
                Reports.rpt_IC_StoreMove r = new Reports.rpt_IC_StoreMove();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_IC_StoreMove.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_IC_StoreMove.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "12")
            {
                Reports.rpt_SL_Quote r = new Reports.rpt_SL_Quote();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_SL_Quote.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_SL_Quote.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "13")
            {
                Reports.rpt_ACC_Journal r = new Reports.rpt_ACC_Journal();
                if (Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_ACC_Journal.en-US.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_ACC_Journal.en-US.repx");
                else
                if (!Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_ACC_Journal.ar-EG.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_ACC_Journal.ar-EG.repx");
                else
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_ACC_Journal.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_ACC_Journal.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "14")
            {
                Reports.rpt_ACC_Statment r = new Reports.rpt_ACC_Statment();
                if (Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_ACC_Statment.en-US.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_ACC_Statment.en-US.repx");
                else if (!Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_ACC_Statment.ar-EG.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_ACC_Statment.ar-EG.repx");
                else if (System.IO.File.Exists(Shared.ReportsPath + "rpt_ACC_Statment.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_ACC_Statment.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "15")
            {
                Reports.rpt_PriceLevel r = new Reports.rpt_PriceLevel();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_PriceLevel.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PriceLevel.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "16")
            {
                Reports.rpt_PR_PurchaseOrder r = new Reports.rpt_PR_PurchaseOrder();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_PR_PurchaseOrder.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PR_PurchaseOrder.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "17")
            {
                Reports.rpt_Template r = new Reports.rpt_Template();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Template.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_Template.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "18")
            {
                Reports.rpt_PR_PriceLevel r = new Reports.rpt_PR_PriceLevel();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_PR_PriceLevel.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PR_PriceLevel.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "19")
            {
                //Reports.rpt_HR_SalesEmpTargetCommission r = new Reports.rpt_HR_SalesEmpTargetCommission();
                //if (System.IO.File.Exists(Shared.ReportsPath + "rpt_HR_SalesEmpTargetCommission.repx"))
                //    r.LoadLayout(Shared.ReportsPath + "rpt_HR_SalesEmpTargetCommission.repx");
                //r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "20")
            {
                Reports.rpt_SL_SalesOrder r = new Reports.rpt_SL_SalesOrder();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_SL_SalesOrder.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_SL_SalesOrder.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "21")
            {
                Reports.rpt_IC_Damaged r = new Reports.rpt_IC_Damaged();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_IC_Damaged.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_IC_Damaged.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "22")
            {
                Reports.rpt_IC_OutTrns r = new Reports.rpt_IC_OutTrns();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_IC_OutTrns.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_IC_OutTrns.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "23")
            {
                Reports.rpt_IC_InTrns r = new Reports.rpt_IC_InTrns();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_IC_InTrns.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_IC_InTrns.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "24")
            {
                //Reports.rpt_Acc_BalanceT r = new Reports.rpt_Acc_BalanceT();
                //if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Acc_BalanceT.repx"))
                //    r.LoadLayout(Shared.ReportsPath + "rpt_Acc_BalanceT.repx");
                //r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "25")
            {
                Reports.rpt_Template r = new Reports.rpt_Template();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Acc_IncomeT.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_Acc_IncomeT.repx");
                r.ShowDesigner();
            }

            //if (cmbReport.EditValue.ToString() == "26")
            //{
            //    Reports.rpt_Acc_Expenses_RevenuesT r = new Reports.rpt_Acc_Expenses_RevenuesT();
            //    if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Acc_Expenses_RevenuesT.repx"))
            //        r.LoadLayout(Shared.ReportsPath + "rpt_Acc_Expenses_RevenuesT.repx");
            //    r.ShowDesigner();
            //}

            if (cmbReport.EditValue.ToString() == "27")
            {
                Reports.rpt_JO_JobOrder r = new Reports.rpt_JO_JobOrder();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_JO_JobOrder.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_JO_JobOrder.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "28")
            {
                Reports.rpt_JO_JobOrderListCustomer r = new Reports.rpt_JO_JobOrderListCustomer();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_JO_JobOrderListCustomer.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_JO_JobOrderListCustomer.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "29")
            {
                Reports.rpt_JO_JobOrderListDept r = new Reports.rpt_JO_JobOrderListDept();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_JO_JobOrderListDept.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_JO_JobOrderListDept.repx");
                r.ShowDesigner();
            }

            //this report replaced by special reports "Mahmoud Ismail 16-10-2014"

            //if (cmbReport.EditValue.ToString() == "30")
            //{
            //    Reports.rpt_Acc_AccountDetails r = new Reports.rpt_Acc_AccountDetails();
            //    if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Acc_AccountDetails.repx"))
            //        r.LoadLayout(Shared.ReportsPath + "rpt_Acc_AccountDetails.repx");
            //    r.ShowDesigner();
            //}
            if (cmbReport.EditValue.ToString() == "31")
            {
                Reports.rpt_Manf_QC r = new Reports.rpt_Manf_QC();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Manf_QC.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_Manf_QC.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "32")
            {
                Reports.rpt_PR_Quote r = new Reports.rpt_PR_Quote();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_PR_Quote.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PR_Quote.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "33")
            {
                Reports.rpt_PR_Request r = new Reports.rpt_PR_Request();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_PR_Request.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_PR_Request.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "34")
            {
                Reports.rpt_Weight r = new Reports.rpt_Weight();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Weight.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_Weight.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "35")
            {
                Reports.rpt_IC_BOM r = new Reports.rpt_IC_BOM();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_IC_BOM.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_IC_BOM.repx");
                r.ShowDesigner();
            }

            if (cmbReport.EditValue.ToString() == "36")
            {
                Reports.rpt_Template r = new Reports.rpt_Template(true);
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_TemplateLandScape.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_TemplateLandScape.repx");
                r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "37")
            {
                //Reports.rpt_HR_Pay r = new rpt_HR_Pay();
                //if (System.IO.File.Exists(Shared.ReportsPath + "rpt_HR_Pay.repx"))
                //    r.LoadLayout(Shared.ReportsPath + "rpt_HR_Pay.repx");
                //r.ShowDesigner();
            }
            if (cmbReport.EditValue.ToString() == "38")
            {
                Reports.rpt_SL_Add r = new rpt_SL_Add();
                if (System.IO.File.Exists(Shared.ReportsPath + "rpt_SL_Add.repx"))
                    r.LoadLayout(Shared.ReportsPath + "rpt_SL_Add.repx");
                r.ShowDesigner();
            }

        }

        private void btnReportPath_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog OFD = new FolderBrowserDialog();

            if (OFD.ShowDialog() == DialogResult.OK && OFD.SelectedPath != string.Empty)
            {
                txtSecondReportPath.Text = OFD.SelectedPath;
            }
        }

        private void chk_AutoSerialBatch_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_AutoSerialBatch.Checked)
            {
                chkBatch.Enabled = false;
                chkBatch.Checked = true;

                chkSerialForAssembly.Checked = false;
                chkSerialForAssembly.Enabled = false;
            }
            else
            {
                chkBatch.Enabled = true;
                chkSerialForAssembly.Enabled = true;
            }
        }

        private void btnInvBooks_Click(object sender, EventArgs e)
        {
            new frm_ST_InvBooksList().ShowDialog();
        }

        #region Due Grid
        private void gvDue_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {

            if (e.ListSourceRowIndex >= 0)
                e.Value = e.RowHandle() + 1;
        }

        private void gvDue_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            DataModified = true;
        }

        private void grdDue_ProcessGridKey(object sender, KeyEventArgs e)
        {
            int index = gvDue.FocusedColumn.VisibleIndex;

            if (e.KeyCode == Keys.Enter || (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift))
            {
                if (gvDue.FocusedColumn.VisibleIndex == 0)
                    gvDue.FocusedColumn = gvDue.VisibleColumns[gvDue.VisibleColumns.Count - 1];
                else
                    gvDue.FocusedColumn = gvDue.VisibleColumns[gvDue.FocusedColumn.VisibleIndex - 1];

                if (index != 0)
                {
                    e.Handled = true;
                    return;
                }
                else
                {
                    if (gvDue.FocusedRowHandle < 0)
                    {
                        gvDue.AddNewRow();
                        gvDue.FocusedColumn = gvDue.VisibleColumns[gvDue.VisibleColumns.Count - 1];
                    }
                    else
                    {
                        gvDue.FocusedRowHandle += 1;
                        gvDue.FocusedColumn = gvDue.VisibleColumns[gvDue.VisibleColumns.Count - 1];
                    }
                    e.Handled = true;
                    return;
                }
            }
            if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
            {
                if (gvDue.FocusedColumn.VisibleIndex == gvDue.VisibleColumns.Count)
                    gvDue.FocusedColumn = gvDue.VisibleColumns[0];
                else
                    gvDue.FocusedColumn = gvDue.VisibleColumns[gvDue.FocusedColumn.VisibleIndex + 1];

                e.Handled = true;
                return;
            }

        }
        #endregion

        #region Currency
        private void gv_Crnc_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            if (gv_Crnc.GetRowCellValue(e.FocusedRowHandle, colCrncId) == null ||
                Convert.ToInt32(gv_Crnc.GetRowCellValue(e.FocusedRowHandle, colCrncId)) == 0)
            {
                grpCrncChange.Enabled = false;
                gv_CrncChange.Columns["CrncId"].FilterMode = DevExpress.XtraGrid.ColumnFilterMode.Value;
                gv_CrncChange.ActiveFilterString = "[CrncId] = -1";
            }
            else
            {
                grpCrncChange.Enabled = true;
                gv_CrncChange.Columns["CrncId"].FilterMode = DevExpress.XtraGrid.ColumnFilterMode.Value;
                gv_CrncChange.ActiveFilterString = "[CrncId] = " + gv_Crnc.GetRowCellValue(e.FocusedRowHandle, colCrncId).ToString();
                crncId = Convert.ToInt32(gv_Crnc.GetRowCellValue(e.FocusedRowHandle, colCrncId));
            }
        }

        private void gv_CrncChange_InitNewRow(object sender, DevExpress.XtraGrid.Views.Grid.InitNewRowEventArgs e)
        {
            gv_CrncChange.SetFocusedRowCellValue(colCrncId2, crncId);
        }

        private void gv_Crnc_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, //"حذف صف ؟"
                    Shared.IsEnglish == true ? ResICEn.MsgTQues : ResICAr.MsgTQues,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;


                if (DBbind.ACC_JournalDetails.Where(d => d.CrncId > 0 && d.CrncId == Convert.ToInt32(gv_Crnc.GetFocusedRowCellValue(colCrncId)))
                    .Count() > 0 ||
                    DBbind.ST_CurrencyChanges.Where(d => d.CrncId > 0 && d.CrncId == Convert.ToInt32(gv_Crnc.GetFocusedRowCellValue(colCrncId)))
                    .Count() > 0)
                {
                    XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResEn.DelEntryDenied : ResAr.DelEntryDenied
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    return;
                }

                gv_Crnc.DeleteRow(gv_Crnc.FocusedRowHandle);
            }

        }

        private void gv_Crnc_ProcessGridKey(object sender, KeyEventArgs e)
        {
            DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
            var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);

            var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
            int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

            int index = gv_Crnc.FocusedColumn.VisibleIndex;

            if (e.KeyCode == Keys.Enter)
            {
                if (view.FocusedColumn.VisibleIndex - 1 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1].OptionsColumn.AllowFocus)
                {
                    view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    return;
                }
                else if (view.FocusedColumn.VisibleIndex - 2 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2].OptionsColumn.AllowFocus)
                {
                    view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2];
                    return;
                }
                else
                    gv_Crnc_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));

                if (view.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                {
                    view.AddNewRow();
                    view.FocusedColumn = colcrncName;
                }
                else
                {
                    view.FocusedRowHandle = focused_row_handle + 1;
                    view.FocusedColumn = colcrncName;
                }

                e.Handled = true;
                return;
            }
            if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
            {
                if (view.FocusedColumn.VisibleIndex == 0)
                    view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                else
                    view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                e.Handled = true;
                return;
            }
            if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
            {
                if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                    view.FocusedColumn = view.VisibleColumns[0];
                else
                    view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                e.Handled = true;
                return;
            }
            if ((view.IsNewItemRow(focused_row_handle) && (view.GetFocusedRowCellValue("crncName") == null || view.GetFocusedRowCellValue("crncName").ToString() == string.Empty)))
            {
                if (e.KeyCode == Keys.Up)
                    view.DeleteRow(view.FocusedRowHandle);
            }

        }
        #endregion

        private void btn_ReevaluateOutTrns_Click(object sender, EventArgs e)
        {
            try
            {
                frm_ReEvaluate f = new frm_ReEvaluate();
                f.BringToFront();
                f.ShowDialog();
            }
            catch (Exception x) { MessageBox.Show(x.Message + " Inner: " + x?.InnerException?.Message); };
        }

        private void gv_Crnc_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            if ((e.Row as ST_Currency).crncName == null)
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore;
            else
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gv_Crnc_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            if (gv_Crnc.GetRowCellValue(e.RowHandle, colcrncName) == null || gv_Crnc.GetRowCellValue(e.RowHandle, colcrncName).ToString() == string.Empty)
            {
                e.Valid = false;
                gv_Crnc.SetColumnError(colcrncName, Shared.IsEnglish ? ResEn.MsgIncorrectData : ResEn.MsgIncorrectData);
            }
            if (gv_Crnc.GetRowCellValue(e.RowHandle, colCurrencyDigitsCount) == null || gv_Crnc.GetRowCellValue(e.RowHandle, colCurrencyDigitsCount).ToString() == string.Empty
                || Convert.ToInt32(gv_Crnc.GetRowCellValue(e.RowHandle, colCurrencyDigitsCount)) <= 0)
            {
                e.Valid = false;
                gv_Crnc.SetColumnError(colCurrencyDigitsCount, Shared.IsEnglish ? ResEn.MsgIncorrectData : ResEn.MsgIncorrectData);
            }
            if (gv_Crnc.GetRowCellValue(e.RowHandle, colLastRate) == null || gv_Crnc.GetRowCellValue(e.RowHandle, colLastRate).ToString() == string.Empty)
            {
                e.Valid = false;
                gv_Crnc.SetColumnError(colLastRate, Shared.IsEnglish ? ResEn.MsgIncorrectData : ResEn.MsgIncorrectData);
            }
        }

        private void rd_InvoicesCodeRedundancy_EditValueChanged(object sender, EventArgs e)
        {
            if (rd_InvoicesCodeRedundancy.EditValue == null || Convert.ToBoolean(rd_InvoicesCodeRedundancy.EditValue) == true)
            {
                chk_GenerateNewInvCodeOnSave.Checked = false;
                chk_GenerateNewInvCodeOnSave.Enabled = false;
            }
            else
            {
                chk_GenerateNewInvCodeOnSave.Enabled = true;
            }
        }



        private void chk_IncomeTaxAvailable_CheckedChanged(object sender, EventArgs e)
        {
            grd_IncomeTaxDisc.Enabled = grd_IncomeTaxLevel.Enabled = chk_IncomeTaxAvailable.Checked;
        }

        private void gv_IncomeTaxDisc_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            if (gv_IncomeTaxDisc.GetRowCellValue(e.RowHandle, col_TaxDisName) == null ||
                gv_IncomeTaxDisc.GetRowCellValue(e.RowHandle, col_TaxDisName).ToString() == string.Empty)
            {
                e.Valid = false;
                gv_IncomeTaxDisc.SetColumnError(col_TaxDisName, "*");
            }
            if (gv_IncomeTaxDisc.GetRowCellValue(e.RowHandle, col_TaxDisAnnualValue) == null ||
                gv_IncomeTaxDisc.GetRowCellValue(e.RowHandle, col_TaxDisAnnualValue).ToString() == string.Empty ||
                Convert.ToDecimal(gv_IncomeTaxDisc.GetRowCellValue(e.RowHandle, col_TaxDisAnnualValue)) <= 0)
            {
                e.Valid = false;
                gv_IncomeTaxDisc.SetColumnError(col_TaxDisAnnualValue, "*");
            }
        }

        private void gv_IncomeTaxLevel_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            if (gv_IncomeTaxLevel.GetRowCellValue(e.RowHandle, col_From) == null ||
                gv_IncomeTaxLevel.GetRowCellValue(e.RowHandle, col_From).ToString() == string.Empty ||
                Convert.ToDecimal(gv_IncomeTaxLevel.GetRowCellValue(e.RowHandle, col_From)) < 0)
            {
                e.Valid = false;
                gv_IncomeTaxLevel.SetColumnError(col_From, "*");
            }
            if (gv_IncomeTaxLevel.GetRowCellValue(e.RowHandle, col_To) == null ||
                gv_IncomeTaxLevel.GetRowCellValue(e.RowHandle, col_To).ToString() == string.Empty ||
                Convert.ToDecimal(gv_IncomeTaxLevel.GetRowCellValue(e.RowHandle, col_To)) <= 0)
            {
                e.Valid = false;
                gv_IncomeTaxLevel.SetColumnError(col_To, "*");
            }
            if (gv_IncomeTaxLevel.GetRowCellValue(e.RowHandle, col_Ratio) == null ||
                gv_IncomeTaxLevel.GetRowCellValue(e.RowHandle, col_Ratio).ToString() == string.Empty ||
                Convert.ToDecimal(gv_IncomeTaxLevel.GetRowCellValue(e.RowHandle, col_Ratio)) <= 0)
            {
                e.Valid = false;
                gv_IncomeTaxLevel.SetColumnError(col_Ratio, "*");
            }
        }

        private void gv_IncomeTaxDisc_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex >= 0)
            {
                if (e.Column == col_TaxDiscMonthValue)
                {
                    if (gv_IncomeTaxDisc.GetRowCellValue(e.ListSourceRowIndex, "TaxDisAnnualValue") == null ||
                     gv_IncomeTaxDisc.GetRowCellValue(e.ListSourceRowIndex, "TaxDisAnnualValue").ToString() == string.Empty)
                        return;

                    e.Value = Convert.ToDecimal(gv_IncomeTaxDisc.GetRowCellValue(e.ListSourceRowIndex, "TaxDisAnnualValue")) / 12;
                }
            }
        }

        private void gv_IncomeTaxDisc_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gv_IncomeTaxLevel_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gv_IncomeTaxDiscount_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                gv_IncomeTaxDisc.DeleteRow(gv_IncomeTaxDisc.FocusedRowHandle);
            }
        }
        private void gv_IncomeTaxLevel_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                gv_IncomeTaxLevel.DeleteRow(gv_IncomeTaxLevel.FocusedRowHandle);
            }
        }

        private void btn_ImportItems_Click(object sender, EventArgs e)
        {
            new frm_IC_ImportItems().ShowDialog();
        }

        private void rd_PrintBarcodePerInventory_EditValueChanged(object sender, EventArgs e)
        {
            if (Convert.ToBoolean(rd_PrintBarcodePerInventory.EditValue) == true)
                grp_BarcodeTemplate.Enabled = false;
            else
                grp_BarcodeTemplate.Enabled = true;
        }

        private void btn_BarcodeMatchTable_Click(object sender, EventArgs e)
        {
            new frm_ST_BarcodeMatchTable().ShowDialog();
        }

        private void tab_Inv_Paint(object sender, PaintEventArgs e)
        {

        }

        private void checkEdit2_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void btnAttachPath_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog OFD = new FolderBrowserDialog();

            if (OFD.ShowDialog() == DialogResult.OK && OFD.SelectedPath != string.Empty)
            {
                txtAttachPath.Text = OFD.SelectedPath;
            }
        }

        private void rdo_ScaleBarcodeType_EditValueChanged(object sender, EventArgs e)
        {
            txtPrice.Enabled = Convert.ToBoolean(rdo_ScaleBarcodeType.EditValue);
        }

        private void gv_PrintSamples_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                gv_PrintSamples.DeleteRow(gv_PrintSamples.FocusedRowHandle);
            }
        }

        private void gv_PrintSamples_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            if (gv_PrintSamples.GetRowCellValue(e.RowHandle, colProcessId) == null ||
                gv_PrintSamples.GetRowCellValue(e.RowHandle, colPrintFileName) == null ||
                gv_PrintSamples.GetRowCellValue(e.RowHandle, colSampleName) == null ||
                gv_PrintSamples.GetRowCellValue(e.RowHandle, colPrintFileName).ToString() == string.Empty ||
                gv_PrintSamples.GetRowCellValue(e.RowHandle, colSampleName).ToString() == string.Empty)
            {
                e.Valid = false;
            }
        }

        private void btn_Transport_Vehicles_Click(object sender, EventArgs e)
        {
            new frm_ST_Cars().ShowDialog();
        }

        private void txt_TotalMonthDays_HR_Validating(object sender, CancelEventArgs e)
        {
            if (txt_TotalMonthDays_HR.Value < 0)
            {
                e.Cancel = false;
            }
        }

        private void groupBox11_Enter(object sender, EventArgs e)
        {

        }

        private void lkp_TransferRevenue_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void chkSellRawMaterial_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void slDefaultDecimalPoint_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
           var val = Convert.ToDecimal(e.NewValue);
            e.Cancel = val > slDefaultDecimalPoint.Properties.MaxValue ||
                       val < slDefaultDecimalPoint.Properties.MinValue;
        }
    }
}