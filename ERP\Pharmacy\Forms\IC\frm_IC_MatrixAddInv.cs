﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using System.Data.Linq;
using System.Linq;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraPivotGrid;
using System.Collections;

namespace Pharmacy.Forms
{
    public partial class frm_IC_MatrixAddInv : DevExpress.XtraEditors.XtraForm
    {
        IC_Item item;
        List<MtrxItem> lstItems = new List<MtrxItem>();
        ERPDataContext DB = new ERPDataContext();

        public static List<MtrxItem> lst_InvMatrixItems = new List<MtrxItem>();

        public frm_IC_MatrixAddInv()
        {
            RTL.EnCulture(Shared.IsEnglish);

            InitializeComponent();            
        }

        private void frm_IC_MatrixAddInv_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            //lkpItems.Properties.ValueMember = "ItemId";
            //lkpItems.Properties.DisplayMember = "ItemNameAr";

            lkpItems.Properties.DataSource =
               (from i in DB.IC_Items
                where i.ItemType == (int)ItemType.MatrixParent
                select new
                {
                    i.ItemId,
                    i.ItemCode1,
                    i.ItemCode2,
                    i.ItemNameAr,
                    i.ItemNameEn
                }).ToList();

            lst_InvMatrixItems = new List<MtrxItem>();
        }

        private void lkpItems_EditValueChanged(object sender, EventArgs e)
        {
            item = DB.IC_Items.Where(i => i.ItemId == Convert.ToInt32(lkpItems.EditValue)).First();
            LoadMatrixGrid();
        }

        private void btAdd_Click(object sender, EventArgs e)
        {
            lst_InvMatrixItems = lstItems.Where(i => i.Qty > 0).ToList();
            this.Close();
        }

        private void pivotGridControl1_EditValueChanged(object sender, DevExpress.XtraPivotGrid.EditValueChangedEventArgs e)
        {
            IList list = e.CreateDrillDownDataSource();
            PivotGridControl grid = sender as PivotGridControl;

            List<MtrxItem> oldData = ((List<MtrxItem>)(grid.DataSource));
            for (int i = 0; i < list.Count; i++)
            {
                PivotDrillDownDataRow row = list[i] as PivotDrillDownDataRow;
                int myint = row.ListSourceRowIndex;

                lstItems[row.ListSourceRowIndex].Qty = Convert.ToDecimal(e.Editor.EditValue);
            }
            pivotGridControl1.RefreshData();
        }        

        private void LoadMatrixGrid()
        {
            ERPDataContext DB = new ERPDataContext();

            var metrices = DB.IC_Matrixes.ToList();
            var attributes = (from m in DB.IC_Matrixes.AsEnumerable()
                              join d in DB.IC_MatrixDetails.AsEnumerable()
                              on m.MatrixId equals d.MatrixId
                              select new 
                              {
                                  m.MatrixId,
                                  m.MatrixCode,
                                  m.MatrixName,
                                  d.MatrixDetailId,
                                  d.MDCode,
                                  d.MDName
                              }).ToList();

            if (item.mtrxId1 != null) //attributes
            {
                col_MtrxAttribute1.Caption = attributes.Where(i => i.MatrixId == item.mtrxId1).Select(i => i.MatrixName).First();
                col_MtrxAttribute1.Visible = true;
                col_MtrxAttribute1.Area = DevExpress.XtraPivotGrid.PivotArea.RowArea;
            }
            else
                col_MtrxAttribute1.Visible = false;
            if (item.mtrxId2 != null)
            {
                col_MtrxAttribute2.Caption = attributes.Where(i => i.MatrixId == item.mtrxId2).Select(i => i.MatrixName).First();
                col_MtrxAttribute2.Visible = true;
                col_MtrxAttribute2.Area = DevExpress.XtraPivotGrid.PivotArea.RowArea;
            }
            else
                col_MtrxAttribute2.Visible = false;

            if (item.mtrxId3 != null)
            {
                col_MtrxAttribute3.Caption = attributes.Where(i => i.MatrixId == item.mtrxId3).Select(i => i.MatrixName).First();
                col_MtrxAttribute3.Visible = true;
                col_MtrxAttribute3.Area = DevExpress.XtraPivotGrid.PivotArea.ColumnArea;
            }
            else
                col_MtrxAttribute3.Visible = false;

            lstItems = (from i in DB.IC_Items.Where(i=> i.mtrxParentItem == item.ItemId).AsEnumerable()                        
                        orderby i.mtrxAttribute1, i.mtrxAttribute2, i.mtrxAttribute3
                        select new MtrxItem()
                        {
                            ItemId = i.ItemId,
                            MtrxAttribute1_Id = i.mtrxAttribute1,
                            MtrxAttribute2_Id = i.mtrxAttribute2,
                            MtrxAttribute3_Id = i.mtrxAttribute3,

                            MtrxAttribute1 = i.mtrxAttribute1.HasValue? 
                            attributes.Where(x => x.MatrixDetailId == i.mtrxAttribute1).Select(x => x.MDName).FirstOrDefault() 
                            : string.Empty,
                            MtrxAttribute2 = i.mtrxAttribute2.HasValue ?
                            attributes.Where(x => x.MatrixDetailId == i.mtrxAttribute2).Select(x => x.MDName).FirstOrDefault()
                            : string.Empty,
                            MtrxAttribute3 = i.mtrxAttribute3.HasValue?
                            attributes.Where(x => x.MatrixDetailId == i.mtrxAttribute3).Select(x => x.MDName).FirstOrDefault()
                            : string.Empty,

                            Qty = 0
                        }).ToList();

            pivotGridControl1.DataSource = lstItems;
            pivotGridControl1.RefreshData();
            


        }

        private void repositoryItemSpinEdit1_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            //dont use spin
            e.Handled = true;
        }

        
    }

    public class MtrxItem
    {
        int itemId;
        int? mtrxAttribute1_Id;
        int? mtrxAttribute2_Id;
        int? mtrxAttribute3_Id;
        string mtrxAttribute1;
        string mtrxAttribute2;
        string mtrxAttribute3;
        decimal qty;

        public int? MtrxAttribute1_Id
        {
            get { return mtrxAttribute1_Id; }
            set { mtrxAttribute1_Id = value; }
        }

        public int? MtrxAttribute2_Id
        {
            get { return mtrxAttribute2_Id; }
            set { mtrxAttribute2_Id = value; }
        }

        public int? MtrxAttribute3_Id
        {
            get { return mtrxAttribute3_Id; }
            set { mtrxAttribute3_Id = value; }
        }

        public string MtrxAttribute1
        {
            get { return mtrxAttribute1; }
            set { mtrxAttribute1 = value; }
        }

        public string MtrxAttribute2
        {
            get { return mtrxAttribute2; }
            set { mtrxAttribute2 = value; }
        }

        public string MtrxAttribute3
        {
            get { return mtrxAttribute3; }
            set { mtrxAttribute3 = value; }
        }

        public int ItemId
        {
            get { return itemId; }
            set { itemId = value; }
        }

        public decimal Qty
        {
            get { return qty; }
            set { qty = value; }
        }
    }     
}