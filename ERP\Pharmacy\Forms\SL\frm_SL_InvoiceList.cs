﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using System.Data.SqlClient;
using ExcelDataReader;
using System.IO;

namespace Pharmacy.Forms
{
    public partial class frm_SL_InvoiceList : DevExpress.XtraEditors.XtraForm
    {
        int customerId;
        DateTime dateFrom, dateTo;

        bool Is_OpenForSelect = false;
        public static int SelectedInvId = 0;
        public static string SelectedInvCode;
        bool cars = false;
  
        public frm_SL_InvoiceList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }
        public frm_SL_InvoiceList(bool _cars, bool IsCars)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
            cars = _cars;

        }

        public frm_SL_InvoiceList(bool _is_OpenForSelect)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            Is_OpenForSelect = _is_OpenForSelect;
        }
        public frm_SL_InvoiceList(bool _is_OpenForSelect, int customerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            Is_OpenForSelect = _is_OpenForSelect;
            this.customerId = customerId;
        }

        public frm_SL_InvoiceList(int customerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.customerId = customerId;
        }
      

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            #region Get Date Range
            if (Shared.SL_I_FromDate != null)
                dt1.DateTime = dateFrom = Shared.SL_I_FromDate.Value;
            else
            {
                dateFrom = Shared.minDate;
                dt1.EditValue = null;
            }

            if (Shared.SL_I_ToDate != null)
                dt2.DateTime = dateTo = Shared.SL_I_ToDate.Value;
            else
            {
                dateTo = Shared.maxDate;
                dt2.EditValue = null;
            }
            #endregion

            
            #region CustomersGroups
            ERPDataContext DB = new ERPDataContext();
            rep_CategoryId.DataSource = DB.SL_CustomerGroups.Select(x => new { x.CustomerGroupId, x.CGNameAr }).ToList();
            rep_CategoryId.DisplayMember = "CGNameAr";
            rep_CategoryId.ValueMember = "CustomerGroupId";
            #endregion


            GetInvoices();

            //#region SalesEmp
            //DataTable dt_SalesEmps = new DataTable();
            //MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            //rep_salesEmp.DataSource = dt_SalesEmps;
            //rep_salesEmp.DisplayMember = "EmpName";
            //rep_salesEmp.ValueMember = "EmpId";
            //#endregion

            

            #region invoice bbok            
            //rep_InvoiceBook.DataSource = DB.ST_InvoiceBooks.Where(x => x.ProcessId == (int)Process.SellInvoice)
            //    .Select(x => new { x.InvoiceBookId, x.InvoiceBookName }).ToList();
            //rep_InvoiceBook.DisplayMember = "InvoiceBookName";
            //rep_InvoiceBook.ValueMember = "InvoiceBookId";
            #endregion

            #region Currencies
            repCrncy.DataSource = Shared.lstCurrency;
            repCrncy.ValueMember = "CrncId";
            repCrncy.DisplayMember = "crncName";
            #endregion

            rep_User.DataSource = DB.HR_Users;
            rep_Accounts.DataSource = DB.ACC_Accounts;
            rep_Accounts.ValueMember = "AccountId";
            rep_Accounts.DisplayMember = Shared.IsEnglish ? "AcNameEn" : "AcNameAr";

            if (Shared.InvoicePostToStore)
                col_Is_OutTrans.Visible = false;

            ErpUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grdCategory);

            if (Shared.user.HidePurchasePrice)
            {
                col_Profit.Visible = col_Profit.OptionsColumn.ShowInCustomizationForm =
                    col_TotalCostPrice.Visible = col_TotalCostPrice.OptionsColumn.ShowInCustomizationForm =
                    col_ProfitRatio.Visible = col_ProfitRatio.OptionsColumn.ShowInCustomizationForm = false;
            }

            ErpUtils.ColumnChooser(grdCategory);
            LoadPrivilege();
            btnCashPayment.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
        }

        private void frm_SL_InvoiceList_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                dt1.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                grdCategory.Focus();
            }
        }

        private void frm_SL_InvoiceList_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (dt1.EditValue != null)
                Shared.SL_I_FromDate = dateFrom;
            else
                Shared.SL_I_FromDate = null;

            if (dt2.EditValue != null)
                Shared.SL_I_ToDate = dateTo;
            else
                Shared.SL_I_ToDate = null;

            ErpUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }

        private void dt1_EditValueChanged(object sender, EventArgs e)
        {
            if (dateFrom != DateTime.MinValue && dateTo != DateTime.MinValue)
            {
                if (dt1.DateTime != DateTime.MinValue)
                    dateFrom = dt1.DateTime;
                else
                    dateFrom = Shared.minDate;

                if (dt2.DateTime != DateTime.MinValue)
                    dateTo = dt2.DateTime;
                else
                {
                    dateTo = Shared.maxDate;
                }
            }
        }

        private void btnClearSearch_Click(object sender, EventArgs e)
        {
            dateFrom = Shared.minDate;
            dateTo = Shared.maxDate;
            dt1.EditValue = null;
            dt2.EditValue = null;
            barBtnRefresh.PerformClick();
        }
        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Shared.CarsAvailable == false)
            {
                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].Close();

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].BringToFront();
                else
                    new frm_SL_Invoice().Show();
            }
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GetInvoices();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_Invoice();
        }


        private void grdCategory_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_Invoice();
        }

        private void NBI_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            if (((NavBarItem)sender).Name == "NBI_Customers")
            {
                frmMain.OpenSL_Customer();
            }

            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int strId = Convert.ToInt32(view.GetFocusedRowCellValue(colStore));
            string strName = view.GetFocusedRowCellDisplayText(col_StoreId).ToString();
            string strFltr = (Shared.IsEnglish == true ? ResSLEn.txtStore : ResSLAr.txtStore)//"المخزن: " 
                + strName;

            
        }

        private void Open_Selected_Invoice()
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_SL_InvoiceId));

            if (Is_OpenForSelect == true)
            {
                SelectedInvId = inv_id;
                SelectedInvCode = view.GetRowCellValue(focused_row_index, col_InvoiceCode).ToString();

                this.Close();
                return;
            }
            if (cars == false)
            {
                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].Close();

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].BringToFront();                  
                else
                    new frm_SL_Invoice(inv_id).Show();
            }
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    NBI_Customers.Enabled = false;
                    mi_OpenDealer.Enabled = false;
                }

                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barMnu_Print.Enabled = barBtn_Print1.Enabled = barBtn_PrintData.Enabled = false;
            }
        }

        private void GetInvoices()
        {
            int focusedIndex = (grdCategory.FocusedView as GridView).FocusedRowHandle;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            try
            {
                if (Shared.E_invoiceAvailable == true)
                {
                    var invoices = (from c in DB.SL_Invoices
                                    join v in DB.SL_Customers on c.CustomerId equals v.CustomerId
                                    where customerId == 0 ? true : c.CustomerId == customerId
                                    join b in DB.ST_InvoiceBooks
                                    on c.InvoiceBookId equals b.InvoiceBookId
                                    where b.IsTaxable == true && b.ProcessId == (int)Process.SellInvoice
                                    where c.InvoiceDate.Date >= dateFrom && c.InvoiceDate.Date <= dateTo
                                    where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                         x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(c.StoreId)// c.StoreId == Shared.user.DefaultStore
                                    where Shared.user.AccessOtherUserTrns ? true : c.UserId == Shared.UserId

                                    from lkp in DB.LKP_Processes.Where(lkp => lkp.ProcessId == c.ProcessId).DefaultIfEmpty()
                                    orderby c.InvoiceDate
                                    let invoiceDetailIds = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).Select(a => a.SL_InvoiceDetailId).ToList()
                                    let Total = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).ToList()
                                    let TaxIdDiscount = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault()

                                    let TotalTaxesAddedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                                               join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                               where invoiceDetailIds.Contains(r.InvoiceDetailId)
                                                               where rd.ParentTaxId != TaxIdDiscount.E_TaxableTypeId
                                                               select r).ToList()
                                    let TotalTaxesRemovedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                                                 join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                                 where invoiceDetailIds.Contains(r.InvoiceDetailId)
                                                                 where rd.ParentTaxId == TaxIdDiscount.E_TaxableTypeId
                                                                 select r).ToList()
                                    let TotaltaxesAddValue = TotalTaxesAddedList.Count != 0 ? Convert.ToDouble(TotalTaxesAddedList.Sum(z => z.value)) : 0
                                    let TotaltaxesRemovedValue = TotalTaxesRemovedList.Count != 0 ? Convert.ToDouble(TotalTaxesRemovedList.Sum(z => z.value)) : 0

                                    join st in DB.IC_Stores on c.StoreId equals st.StoreId
                                    select new
                                    {
                                        Process = lkp == null ? null : (Shared.IsEnglish ? lkp.ProcessEnglishName : lkp.ProcessName),
                                        DiscountRatio = c.DiscountRatio,
                                        DiscountValue = c.DiscountValue,
                                        Expenses = c.Expenses,
                                        Net = c.Net,
                                        Paid = c.Paid,
                                        Remains = c.Remains,
                                        Total = Total.Count != 0 ? Convert.ToDouble(Total.Sum(z => z.TotalSellPrice)) : 0,
                                        c.InvoiceCode,
                                        c.InvoiceDate,
                                        c.JornalId,
                                        c.Notes,
                                        c.PayMethod,
                                        c.SL_InvoiceId,
                                        StoreId = st.StoreNameAr,
                                        store = c.StoreId,
                                        c.UserId,
                                        CustomerId = v.CusNameAr,
                                        CustId = v.CustomerId,
                                        c.SalesEmpId,
                                        TotalCostPrice = c.TotalCostPrice,
                                        Profit = c.Net - c.TotalCostPrice,
                                        GroupId = v.CategoryId,
                                        c.Is_OutTrans,
                                        c.InvoiceBookId,
                                        c.AddTaxValue,
                                        c.DeductTaxValue,
                                        c.TaxValue,
                                        c.DueDate,
                                        c.CrncId,
                                        c.CrncRate,
                                        c.DriverName,
                                        c.VehicleNumber,
                                        c.Destination,
                                        c.IsOffer,
                                        profitRatio = c.TotalCostPrice == 0 ? 1 : (c.Net > 0 ? (c.Net - c.TotalCostPrice) / c.Net : 0),
                                        ProfitCostRatio = c.TotalCostPrice == 0 ? 1 : (c.TotalCostPrice > 0 ? (c.Net - c.TotalCostPrice) / c.TotalCostPrice : 0),
                                        c.DrawerAccountId,
                                        //mohammad 10/11/2019
                                        CategoryId = v.CategoryId,// custGroup.Where(x => x.GroupId == v.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault()
                                        v.City,
                                        Region,
                                        c.uuid,
                                        c.Estatus,
                                        c.EstatusCode,
                                        c.issuerId,
                                        c.syncDate,
                                        c.lastSyncDate,
                                        Totaltaxes = TotaltaxesAddValue - TotaltaxesRemovedValue

                                    }).Distinct().ToList();

                    grdCategory.DataSource = invoices;

                    (grdCategory.FocusedView as GridView).FocusedRowHandle = focusedIndex;
                }
                else
                {
                    var invoices = (from c in DB.SL_Invoices
                                    join v in DB.SL_Customers on c.CustomerId equals v.CustomerId
                                    where customerId == 0 ? true : c.CustomerId == customerId
                                    where c.InvoiceDate.Date >= dateFrom && c.InvoiceDate.Date <= dateTo
                                    where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                         x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(c.StoreId)// c.StoreId == Shared.user.DefaultStore
                                    where Shared.user.AccessOtherUserTrns ? true : c.UserId == Shared.UserId

                                    from lkp in DB.LKP_Processes.Where(lkp => lkp.ProcessId == c.ProcessId).DefaultIfEmpty()
                                    orderby c.InvoiceDate
                                    let invoiceDetailIds = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).Select(a => a.SL_InvoiceDetailId).ToList()
                                    let Total = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).ToList()
                                    let TaxIdDiscount = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault()

                                    let TotalTaxesAddedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                                               join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                               where invoiceDetailIds.Contains(r.InvoiceDetailId)
                                                               where rd.ParentTaxId != TaxIdDiscount.E_TaxableTypeId
                                                               select r).ToList()
                                    let TotalTaxesRemovedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                                                 join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                                 where invoiceDetailIds.Contains(r.InvoiceDetailId)
                                                                 where rd.ParentTaxId == TaxIdDiscount.E_TaxableTypeId
                                                                 select r).ToList()
                                    let TotaltaxesAddValue = TotalTaxesAddedList.Count != 0 ? Convert.ToDouble(TotalTaxesAddedList.Sum(z => z.value)) : 0
                                    let TotaltaxesRemovedValue = TotalTaxesRemovedList.Count != 0 ? Convert.ToDouble(TotalTaxesRemovedList.Sum(z => z.value)) : 0
                                    join st in DB.IC_Stores on c.StoreId equals st.StoreId
                                    select new
                                    {
                                        Process = lkp == null ? null : (Shared.IsEnglish ? lkp.ProcessEnglishName : lkp.ProcessName),
                                        DiscountRatio = c.DiscountRatio,
                                        DiscountValue = c.DiscountValue,
                                        Expenses = c.Expenses,
                                        Net = c.Net,
                                        Paid = c.Paid,
                                        Remains = c.Remains,
                                        Total = Total.Count != 0 ? Convert.ToDouble(Total.Sum(z => z.TotalSellPrice)) : 0,
                                        c.InvoiceCode,
                                        c.InvoiceDate,
                                        c.JornalId,
                                        c.Notes,
                                        c.PayMethod,
                                        c.SL_InvoiceId,
                                        StoreId = st.StoreNameAr,
                                        store = c.StoreId,
                                        c.UserId,
                                        CustomerId = v.CusNameAr,
                                        CustId = v.CustomerId,
                                        c.SalesEmpId,
                                        TotalCostPrice = c.TotalCostPrice,
                                        Profit = c.Net - c.TotalCostPrice,
                                        GroupId = v.CategoryId,
                                        c.Is_OutTrans,
                                        c.InvoiceBookId,
                                        c.AddTaxValue,
                                        c.DeductTaxValue,
                                        c.TaxValue,
                                        c.DueDate,
                                        c.CrncId,
                                        c.CrncRate,
                                        c.DriverName,
                                        c.VehicleNumber,
                                        c.Destination,
                                        c.IsOffer,
                                        profitRatio = c.TotalCostPrice == 0 ? 1 : (c.Net > 0 ? (c.Net - c.TotalCostPrice) / c.Net : 0),
                                        ProfitCostRatio = c.TotalCostPrice == 0 ? 1 : (c.TotalCostPrice > 0 ? (c.Net - c.TotalCostPrice) / c.TotalCostPrice : 0),
                                        c.DrawerAccountId,
                                        //mohammad 10/11/2019
                                        CategoryId = v.CategoryId,// custGroup.Where(x => x.GroupId == v.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault()
                                        v.City,
                                        Region,
                                        c.uuid,
                                        c.Estatus,
                                        c.EstatusCode,
                                        c.issuerId,
                                        c.syncDate,
                                        c.lastSyncDate,
                                        Totaltaxes = TotaltaxesAddValue - TotaltaxesRemovedValue
                                    }).Distinct().ToList();

                    grdCategory.DataSource = invoices;

                    (grdCategory.FocusedView as GridView).FocusedRowHandle = focusedIndex;
                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مبيعات جديدة");
        }

        private void mi_OpenDealer_Click(object sender, EventArgs e)
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int DealerId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_CustId));

            if (ErpUtils.IsFormOpen(typeof(frm_SL_Customer)))
                Application.OpenForms["frm_SL_Customer"].Close();

            new frm_SL_Customer(DealerId).Show();
        }

        private void barBtn_Print1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void btn_Import_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // create a function to import from excel sheet directly to sql server, like the one in sql server management studio
            ERPDataContext db = new ERPDataContext();
            SqlBulkCopy bulkCopy = new SqlBulkCopy(db.Connection.ConnectionString);
            bulkCopy.DestinationTableName = "[Sheet1$]";
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    ExcelDataSetConfiguration conf = new ExcelDataSetConfiguration();
                    conf.UseColumnDataType = true;
                    conf.ConfigureDataTable = (tableReader) => new ExcelDataTableConfiguration()
                    {
                        UseHeaderRow = true
                    };
                    DataSet result =  excelReader.AsDataSet();
                    #region
                    //result.Tables.Add("Sheet1");

                    //// create a table in sql server with the same columns as the excel sheet
                    //// then import the data from the excel sheet to the sql server table
                    //result.Tables[0].Columns.Add("رقم الفاتورة", typeof(string));
                    //result.Tables[0].Columns.Add("اسم العميل", typeof(string));
                    //result.Tables[0].Columns.Add("التاريخ", typeof(string));
                    //result.Tables[0].Columns.Add("الفرع", typeof(string));
                    //result.Tables[0].Columns.Add("مركز التكلفة", typeof(string));
                    //result.Tables[0].Columns.Add("العملة", typeof(string));
                    //result.Tables[0].Columns.Add("معامل التحويل", typeof(decimal));
                    //result.Tables[0].Columns.Add("المدفوع", typeof(decimal));
                    //result.Tables[0].Columns.Add("الخصم المسموح به", typeof(decimal));
                    //result.Tables[0].Columns.Add("الإجمالي", typeof(decimal));
                    //result.Tables[0].Columns.Add("اسم الصنف", typeof(string));
                    //result.Tables[0].Columns.Add("وحدة القياس", typeof(string));
                    //result.Tables[0].Columns.Add("الكمية", typeof(decimal));
                    //result.Tables[0].Columns.Add("الطول", typeof(decimal));
                    //result.Tables[0].Columns.Add("العرض", typeof(decimal));
                    //result.Tables[0].Columns.Add("الارتفاع", typeof(decimal));
                    //result.Tables[0].Columns.Add("سعر البيع", typeof(decimal));
                    //result.Tables[0].Columns.Add("نسبة خصم", typeof(decimal));
                    //result.Tables[0].Columns.Add("قيمة الخصم", typeof(decimal));
                    //result.Tables[0].Columns.Add("خصم بعد الضريبة (للبونص)", typeof(decimal));
                    //result.Tables[0].Columns.Add("نوع الضريبة الفرعية", typeof(decimal));
                    //result.Tables[0].Columns.Add("نسبة الضريبة", typeof(decimal));
                    //result.Tables[0].Columns.Add("قيمة الضريبة", typeof(decimal));


                    //result = excelReader.AsDataSet();
                    #endregion

                    result.Tables[0].Rows.RemoveAt(0);

                    bulkCopy.WriteToServer(result.Tables[0]);
                    excelReader.Close();
                    stream.Close();

                    //exceute StoreProcedure to insert data from the table to the main table

                    using (var conn = new SqlConnection(db.Connection.ConnectionString))
                    using (var command = new SqlCommand("importInvoices", conn)
                    {
                        CommandType = CommandType.StoredProcedure
                    })
                    {
                        conn.Open();
                        int rows = command.ExecuteNonQuery();
                        if (rows% result.Tables[0].Rows.Count==0)
                            MessageBox.Show("تم سحب البيانات بنجاح");
                        else
                        {
                            string query = "delete from [Sheet1$]";
                            db.ExecuteCommand(query);
                            MessageBox.Show("حدث خطأ أثناء سحب البيانات، يرجى المحاولة مرة أخرى");
                        }
                        conn.Close();
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }

        }

        private void barBtn_PrintData_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory, false, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }
    }
}