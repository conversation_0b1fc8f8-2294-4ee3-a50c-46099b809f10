namespace Reports
{
    partial class rpt_EmployeeDetails
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(rpt_EmployeeDetails));
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.lbl_Mobile1 = new DevExpress.XtraReports.UI.XRLabel();
            this.txtMobile = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_contract = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_contractEndDdate = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel18 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_MilitryStatus = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Address = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel25 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_gender = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_nationality = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel27 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_birthDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_birthPlace = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_nationalEndDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_nationalId = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel17 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Religion = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Marital = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel21 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_InsturanceDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Insturance = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel24 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_dep = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_group = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Qualification = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_job = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_empName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_empNameF = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_empStatus = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_empCode = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrLabel22 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_Mobile1,
            this.txtMobile,
            this.xrLabel10,
            this.lbl_contract,
            this.lbl_contractEndDdate,
            this.xrLabel18,
            this.xrLabel19,
            this.lbl_MilitryStatus,
            this.lbl_Address,
            this.xrLabel25,
            this.xrLabel7,
            this.lbl_gender,
            this.lbl_nationality,
            this.xrLabel27,
            this.xrLabel5,
            this.lbl_birthDate,
            this.lbl_birthPlace,
            this.xrLabel11,
            this.xrLabel13,
            this.lbl_nationalEndDate,
            this.lbl_nationalId,
            this.xrLabel16,
            this.xrLabel17,
            this.lbl_Religion,
            this.lbl_Marital,
            this.xrLabel20,
            this.xrLabel21,
            this.lbl_InsturanceDate,
            this.lbl_Insturance,
            this.xrLabel24,
            this.xrLabel4,
            this.lbl_dep,
            this.lbl_group,
            this.xrLabel8,
            this.xrLabel9,
            this.lbl_Qualification,
            this.lbl_job,
            this.xrLabel12,
            this.xrLabel3,
            this.lbl_empName,
            this.lbl_empNameF,
            this.xrLabel6,
            this.xrLabel2,
            this.lbl_empStatus,
            this.lbl_empCode,
            this.xrLabel1});
            resources.ApplyResources(this.Detail, "Detail");
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // lbl_Mobile1
            // 
            resources.ApplyResources(this.lbl_Mobile1, "lbl_Mobile1");
            this.lbl_Mobile1.Name = "lbl_Mobile1";
            this.lbl_Mobile1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Mobile1.StylePriority.UseFont = false;
            this.lbl_Mobile1.StylePriority.UseTextAlignment = false;
            // 
            // txtMobile
            // 
            resources.ApplyResources(this.txtMobile, "txtMobile");
            this.txtMobile.Name = "txtMobile";
            this.txtMobile.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.txtMobile.StylePriority.UseFont = false;
            this.txtMobile.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel10
            // 
            resources.ApplyResources(this.xrLabel10, "xrLabel10");
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            // 
            // lbl_contract
            // 
            resources.ApplyResources(this.lbl_contract, "lbl_contract");
            this.lbl_contract.Name = "lbl_contract";
            this.lbl_contract.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_contract.StylePriority.UseFont = false;
            this.lbl_contract.StylePriority.UseTextAlignment = false;
            // 
            // lbl_contractEndDdate
            // 
            resources.ApplyResources(this.lbl_contractEndDdate, "lbl_contractEndDdate");
            this.lbl_contractEndDdate.Name = "lbl_contractEndDdate";
            this.lbl_contractEndDdate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_contractEndDdate.StylePriority.UseFont = false;
            this.lbl_contractEndDdate.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel18
            // 
            resources.ApplyResources(this.xrLabel18, "xrLabel18");
            this.xrLabel18.Name = "xrLabel18";
            this.xrLabel18.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel18.StylePriority.UseFont = false;
            this.xrLabel18.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel19
            // 
            resources.ApplyResources(this.xrLabel19, "xrLabel19");
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            // 
            // lbl_MilitryStatus
            // 
            resources.ApplyResources(this.lbl_MilitryStatus, "lbl_MilitryStatus");
            this.lbl_MilitryStatus.Name = "lbl_MilitryStatus";
            this.lbl_MilitryStatus.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_MilitryStatus.StylePriority.UseFont = false;
            this.lbl_MilitryStatus.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Address
            // 
            resources.ApplyResources(this.lbl_Address, "lbl_Address");
            this.lbl_Address.Multiline = true;
            this.lbl_Address.Name = "lbl_Address";
            this.lbl_Address.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Address.StylePriority.UseFont = false;
            this.lbl_Address.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel25
            // 
            resources.ApplyResources(this.xrLabel25, "xrLabel25");
            this.xrLabel25.Name = "xrLabel25";
            this.xrLabel25.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel25.StylePriority.UseFont = false;
            this.xrLabel25.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel7
            // 
            resources.ApplyResources(this.xrLabel7, "xrLabel7");
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            // 
            // lbl_gender
            // 
            resources.ApplyResources(this.lbl_gender, "lbl_gender");
            this.lbl_gender.Name = "lbl_gender";
            this.lbl_gender.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_gender.StylePriority.UseFont = false;
            this.lbl_gender.StylePriority.UseTextAlignment = false;
            // 
            // lbl_nationality
            // 
            resources.ApplyResources(this.lbl_nationality, "lbl_nationality");
            this.lbl_nationality.Name = "lbl_nationality";
            this.lbl_nationality.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_nationality.StylePriority.UseFont = false;
            this.lbl_nationality.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel27
            // 
            resources.ApplyResources(this.xrLabel27, "xrLabel27");
            this.xrLabel27.Name = "xrLabel27";
            this.xrLabel27.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel27.StylePriority.UseFont = false;
            this.xrLabel27.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel5
            // 
            resources.ApplyResources(this.xrLabel5, "xrLabel5");
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            // 
            // lbl_birthDate
            // 
            resources.ApplyResources(this.lbl_birthDate, "lbl_birthDate");
            this.lbl_birthDate.Name = "lbl_birthDate";
            this.lbl_birthDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_birthDate.StylePriority.UseFont = false;
            this.lbl_birthDate.StylePriority.UseTextAlignment = false;
            // 
            // lbl_birthPlace
            // 
            resources.ApplyResources(this.lbl_birthPlace, "lbl_birthPlace");
            this.lbl_birthPlace.Name = "lbl_birthPlace";
            this.lbl_birthPlace.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_birthPlace.StylePriority.UseFont = false;
            this.lbl_birthPlace.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel11
            // 
            resources.ApplyResources(this.xrLabel11, "xrLabel11");
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel13
            // 
            resources.ApplyResources(this.xrLabel13, "xrLabel13");
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            // 
            // lbl_nationalEndDate
            // 
            resources.ApplyResources(this.lbl_nationalEndDate, "lbl_nationalEndDate");
            this.lbl_nationalEndDate.Name = "lbl_nationalEndDate";
            this.lbl_nationalEndDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_nationalEndDate.StylePriority.UseFont = false;
            this.lbl_nationalEndDate.StylePriority.UseTextAlignment = false;
            // 
            // lbl_nationalId
            // 
            resources.ApplyResources(this.lbl_nationalId, "lbl_nationalId");
            this.lbl_nationalId.Name = "lbl_nationalId";
            this.lbl_nationalId.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_nationalId.StylePriority.UseFont = false;
            this.lbl_nationalId.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel16
            // 
            resources.ApplyResources(this.xrLabel16, "xrLabel16");
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel17
            // 
            resources.ApplyResources(this.xrLabel17, "xrLabel17");
            this.xrLabel17.Name = "xrLabel17";
            this.xrLabel17.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel17.StylePriority.UseFont = false;
            this.xrLabel17.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Religion
            // 
            resources.ApplyResources(this.lbl_Religion, "lbl_Religion");
            this.lbl_Religion.Name = "lbl_Religion";
            this.lbl_Religion.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Religion.StylePriority.UseFont = false;
            this.lbl_Religion.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Marital
            // 
            resources.ApplyResources(this.lbl_Marital, "lbl_Marital");
            this.lbl_Marital.Name = "lbl_Marital";
            this.lbl_Marital.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Marital.StylePriority.UseFont = false;
            this.lbl_Marital.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel20
            // 
            resources.ApplyResources(this.xrLabel20, "xrLabel20");
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel20.StylePriority.UseFont = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel21
            // 
            resources.ApplyResources(this.xrLabel21, "xrLabel21");
            this.xrLabel21.Name = "xrLabel21";
            this.xrLabel21.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel21.StylePriority.UseFont = false;
            this.xrLabel21.StylePriority.UseTextAlignment = false;
            // 
            // lbl_InsturanceDate
            // 
            resources.ApplyResources(this.lbl_InsturanceDate, "lbl_InsturanceDate");
            this.lbl_InsturanceDate.Name = "lbl_InsturanceDate";
            this.lbl_InsturanceDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_InsturanceDate.StylePriority.UseFont = false;
            this.lbl_InsturanceDate.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Insturance
            // 
            resources.ApplyResources(this.lbl_Insturance, "lbl_Insturance");
            this.lbl_Insturance.Name = "lbl_Insturance";
            this.lbl_Insturance.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Insturance.StylePriority.UseFont = false;
            this.lbl_Insturance.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel24
            // 
            resources.ApplyResources(this.xrLabel24, "xrLabel24");
            this.xrLabel24.Name = "xrLabel24";
            this.xrLabel24.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel24.StylePriority.UseFont = false;
            this.xrLabel24.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel4
            // 
            resources.ApplyResources(this.xrLabel4, "xrLabel4");
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            // 
            // lbl_dep
            // 
            resources.ApplyResources(this.lbl_dep, "lbl_dep");
            this.lbl_dep.Name = "lbl_dep";
            this.lbl_dep.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_dep.StylePriority.UseFont = false;
            this.lbl_dep.StylePriority.UseTextAlignment = false;
            // 
            // lbl_group
            // 
            resources.ApplyResources(this.lbl_group, "lbl_group");
            this.lbl_group.Name = "lbl_group";
            this.lbl_group.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_group.StylePriority.UseFont = false;
            this.lbl_group.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel8
            // 
            resources.ApplyResources(this.xrLabel8, "xrLabel8");
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel9
            // 
            resources.ApplyResources(this.xrLabel9, "xrLabel9");
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Qualification
            // 
            resources.ApplyResources(this.lbl_Qualification, "lbl_Qualification");
            this.lbl_Qualification.Name = "lbl_Qualification";
            this.lbl_Qualification.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Qualification.StylePriority.UseFont = false;
            this.lbl_Qualification.StylePriority.UseTextAlignment = false;
            // 
            // lbl_job
            // 
            resources.ApplyResources(this.lbl_job, "lbl_job");
            this.lbl_job.Name = "lbl_job";
            this.lbl_job.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_job.StylePriority.UseFont = false;
            this.lbl_job.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel12
            // 
            resources.ApplyResources(this.xrLabel12, "xrLabel12");
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel3
            // 
            resources.ApplyResources(this.xrLabel3, "xrLabel3");
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            // 
            // lbl_empName
            // 
            resources.ApplyResources(this.lbl_empName, "lbl_empName");
            this.lbl_empName.Name = "lbl_empName";
            this.lbl_empName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_empName.StylePriority.UseFont = false;
            this.lbl_empName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_empNameF
            // 
            resources.ApplyResources(this.lbl_empNameF, "lbl_empNameF");
            this.lbl_empNameF.Name = "lbl_empNameF";
            this.lbl_empNameF.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_empNameF.StylePriority.UseFont = false;
            this.lbl_empNameF.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel6
            // 
            resources.ApplyResources(this.xrLabel6, "xrLabel6");
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel2
            // 
            resources.ApplyResources(this.xrLabel2, "xrLabel2");
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            // 
            // lbl_empStatus
            // 
            resources.ApplyResources(this.lbl_empStatus, "lbl_empStatus");
            this.lbl_empStatus.Name = "lbl_empStatus";
            this.lbl_empStatus.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_empStatus.StylePriority.UseFont = false;
            this.lbl_empStatus.StylePriority.UseTextAlignment = false;
            // 
            // lbl_empCode
            // 
            resources.ApplyResources(this.lbl_empCode, "lbl_empCode");
            this.lbl_empCode.Name = "lbl_empCode";
            this.lbl_empCode.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_empCode.StylePriority.UseFont = false;
            this.lbl_empCode.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel1
            // 
            resources.ApplyResources(this.xrLabel1, "xrLabel1");
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lblCompName,
            this.picLogo});
            resources.ApplyResources(this.TopMargin, "TopMargin");
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // lblCompName
            // 
            resources.ApplyResources(this.lblCompName, "lblCompName");
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // BottomMargin
            // 
            resources.ApplyResources(this.BottomMargin, "BottomMargin");
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // lbl_date
            // 
            resources.ApplyResources(this.lbl_date, "lbl_date");
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel22,
            this.xrLabel14,
            this.lbl_date});
            resources.ApplyResources(this.PageHeader, "PageHeader");
            this.PageHeader.Name = "PageHeader";
            // 
            // xrLabel22
            // 
            resources.ApplyResources(this.xrLabel22, "xrLabel22");
            this.xrLabel22.Multiline = true;
            this.xrLabel22.Name = "xrLabel22";
            this.xrLabel22.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel22.StylePriority.UseBackColor = false;
            this.xrLabel22.StylePriority.UseFont = false;
            this.xrLabel22.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel14
            // 
            resources.ApplyResources(this.xrLabel14, "xrLabel14");
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            // 
            // rpt_EmployeeDetails
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader});
            resources.ApplyResources(this, "$this");
            this.ShowPrintMarginsWarning = false;
            this.Version = "19.1";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRLabel lbl_empCode;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lbl_empStatus;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel lbl_empName;
        private DevExpress.XtraReports.UI.XRLabel lbl_empNameF;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel lbl_dep;
        private DevExpress.XtraReports.UI.XRLabel lbl_group;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel lbl_Qualification;
        private DevExpress.XtraReports.UI.XRLabel lbl_job;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lbl_birthDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_birthPlace;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
        private DevExpress.XtraReports.UI.XRLabel lbl_nationalEndDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_nationalId;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel xrLabel17;
        private DevExpress.XtraReports.UI.XRLabel lbl_Religion;
        private DevExpress.XtraReports.UI.XRLabel lbl_Marital;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel xrLabel21;
        private DevExpress.XtraReports.UI.XRLabel lbl_InsturanceDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_Insturance;
        private DevExpress.XtraReports.UI.XRLabel xrLabel24;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel lbl_gender;
        private DevExpress.XtraReports.UI.XRLabel lbl_nationality;
        private DevExpress.XtraReports.UI.XRLabel xrLabel27;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel lbl_contract;
        private DevExpress.XtraReports.UI.XRLabel lbl_contractEndDdate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel18;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel lbl_MilitryStatus;
        private DevExpress.XtraReports.UI.XRLabel lbl_Address;
        private DevExpress.XtraReports.UI.XRLabel xrLabel25;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel22;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel lbl_Mobile1;
        private DevExpress.XtraReports.UI.XRLabel txtMobile;
    }
}
