﻿namespace Reports.SL
{
    partial class frm_SL_Profit_Loss
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_Profit_Loss));
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.grd_data = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Index = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CusName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CGNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SalesEmpName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Store = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Net = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_purchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Profit_Loss = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Profit_LossPercentage = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Profit_Loss_Sign = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CrncId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Currency = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_ExchangeRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_NetLocal = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustGroup = new DevExpress.XtraGrid.Columns.GridColumn();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl1 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl2 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl3 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl4 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl5 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl6 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl7 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl8 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl9 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl10 = new DevExpress.XtraBars.BarDockControl();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar3 = new DevExpress.XtraBars.Bar();
            this.barBtn_PreviewData = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControl11 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl12 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl13 = new DevExpress.XtraBars.BarDockControl();
            this.barDockControl14 = new DevExpress.XtraBars.BarDockControl();
            this.barBtn_Preview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_data)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Currency)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            this.SuspendLayout();
            // 
            // bar1
            // 
            this.bar1.BarAppearance.Normal.Options.UseTextOptions = true;
            this.bar1.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bar1.BarItemHorzIndent = 9;
            this.bar1.BarItemVertIndent = 0;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(61, 172);
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.DrawSizeGrip = true;
            this.bar1.OptionsBar.MultiLine = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // grd_data
            // 
            resources.ApplyResources(this.grd_data, "grd_data");
            this.grd_data.MainView = this.gridView1;
            this.grd_data.Name = "grd_data";
            this.grd_data.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_Currency});
            this.grd_data.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.Appearance.HeaderPanel.Font")));
            this.gridView1.Appearance.HeaderPanel.Options.UseFont = true;
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.ColumnPanelRowHeight = 35;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Index,
            this.col_InvoiceDate,
            this.col_InvoiceCode,
            this.col_CusName,
            this.col_CGNameAr,
            this.col_SalesEmpName,
            this.col_Store,
            this.col_Net,
            this.col_purchasePrice,
            this.col_Profit_Loss,
            this.col_Profit_LossPercentage,
            this.col_Profit_Loss_Sign,
            this.col_CrncId,
            this.col_ExchangeRate,
            this.col_NetLocal,
            this.col_CustGroup});
            this.gridView1.CustomizationFormBounds = new System.Drawing.Rectangle(932, 415, 210, 277);
            this.gridView1.DetailHeight = 50;
            this.gridView1.GridControl = this.grd_data;
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary"))), resources.GetString("gridView1.GroupSummary1"), this.col_Profit_Loss, resources.GetString("gridView1.GroupSummary2")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary3"))), resources.GetString("gridView1.GroupSummary4"), this.col_Profit_LossPercentage, resources.GetString("gridView1.GroupSummary5"))});
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowChildrenInGroupPanel = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            // 
            // col_Index
            // 
            resources.ApplyResources(this.col_Index, "col_Index");
            this.col_Index.FieldName = "Index";
            this.col_Index.Name = "col_Index";
            this.col_Index.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.col_Index.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // col_InvoiceDate
            // 
            resources.ApplyResources(this.col_InvoiceDate, "col_InvoiceDate");
            this.col_InvoiceDate.FieldName = "InvoiceDate";
            this.col_InvoiceDate.Name = "col_InvoiceDate";
            this.col_InvoiceDate.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.col_InvoiceDate.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            // 
            // col_InvoiceCode
            // 
            resources.ApplyResources(this.col_InvoiceCode, "col_InvoiceCode");
            this.col_InvoiceCode.FieldName = "InvoiceCode";
            this.col_InvoiceCode.Name = "col_InvoiceCode";
            this.col_InvoiceCode.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_CusName
            // 
            resources.ApplyResources(this.col_CusName, "col_CusName");
            this.col_CusName.FieldName = "CusName";
            this.col_CusName.Name = "col_CusName";
            this.col_CusName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            // 
            // col_CGNameAr
            // 
            resources.ApplyResources(this.col_CGNameAr, "col_CGNameAr");
            this.col_CGNameAr.FieldName = "CGNameAr";
            this.col_CGNameAr.Name = "col_CGNameAr";
            this.col_CGNameAr.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            // 
            // col_SalesEmpName
            // 
            resources.ApplyResources(this.col_SalesEmpName, "col_SalesEmpName");
            this.col_SalesEmpName.FieldName = "SalesEmpName";
            this.col_SalesEmpName.Name = "col_SalesEmpName";
            this.col_SalesEmpName.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            // 
            // col_Store
            // 
            resources.ApplyResources(this.col_Store, "col_Store");
            this.col_Store.FieldName = "Store";
            this.col_Store.Name = "col_Store";
            this.col_Store.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            // 
            // col_Net
            // 
            resources.ApplyResources(this.col_Net, "col_Net");
            this.col_Net.DisplayFormat.FormatString = "n2";
            this.col_Net.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Net.FieldName = "Net";
            this.col_Net.Name = "col_Net";
            this.col_Net.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.col_Net.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Net.Summary"))), resources.GetString("col_Net.Summary1"), resources.GetString("col_Net.Summary2"))});
            // 
            // col_purchasePrice
            // 
            resources.ApplyResources(this.col_purchasePrice, "col_purchasePrice");
            this.col_purchasePrice.FieldName = "purchasePrice";
            this.col_purchasePrice.Name = "col_purchasePrice";
            this.col_purchasePrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_purchasePrice.Summary"))), resources.GetString("col_purchasePrice.Summary1"), resources.GetString("col_purchasePrice.Summary2"))});
            // 
            // col_Profit_Loss
            // 
            resources.ApplyResources(this.col_Profit_Loss, "col_Profit_Loss");
            this.col_Profit_Loss.FieldName = "Profit_Loss";
            this.col_Profit_Loss.Name = "col_Profit_Loss";
            this.col_Profit_Loss.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            this.col_Profit_Loss.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Profit_Loss.Summary"))), resources.GetString("col_Profit_Loss.Summary1"), resources.GetString("col_Profit_Loss.Summary2"))});
            // 
            // col_Profit_LossPercentage
            // 
            resources.ApplyResources(this.col_Profit_LossPercentage, "col_Profit_LossPercentage");
            this.col_Profit_LossPercentage.FieldName = "Profit_LossPercentage";
            this.col_Profit_LossPercentage.Name = "col_Profit_LossPercentage";
            this.col_Profit_LossPercentage.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_Profit_Loss_Sign
            // 
            resources.ApplyResources(this.col_Profit_Loss_Sign, "col_Profit_Loss_Sign");
            this.col_Profit_Loss_Sign.FieldName = "Profit_Loss_Sign";
            this.col_Profit_Loss_Sign.Name = "col_Profit_Loss_Sign";
            // 
            // col_CrncId
            // 
            resources.ApplyResources(this.col_CrncId, "col_CrncId");
            this.col_CrncId.ColumnEdit = this.rep_Currency;
            this.col_CrncId.FieldName = "CrncId";
            this.col_CrncId.Name = "col_CrncId";
            // 
            // rep_Currency
            // 
            resources.ApplyResources(this.rep_Currency, "rep_Currency");
            this.rep_Currency.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Currency.Buttons"))))});
            this.rep_Currency.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Currency.Columns"), resources.GetString("rep_Currency.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Currency.Columns2"), resources.GetString("rep_Currency.Columns3"), ((int)(resources.GetObject("rep_Currency.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_Currency.Columns5"))), resources.GetString("rep_Currency.Columns6"), ((bool)(resources.GetObject("rep_Currency.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_Currency.Columns8"))))});
            this.rep_Currency.Name = "rep_Currency";
            // 
            // col_ExchangeRate
            // 
            resources.ApplyResources(this.col_ExchangeRate, "col_ExchangeRate");
            this.col_ExchangeRate.FieldName = "CrncRate";
            this.col_ExchangeRate.Name = "col_ExchangeRate";
            // 
            // col_NetLocal
            // 
            resources.ApplyResources(this.col_NetLocal, "col_NetLocal");
            this.col_NetLocal.FieldName = "Net_Local";
            this.col_NetLocal.Name = "col_NetLocal";
            this.col_NetLocal.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_NetLocal.Summary"))), resources.GetString("col_NetLocal.Summary1"), resources.GetString("col_NetLocal.Summary2"))});
            // 
            // col_CustGroup
            // 
            resources.ApplyResources(this.col_CustGroup, "col_CustGroup");
            this.col_CustGroup.FieldName = "CustGroup";
            this.col_CustGroup.Name = "col_CustGroup";
            // 
            // bar2
            // 
            this.bar2.BarAppearance.Normal.Options.UseTextOptions = true;
            this.bar2.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bar2.BarItemHorzIndent = 9;
            this.bar2.BarItemVertIndent = 0;
            this.bar2.BarName = "Tools";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar2.FloatLocation = new System.Drawing.Point(61, 172);
            this.bar2.OptionsBar.AllowQuickCustomization = false;
            this.bar2.OptionsBar.DisableCustomization = true;
            this.bar2.OptionsBar.DrawSizeGrip = true;
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // barDockControl1
            // 
            this.barDockControl1.CausesValidation = false;
            resources.ApplyResources(this.barDockControl1, "barDockControl1");
            // 
            // barDockControl2
            // 
            this.barDockControl2.CausesValidation = false;
            resources.ApplyResources(this.barDockControl2, "barDockControl2");
            // 
            // barDockControl3
            // 
            this.barDockControl3.CausesValidation = false;
            resources.ApplyResources(this.barDockControl3, "barDockControl3");
            // 
            // barDockControl4
            // 
            this.barDockControl4.CausesValidation = false;
            resources.ApplyResources(this.barDockControl4, "barDockControl4");
            // 
            // barDockControl5
            // 
            this.barDockControl5.CausesValidation = false;
            resources.ApplyResources(this.barDockControl5, "barDockControl5");
            // 
            // barDockControl6
            // 
            this.barDockControl6.CausesValidation = false;
            resources.ApplyResources(this.barDockControl6, "barDockControl6");
            // 
            // barDockControl7
            // 
            this.barDockControl7.CausesValidation = false;
            resources.ApplyResources(this.barDockControl7, "barDockControl7");
            // 
            // barDockControl8
            // 
            this.barDockControl8.CausesValidation = false;
            resources.ApplyResources(this.barDockControl8, "barDockControl8");
            // 
            // barDockControl9
            // 
            this.barDockControl9.CausesValidation = false;
            resources.ApplyResources(this.barDockControl9, "barDockControl9");
            // 
            // barDockControl10
            // 
            this.barDockControl10.CausesValidation = false;
            resources.ApplyResources(this.barDockControl10, "barDockControl10");
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowMoveBarOnToolbar = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar3});
            this.barManager1.DockControls.Add(this.barDockControl11);
            this.barManager1.DockControls.Add(this.barDockControl12);
            this.barManager1.DockControls.Add(this.barDockControl13);
            this.barManager1.DockControls.Add(this.barDockControl14);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtn_Preview,
            this.barBtn_Help,
            this.barBtnClose,
            this.barBtn_PreviewData,
            this.barButtonItem1,
            this.barButtonItem2,
            this.barButtonItem3,
            this.barButtonItem4,
            this.barButtonItem5});
            this.barManager1.MaxItemId = 38;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar3
            // 
            this.bar3.BarAppearance.Normal.Options.UseTextOptions = true;
            this.bar3.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bar3.BarItemHorzIndent = 9;
            this.bar3.BarItemVertIndent = 0;
            this.bar3.BarName = "Tools";
            this.bar3.DockCol = 0;
            this.bar3.DockRow = 0;
            this.bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar3.FloatLocation = new System.Drawing.Point(61, 172);
            this.bar3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtn_PreviewData, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barButtonItem5, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar3.OptionsBar.AllowQuickCustomization = false;
            this.bar3.OptionsBar.DisableCustomization = true;
            this.bar3.OptionsBar.DrawSizeGrip = true;
            this.bar3.OptionsBar.MultiLine = true;
            this.bar3.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar3, "bar3");
            // 
            // barBtn_PreviewData
            // 
            this.barBtn_PreviewData.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_PreviewData, "barBtn_PreviewData");
            this.barBtn_PreviewData.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtn_PreviewData.Id = 31;
            this.barBtn_PreviewData.Name = "barBtn_PreviewData";
            this.barBtn_PreviewData.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_PreviewData_ItemClick);
            // 
            // barButtonItem5
            // 
            this.barButtonItem5.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barButtonItem5, "barButtonItem5");
            this.barButtonItem5.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barButtonItem5.Id = 37;
            this.barButtonItem5.Name = "barButtonItem5";
            this.barButtonItem5.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem5_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 28;
            this.barBtnClose.ItemAppearance.Normal.Font = ((System.Drawing.Font)(resources.GetObject("barBtnClose.ItemAppearance.Normal.Font")));
            this.barBtnClose.ItemAppearance.Normal.Options.UseFont = true;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick_1);
            // 
            // barDockControl11
            // 
            this.barDockControl11.Appearance.Options.UseTextOptions = true;
            this.barDockControl11.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControl11.CausesValidation = false;
            resources.ApplyResources(this.barDockControl11, "barDockControl11");
            // 
            // barDockControl12
            // 
            this.barDockControl12.CausesValidation = false;
            resources.ApplyResources(this.barDockControl12, "barDockControl12");
            // 
            // barDockControl13
            // 
            this.barDockControl13.CausesValidation = false;
            resources.ApplyResources(this.barDockControl13, "barDockControl13");
            // 
            // barDockControl14
            // 
            this.barDockControl14.CausesValidation = false;
            resources.ApplyResources(this.barDockControl14, "barDockControl14");
            // 
            // barBtn_Preview
            // 
            this.barBtn_Preview.ActAsDropDown = true;
            this.barBtn_Preview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Preview.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            resources.ApplyResources(this.barBtn_Preview, "barBtn_Preview");
            this.barBtn_Preview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtn_Preview.Id = 1;
            this.barBtn_Preview.ItemAppearance.Normal.Font = ((System.Drawing.Font)(resources.GetObject("barBtn_Preview.ItemAppearance.Normal.Font")));
            this.barBtn_Preview.ItemAppearance.Normal.Options.UseFont = true;
            this.barBtn_Preview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtn_Preview.Name = "barBtn_Preview";
            this.barBtn_Preview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barBtn_Help
            // 
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barButtonItem1
            // 
            resources.ApplyResources(this.barButtonItem1, "barButtonItem1");
            this.barButtonItem1.Id = 33;
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // barButtonItem2
            // 
            resources.ApplyResources(this.barButtonItem2, "barButtonItem2");
            this.barButtonItem2.Id = 34;
            this.barButtonItem2.Name = "barButtonItem2";
            // 
            // barButtonItem3
            // 
            resources.ApplyResources(this.barButtonItem3, "barButtonItem3");
            this.barButtonItem3.Id = 35;
            this.barButtonItem3.Name = "barButtonItem3";
            // 
            // barButtonItem4
            // 
            resources.ApplyResources(this.barButtonItem4, "barButtonItem4");
            this.barButtonItem4.Id = 36;
            this.barButtonItem4.Name = "barButtonItem4";
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // frm_SL_Profit_Loss
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grd_data);
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Controls.Add(this.barDockControl13);
            this.Controls.Add(this.barDockControl14);
            this.Controls.Add(this.barDockControl12);
            this.Controls.Add(this.barDockControl11);
            this.Name = "frm_SL_Profit_Loss";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_Profit_Loss_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_Profit_Loss_Load);
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_data)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Currency)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraGrid.GridControl grd_data;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarDockControl barDockControl1;
        private DevExpress.XtraBars.BarDockControl barDockControl2;
        private DevExpress.XtraBars.BarDockControl barDockControl3;
        private DevExpress.XtraBars.BarDockControl barDockControl4;
        private DevExpress.XtraBars.BarDockControl barDockControl5;
        private DevExpress.XtraBars.BarDockControl barDockControl6;
        private DevExpress.XtraBars.BarDockControl barDockControl7;
        private DevExpress.XtraBars.BarDockControl barDockControl8;
        private DevExpress.XtraBars.BarDockControl barDockControl9;
        private DevExpress.XtraBars.BarDockControl barDockControl10;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar3;
        private DevExpress.XtraBars.BarButtonItem barBtn_PreviewData;
        private DevExpress.XtraBars.BarButtonItem barButtonItem5;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControl11;
        private DevExpress.XtraBars.BarDockControl barDockControl12;
        private DevExpress.XtraBars.BarDockControl barDockControl13;
        private DevExpress.XtraBars.BarDockControl barDockControl14;
        private DevExpress.XtraBars.BarButtonItem barBtn_Preview;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem3;
        private DevExpress.XtraBars.BarButtonItem barButtonItem4;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_CusName;
        private DevExpress.XtraGrid.Columns.GridColumn col_CGNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_SalesEmpName;
        private DevExpress.XtraGrid.Columns.GridColumn col_Net;
        private DevExpress.XtraGrid.Columns.GridColumn col_Index;
        private DevExpress.XtraGrid.Columns.GridColumn col_Profit_Loss;
        private DevExpress.XtraGrid.Columns.GridColumn col_Profit_LossPercentage;
        private DevExpress.XtraGrid.Columns.GridColumn col_Store;
        private DevExpress.XtraGrid.Columns.GridColumn col_purchasePrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_Profit_Loss_Sign;
        private DevExpress.XtraGrid.Columns.GridColumn col_CrncId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_Currency;
        private DevExpress.XtraGrid.Columns.GridColumn col_ExchangeRate;
        private DevExpress.XtraGrid.Columns.GridColumn col_NetLocal;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustGroup;
    }
}